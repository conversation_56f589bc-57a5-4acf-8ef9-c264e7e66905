{"name": "gql-generator", "version": "1.0.0", "description": "A tool to generate TypeScript types from GraphQL schemas", "main": "dist/src/index.js", "bin": {"gql-generator": "src/cli.js", "gqgen": "src/cli.js"}, "_moduleAliases": {"@core": "dist/src", "@utils": "dist/src/utils", "@generators": "dist/src/generators", "@templates": "dist/src/generators/templates", "@resolvers": "dist/src/generators/resolver-generators", "@gql-generator/context": "dist/src/context"}, "exports": {".": "./dist/src/index.js", "./types": "./dist/src/generate-types.js", "./context": "./dist/src/context.js"}, "scripts": {"prebuild": "node -e \"require('fs').existsSync('dist') && require('fs-extra').emptyDirSync('dist')\"", "build": "tsc", "postbuild": "node scripts/make-executable.js", "start": "ts-node src/index.ts", "clean": "ts-node src/clean.ts", "generate": "npx graphql-codegen --config codegen.ts", "generate:types": "ts-node src/generate-types.ts", "generate:all": "npm run generate && npm run generate:types", "generate:debug": "ts-node src/index.ts generate --debug", "init": "ts-node src/init.ts", "prepare": "npm run build", "postinstall": "node -e \"process.env.INIT_CWD !== process.cwd() && process.exit(0)\" || npm run build", "build-and-install": "npm install && npm run build && npm install -g .", "setup": "node scripts/cross-platform-setup.js", "test": "jest", "test:phase5": "node test/phase5/run-all-tests.js", "test:regression": "node test/phase5/regression/run-regression-tests.js", "test:performance": "node test/phase5/performance/benchmark-runner.js", "test:comment-directive-performance": "jest --testPathPattern=comment-directive-performance", "validate-performance": "node scripts/validate-performance.js", "verify-wasm": "node scripts/verify-wasm-binaries.js", "benchmark": "gql-generator benchmark --test-size medium", "quick-benchmark": "gql-generator quick-benchmark --test-size small", "test:cross-platform": "npm test -- --testPathPattern=phase5-cross-platform", "test:docs": "node test/phase5/docs/validate-examples.js", "lint": "eslint \"src/**/*.ts\"", "format": "prettier --write \"src/**/*.ts\""}, "engines": {"node": ">=16.0.0"}, "keywords": ["graphql", "typescript", "code-generation", "schema"], "author": "", "license": "MIT", "dependencies": {"@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/schema-ast": "^4.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-resolvers": "^4.0.1", "@graphql-tools/load-files": "^7.0.0", "@graphql-tools/merge": "^9.0.0", "@graphql-tools/schema": "^10.0.0", "@napi-rs/cli": "^2.18.4", "@swc/core": "^1.12.11", "chalk": "^5.4.1", "chokidar": "^4.0.3", "commander": "^11.1.0", "fs-extra": "^11.1.1", "glob": "^10.4.5", "graphql": "^16.8.1", "handlebars": "^4.7.8", "module-alias": "^2.2.3", "ora": "^8.2.0", "oxc-parser": "^0.77.0"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.5", "@types/lodash": "^4.17.16", "@types/node": "^20.8.3", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.26.1", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.1.1", "ts-morph": "^25.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint-parser": "^21.0.2", "typescript-parser": "^2.6.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleNameMapper": {"^@utils/(.*)$": "<rootDir>/src/utils/$1", "^@core/(.*)$": "<rootDir>/src/$1", "^@generators/(.*)$": "<rootDir>/src/generators/$1", "^@resolvers/(.*)$": "<rootDir>/src/generators/resolver-generators/$1", "^@templates/(.*)$": "<rootDir>/src/generators/templates/$1", "^@gql-generator/context$": "<rootDir>/src/context.ts"}}, "bundleDependencies": ["@graphql-codegen/cli", "@graphql-codegen/schema-ast", "@graphql-codegen/typescript", "@graphql-codegen/typescript-operations", "@graphql-codegen/typescript-resolvers", "@graphql-tools/load-files", "@graphql-tools/merge", "@graphql-tools/schema"]}