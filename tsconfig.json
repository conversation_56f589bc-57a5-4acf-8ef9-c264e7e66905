{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "outDir": "dist", "declaration": true, "sourceMap": true, "rootDir": ".", "resolveJsonModule": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@gql-generator/context": ["./src/context.ts"], "@core/*": ["./src/*"], "@utils/*": ["./src/utils/*"], "@generators/*": ["./src/generators/*"], "@templates/*": ["./src/generators/templates/*"], "@resolvers/*": ["./src/generators/resolver-generators/*"]}}, "include": ["src/**/*", "src/templates", "src/generated/**/*", "codegen.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "src/generated/**/*"]}