import * as fs from 'fs-extra';
import * as path from 'path';
import type { GraphQLSchema } from 'graphql';
import type { GeneratorOptions } from '@utils/base-generator';
import { BaseGenerator } from '@utils/base-generator';
import { FieldResolverGenerator } from '@resolvers/field-resolver-generator';
import { TypeResolverGenerator } from '@resolvers/type-resolver-generator';
import { ObjectTypeProcessor } from '@resolvers/object-type-processor';
import { OperationTypeProcessor } from '@resolvers/operation-type-processor';
import { InterfaceUnionProcessor } from '@resolvers/interface-union-processor';
import { RootIndexGenerator } from '@resolvers/root-index-generator';
import { CleanupService } from '@generators/utils/cleanup-service';
import { SchemaTypeEnumGenerator } from '@generators/schema-type-enum-generator';
import { FunctionMapGenerator } from '@generators/function-map-generator';
import { DirectiveParser } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';
import * as glob from 'glob';
import { MutationTestGenerator } from '@generators/test-generators';
import { DecoratorParser } from '@utils/decorator-parser';
import { DecoratorProcessor } from '@utils/decorator-processor';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '@utils/decorator-types';
import { PerformanceOptimizer } from '@utils/performance-optimizer';
import type { DecoratorContainer } from '@utils/decorator-parser';
import type { DirectiveContainer } from '@utils/directive-parser';
import { postProcessTypes } from '@utils/post-process-types';
import { AliasConfig, AliasConfigUtils } from '@utils/alias-config';
import { initializeGlobalTemplateSystem } from '@utils/template-initialization-service';
import { getGlobalBatchFileWriter, shutdownGlobalBatchFileWriter } from '@utils/batch-file-writer';
import { getGlobalDependencyGraph } from '@utils/dependency-graph';
import { getGlobalSmartCacheInvalidation } from '@utils/smart-cache-invalidation';
import { getGlobalIncrementalTypeProcessor } from '@utils/incremental-type-processor';
import { TypeMapBuilder, getGlobalTypeMapBuilder } from '@utils/type-map-builder';
import { formatDirectory, FormatterType, BatchFormatter } from '@utils/batch-formatter';
import type { TypeMap, TypeMapConfig } from '@utils/type-map';

/**
 * Options for the schema-based generator
 */
export interface SchemaBasedGeneratorOptions extends GeneratorOptions {
  /** Use force mode to overwrite existing files */
  force?: boolean;
  /** Whether to preserve custom implementations */
  preserveCustomImplementations?: boolean;
  /** Path to the output directory for mutations */
  mutationOutput?: string;
  /** Path to the output directory for tests */
  testOutput?: string;
  /** Whether to generate tests */
  generateTests?: boolean;
  /** Whether to generate individual test files for each mutation */
  individualTests?: boolean;
  /** Enable debug mode for verbose logging */
  debug?: boolean;
  /** Whether to apply alias directives even for custom implementations */
  applyAliases?: boolean;
  /** Path to user codebase directory for decorator scanning */
  codebaseDir?: string;
  /** Enable TypeScript decorator scanning */
  enableDecorators?: boolean;
  /** Import alias for codebase directory */
  aliasCodebase?: string;
  /** Enable two-phase processing with type maps */
  enableTwoPhaseProcessing?: boolean;
  /** Pre-built type map for two-phase processing */
  typeMap?: TypeMap;
  /** Enable post-generation formatting (default: true) */
  enableFormatting?: boolean;
  /** Formatter to use (biome, prettier, swc, none) - defaults to auto-detect with biome preferred */
  formatter?: FormatterType;
}

/**
 * Schema-based generator that creates implementations based on the schema directory structure
 * This class orchestrates the generation process by delegating to specialized generators.
 */
export class SchemaBasedGenerator extends BaseGenerator {
  private deletedFiles: Set<string> = new Set();
  private preservedFiles: Set<string> = new Set();
  private processedFiles: Set<string> = new Set();
  private processedDirectories: Set<string> = new Set();
  private generatedFileTimestamps: Map<string, number> = new Map();
  protected schemaBasedOptions: SchemaBasedGeneratorOptions;

  // Decorator processing components
  private decoratorParser?: DecoratorParser;
  private decoratorProcessor?: DecoratorProcessor;
  private decoratorMetadata?: DecoratorContainer;

  // Two-phase processing components
  private typeMapBuilder?: TypeMapBuilder;
  private typeMap?: TypeMap;

  // Phase 2 Optimization: Dependency tracking components
  private dependencyGraph = getGlobalDependencyGraph();
  private cacheInvalidation = getGlobalSmartCacheInvalidation();
  private incrementalProcessor = getGlobalIncrementalTypeProcessor();

  // Specialized generators
  private fieldResolverGenerator!: FieldResolverGenerator;
  private typeResolverGenerator!: TypeResolverGenerator;
  private objectTypeProcessor!: ObjectTypeProcessor;
  private operationTypeProcessor!: OperationTypeProcessor;
  private interfaceUnionProcessor!: InterfaceUnionProcessor;
  private rootIndexGenerator!: RootIndexGenerator;
  private cleanupService!: CleanupService;
  private schemaTypeEnumGenerator!: SchemaTypeEnumGenerator;
  private functionMapGenerator!: FunctionMapGenerator;

  // Performance monitoring for Phase 1 optimizations
  private parallelProcessingMetrics: {
    parallelTime?: number;
    sequentialTime?: number;
    speedupRatio?: number;
    processingMode?: 'parallel' | 'sequential' | 'fallback' | 'incremental';
    memoryUsage?: {
      initial: number;
      peak: number;
      final: number;
    };
  } = {};

  /**
   * Create a new schema-based generator
   * @param schema The GraphQL schema
   * @param options Generator options
   */
  constructor(schema: GraphQLSchema, options: SchemaBasedGeneratorOptions = {}) {
    super(schema, options);
    this.schemaBasedOptions = options;

    // Initialize all the specialized generators
    this.initializeGenerators();
  }

  /**
   * Initialize all the specialized generators
   */
  private async initializeGenerators(): Promise<void> {
    // Get context information from options or schema directive
    let contextPath = this.schemaBasedOptions.context;
    let contextName = this.schemaBasedOptions.contextName ?? 'Context';

    // If no context specified in options, try to get from decorators first, then schema directive
    if (!contextPath) {
      // Try to get context from decorators if available
      const decoratorContext = this.extractContextFromDecorators();
      if (decoratorContext) {
        if (this.schemaBasedOptions.debug) {
          if (process.env.DEBUG_PARSER) {
            console.log(`Found context decorator: path=${decoratorContext.path}, name=${decoratorContext.name}`);
          }
        }
        contextPath = decoratorContext.path;
        contextName = decoratorContext.name;
      } else {
        // Fall back to schema directive
        const schemaContext = await this.extractContextFromSchema();
        if (schemaContext) {
          if (this.schemaBasedOptions.debug) {
            if (process.env.DEBUG_PARSER) {
              console.log(`Found context directive in schema: path=${schemaContext.path}, name=${schemaContext.name}`);
            }
          }
          contextPath = schemaContext.path;
          contextName = schemaContext.name;
        }
      }
    }

    // If still no context path, use default
    contextPath ??= '@gql-generator/context';

    // If it's not an alias path (doesn't start with @), ensure it's properly resolved
    if (!contextPath.startsWith('@')) {
      let absoluteContextPath: string;

      if (path.isAbsolute(contextPath)) {
        absoluteContextPath = contextPath;
      } else {
        absoluteContextPath = path.resolve(process.cwd(), contextPath);
      }

      contextPath = path.relative(process.cwd(), absoluteContextPath).replace(/\\/g, '/');

      // Ensure the path has at least ./ if it doesn't go up directories
      if (!contextPath.startsWith('.')) {
        contextPath = './' + contextPath;
      }
    }

    if (this.schemaBasedOptions.debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`Using context path: ${contextPath}, name: ${contextName}`);
      }
    }

    // Store updated context info for this generator instance
    this.schemaBasedOptions.context = contextPath;
    this.schemaBasedOptions.contextName = contextName;

    // Create the field resolver generator with updated context info
    this.fieldResolverGenerator = new FieldResolverGenerator(this.schema, {
      outputRoot: this.outputRoot,
      force: !!this.schemaBasedOptions.force,
      contextPath,
      contextName,
      preservedFiles: this.preservedFiles,
      processedFiles: this.processedFiles,
      processedDirectories: this.processedDirectories,
      generatedFileTimestamps: this.generatedFileTimestamps,
      schemaMapper: this.schemaMapper,
      codebaseDir: this.schemaBasedOptions.codebaseDir
    });

    // Set decorator providers if decorators are enabled
    if (this.schemaBasedOptions.enableDecorators) {
      this.fieldResolverGenerator.setDecoratorProviders(
        (typeName, fieldName, schemaId) => this.getDecoratorDirectives(typeName, fieldName, schemaId),
        (typeName, fieldName, targetOutputPath, schemaId) => this.getDecoratorSmartImports(typeName, fieldName, targetOutputPath, schemaId),
        () => this.decoratorMetadata
      );
      // Set the global schema ID for multi-schema support
      this.fieldResolverGenerator.setGlobalSchemaId(this.schemaBasedOptions.schemaId);

      // Set alias configuration for import path generation
      this.fieldResolverGenerator.setAliasConfig(
        this.schemaBasedOptions.codebaseDir,
        this.schemaBasedOptions.aliasCodebase
      );
    }

    // Create the type resolver generator with updated context info
    this.typeResolverGenerator = new TypeResolverGenerator(this.schema, {
      outputRoot: this.outputRoot,
      force: !!this.schemaBasedOptions.force,
      contextPath,
      contextName,
      preservedFiles: this.preservedFiles,
      processedFiles: this.processedFiles,
      processedDirectories: this.processedDirectories,
      schemaMapper: this.schemaMapper
    });

    // Set decorator providers for type resolver generator if decorators are enabled
    if (this.schemaBasedOptions.enableDecorators) {
      this.typeResolverGenerator.setDecoratorProviders(
        (typeName, fieldName, schemaId) => this.getDecoratorDirectives(typeName, fieldName, schemaId),
        (typeName, fieldName, targetOutputPath, schemaId) => this.getDecoratorSmartImports(typeName, fieldName, targetOutputPath, schemaId)
      );

    }

    // Create the object type processor
    this.objectTypeProcessor = new ObjectTypeProcessor(
      this.schema,
      this.fieldResolverGenerator,
      this.typeResolverGenerator,
      this.schemaMapper
    );

    // Create the operation type processor
    this.operationTypeProcessor = new OperationTypeProcessor(
      this.schema,
      this.fieldResolverGenerator,
      this.schemaMapper
    );

    // Create the interface/union processor
    this.interfaceUnionProcessor = new InterfaceUnionProcessor(
      this.schema,
      this.typeResolverGenerator,
      this.fieldResolverGenerator,
      this.schemaMapper
    );

    // Create the root index generator
    this.rootIndexGenerator = new RootIndexGenerator(
      this.outputRoot,
      this.processedFiles
    );

    // Create the cleanup service
    this.cleanupService = new CleanupService({
      outputRoot: this.outputRoot,
      processedFiles: this.processedFiles,
      processedDirectories: this.processedDirectories,
      preservedFiles: this.preservedFiles,
      generatedFileTimestamps: this.generatedFileTimestamps
    });

    // Create the schema type enum generator
    this.schemaTypeEnumGenerator = new SchemaTypeEnumGenerator(this.schema, {
      schema: this.schemaBasedOptions.schema,
      output: this.schemaBasedOptions.output,
      force: this.schemaBasedOptions.force,
      debug: this.schemaBasedOptions.debug,
      schemaId: this.schemaBasedOptions.schemaId
    });

    // Create the function map generator
    this.functionMapGenerator = new FunctionMapGenerator(this.schema, {
      outputRoot: this.outputRoot,
      schemaMapper: this.schemaMapper
    });

    // Set decorator provider for function map generator if decorators are enabled
    if (this.schemaBasedOptions.enableDecorators) {
      this.functionMapGenerator.setDecoratorProvider(
        (typeName: string, fieldName?: string, schemaId?: string) => this.getDecoratorDirectives(typeName, fieldName, schemaId)
      );
      // Set the global schema ID for multi-schema support
      this.functionMapGenerator.setGlobalSchemaId(this.schemaBasedOptions.schemaId);
    }
  }

  /**
   * Generate implementation files for all types in the schema
   */
  public async generate(): Promise<void> {
    if (this.schemaBasedOptions.debug) {
      if (process.env.DEBUG_PARSER) {
        console.log('Generating schema-based implementations...');
      }
    }

    // Debug logging
    if (this.schemaBasedOptions.debug) {
      console.log(`[DEBUG] Starting schema-based generation with options:`, JSON.stringify(this.schemaBasedOptions, null, 2));
    }

    // Clear previous generation tracking data to ensure fresh generation
    this.processedFiles.clear();
    this.processedDirectories.clear();
    // Don't clear preservedFiles as we need to retain that information
    // this.preservedFiles.clear();
    // But clear the timestamps to ensure they're refreshed
    this.generatedFileTimestamps.clear();

    // Initialize the schema mapper
    await this.init();

    // Pre-compile templates at startup for Phase 1 optimization
    await this.initializeTemplateSystem();

    // Make sure the generators are initialized (async)
    await this.initializeGenerators();

    // Debug logging
    if (this.schemaBasedOptions.debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`[DEBUG] Generators initialized, starting decorator scanning and type processing`);
      }
    }

    // Phase 2 Optimization: Initialize dependency tracking
    await this.initializeDependencyTracking();

    // Two-phase processing: Only enable when explicitly requested OR when decorators are enabled with codebase
    const shouldUseTwoPhase = this.schemaBasedOptions.enableTwoPhaseProcessing ?? false;

    if (shouldUseTwoPhase) {
      if (this.schemaBasedOptions.debug) {
        console.log('🚀 Using two-phase processing for optimal performance');
      }
      await this.buildTypeMap();
    } else {
      // Scan for decorators if enabled (legacy mode)
      if (this.schemaBasedOptions.enableDecorators && this.schemaBasedOptions.codebaseDir) {
        if (this.schemaBasedOptions.debug) {
          console.log('🔍 Scanning codebase for TypeScript decorators...');
        }
        await this.scanDecorators();
      } else if (this.schemaBasedOptions.codebaseDir && this.schemaBasedOptions.debug) {
        console.log('📁 Codebase directory provided but decorators not enabled - skipping TypeScript scanning');
      }
    }

    // Phase 2 Optimization: Create incremental processing plan
    const processingPlan = await this.createIncrementalProcessingPlan();

    // Phase 1 Optimization: Process type categories in parallel for 2-4x speedup
    await this.processTypesInParallelWithIncremental(processingPlan);

    // Generate the root index file
    await this.rootIndexGenerator.generateRootIndex();

    // Generate schema type enum constants
    await this.schemaTypeEnumGenerator.generate();

    // Generate function maps
    await this.generateFunctionMaps();

    // Clean up any orphaned files
    if (!this.schemaBasedOptions.preserveCustomImplementations) {
      await this.cleanupService.cleanupObsoleteFiles();
    }

    // Generate test stubs if enabled
    if (this.schemaBasedOptions.generateTests) {
      await this.generateTestStubs();
    }

    // Cleanup empty directories
    this.cleanupEmptyDirectories(this.outputRoot);

    // Post-process the generated types to include @field directive fields and @GQLField decorators
    await this.postProcessGeneratedTypes();

    // Flush any remaining batched file writes for Phase 1 optimization
    await this.flushBatchedWrites();

    // Shutdown batch writer to clean up timers and prevent hanging
    await this.shutdownBatchWriter();

    // Format generated files if enabled
    await this.formatGeneratedFiles();

    // Report results
    this.reportResults();

    // Report Phase 1 optimization metrics
    this.reportPhase1Metrics();

    // Phase 2 Optimization: Save dependency tracking state
    await this.saveDependencyTrackingState();

    if (this.schemaBasedOptions.debug) {
      console.log('Schema-based implementation generation complete!');
    }
  }

  /**
   * Generate function maps for all types and interfaces
   */
  private async generateFunctionMaps(): Promise<void> {
    // Set interface inheritance handler if available
    if (this.fieldResolverGenerator && (this.fieldResolverGenerator as any).interfaceInheritanceHandler) {
      this.functionMapGenerator.setInterfaceInheritanceHandler((this.fieldResolverGenerator as any).interfaceInheritanceHandler);
    }

    await this.functionMapGenerator.generateFunctionMaps();
  }

  /**
   * Post-process the generated types to include @field directive fields and @GQLField decorators
   */
  private async postProcessGeneratedTypes(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Post-processing generated types...');
    }

    // Path to the generated graphql.ts file
    const graphqlTsPath = path.join(this.outputRoot, 'graphql.ts');

    // Check if the file exists
    if (!fs.existsSync(graphqlTsPath)) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Generated types file not found: ${graphqlTsPath}. Skipping post-processing.`);
      }
      return;
    }

    try {
      // Call postProcessTypes with type map if available (two-phase processing)
      await postProcessTypes(
        graphqlTsPath,
        this.schemaBasedOptions.schema || this.schemaRoot,
        this.schemaBasedOptions.debug || false,
        this.decoratorMetadata,
        this.schemaBasedOptions.codebaseDir,
        this.schemaBasedOptions.aliasCodebase,
        this.typeMap || this.schemaBasedOptions.typeMap
      );

      console.log('Post-processing of generated types completed successfully.');
    } catch (error) {
      console.error('Error during post-processing of generated types:', error);
      // Don't fail the entire generation process if post-processing fails
    }
  }

  /**
   * Generate test stubs for mutations
   */
  private async generateTestStubs(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Generating test stubs...');
    }

    // Find mutation output directory
    const mutationRoot = path.resolve(this.schemaBasedOptions.mutationOutput ?? this.outputRoot);

    // Process the test output path - replace {{output}} placeholder with actual output path
    let testOutputPath = this.schemaBasedOptions.testOutput ?? path.join(this.outputRoot, '__test__');
    if (testOutputPath.includes('{{output}}')) {
      testOutputPath = testOutputPath.replace('{{output}}', this.outputRoot);
    }

    // Create the test generator
    const testGenerator = new MutationTestGenerator({
      schema: this.schema,
      testOutput: testOutputPath,
      outputRoot: this.outputRoot,
      schemaRoot: this.schemaRoot,
      force: this.schemaBasedOptions.force ?? false,
      contextPath: this.schemaBasedOptions.context ?? '@gql-generator/context',
      contextName: this.schemaBasedOptions.contextName ?? 'Context',
      individualTests: this.schemaBasedOptions.individualTests ?? false,
      schemaMapper: this.schemaMapper,
      mutationRoot: fs.existsSync(mutationRoot) ? mutationRoot : null,
      applyAliases: this.schemaBasedOptions.applyAliases ?? false
    });

    // Generate test stubs
    await testGenerator.generate();
  }

  /**
   * Report the results of the generation process
   */
  private reportResults(): void {
    console.log('\nSchema-based generation completed:');
    if (process.env.DEBUG_PARSER) {
      console.log(`- Generated ${this.processedFiles.size} implementation files`);
    }
    if (process.env.DEBUG_PARSER) {
      console.log(`- Processed ${this.processedDirectories.size} directories`);
    }
    if (process.env.DEBUG_PARSER) {
      console.log(`- Preserved ${this.preservedFiles.size} files with custom implementation`);
    }
    if (process.env.DEBUG_PARSER) {
      console.log(`- Deleted ${this.cleanupService.getDeletedFilesCount()} obsolete files`);
    }
  }

  /**
   * Scan codebase for TypeScript decorators
   */
  private async scanDecorators(): Promise<void> {
    if (!this.schemaBasedOptions.codebaseDir || !this.schemaBasedOptions.enableDecorators) {
      return;
    }

    try {
      if (process.env.DEBUG_PARSER) {
        console.log(`Scanning decorators in codebase directory: ${this.schemaBasedOptions.codebaseDir}`);
      }

      // Initialize decorator parser with performance optimization if not already done
      if (!this.decoratorParser) {
        // Estimate file count for performance optimization
        const glob = require('glob');
        const patterns = ['**/*.ts', '**/*.tsx'];
        const allPatterns = patterns.map(pattern => `${this.schemaBasedOptions.codebaseDir}/${pattern}`);
        let estimatedFileCount = 0;

        for (const pattern of allPatterns) {
          const matchedFiles = glob.sync(pattern, {
            ignore: ['**/node_modules/**', '**/dist/**', '**/*.d.ts'],
            absolute: true
          });
          estimatedFileCount += matchedFiles.length;
        }

        // Use performance optimizer to create optimized parser
        const optimizer = new PerformanceOptimizer({
          enableParallelProcessing: estimatedFileCount > 50, // Enable for large codebases
          enableEnhancedCaching: true,
          enableMemoryOptimization: true,
          enablePerformanceMonitoring: this.schemaBasedOptions.debug
        });

        if (this.schemaBasedOptions.debug) {
          optimizer.logOptimizationSummary(estimatedFileCount);
        }

        this.decoratorParser = optimizer.createOptimizedParser(estimatedFileCount);
      }

      // Initialize decorator processor if not already done
      if (!this.decoratorProcessor) {
        const config = {
          ...DEFAULT_DECORATOR_CONFIG,
          enabled: true,
          codebaseDir: this.schemaBasedOptions.codebaseDir,
        };
        this.decoratorProcessor = new DecoratorProcessor(config, DEFAULT_PRECEDENCE_RULES);
      }

      // Scan the codebase for decorators
      this.decoratorMetadata = await this.decoratorParser.scanCodebase(this.schemaBasedOptions.codebaseDir);

      // Set the decorator metadata in the processor for smart import generation
      if (this.decoratorProcessor) {
        this.decoratorProcessor.setDecoratorMetadata(this.decoratorMetadata);

        // Set alias configuration if provided
        const aliasConfig = AliasConfigUtils.createAliasConfig(
          this.schemaBasedOptions.codebaseDir,
          this.schemaBasedOptions.aliasCodebase
        );
        this.decoratorProcessor.setAliasConfig(aliasConfig || undefined);
      }

      if (this.schemaBasedOptions.debug) {
        const stats = {
          methodCalls: this.decoratorMetadata.methodCalls.length,
          imports: this.decoratorMetadata.imports.length,
          fields: this.decoratorMetadata.fields.length,
          contexts: this.decoratorMetadata.contexts.length,
        };
        if (process.env.DEBUG_PARSER) {
          console.log(`[DEBUG] Decorator scan results:`, stats);
        }
      }

      console.log(`Found ${this.decoratorMetadata.methodCalls.length} @GQLMethodCall, ${this.decoratorMetadata.imports.length} @GQLImport, ${this.decoratorMetadata.fields.length} @GQLField decorators`);

    } catch (error) {
      console.error(`Error scanning decorators: ${error}`);
      if (this.schemaBasedOptions.debug) {
        console.error(`[DEBUG] Decorator scanning error details:`, error);
      }
      // Don't fail the entire generation process if decorator scanning fails
      this.decoratorMetadata = {
        methodCalls: [],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };
    }
  }

  /**
   * Get decorator-sourced directives for a specific type and field
   * @param typeName The GraphQL type name
   * @param fieldName Optional field name
   * @param schemaId Optional schema identifier for multi-schema support
   * @returns DirectiveContainer with decorator-sourced directives
   */
  public getDecoratorDirectives(typeName: string, fieldName?: string, schemaId?: string): DirectiveContainer {
    if (!this.decoratorMetadata || !this.decoratorProcessor) {
      return {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };
    }

    try {
      // Convert decorator metadata to directive container
      const decoratorDirectives = this.decoratorProcessor.convertToDirectiveContainer(
        this.decoratorMetadata,
        schemaId
      );

      // Filter directives for the specific type and field
      const filteredDirectives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };

      // Filter method calls for this type/field with deduplication
      const seenMethodCalls = new Set<string>();
      for (const methodCall of decoratorDirectives.methodCalls) {
        // Parse the method call content to extract type and field information
        // This is a simplified approach - in practice, we'd need more sophisticated parsing
        if (this.isMethodCallForTypeField(methodCall, typeName, fieldName)) {
          // Create a more specific deduplication key that includes type and field information
          // This prevents different decorators for different types/fields from being deduplicated together
          const methodCallKey = `${typeName}.${fieldName || 'undefined'}-${methodCall.content || ''}-${methodCall.name || ''}`;
          if (!seenMethodCalls.has(methodCallKey)) {
            seenMethodCalls.add(methodCallKey);
            filteredDirectives.methodCalls.push(methodCall);
          }
        }
      }

      // Include all imports (they're global)
      filteredDirectives.imports = decoratorDirectives.imports;

      // Filter field directives by ref (type name) and optionally by field name
      filteredDirectives.fieldFields = decoratorDirectives.fieldFields.filter(fieldField => {
        // Check if this field directive is for the current type
        // We need to find the original @GQLField decorator to get the ref
        if (!this.decoratorMetadata) {
          return false;
        }
        const matchingFieldDecorator = this.decoratorMetadata.fields.find(fd => {
          const typeMatches = fd.data.ref === typeName;
          const fieldMatches = fd.data.name === fieldField.name;
          const specificFieldMatches = !fieldName || fd.data.name === fieldName;
          return typeMatches && fieldMatches && specificFieldMatches;
        });
        return !!matchingFieldDecorator;
      });

      return filteredDirectives;

    } catch (error) {
      console.error(`Error processing decorator directives for ${typeName}${fieldName ? `.${fieldName}` : ''}: ${error}`);
      return {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };
    }
  }

  /**
   * Check if a method call directive is for a specific type and field
   * @param methodCall The method call directive
   * @param typeName The target type name
   * @param fieldName The target field name (optional)
   * @returns True if the method call matches the type/field
   */
  private isMethodCallForTypeField(methodCall: any, typeName: string, fieldName?: string): boolean {
    // Enhanced implementation using decorator metadata
    if (!this.decoratorMetadata) {
      return false;
    }

    // Check if this is a smart default method call
    const isSmartDefault = methodCall.content.startsWith('__SMART_DEFAULT__');

    // Find the original decorator metadata for this method call
    const matchingDecorator = this.decoratorMetadata.methodCalls.find(mc => {
      const typeMatches = mc.data.type === typeName;
      const fieldMatches = !fieldName || mc.data.field === fieldName;

      if (isSmartDefault) {
        // For smart defaults, match by type and field only, and ensure the decorator has no call
        const expectedMarker = `__SMART_DEFAULT__${typeName}.${fieldName}`;
        return typeMatches && fieldMatches && !mc.data.call && methodCall.content === expectedMarker;
      } else {
        // For explicit calls, match by content as well
        const generatedContent = mc.data.call;
        const contentMatches = methodCall.content === generatedContent || methodCall.content.includes(generatedContent);
        return typeMatches && fieldMatches && contentMatches;
      }
    });

    if (!matchingDecorator) {
      // Fallback to content-based matching for non-decorator method calls
      const content = methodCall.content || '';
      return content.includes(typeName) && (!fieldName || content.includes(fieldName));
    }

    // If we found a matching decorator, return true
    return true;
  }

  /**
   * Extract context information from decorator metadata
   * @param schemaId Optional schema identifier for multi-schema support
   * @returns Context data if found, otherwise null
   */
  private extractContextFromDecorators(schemaId?: string): { path: string; name: string } | null {
    if (!this.decoratorMetadata) {
      return null;
    }

    // Find context decorators for the specified schema (or default)
    const contextDecorators = this.decoratorMetadata.contexts.filter(context => {
      if (schemaId) {
        return context.data.schema === schemaId;
      }
      // If no schemaId specified, prefer decorators without schema or with 'default' schema
      return !context.data.schema || context.data.schema === 'default';
    });

    if (contextDecorators.length === 0) {
      return null;
    }

    // Use the first matching context decorator
    const contextData = contextDecorators[0].data;
    return {
      path: contextData.path,
      name: contextData.name,
    };
  }

  /**
   * Extract context information from schema directives
   * @returns Context data if found, otherwise null
   */
  private async extractContextFromSchema(): Promise<{ path: string; name: string } | null> {
    try {
      // Start by checking the schema.gql file for context directive
      const schemaFilePath = path.join(this.schemaMapper.schemaRoot, 'schema.gql');

      if (fs.existsSync(schemaFilePath)) {
        const directives = await DirectiveParser.extractSchemaDirectives(schemaFilePath);
        const contextInfo = DirectiveProcessor.extractContextInfo(directives);
        if (contextInfo) {
          if (process.env.DEBUG_PARSER) {
            console.log(`Found context directive in schema.gql: path=${contextInfo.path}, name=${contextInfo.name}`);
          }
          return { path: contextInfo.path, name: contextInfo.name };
        }
      }

      // If not found in schema.gql, check all schema files for context directive
      const schemaGlob = path.join(this.schemaMapper.schemaRoot, '**', '*.gql');
      if (process.env.DEBUG_PARSER) {
        console.log(`Looking for context directive in schema files: ${schemaGlob}`);
      }
      const schemaFiles = glob.sync(schemaGlob, { windowsPathsNoEscape: true });

      for (const file of schemaFiles) {
        // Skip schema.gql since we already checked it
        if (file === schemaFilePath) continue;

        try {
          // Read the file content
          const fileContent = fs.readFileSync(file, 'utf8');

          // Look for context directive
          const contextDirectiveMatch = fileContent.match(/#\s*@context\s*\(\s*path\s*:\s*["']([^"']+)["']\s*(?:,\s*name\s*:\s*["']([^"']+)["'])?\s*\)/);

          if (contextDirectiveMatch) {
            const path = contextDirectiveMatch[1];
            const name = contextDirectiveMatch[2] || 'Context';

            if (process.env.DEBUG_PARSER) {
              console.log(`Found context directive in ${file}: path=${path}, name=${name}`);
            }
            return { path, name };
          }

          // Also try to extract via the directive parser
          const fileName = path.basename(file, '.gql');
          const directives = await DirectiveParser.extractDirectivesFromSchema(file, fileName);
          const contextInfo = DirectiveProcessor.extractContextInfo(directives);
          if (contextInfo) {
            if (process.env.DEBUG_PARSER) {
              console.log(`Found context directive in ${file} via parser: path=${contextInfo.path}, name=${contextInfo.name}`);
            }
            return { path: contextInfo.path, name: contextInfo.name };
          }
        } catch (error) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`Error checking file ${file} for context directive:`, error);
          }
          continue;
        }
      }

      // No context directive found
      return null;
    } catch (error) {
      console.error(`Error extracting context from schema:`, error);
      return null;
    }
  }

  /**
   * Get smart imports for decorator-based method calls for a specific type and field
   * @param typeName The GraphQL type name
   * @param fieldName The GraphQL field name (optional)
   * @param targetOutputPath The target resolver output path
   * @param schemaId Optional schema identifier for multi-schema support
   * @returns Array of import statements
   */
  public getDecoratorSmartImports(
    typeName: string,
    fieldName: string | undefined,
    targetOutputPath: string,
    schemaId?: string
  ): string[] {
    if (!this.decoratorProcessor) {
      return [];
    }

    return this.decoratorProcessor.generateSmartImportsForTypeField(
      typeName,
      fieldName,
      targetOutputPath,
      schemaId
    );
  }

  /**
   * Get context configuration for a specific schema
   * @param schemaId Optional schema identifier
   * @returns Context configuration or null if not found
   */
  public getContextForSchema(schemaId?: string): { path: string; name: string } | null {
    // Try decorator context first
    const decoratorContext = this.extractContextFromDecorators(schemaId);
    if (decoratorContext) {
      return decoratorContext;
    }

    // Fall back to current context configuration
    if (this.schemaBasedOptions.context && this.schemaBasedOptions.contextName) {
      return {
        path: this.schemaBasedOptions.context,
        name: this.schemaBasedOptions.contextName,
      };
    }

    return null;
  }

  /**
   * Process type categories in parallel for Phase 1 optimization
   * @private
   */
  private async processTypesInParallel(): Promise<void> {
    // Check if parallel processing is enabled (default: true)
    const enableParallelProcessing = process.env.ENABLE_PARALLEL_PROCESSING !== 'false';

    if (!enableParallelProcessing) {
      if (this.schemaBasedOptions.debug) {
        console.log('🔄 Parallel processing disabled, using sequential processing');
      }
      await this.processTypesSequentially();
      return;
    }

    try {
      const startTime = Date.now();
      const initialMemory = this.getMemoryUsage();

      if (this.schemaBasedOptions.debug) {
        console.log('⚡ Processing type categories in parallel...');
        console.log(`   Initial memory usage: ${Math.round(initialMemory / 1024 / 1024)}MB`);
      }

      // Monitor memory usage during parallel processing
      let peakMemory = initialMemory;
      const memoryMonitor = setInterval(() => {
        const currentMemory = this.getMemoryUsage();
        if (currentMemory > peakMemory) {
          peakMemory = currentMemory;
        }
      }, 100); // Check every 100ms

      // Process independent type categories in parallel
      // These processors are independent and can safely run concurrently
      await Promise.all([
        // Operation types (queries, mutations, subscriptions)
        this.processOperationTypes(),

        // Object types
        this.objectTypeProcessor.processObjectTypes(),

        // Interfaces and unions
        this.interfaceUnionProcessor.processInterfacesAndUnions()
      ]);

      clearInterval(memoryMonitor);
      const parallelTime = Date.now() - startTime;
      const finalMemory = this.getMemoryUsage();

      // Record performance metrics
      this.parallelProcessingMetrics = {
        parallelTime,
        processingMode: 'parallel',
        memoryUsage: {
          initial: initialMemory,
          peak: peakMemory,
          final: finalMemory,
        },
      };

      if (this.schemaBasedOptions.debug) {
        console.log(`✅ Parallel type processing completed in ${parallelTime}ms`);
        console.log(`   Memory usage: ${Math.round(initialMemory / 1024 / 1024)}MB → ${Math.round(peakMemory / 1024 / 1024)}MB (peak) → ${Math.round(finalMemory / 1024 / 1024)}MB`);

        // Check for memory pressure
        const memoryIncrease = peakMemory - initialMemory;
        const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
        if (memoryIncreasePercent > 50) {
          console.log(`⚠️  High memory pressure detected: ${memoryIncreasePercent.toFixed(1)}% increase`);
        }

        // Estimate sequential time for comparison (rough estimate based on typical ratios)
        const estimatedSequentialTime = parallelTime * 3; // Conservative 3x estimate
        const estimatedSpeedup = estimatedSequentialTime / parallelTime;
        console.log(`📊 Estimated speedup: ${estimatedSpeedup.toFixed(1)}x (${estimatedSequentialTime}ms → ${parallelTime}ms)`);
      }

    } catch (error) {
      // If parallel processing fails, fall back to sequential processing
      if (this.schemaBasedOptions.debug) {
        console.warn('⚠️  Parallel processing failed, falling back to sequential processing:', error);
      }

      const fallbackStartTime = Date.now();
      await this.processTypesSequentially();
      const fallbackTime = Date.now() - fallbackStartTime;

      // Record fallback metrics
      this.parallelProcessingMetrics = {
        sequentialTime: fallbackTime,
        processingMode: 'fallback',
      };

      if (this.schemaBasedOptions.debug) {
        console.log(`🔄 Fallback sequential processing completed in ${fallbackTime}ms`);
      }
    }
  }

  /**
   * Process operation types (queries, mutations, subscriptions) in parallel
   * @private
   */
  private async processOperationTypes(): Promise<void> {
    // Operation types can also be processed in parallel since they're independent
    await Promise.all([
      this.operationTypeProcessor.processQueryTypes(),
      this.operationTypeProcessor.processMutationTypes(),
      this.operationTypeProcessor.processSubscriptionTypes()
    ]);
  }

  /**
   * Fallback sequential processing for compatibility
   * @private
   */
  private async processTypesSequentially(): Promise<void> {
    const startTime = Date.now();

    if (this.schemaBasedOptions.debug) {
      console.log('🔄 Processing types sequentially...');
    }

    // Process query operations
    await this.operationTypeProcessor.processQueryTypes();

    // Process mutation operations
    await this.operationTypeProcessor.processMutationTypes();

    // Process subscription operations
    await this.operationTypeProcessor.processSubscriptionTypes();

    // Process object types
    await this.objectTypeProcessor.processObjectTypes();

    // Process interfaces and unions
    await this.interfaceUnionProcessor.processInterfacesAndUnions();

    const sequentialTime = Date.now() - startTime;

    // Record sequential processing metrics
    if (this.parallelProcessingMetrics.processingMode !== 'fallback') {
      this.parallelProcessingMetrics = {
        ...this.parallelProcessingMetrics,
        sequentialTime,
        processingMode: 'sequential',
      };
    }

    if (this.schemaBasedOptions.debug) {
      console.log(`✅ Sequential type processing completed in ${sequentialTime}ms`);
    }
  }

  /**
   * Initialize template system with precompilation for Phase 1 optimization
   * @private
   */
  private async initializeTemplateSystem(): Promise<void> {
    // Check if template precompilation is enabled (default: true)
    const enableTemplatePrecompilation = process.env.ENABLE_TEMPLATE_PRECOMPILATION !== 'false';

    if (!enableTemplatePrecompilation) {
      if (this.schemaBasedOptions.debug) {
        console.log('📝 Template precompilation disabled via ENABLE_TEMPLATE_PRECOMPILATION=false');
      }
      return;
    }

    try {
      const startTime = Date.now();

      if (this.schemaBasedOptions.debug) {
        console.log('🚀 Initializing template system with precompilation...');
      }

      // Initialize the global template system with precompilation
      const result = await initializeGlobalTemplateSystem({
        enableLogging: this.schemaBasedOptions.debug,
        async: true,
        highPriorityOnly: false, // Precompile all templates for maximum benefit
        validateCatalog: true,
        cacheOptions: {
          enableLogging: this.schemaBasedOptions.debug,
          enablePerformanceTracking: true,
        },
        precompilationOptions: {
          enableLogging: this.schemaBasedOptions.debug,
          validateTemplates: true,
        },
      });

      const initTime = Date.now() - startTime;

      if (this.schemaBasedOptions.debug) {
        console.log(`✅ Template system initialized in ${initTime}ms`);
        console.log(`📊 Precompiled ${result.precompilationSummary.successfulCompilations} templates`);
        if (result.precompilationSummary.failedCompilations > 0) {
          console.warn(`⚠️  ${result.precompilationSummary.failedCompilations} template compilation failures`);
        }
        if (result.estimatedTimeSavings) {
          console.log(`⚡ Estimated time savings: ${result.estimatedTimeSavings}ms per generation`);
        }
      }

    } catch (error) {
      // Template precompilation failure should not block generation
      if (this.schemaBasedOptions.debug) {
        console.warn('⚠️  Template precompilation failed, continuing with runtime compilation:', error);
      }
    }
  }

  /**
   * Flush any remaining batched file writes for Phase 1 optimization
   * @private
   */
  private async flushBatchedWrites(): Promise<void> {
    // PERFORMANCE FIX: Disable batch I/O as it's causing 20+ second delays
    // The batch writer has performance issues that need to be resolved
    const enableBatchIO = false; // process.env.ENABLE_BATCH_IO !== 'false';

    if (!enableBatchIO) {
      return;
    }

    try {
      const startTime = Date.now();

      if (this.schemaBasedOptions.debug) {
        console.log('🔄 Flushing batched file writes...');
      }

      const batchWriter = getGlobalBatchFileWriter();
      const queueStatus = batchWriter.getQueueStatus();

      if (queueStatus.queuedFiles > 0) {
        await batchWriter.flushAll();

        const flushTime = Date.now() - startTime;
        const metrics = batchWriter.getMetrics();

        if (this.schemaBasedOptions.debug) {
          console.log(`✅ Flushed ${queueStatus.queuedFiles} files in ${flushTime}ms`);
          console.log(`📊 Batch I/O metrics: ${metrics.totalBatches} batches, ${Math.round(metrics.timesSaved)}ms saved`);
        }
      } else if (this.schemaBasedOptions.debug) {
        console.log('📦 No files queued for batch writing');
      }

    } catch (error) {
      // Batch flush failure should not block generation completion
      if (this.schemaBasedOptions.debug) {
        console.warn('⚠️  Batch flush failed, but generation completed:', error);
      }
    }
  }

  /**
   * Shutdown batch writer to clean up timers and prevent process hanging
   * @private
   */
  private async shutdownBatchWriter(): Promise<void> {
    // PERFORMANCE FIX: Disable batch I/O as it's causing 20+ second delays
    // The batch writer has performance issues that need to be resolved
    const enableBatchIO = false; // process.env.ENABLE_BATCH_IO !== 'false';

    if (!enableBatchIO) {
      return;
    }

    try {
      if (this.schemaBasedOptions.debug) {
        console.log('🔄 Shutting down batch writer...');
      }

      // Shutdown the global batch writer to clean up timers
      await shutdownGlobalBatchFileWriter();

      if (this.schemaBasedOptions.debug) {
        console.log('✅ Batch writer shutdown complete');
      }

    } catch (error) {
      // Batch writer shutdown failure should not block generation completion
      if (this.schemaBasedOptions.debug) {
        console.warn('⚠️  Batch writer shutdown failed:', error);
      }
    }
  }

  /**
   * Format all generated files using batch formatting for optimal performance
   * @private
   */
  private async formatGeneratedFiles(): Promise<void> {
    // Check if formatting is explicitly disabled or formatter is 'none'
    if (this.schemaBasedOptions.enableFormatting === false || this.schemaBasedOptions.formatter === 'none') {
      return;
    }

    // Default to enabled if not specified (undefined) or explicitly enabled (true)
    const shouldFormat = this.schemaBasedOptions.enableFormatting ?? true;
    if (!shouldFormat) {
      return;
    }

    try {
      const startTime = Date.now();

      // Determine the best formatter to use (default to biome for speed, fallback to prettier)
      let formatter: FormatterType = this.schemaBasedOptions.formatter || await BatchFormatter.getBestAvailableFormatter();

      // Check if the specified formatter is available
      if (!(await BatchFormatter.isFormatterAvailable(formatter))) {
        if (this.schemaBasedOptions.debug) {
          console.log(`⚠️  Formatter '${formatter}' not available, finding best alternative...`);
        }
        formatter = await BatchFormatter.getBestAvailableFormatter();
      }

      if (formatter === 'none') {
        // Critical failure: No formatter available at all - always report this
        console.warn('⚠️  No code formatter available (prettier, biome, etc.). Generated files may not be properly formatted.');
        return;
      }

      if (this.schemaBasedOptions.debug) {
        console.log(`🎨 Formatting generated files with ${formatter}...`);
      }

      // Format the output directory
      const result = await formatDirectory(this.outputRoot, {
        type: formatter,
        enabled: true,
        debug: this.schemaBasedOptions.debug
      });

      if (result.success) {
        if (this.schemaBasedOptions.debug) {
          console.log(`✅ Formatted ${result.filesFormatted} files in ${result.duration}ms using ${result.formatter}`);
        }
      } else {
        // Only show formatting errors in debug mode - these are internal errors
        if (this.schemaBasedOptions.debug) {
          console.warn(`⚠️  Formatting completed with errors:`, result.errors);
        }
      }

    } catch (error) {
      // Only show detailed formatting errors in debug mode
      if (this.schemaBasedOptions.debug) {
        console.error('❌ Error during formatting:', error);
      }
      // Don't throw - formatting errors shouldn't break generation
    }
  }

  /**
   * Get current memory usage in bytes
   * @private
   */
  private getMemoryUsage(): number {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed;
  }

  /**
   * Report Phase 1 optimization performance metrics
   * @private
   */
  private reportPhase1Metrics(): void {
    if (!this.schemaBasedOptions.debug) {
      return;
    }

    console.log('\n📊 Phase 1 Optimization Metrics:');

    // Parallel processing metrics
    if (this.parallelProcessingMetrics.processingMode) {
      console.log(`   Type Processing: ${this.parallelProcessingMetrics.processingMode}`);

      if (this.parallelProcessingMetrics.parallelTime) {
        console.log(`   Parallel Time: ${this.parallelProcessingMetrics.parallelTime}ms`);
      }

      if (this.parallelProcessingMetrics.sequentialTime) {
        console.log(`   Sequential Time: ${this.parallelProcessingMetrics.sequentialTime}ms`);
      }

      if (this.parallelProcessingMetrics.parallelTime && this.parallelProcessingMetrics.sequentialTime) {
        const speedup = this.parallelProcessingMetrics.sequentialTime / this.parallelProcessingMetrics.parallelTime;
        console.log(`   Speedup Ratio: ${speedup.toFixed(1)}x`);
      }

      // Memory usage metrics
      if (this.parallelProcessingMetrics.memoryUsage) {
        const { initial, peak, final } = this.parallelProcessingMetrics.memoryUsage;
        console.log(`   Memory: ${Math.round(initial / 1024 / 1024)}MB → ${Math.round(peak / 1024 / 1024)}MB → ${Math.round(final / 1024 / 1024)}MB`);

        const memoryIncrease = peak - initial;
        const memoryIncreasePercent = (memoryIncrease / initial) * 100;
        if (memoryIncreasePercent > 50) {
          console.log(`   ⚠️  Memory pressure: ${memoryIncreasePercent.toFixed(1)}% increase`);
        }
      }
    }

    // Batch I/O metrics
    try {
      const batchWriter = getGlobalBatchFileWriter();
      const metrics = batchWriter.getMetrics();

      if (metrics.totalFiles > 0) {
        console.log(`   Batch I/O: ${metrics.totalBatches} batches, ${metrics.totalFiles} files`);
        console.log(`   Time Saved: ${Math.round(metrics.timesSaved)}ms`);
        console.log(`   Error Rate: ${(metrics.errorRate * 100).toFixed(1)}%`);
      }
    } catch (error) {
      // Batch writer not available
    }

    console.log('');
  }

  /**
   * Phase 2 Optimization: Initialize dependency tracking system
   */
  private async initializeDependencyTracking(): Promise<void> {
    if (this.schemaBasedOptions.debug) {
      console.log('🔗 Initializing Phase 2 dependency tracking...');
    }

    // Load existing dependency graph
    await this.dependencyGraph.load();

    // Register schema files as dependency nodes
    const schemaFiles = glob.sync(path.join(this.schemaRoot, '**', '*.{gql,graphql}'), {
      windowsPathsNoEscape: true,
    });

    for (const schemaFile of schemaFiles) {
      const nodeId = `schema:${path.relative(this.schemaRoot, schemaFile)}`;
      this.dependencyGraph.addNode(nodeId, 'schema', schemaFile, [], {
        schemaType: 'graphql',
        relativePath: path.relative(this.schemaRoot, schemaFile)
      });
    }

    if (this.schemaBasedOptions.debug) {
      console.log(`✅ Registered ${schemaFiles.length} schema files in dependency graph`);
    }
  }

  /**
   * Phase 2 Optimization: Create incremental processing plan
   */
  private async createIncrementalProcessingPlan(): Promise<any> {
    if (!process.env.ENABLE_INCREMENTAL_PROCESSING || process.env.ENABLE_INCREMENTAL_PROCESSING === 'false') {
      return null; // Incremental processing disabled
    }

    if (this.schemaBasedOptions.debug) {
      console.log('📋 Creating incremental processing plan...');
    }

    // Register all types for incremental processing
    const typeMap = this.schema.getTypeMap();
    const typeIds: string[] = [];

    for (const [typeName, type] of Object.entries(typeMap)) {
      if (typeName.startsWith('__')) continue; // Skip introspection types

      const typeId = `type:${typeName}`;
      const outputFiles = this.getExpectedOutputFiles(typeName, type);

      this.incrementalProcessor.registerType(
        typeId,
        typeName,
        this.getTypeKind(type),
        this.schemaRoot,
        [], // Dependencies will be calculated later
        outputFiles,
        { typeName, typeKind: type.constructor.name }
      );

      typeIds.push(typeId);
    }

    // Create processing plan
    const plan = await this.incrementalProcessor.createProcessingPlan(typeIds);

    if (this.schemaBasedOptions.debug) {
      console.log(`📊 Incremental processing plan: ${plan.typesToProcess.length} to process, ${plan.typesToSkip.length} to skip`);
      console.log(`⚡ Estimated time reduction: ${Math.round(plan.estimatedTimeReduction)}%`);
    }

    return plan;
  }

  /**
   * Phase 2 Optimization: Process types with incremental optimization
   */
  private async processTypesInParallelWithIncremental(processingPlan: any): Promise<void> {
    if (!processingPlan) {
      // Fall back to regular parallel processing
      await this.processTypesInParallel();
      return;
    }

    if (this.schemaBasedOptions.debug) {
      console.log('🚀 Starting incremental parallel type processing...');
    }

    const startTime = Date.now();

    // Execute incremental processing plan
    await this.incrementalProcessor.executeProcessingPlan(
      processingPlan,
      async (typeIds: string[]) => {
        // Extract type names from IDs
        const typeNames = typeIds.map(id => id.replace('type:', ''));

        // Process only the specified types
        await this.processSpecificTypes(typeNames);
      }
    );

    const processingTime = Date.now() - startTime;

    if (this.schemaBasedOptions.debug) {
      console.log(`✅ Incremental processing completed in ${processingTime}ms`);
    }

    // Update metrics
    this.parallelProcessingMetrics.parallelTime = processingTime;
    this.parallelProcessingMetrics.processingMode = 'incremental';
  }

  /**
   * Process specific types (used by incremental processor)
   */
  private async processSpecificTypes(typeNames: string[]): Promise<void> {
    const typeMap = this.schema.getTypeMap();

    // Filter types to only process the specified ones
    const typesToProcess = typeNames.map(name => typeMap[name]).filter(Boolean);

    // Process in parallel as before, but only for specified types
    // Note: For now, we'll process all types since the processors don't support filtering
    // This is a placeholder for future enhancement
    await Promise.all([
      this.objectTypeProcessor.processObjectTypes(),
      this.operationTypeProcessor.processQueryTypes(),
      this.interfaceUnionProcessor.processInterfacesAndUnions()
    ]);
  }

  /**
   * Get expected output files for a type
   */
  private getExpectedOutputFiles(typeName: string, type: any): string[] {
    const outputFiles: string[] = [];

    // This is a simplified version - in practice, you'd calculate based on type structure
    const baseOutputPath = path.join(this.outputRoot, 'types', `${typeName.toLowerCase()}.ts`);
    outputFiles.push(baseOutputPath);

    return outputFiles;
  }

  /**
   * Get type kind for incremental processor
   */
  private getTypeKind(type: any): 'object' | 'interface' | 'union' | 'enum' | 'input' | 'scalar' {
    if (type.constructor.name.includes('Object')) return 'object';
    if (type.constructor.name.includes('Interface')) return 'interface';
    if (type.constructor.name.includes('Union')) return 'union';
    if (type.constructor.name.includes('Enum')) return 'enum';
    if (type.constructor.name.includes('Input')) return 'input';
    return 'scalar';
  }

  /**
   * Phase 2 Optimization: Save dependency tracking state
   */
  private async saveDependencyTrackingState(): Promise<void> {
    try {
      await this.dependencyGraph.save();
      await this.incrementalProcessor.save();

      if (this.schemaBasedOptions.debug) {
        console.log('💾 Dependency tracking state saved');
      }
    } catch (error) {
      if (this.schemaBasedOptions.debug) {
        console.warn('⚠️  Failed to save dependency tracking state:', error);
      }
    }
  }

  /**
   * Two-phase processing: Build type map from TypeScript files
   */
  private async buildTypeMap(): Promise<void> {
    if (this.schemaBasedOptions.debug) {
      console.log('🚀 Starting two-phase processing - Phase 1: Building type map');
    }

    const startTime = Date.now();

    try {
      // Use provided type map if available
      if (this.schemaBasedOptions.typeMap) {
        this.typeMap = this.schemaBasedOptions.typeMap;
        if (this.schemaBasedOptions.debug) {
          console.log('✅ Using provided type map');
        }
        return;
      }

      // Build type map configuration
      const typeMapConfig: TypeMapConfig = {
        baseDirectory: this.schemaBasedOptions.codebaseDir || process.cwd(),
        includePatterns: ['**/*.ts', '**/*.tsx'],
        excludePatterns: ['**/node_modules/**', '**/dist/**', '**/*.d.ts'],
        maxWorkers: Math.max(1, require('os').cpus().length - 1),
        enableCaching: true,
        cacheDirectory: path.join(process.cwd(), '.gql-generator-cache'),
        schemaId: this.schemaBasedOptions.schemaId || 'default',
        debug: this.schemaBasedOptions.debug || false,
        memoryPressureThreshold: 0.8,
        enableDecorators: this.schemaBasedOptions.enableDecorators || false
      };

      // Initialize type map builder
      this.typeMapBuilder = getGlobalTypeMapBuilder(typeMapConfig);

      // Build type map
      const buildResult = await this.typeMapBuilder.buildTypeMap();
      this.typeMap = buildResult.typeMap;

      const buildTime = Date.now() - startTime;

      if (this.schemaBasedOptions.debug) {
        console.log(`✅ Type map built in ${buildTime}ms`);
        console.log(`📊 Processed ${buildResult.typeMap.metrics.totalFiles} files`);
        console.log(`📈 Successfully parsed: ${buildResult.typeMap.metrics.successfullyParsed}`);
        console.log(`❌ Failed to parse: ${buildResult.typeMap.metrics.failedToParse}`);

        if (buildResult.fromCache) {
          console.log('🎯 Type map loaded from cache');
        }

        if (buildResult.warnings.length > 0) {
          console.warn('⚠️  Warnings during type map building:', buildResult.warnings);
        }

        if (buildResult.errors.length > 0) {
          console.error('❌ Errors during type map building:', buildResult.errors);
        }
      }

    } catch (error) {
      console.error('Failed to build type map:', error);

      // Fall back to legacy decorator processing
      if (this.schemaBasedOptions.debug) {
        console.log('🔄 Falling back to legacy decorator processing');
      }

      if (this.schemaBasedOptions.enableDecorators && this.schemaBasedOptions.codebaseDir) {
        await this.scanDecorators();
      }
    }
  }
}