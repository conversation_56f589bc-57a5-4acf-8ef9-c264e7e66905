import * as fs from 'fs-extra';
import * as path from 'path';
import type { 
  GraphQLSchema, 
  GraphQLObjectType, 
  GraphQLInterfaceType, 
  GraphQLUnionType, 
  GraphQLInputObjectType,
  GraphQLEnumType,
  GraphQLScalarType
} from 'graphql';
import { 
  isObjectType, 
  isInterfaceType, 
  isUnionType, 
  isInputObjectType, 
  isEnumType, 
  isScalarType,
  isIntrospectionType
} from 'graphql';
import { BaseGenerator, GeneratorOptions } from '@utils/base-generator';
import { WatchedFileWriter } from '@utils/watched-file-writer';

/**
 * Options for the schema type enum generator
 */
export interface SchemaTypeEnumGeneratorOptions extends GeneratorOptions {
  /** Whether to force overwrite existing files */
  force?: boolean;
  /** Enable debug mode for verbose logging */
  debug?: boolean;
  /** Schema identifier for namespacing */
  schemaId?: string;
}

/**
 * Generator for creating TypeScript type enum constants from GraphQL schema
 * This generator extracts all GraphQL type names and creates type-safe constants
 * that can be used instead of hardcoded strings in resolver code.
 */
export class SchemaTypeEnumGenerator extends BaseGenerator {
  private enumOptions: SchemaTypeEnumGeneratorOptions;

  constructor(schema: GraphQLSchema, options: SchemaTypeEnumGeneratorOptions) {
    super(schema, options);
    this.enumOptions = options;
  }

  /**
   * Generate the schema type enum file
   */
  public async generate(): Promise<void> {
    await this.init();
    
    if (this.enumOptions.debug) {
      console.log('Generating schema type enum constants...');
    }

    // Extract all type names from the schema
    const typeNames = this.extractTypeNames();

    if (this.enumOptions.debug) {
      console.log(`Found ${typeNames.length} types in schema:`, typeNames);
    }

    // Generate the TypeScript content
    const content = this.generateTypeEnumContent(typeNames);
    
    // Write the file
    const outputPath = path.join(this.outputRoot, 'SchemaTypes.ts');
    WatchedFileWriter.writeFileSync(outputPath, content);
    
    if (this.enumOptions.debug) {
      console.log(`Generated schema type enum file: ${outputPath}`);
    }
  }

  /**
   * Extract all type names from the GraphQL schema
   * @returns Array of type names
   */
  private extractTypeNames(): string[] {
    const typeMap = this.schema.getTypeMap();
    const typeNames: string[] = [];

    for (const [typeName, type] of Object.entries(typeMap)) {
      // Skip introspection types (they start with __)
      if (isIntrospectionType(type)) {
        continue;
      }

      // Include all user-defined types
      if (
        isObjectType(type) ||
        isInterfaceType(type) ||
        isUnionType(type) ||
        isInputObjectType(type) ||
        isEnumType(type) ||
        isScalarType(type)
      ) {
        typeNames.push(typeName);
      }
    }

    // Sort alphabetically for consistent output
    return typeNames.sort();
  }

  /**
   * Generate the TypeScript content for the schema type enum
   * @param typeNames Array of GraphQL type names
   * @returns TypeScript file content
   */
  private generateTypeEnumContent(typeNames: string[]): string {
    const schemaId = this.enumOptions.schemaId;
    const enumName = schemaId ? `${this.capitalizeFirst(schemaId)}SchemaTypes` : 'SchemaTypes';
    const typeName = schemaId ? `${this.capitalizeFirst(schemaId)}SchemaTypeNames` : 'SchemaTypeNames';

    let content = `/**
 * Auto-generated GraphQL Schema Type Constants
 * Generated from: ${this.enumOptions.schema || 'GraphQL schema'}
 * ${schemaId ? `Schema ID: ${schemaId}` : ''}
 *
 * Use these constants instead of hardcoded strings for type-safe GraphQL type references.
 * 
 * Example usage:
 * \`\`\`typescript
 * import { ${enumName} } from './SchemaTypes';
 * 
 * const resolvers = {
 *   [${enumName}.RootQuery]: {
 *     // Query resolvers
 *   },
 *   [${enumName}.RootMutation]: {
 *     // Mutation resolvers  
 *   }
 * };
 * \`\`\`
 */

export const ${enumName} = {
`;

    // Add each type as a constant
    for (const typeName of typeNames) {
      content += `  ${typeName}: '${typeName}',\n`;
    }

    content += `} as const;

/**
 * Union type of all valid schema type names
 */
export type ${typeName} = typeof ${enumName}[keyof typeof ${enumName}];

/**
 * Type-safe helper for checking if a string is a valid schema type
 * @param type The string to check
 * @returns True if the string is a valid schema type name
 */
export function isValid${schemaId ? this.capitalizeFirst(schemaId) : ''}SchemaType(type: string): type is ${typeName} {
  return Object.values(${enumName}).includes(type as ${typeName});
}

/**
 * Get all available schema type names as an array
 * @returns Array of all schema type names
 */
export function get${schemaId ? this.capitalizeFirst(schemaId) : ''}SchemaTypeNames(): ${typeName}[] {
  return Object.values(${enumName});
}
`;

    return content;
  }

  /**
   * Capitalize the first letter of a string
   * @param str The string to capitalize
   * @returns Capitalized string
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}
