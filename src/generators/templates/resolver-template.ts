/**
 * Resolver template for regular fields
 */
export const RESOLVER_TEMPLATE = `{{#if directiveImports}}
{{#each directiveImports}}
{{safeRaw this}}
{{/each}}
{{/if}}
import { {{#if needsResolversImport}}{{typeName}}Resolvers, {{/if}}{{#if hasArgs}}{{typeName}}{{capitalizedFieldName}}Args, {{/if}}ResolversParentTypes{{#if needsReturnTypeImport}}, {{typesToImport}}{{/if}} } from '{{importPathToGenerated}}';{{#if shouldUseTypeAlias}}
import { {{typeAliasName}} } from '{{importPathToResolveTypes}}';{{/if}}
{{#if needsScalarsImport}}import { {{scalarTypesToImport}} } from '{{importPathToScalars}}';
{{/if}}import { {{contextName}} } from '{{importPathToContext}}';

/**
 * {{description}}
 * Resolved {{resolverType}}: \`{{resolverSignature}}\`
 * Defined in: \`{{schemaFilePath}}\`
 *
 * GraphQL Schema Representation:
 * {{schemaRepresentation}}
{{#if directiveMethodCall}}
 *
 * @GQLMethodCall({{directiveMethodCall}}){{#if schemaIdentifier}} - Schema: {{schemaIdentifier}}{{/if}}
{{/if}}
{{#if fieldDirectiveFields}}
 *
 * Field directive fields from @field directive:
{{#each fieldDirectiveFields}}
 * - {{name}}: {{type}} (Access via obj?.{{name}})
{{/each}}
{{/if}}
{{#if inheritedFromInterface}}
 *
{{#if hasInheritedMethodCall}}
 * Method call inherited from interface: {{inheritedFromInterface}}
{{else}}
 * Directives inherited from interface: {{inheritedFromInterface}}
{{/if}}
{{/if}}
 */
const {{camelCaseFieldName}} = async (
  obj: ResolversParentTypes['{{typeName}}'],
  args{{#if hasArgs}}: {{typeName}}{{capitalizedFieldName}}Args{{else}}: Record<string, never>{{/if}},
  context: {{contextName}}
): Promise<{{safeRaw returnTypeAnnotation}}> => {
{{#if directiveMethodCall}}
  return {{safeRaw directiveMethodCall}};
{{else}}
{{#if hasDefaultDirective}}
  {{defaultReturnStatement}}
{{else}}
{{#if smartDefaultMethodCall}}
  return {{smartDefaultMethodCall}};
{{else}}
  try {
    // TODO: Implement your resolver logic here
    {{defaultReturnStatement}}
  } catch (error) {
    console.error(\`Error in {{fieldName}} resolver:\`, error);
    throw error;
  }
{{/if}}
{{/if}}
{{/if}}
}

export { {{camelCaseFieldName}} };
export default {{camelCaseFieldName}};

// Export qualified name for testing purposes
export { {{camelCaseFieldName}} as {{qualifiedName}} };
`;