/**
 * Resolver template for interface/union/object __resolveType function
 */
export const RESOLVE_TYPE_TEMPLATE = `import { {{#if needsResolversImport}}{{typeName}}Resolvers, {{/if}}{{#if needsInterfaceTypeImport}}{{typeName}}, {{/if}}ResolversParentTypes } from '{{importPathToGenerated}}';{{#if shouldUseTypeAlias}}
import { {{typeAliasName}} } from '{{importPathToResolveTypes}}';{{/if}}
import { {{contextName}} } from '{{importPathToContext}}';
{{#if customImports}}
{{customImports}}{{/if}}

/**
 * Type resolver for the {{typeName}} {{typeKind}}
 * Defined in: \`{{schemaFilePath}}\`
 *
 * GraphQL Schema Representation:
 * {{schemaRepresentation}}
 *
 * Possible Types: {{possibleTypes}}
 */
const __resolveType = async (
  obj: ResolversParentTypes['{{typeName}}'] & { __typename?: string },
  context: {{contextName}}
): Promise<{{#if shouldUseTypeAlias}}{{typeAliasName}}{{else}}{{#if possibleTypeChecks}}{{#each possibleTypeChecks}}{{#if @first}}{{/if}}'{{type}}'{{#unless @last}} | {{/unless}}{{/each}}{{else}}{{typeName}}{{/if}}{{/if}}> => {
  {{#if useTypeName}}
  // If __typename is available, use it
  if (obj?.__typename) {
    return obj?.__typename as {{#if shouldUseTypeAlias}}{{typeAliasName}}{{else}}{{#if possibleTypeChecks}}{{#each possibleTypeChecks}}{{#if @first}}{{/if}}'{{type}}'{{#unless @last}} | {{/unless}}{{/each}}{{else}}{{typeName}}{{/if}}{{/if}};
  }
  {{/if}}

  {{#if resolverCall}}
  // Use custom resolver from @resolver directive
  return {{resolverCall}};
  {{else}}
  // TODO: Implement type resolution logic for {{typeName}} {{typeKind}}
  // You can check specific properties of obj to determine the concrete type
  // Example:
  {{#each possibleTypeChecks}}
  // if ('{{typePropertyExample}}' in obj) return '{{type}}';
  {{/each}}

  // Default fallback - you should implement proper type resolution above
  throw new Error('Unable to resolve type for {{typeName}} {{typeKind}}. Please implement type resolution logic in this resolver.');
  {{/if}}
}

export { __resolveType };
export default __resolveType;

// Export qualified name for testing purposes
export { __resolveType as {{qualifiedName}} };
`;