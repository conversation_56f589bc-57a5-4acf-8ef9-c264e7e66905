/**
 * Templates for generating mutation test files
 */

/**
 * Template for individual mutation test file
 */
export const INDIVIDUAL_MUTATION_TEST_TEMPLATE = `{{#if directiveImports}}
{{#each directiveImports}}
{{safeRaw this}}
{{/each}}
{{/if}}
import { {{typeName}}Resolvers, {{#if hasArgs}}{{typeName}}{{capitalizedFieldName}}Args, {{/if}}ResolversParentTypes } from '{{importPathToGenerated}}';
import { {{contextName}} } from '{{importPathToContext}}';
import { createMockContext } from '{{importPathToTestUtils}}';

/**
 * Tests for {{description}}
 * Mutation: \`{{resolverSignature}}\`
 * Defined in: \`{{schemaFilePath}}\`
 * 
 * GraphQL Schema Representation:
 * {{schemaRepresentation}}
 */
describe('{{capitalizedFieldName}} Mutation', () => {
  const mockContext = createMockContext();
  
  // Create a strongly-typed mock parent object
  const mockParent: ResolversParentTypes['{{typeName}}'] = {
    {{#each parentMockFields}}
    {{name}}: {{mockValue}},
    {{/each}}
  };

  // Import resolver function
  let resolverFn;
  
  beforeAll(async () => {
    resolverFn = await import('{{importPathToResolver}}').then(module => module.default);
  });
  
  // Add teardown placeholder
  afterAll(() => {
    // Teardown code can be added here
  });
  
  // User test cases can be added here
});
`;

/**
 * Template for grouped mutation test file
 */
export const GROUPED_MUTATION_TEST_TEMPLATE = `{{#if directiveImports}}
{{#each directiveImports}}
{{safeRaw this}}
{{/each}}
{{/if}}
import { {{typeName}}Resolvers, ResolversParentTypes } from '{{importPathToGenerated}}';
import { {{contextName}} } from '{{importPathToContext}}';
import { createMockContext } from '{{importPathToTestUtils}}';

/**
 * Tests for {{typeName}} mutations
 * Defined in: \`{{schemaFilePath}}\`
 */
describe('{{typeName}} Mutations', () => {
  const mockContext = createMockContext();
  const mockParent: ResolversParentTypes['{{typeName}}'] = {
    {{#each parentMockFields}}
    {{name}}: {{mockValue}},
    {{/each}}
  };
  
  {{#each mutations}}
  describe('{{capitalizedName}} Mutation', () => {
    // Import resolver function
    let resolverFn;
    
    beforeAll(async () => {
      resolverFn = await import('{{importPath}}').then(module => module.default);
    });
    
    // Add teardown placeholder
    afterAll(() => {
      // Teardown code can be added here
    });
    
    // User test cases can be added here
  });
  
  {{/each}}
});
`; 