/**
 * Template for generating a mock context file for tests
 */
export const MOCK_CONTEXT_TEMPLATE = `import { {{contextName}} } from '{{importPathToContext}}';

/**
 * Creates a mock context for testing GraphQL resolvers
 * @param overrides - Optional partial context to override default mocks
 * @returns A fully mocked context object
 */
export function createMockContext(overrides: Partial<{{contextName}}> = {}): {{contextName}} {
  // Create the base mock context
  const mockContext: {{contextName}} = {
    {{#each contextProperties}}
    {{name}}: {{mockValue}},
    {{/each}}
  };

  // Apply any custom overrides
  return {
    ...mockContext,
    ...overrides
  };
}

/**
 * Type for creating typed mock functions on context properties
 */
export type DeepMockProxy<T> = {
  [K in keyof T]: T[K] extends (...args: infer A) => infer R
    ? jest.Mock<R, A>
    : T[K] extends object
    ? DeepMockProxy<T[K]>
    : T[K];
};
`; 