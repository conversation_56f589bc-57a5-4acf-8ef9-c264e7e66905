import * as fs from 'fs-extra';
import * as path from 'path';
import _ from 'lodash';
import type {
  GraphQLSchema,
  GraphQLField,
  GraphQLType
} from 'graphql';
import {
  GraphQLObjectType,
  isObjectType,
  GraphQLNonNull,
  GraphQLList,
  GraphQLScalarType
} from 'graphql';
import Handlebars from 'handlebars';
import {
  INDIVIDUAL_MUTATION_TEST_TEMPLATE,
  GROUPED_MUTATION_TEST_TEMPLATE
} from '@generators/test-generators/templates/mutation-test-template';
import { MOCK_CONTEXT_TEMPLATE } from '@generators/test-generators/templates/mock-context-template';
import { calculateRelativeImportPath } from '@generators/utils/type-utils';
import { hasCustomImplementation } from '@generators/utils/file-utils';
import { isJavaScriptKeyword } from '@generators/utils/keyword-utils';
import {
  generateMockArgs,
  parseContextInterface,
  generateMockValueForType
} from '@generators/test-generators/utils/mock-data-utils';
import * as glob from 'glob';
import { DirectiveParser } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';
import { WatchedFileWriter } from '../../utils/watched-file-writer';
import { TemplateSanitizer } from '../../utils/template-sanitizer';
import { getGlobalTemplateCache } from '../../utils/template-compilation-cache';

// Register a safe helper for raw content (now redundant since escaping is disabled, but kept for compatibility)
Handlebars.registerHelper('safeRaw', function(content) {
  return new Handlebars.SafeString(content || '');
});

// Get the global template cache for optimized compilation
const templateCache = getGlobalTemplateCache({
  enableLogging: false, // Disable logging in production generators
  enablePerformanceTracking: true,
});

// Configure cached template compilation with escaping disabled for TypeScript code generation
const compile = (template: string) => templateCache.compile(template, { noEscape: true });

/**
 * Options for the mutation test generator
 */
export interface MutationTestGeneratorOptions {
  /** GraphQL schema */
  schema: GraphQLSchema;
  /** Path to the output directory for tests */
  testOutput: string;
  /** Path to the output directory for generated files */
  outputRoot: string;
  /** Path to the schema directory */
  schemaRoot: string;
  /** Whether to force overwrite existing files */
  force: boolean;
  /** Path to custom context module */
  contextPath: string;
  /** Name of the context type */
  contextName: string;
  /** Whether to generate individual test files for each mutation */
  individualTests: boolean;
  /** Schema mapper instance */
  schemaMapper: any;
  /** Path to the directory containing the mutation resolvers */
  mutationRoot: string | null;
  /** Whether to apply alias directives even for custom implementations */
  applyAliases?: boolean;
}

/**
 * Generator for mutation test files
 */
export class MutationTestGenerator {
  private schema: GraphQLSchema;
  private testOutputRoot: string;
  private outputRoot: string;
  private schemaRoot: string;
  private force: boolean;
  private contextPath: string;
  private contextName: string;
  private individualTests: boolean;
  private schemaMapper: any;
  private mutationRoot: string | null;
  private applyAliases: boolean;
  private processedFiles: Set<string> = new Set();
  private schemaAliasPath: string | null = null; // Store schema-level alias path

  /**
   * Create a new mutation test generator
   * @param options Configuration options
   */
  constructor(options: MutationTestGeneratorOptions) {
    this.schema = options.schema;
    this.testOutputRoot = options.testOutput;
    this.outputRoot = options.outputRoot;
    this.schemaRoot = options.schemaRoot;
    this.force = options.force;
    this.contextPath = options.contextPath;
    this.contextName = options.contextName;
    this.individualTests = options.individualTests;
    this.schemaMapper = options.schemaMapper;
    this.mutationRoot = options.mutationRoot;
    this.applyAliases = !!options.applyAliases;

    // Ensure the test output directory exists
    fs.ensureDirSync(this.testOutputRoot);

    console.log(`Test stubs will be generated in: ${this.testOutputRoot}`);
  }

  /**
   * Extract schema-level alias path from the schema
   */
  private async extractSchemaAliasPath(): Promise<void> {
    try {
      // Start by checking the schema.gql file for alias directive
      const schemaFilePath = path.join(this.schemaRoot, 'schema.gql');

      if (fs.existsSync(schemaFilePath)) {
        const directives = await DirectiveParser.extractSchemaDirectives(schemaFilePath);
        const aliasPath = DirectiveProcessor.extractSchemaAliasPath(directives);
        if (aliasPath) {
          console.log(`Found schema-level alias directive in schema.gql: path=${aliasPath}`);
          this.schemaAliasPath = aliasPath;
          return;
        }
      }

      // If not found in schema.gql, check other schema files that might contain schema definition
      const schemaGlob = path.join(this.schemaRoot, '**', '*.gql');
      console.log(`Looking for schema-level alias directive in schema files: ${schemaGlob}`);
      const schemaFiles = glob.sync(schemaGlob, { windowsPathsNoEscape: true });

      for (const file of schemaFiles) {
        // Skip schema.gql since we already checked it
        if (file === schemaFilePath) continue;

        try {
          // Read the file content
          const fileContent = fs.readFileSync(file, 'utf8');

          // Only process files that have 'schema {' definition
          if (fileContent.includes('schema {')) {
            const directives = await DirectiveParser.extractSchemaDirectives(file);
            const aliasPath = DirectiveProcessor.extractSchemaAliasPath(directives);
            if (aliasPath) {
              console.log(`Found schema-level alias directive in ${file}: path=${aliasPath}`);
              this.schemaAliasPath = aliasPath;
              return;
            }
          }
        } catch (error) {
          console.warn(`Error checking file ${file} for schema-level alias directive:`, error);
          continue;
        }
      }

      console.log('No schema-level alias directive found.');
    } catch (error) {
      console.error(`Error extracting schema-level alias path:`, error);
    }
  }

  /**
   * Generate all mutation test files
   */
  public async generate(): Promise<void> {
    // First, extract the schema-level alias path if available
    await this.extractSchemaAliasPath();

    // Then generate the mock context utility
    await this.generateMockContext();

    // Find all mutation types in the schema
    const mutationType = this.schema.getMutationType();
    if (!mutationType) {
      console.log('No mutation type found in schema, skipping test generation');
      return;
    }

    // Process the root mutation type
    await this.processMutationType(mutationType);

    // Find and process all named mutation types (e.g., UserMutation, AdminMutation)
    const typesMap = this.schema.getTypeMap();

    for (const typeName in typesMap) {
      const type = typesMap[typeName];

      // Skip built-in types and non-object types
      if (
        !isObjectType(type) ||
        typeName.startsWith('__') ||
        typeName === mutationType.name
      ) {
        continue;
      }

      // Check if the type is a named mutation type
      if (typeName.endsWith('Mutation')) {
        await this.processMutationType(type);
      }
    }

    console.log('Mutation test generation complete!');
  }

  /**
   * Generate mock context file for tests
   */
  private async generateMockContext(): Promise<void> {
    // Create tests/utils directory if it doesn't exist
    const mockContextDir = path.join(this.testOutputRoot, 'utils');
    fs.ensureDirSync(mockContextDir);

    const mockContextPath = path.join(mockContextDir, 'mock-context.ts');

    // Skip if file exists and we're not forcing overwrite
    if (fs.existsSync(mockContextPath) && !this.force) {
      console.log(`Mock context file already exists at ${mockContextPath}, skipping`);
      return;
    }

    // If custom context path is provided, try to parse it to extract properties
    let contextProperties: Array<{ name: string; mockValue: string }> = [];

    if (this.contextPath && !this.contextPath.startsWith('@')) {
      // Check if the context file exists
      const absoluteContextPath = path.isAbsolute(this.contextPath)
        ? this.contextPath
        : path.resolve(process.cwd(), this.contextPath);

      if (fs.existsSync(absoluteContextPath)) {
        // Parse the context file to extract properties
        const contextContent = fs.readFileSync(absoluteContextPath, 'utf8');
        contextProperties = parseContextInterface(contextContent, this.contextName);
      } else {
        console.log(`Custom context file not found at ${absoluteContextPath}, using default empty context`);
      }
    }

    // If no properties were found or context is an alias, use default properties
    if (contextProperties.length === 0) {
      contextProperties = [
        { name: 'isAuthenticated', mockValue: 'true' },
        { name: 'userId', mockValue: '"mock-user-id"' },
        { name: 'loaders', mockValue: '{}' },
        { name: 'dataSources', mockValue: '{}' },
        { name: 'req', mockValue: '{}' },
        { name: 'res', mockValue: '{}' }
      ];
    }

    // Calculate the relative import path for context
    let importPathToContext = this.contextPath;

    if (!importPathToContext.startsWith('@') && !path.isAbsolute(importPathToContext)) {
      // Calculate the relative path from the mock context file to the context file
      const absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
      const relativeContextPath = path.relative(path.dirname(mockContextPath), absoluteContextPath);

      // Convert backslashes to forward slashes for import statements
      importPathToContext = relativeContextPath.replace(/\\/g, '/');

      // Ensure the path starts with ./ or ../
      if (!importPathToContext.startsWith('.')) {
        importPathToContext = './' + importPathToContext;
      }
    }

    // Prepare the template data
    const templateData = {
      contextName: this.contextName,
      importPathToContext,
      contextProperties
    };

    // Compile the template
    const template = compile(MOCK_CONTEXT_TEMPLATE);
    const content = template(templateData);

    // Write the file
    WatchedFileWriter.writeFileSync(mockContextPath, content);
    console.log(`Generated mock context at ${mockContextPath}`);
  }

  /**
   * Process a mutation type and generate tests
   * @param type GraphQL object type
   */
  private async processMutationType(
    type: GraphQLObjectType
  ): Promise<void> {
    const fields = type.getFields();

    // Skip if no fields
    if (Object.keys(fields).length === 0) {
      return;
    }

    // Get the actual type location from the schema
    const typeLocation = this.schemaMapper.getTypeLocation(type.name);

    // If type location not found, use the default directory name
    const sourceDir = typeLocation ? path.dirname(typeLocation.sourceFile) : '';

    // Determine where the mutation resolvers are located
    let mutationDir: string | null = null;

    if (this.mutationRoot) {
      // If mutationRoot is provided, try to find the type in the same relative path as in schema
      if (sourceDir) {
        const relativeDir = path.relative(this.schemaRoot, path.join(this.schemaRoot, sourceDir));
        mutationDir = path.join(this.mutationRoot, relativeDir, _.kebabCase(type.name));

        // If the directory doesn't exist, fall back to the direct path
        if (!fs.existsSync(mutationDir)) {
          mutationDir = path.join(this.mutationRoot, _.kebabCase(type.name));
        }
      } else {
        mutationDir = path.join(this.mutationRoot, _.kebabCase(type.name));
      }
    } else {
      // Try to find mutation resolvers in the output directory
      // First, try to maintain the same directory structure as the schema
      if (sourceDir) {
        const relativeDir = path.relative(this.schemaRoot, path.join(this.schemaRoot, sourceDir));
        const possibleDirWithStructure = path.join(this.outputRoot, relativeDir, _.kebabCase(type.name));

        if (fs.existsSync(possibleDirWithStructure)) {
          mutationDir = possibleDirWithStructure;
        } else {
          // If not found, try directly under output dir
          const possibleDirDirect = path.join(this.outputRoot, _.kebabCase(type.name));
          if (fs.existsSync(possibleDirDirect)) {
            mutationDir = possibleDirDirect;
          }
        }
      } else {
        // Fallback to direct path under output dir
        const possibleMutationDir = path.join(this.outputRoot, _.kebabCase(type.name));
        if (fs.existsSync(possibleMutationDir)) {
          mutationDir = possibleMutationDir;
        }
      }
    }

    // If we still couldn't find the directory, try to find it anywhere in the output tree
    if (!mutationDir || !fs.existsSync(mutationDir)) {
      console.log(`Looking for mutation directory for ${type.name} in the entire output tree...`);

      // Get a list of all directories under outputRoot
      const allDirs = this.getAllDirectories(this.outputRoot);

      // Try to find a directory with the kebab-case type name
      const typeNameKebab = _.kebabCase(type.name);
      for (const dir of allDirs) {
        const dirName = path.basename(dir);
        if (dirName === typeNameKebab) {
          mutationDir = dir;
          console.log(`Found mutation directory at ${mutationDir}`);
          break;
        }
      }
    }

    if (!mutationDir || !fs.existsSync(mutationDir)) {
      console.log(`No mutation directory found for ${type.name}, skipping test generation`);
      return;
    }

    // Create the test directory mirroring the structure
    let testDir: string;

    if (sourceDir) {
      const relativeDir = path.relative(this.schemaRoot, path.join(this.schemaRoot, sourceDir));
      testDir = path.join(this.testOutputRoot, relativeDir, _.kebabCase(type.name));
    } else {
      testDir = path.join(this.testOutputRoot, _.kebabCase(type.name));
    }

    fs.ensureDirSync(testDir);

    if (this.individualTests) {
      // Generate individual test files for each mutation
      await this.generateIndividualTests(type, fields, mutationDir, testDir);
    } else {
      // Generate a grouped test file for all mutations in this type
      await this.generateGroupedTest(type, fields, mutationDir, testDir);
    }
  }

  /**
   * Get all directories in a directory tree
   * @param rootDir - The root directory to start from
   * @returns Array of directory paths
   */
  private getAllDirectories(rootDir: string): string[] {
    const result: string[] = [];

    if (!fs.existsSync(rootDir)) {
      return result;
    }

    const queue = [rootDir];

    while (queue.length > 0) {
      const currentDir = queue.shift()!;

      if (fs.existsSync(currentDir) && fs.statSync(currentDir).isDirectory()) {
        result.push(currentDir);

        // Add subdirectories to the queue
        const entries = fs.readdirSync(currentDir);
        for (const entry of entries) {
          const entryPath = path.join(currentDir, entry);
          if (fs.statSync(entryPath).isDirectory()) {
            queue.push(entryPath);
          }
        }
      }
    }

    return result;
  }

  /**
   * Generate individual test files for each mutation
   * @param type GraphQL object type
   * @param fields GraphQL fields
   * @param mutationDir Mutation resolver directory
   * @param testDir Test output directory
   */
  private async generateIndividualTests(
    type: GraphQLObjectType,
    fields: { [key: string]: GraphQLField<any, any> },
    mutationDir: string,
    testDir: string
  ): Promise<void> {
    for (const fieldName in fields) {
      const field = fields[fieldName];
      const fileName = this.convertToFileName(fieldName);
      const mutationFile = path.join(mutationDir, `${fileName}.ts`);

      // Check if resolver exists at the expected location
      let resolverPath = mutationFile;
      if (!fs.existsSync(resolverPath)) {
        // If not, try to find it anywhere in the output directory
        console.log(`Mutation resolver not found at ${mutationFile}, searching for it...`);
        const foundResolverPath = this.findResolverFile(fileName);

        if (!foundResolverPath) {
          console.log(`Mutation resolver for ${fieldName} not found, skipping test generation`);
          continue;
        }

        resolverPath = foundResolverPath;
        console.log(`Found resolver at: ${resolverPath}`);
      }

      const testFile = path.join(testDir, `${fileName}.test.ts`);

      // Get schema file location
      const typeLocation = this.schemaMapper.getTypeLocation(type.name);

      if (!typeLocation) {
        console.log(`Type location not found for ${type.name}, skipping directive extraction`);
        continue;
      }

      // Get the actual schema file path
      const schemaFileName = typeLocation.sourceFile;

      // Ensure we're using an absolute path that exists
      const possiblePaths = [
        schemaFileName,
        path.join(this.schemaRoot, schemaFileName),
        path.join(process.cwd(), schemaFileName),
        path.join(process.cwd(), this.schemaRoot, schemaFileName),
        path.join(process.cwd(), 'schema', schemaFileName),
      ];

      let schemaFilePath = '';
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          schemaFilePath = possiblePath;
          break;
        }
      }

      if (!schemaFilePath) {
        console.log(`Schema file not found for ${type.name}.${fieldName}, skipping directive extraction`);
        continue;
      }

      console.log(`Looking for directives in schema file: ${schemaFilePath}`);

      // Skip if test file exists and we're not forcing overwrite and there's no schema alias
      // or if we have aliases but we're not applying them
      if (fs.existsSync(testFile) &&
        !this.force &&
        (!this.schemaAliasPath || !this.applyAliases)) {
        // Check if it's custom or generated
        const hasCustomCode = await hasCustomImplementation(testFile);
        if (hasCustomCode) {
          console.log(`Custom test implementation found at ${testFile}, skipping generation`);
          this.processedFiles.add(testFile);
          continue;
        }
      } else if (fs.existsSync(testFile) && (this.force || (this.schemaAliasPath && this.applyAliases))) {
        // If we have schema alias or are forcing regeneration, check if it's a custom implementation
        const hasCustomCode = await hasCustomImplementation(testFile);
        if (hasCustomCode && !this.force && !(this.schemaAliasPath && this.applyAliases)) {
          console.log(`Custom test implementation found at ${testFile}, skipping generation`);
          this.processedFiles.add(testFile);
          continue;
        } else if (hasCustomCode && ((this.schemaAliasPath && this.applyAliases) || this.force)) {
          console.log(`Regenerating custom implementation at ${testFile} due to schema alias directive or force flag`);
        }
      }

      // Calculate import paths
      let importPathToResolver;

      if (this.schemaAliasPath) {
        // Use the schema-level alias path as base path
        console.log(`Using schema-level alias path: ${this.schemaAliasPath}`);

        // Extract the relative path part from the resolver
        let relativePath = path.relative(this.outputRoot, resolverPath).replace(/\\/g, '/');

        // Remove the file extension
        relativePath = relativePath.replace(/\.ts$/, '');

        // Combine schema alias with relative path
        importPathToResolver = `${this.schemaAliasPath}/${relativePath}`;

        console.log(`Generated alias import path: ${importPathToResolver}`);
      } else {
        // Calculate relative path to the resolver
        importPathToResolver = path.relative(
          path.dirname(testFile),
          resolverPath
        ).replace(/\\/g, '/');

        // Ensure the path starts with ./ or ../
        if (!importPathToResolver.startsWith('.')) {
          importPathToResolver = './' + importPathToResolver;
        }
      }

      const relativePathToGenerated = calculateRelativeImportPath(
        testFile, 'graphql.ts', this.outputRoot
      );

      // Calculate the relative import path for test utilities
      let relativePathToTestUtils = path.relative(
        path.dirname(testFile),
        path.join(this.testOutputRoot, 'utils', 'mock-context.ts')
      ).replace(/\\/g, '/');

      if (!relativePathToTestUtils.startsWith('.')) {
        relativePathToTestUtils = './' + relativePathToTestUtils;
      }

      // Calculate the relative import path for context
      let importPathToContext = this.contextPath;

      if (!importPathToContext.startsWith('@') && !path.isAbsolute(importPathToContext)) {
        // Calculate the relative path from the test file to the context file
        const absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
        const relativeContextPath = path.relative(path.dirname(testFile), absoluteContextPath);

        // Convert backslashes to forward slashes for import statements
        importPathToContext = relativeContextPath.replace(/\\/g, '/');

        // Ensure the path starts with ./ or ../
        if (!importPathToContext.startsWith('.')) {
          importPathToContext = './' + importPathToContext;
        }
      }

      // Get mock args
      const mockArgs = generateMockArgs(field.args, this.schema);

      // Prepare template data
      const camelCaseFieldName = _.camelCase(fieldName);
      const capitalizedFieldName = _.upperFirst(camelCaseFieldName);

      // Generate the schema representation
      const schemaRepresentation = `${fieldName}${field.args.length > 0
        ? `(${field.args.map(arg => `${arg.name}: ${arg.type.toString()}`).join(', ')})`
        : ''}: ${field.type.toString()}`;

      const templateData = {
        typeName: type.name,
        fieldName,
        camelCaseFieldName,
        capitalizedFieldName,
        description: field.description ?? `${capitalizedFieldName} mutation`,
        resolverSignature: `${fieldName}(${field.args.map(arg => `${arg.name}: ${arg.type.toString()}`).join(', ')}): ${field.type.toString()}`,
        schemaFilePath,
        schemaRepresentation,
        hasArgs: field.args.length > 0,
        mockArgs,
        importPathToGenerated: relativePathToGenerated,
        importPathToResolver: importPathToResolver,
        importPathToTestUtils: relativePathToTestUtils,
        importPathToContext,
        contextName: this.contextName,
        hasDirectives: false, // For future use with directive support
        directiveImports: [], // For future use with directive support
        parentMockFields: this.generateParentMockFields(type),
        mockReturnValue: true,
        returnType: this.getReturnTypeForAssertion(field.type)
      };

      // Compile the template
      const template = compile(INDIVIDUAL_MUTATION_TEST_TEMPLATE);
      const content = template(templateData);

      // Write the file
      WatchedFileWriter.writeFileSync(testFile, content);
      console.log(`Generated individual test at ${testFile}`);
      this.processedFiles.add(testFile);
    }
  }

  /**
   * Generate a grouped test file for all mutations in a type
   * @param type GraphQL object type
   * @param fields GraphQL fields
   * @param mutationDir Mutation resolver directory
   * @param testDir Test output directory
   */
  private async generateGroupedTest(
    type: GraphQLObjectType,
    fields: { [key: string]: GraphQLField<any, any> },
    mutationDir: string,
    testDir: string
  ): Promise<void> {
    const testFile = path.join(testDir, `${_.kebabCase(type.name)}.test.ts`);

    // Get schema file location
    const typeLocation = this.schemaMapper.getTypeLocation(type.name);

    if (!typeLocation) {
      console.log(`Type location not found for ${type.name}, skipping directive extraction`);
      return;
    }

    // Get the actual schema file path
    const schemaFileName = typeLocation.sourceFile;

    // Ensure we're using an absolute path that exists
    const possiblePaths = [
      schemaFileName,
      path.join(this.schemaRoot, schemaFileName),
      path.join(process.cwd(), schemaFileName),
      path.join(process.cwd(), this.schemaRoot, schemaFileName),
      path.join(process.cwd(), 'schema', schemaFileName),
    ];

    let schemaFilePath = '';
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        schemaFilePath = possiblePath;
        break;
      }
    }

    if (!schemaFilePath) {
      console.log(`Schema file not found for ${type.name}, skipping directive extraction`);
      return;
    }

    console.log(`Looking for directives in schema file: ${schemaFilePath}`);

    // Skip if test file exists and we're not forcing overwrite
    if (fs.existsSync(testFile) &&
      !this.force &&
      (!this.schemaAliasPath || !this.applyAliases)) {
      // Check if it's custom or generated
      const hasCustomCode = await hasCustomImplementation(testFile);
      if (hasCustomCode) {
        console.log(`Custom test implementation found at ${testFile}, skipping generation`);
        this.processedFiles.add(testFile);
        return;
      }
    } else if (fs.existsSync(testFile) && (this.force || (this.schemaAliasPath && this.applyAliases))) {
      // If we have schema alias or are forcing regeneration, check if it's a custom implementation
      const hasCustomCode = await hasCustomImplementation(testFile);
      if (hasCustomCode && !this.force && !(this.schemaAliasPath && this.applyAliases)) {
        console.log(`Custom test implementation found at ${testFile}, skipping generation`);
        this.processedFiles.add(testFile);
        return;
      } else if (hasCustomCode && ((this.schemaAliasPath && this.applyAliases) || this.force)) {
        console.log(`Regenerating custom implementation at ${testFile} due to schema alias directive or force flag`);
      }
    }

    // Prepare mutations data
    const mutations: any[] = [];

    for (const fieldName in fields) {
      const field = fields[fieldName];
      const fileName = this.convertToFileName(fieldName);
      const mutationFile = path.join(mutationDir, `${fileName}.ts`);

      // Check if resolver exists at the expected location
      let resolverPath = mutationFile;
      if (!fs.existsSync(resolverPath)) {
        // If not, try to find it anywhere in the output directory
        console.log(`Mutation resolver not found at ${mutationFile}, searching for it...`);
        const foundResolverPath = this.findResolverFile(fileName);

        if (!foundResolverPath) {
          console.log(`Mutation resolver for ${fieldName} not found, skipping in grouped test`);
          continue;
        }

        resolverPath = foundResolverPath;
        console.log(`Found resolver at: ${resolverPath}`);
      }

      // Get mock args
      const mockArgs = generateMockArgs(field.args, this.schema);

      // Calculate relative import path to the resolver
      let importPath;
      if (this.schemaAliasPath) {
        // Use the schema-level alias path as base path
        // Extract the relative path part from the resolver
        let relativePath = path.relative(this.outputRoot, resolverPath).replace(/\\/g, '/');

        // Remove the file extension
        relativePath = relativePath.replace(/\.ts$/, '');

        // Combine schema alias with relative path
        importPath = `${this.schemaAliasPath}/${relativePath}`;

        console.log(`Generated alias import path for mutation in grouped test: ${importPath}`);
      } else {
        // Calculate relative path
        importPath = path.relative(
          path.dirname(testFile),
          resolverPath
        ).replace(/\\/g, '/');

        if (!importPath.startsWith('.')) {
          importPath = './' + importPath;
        }
      }

      const camelCaseName = _.camelCase(fieldName);
      const capitalizedName = _.upperFirst(camelCaseName);

      mutations.push({
        name: fieldName,
        camelCaseName,
        capitalizedName,
        importPath,
        hasArgs: field.args.length > 0,
        mockArgs,
      });
    }

    // Skip if no mutations to test
    if (mutations.length === 0) {
      console.log(`No mutation resolvers found for ${type.name}, skipping grouped test generation`);
      return;
    }

    // Calculate import paths
    const relativePathToGenerated = calculateRelativeImportPath(
      testFile, 'graphql.ts', this.outputRoot
    );

    // Calculate import path to the mock context
    let relativePathToTestUtils = path.relative(
      path.dirname(testFile),
      path.join(this.testOutputRoot, 'utils', 'mock-context.ts')
    ).replace(/\\/g, '/');

    if (!relativePathToTestUtils.startsWith('.')) {
      relativePathToTestUtils = './' + relativePathToTestUtils;
    }

    // Calculate the relative import path for context
    let importPathToContext = this.contextPath;

    if (!importPathToContext.startsWith('@') && !path.isAbsolute(importPathToContext)) {
      // Calculate the relative path from the test file to the context file
      const absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
      const relativeContextPath = path.relative(path.dirname(testFile), absoluteContextPath);

      // Convert backslashes to forward slashes for import statements
      importPathToContext = relativeContextPath.replace(/\\/g, '/');

      // Ensure the path starts with ./ or ../
      if (!importPathToContext.startsWith('.')) {
        importPathToContext = './' + importPathToContext;
      }
    }

    const templateData = {
      typeName: type.name,
      mutations: mutations.map(mutation => ({
        ...mutation,
        mockReturnValue: true,
        returnType: this.getReturnTypeForMutation(type, mutation.name)
      })),
      schemaFilePath,
      importPathToGenerated: relativePathToGenerated,
      importPathToTestUtils: relativePathToTestUtils,
      importPathToContext,
      contextName: this.contextName,
      hasDirectives: false, // For future use with directive support
      directiveImports: [], // For future use with directive support
      parentMockFields: this.generateParentMockFields(type)
    };

    // Compile the template
    const template = compile(GROUPED_MUTATION_TEST_TEMPLATE);
    const content = template(templateData);

    // Write the file
    WatchedFileWriter.writeFileSync(testFile, content);
    console.log(`Generated grouped test at ${testFile}`);
    this.processedFiles.add(testFile);
  }

  /**
   * Find a resolver file by name anywhere in the output directory
   * @param fileName - The file name to search for (without extension)
   * @returns The full path to the resolver file if found, null otherwise
   */
  private findResolverFile(fileName: string): string | null {
    console.log(`Searching for resolver file ${fileName}.ts in ${this.outputRoot}...`);

    // First, try the exact file name
    const exactPattern = `**/${fileName}.ts`;
    const exactFiles = glob.sync(exactPattern, {
      cwd: this.outputRoot,
      absolute: true,
      windowsPathsNoEscape: true
    });

    if (exactFiles.length > 0) {
      console.log(`Found exact match: ${exactFiles[0]}`);
      return exactFiles[0];
    }

    // If not found, try to find in subdirectories with mutation wrapper patterns
    // Common patterns: mutation-wrappers/, mutation_wrappers/, mutations/, etc.
    const commonPrefixes = [
      'mutation-wrappers',
      'mutation_wrappers',
      'mutations',
      'mutators',
      'mutation'
    ];

    for (const prefix of commonPrefixes) {
      const prefixPattern = `**/${prefix}/**/${fileName}.ts`;
      const prefixFiles = glob.sync(prefixPattern, {
        cwd: this.outputRoot,
        absolute: true,
        windowsPathsNoEscape: true
      });

      if (prefixFiles.length > 0) {
        console.log(`Found in ${prefix} directory: ${prefixFiles[0]}`);
        return prefixFiles[0];
      }
    }

    // If still not found, look in any subdirectory that might contain *Mutation* in its name
    const mutationDirPattern = `**/*[mM]utation*/**/${fileName}.ts`;
    const mutationDirFiles = glob.sync(mutationDirPattern, {
      cwd: this.outputRoot,
      absolute: true,
      windowsPathsNoEscape: true
    });

    if (mutationDirFiles.length > 0) {
      console.log(`Found in mutation subdirectory: ${mutationDirFiles[0]}`);
      return mutationDirFiles[0];
    }

    // Finally, try a broad search for any file with this name
    const broadPattern = `**/**/${fileName}.ts`;
    const broadFiles = glob.sync(broadPattern, {
      cwd: this.outputRoot,
      absolute: true,
      windowsPathsNoEscape: true
    });

    if (broadFiles.length > 0) {
      console.log(`Found through broad search: ${broadFiles[0]}`);
      return broadFiles[0];
    }

    // Not found anywhere
    console.log(`No resolver file found for ${fileName}.ts`);
    return null;
  }

  /**
   * Convert a field name to a file name
   * @param fieldName GraphQL field name
   * @returns File name in kebab-case
   */
  private convertToFileName(fieldName: string): string {
    // Convert camelCase to kebab-case
    const kebabCase = _.kebabCase(fieldName);

    // Check if the original name is a JavaScript keyword
    // If it is, append "-handler" to the kebab-case name
    return isJavaScriptKeyword(fieldName) ? `${kebabCase}-handler` : kebabCase;
  }

  /**
   * Generate parent mock fields based on the GraphQL type
   * @param type GraphQL object type
   * @returns Array of mock fields with their names and mock values
   */
  private generateParentMockFields(
    type: GraphQLObjectType
  ): Array<{ name: string; mockValue: string }> {
    const typeName = type.name;
    const mockFields: Array<{ name: string; mockValue: string }> = [];

    // First, try to get parent types from generated types file
    // This will handle @field directive changes
    const generatedTypesPath = path.join(this.outputRoot, 'graphql.ts');

    if (fs.existsSync(generatedTypesPath)) {
      try {
        const typesContent = fs.readFileSync(generatedTypesPath, 'utf8');

        console.log(`Looking for ${typeName} in generated types...`);

        // First try to find the type definition and extract fields from it
        // This approach handles nested types properly
        const typeDefRegex = new RegExp(`export\\s+type\\s+${typeName}\\s*=\\s*\\{([\\s\\S]*?)\\};`, 'i');
        const typeDefMatch = typesContent.match(typeDefRegex);

        if (typeDefMatch && typeDefMatch[1]) {
          const typeDefContent = typeDefMatch[1].trim();
          console.log(`Found type definition for ${typeName}: ${typeDefContent}`);

          // Extract fields from the type definition
          const fieldsRegex = /([a-zA-Z0-9_]+)(?:\??):\s*([^;]+);/g;
          let fieldMatch;

          while ((fieldMatch = fieldsRegex.exec(typeDefContent)) !== null) {
            const fieldName = fieldMatch[1].trim();
            // Skip __typename
            if (fieldName === '__typename') continue;

            const fieldType = fieldMatch[2].trim();
            console.log(`Found field in type definition: ${fieldName} of type ${fieldType}`);

            // Generate mock value based on field type
            let mockValue: string;

            if (fieldType.includes('String')) {
              mockValue = `"mock-${fieldName}"`;
            } else if (fieldType.includes('ID')) {
              mockValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
            } else if (fieldType.includes('Int') || fieldType.includes('Float')) {
              mockValue = `${Math.floor(Math.random() * 100)}`;
            } else if (fieldType.includes('Boolean')) {
              mockValue = 'false';
            } else {
              // Check if this is a nested object type (without Scalars or other qualifiers)
              const nestedTypeMatch = fieldType.match(/^([A-Za-z][A-Za-z0-9_]*)(?:;|$)/);
              if (nestedTypeMatch) {
                const nestedTypeName = nestedTypeMatch[1];
                console.log(`Found nested type in field ${fieldName}: ${nestedTypeName}`);

                // Get fields for this nested type
                const nestedTypeDefRegex = new RegExp(`export\\s+type\\s+${nestedTypeName}\\s*=\\s*\\{([\\s\\S]*?)\\};`, 'i');
                const nestedTypeDefMatch = typesContent.match(nestedTypeDefRegex);

                if (nestedTypeDefMatch && nestedTypeDefMatch[1]) {
                  const nestedTypeDefContent = nestedTypeDefMatch[1].trim();
                  console.log(`Found nested type definition for ${nestedTypeName}: ${nestedTypeDefContent}`);

                  // Extract fields from nested type
                  const nestedFieldsRegex = /([a-zA-Z0-9_]+)(?:\??):\s*([^;]+);/g;
                  let nestedFieldMatch;
                  const nestedFields: Record<string, string> = {};

                  while ((nestedFieldMatch = nestedFieldsRegex.exec(nestedTypeDefContent)) !== null) {
                    const nestedFieldName = nestedFieldMatch[1].trim();
                    // Skip __typename
                    if (nestedFieldName === '__typename') continue;

                    const nestedFieldType = nestedFieldMatch[2].trim();
                    nestedFields[nestedFieldName] = nestedFieldType;
                  }

                  // Generate mock values for nested fields
                  const nestedMockProps = Object.entries(nestedFields)
                    .map(([propName, propType]) => {
                      let propValue: string;
                      if (propType.includes('String')) {
                        propValue = `"mock-${propName}"`;
                      } else if (propType.includes('ID')) {
                        propValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
                      } else if (propType.includes('Int') || propType.includes('Float')) {
                        propValue = `${Math.floor(Math.random() * 100)}`;
                      } else if (propType.includes('Boolean')) {
                        propValue = 'false';
                      } else {
                        propValue = '{}';
                      }
                      return `${propName}: ${propValue}`;
                    })
                    .join(', ');

                  mockValue = `{ ${nestedMockProps} }`;
                } else {
                  mockValue = '{}';
                }
              } else {
                mockValue = '{}';
              }
            }

            mockFields.push({
              name: fieldName,
              mockValue
            });
          }

          if (mockFields.length > 0) {
            console.log(`Using fields from type definition for ${typeName}: ${JSON.stringify(mockFields)}`);
            return mockFields;
          }
        }

        // If we couldn't extract from type definition, check for ResolversParentTypes section
        const parentTypeRegexPattern = `\\b${typeName}:\\s*\\{([^}]*)\\}[,;]`;
        const parentTypeRegex = new RegExp(parentTypeRegexPattern, 'i');
        const parentTypeMatch = typesContent.match(parentTypeRegex);

        if (parentTypeMatch && parentTypeMatch[1]) {
          const fieldsContent = parentTypeMatch[1].trim();
          console.log(`Found ${typeName} fields in ResolversParentTypes: ${fieldsContent}`);

          // This regex handles both property names and quoted property names
          const fieldsRegex = /(?:["']([^"']+)["']|([a-zA-Z][a-zA-Z0-9_]*)):\s*([^;,]+)[;,]/g;
          let fieldMatch;

          while ((fieldMatch = fieldsRegex.exec(fieldsContent)) !== null) {
            // Either quoted field name or normal identifier
            const fieldName = (fieldMatch[1] || fieldMatch[2]).trim();
            const fieldType = fieldMatch[3].trim();
            console.log(`Found field: ${fieldName} of type ${fieldType}`);

            // Generate appropriate mock value based on the field type
            let mockValue: string;

            if (fieldType.includes('string') || fieldType.includes('String')) {
              mockValue = `"mock-${fieldName}"`;
            } else if (fieldType.includes('number') || fieldType.includes('Number') ||
              fieldType.includes('Int') || fieldType.includes('Float')) {
              mockValue = `${Math.floor(Math.random() * 100)}`;
            } else if (fieldType.includes('boolean') || fieldType.includes('Boolean')) {
              mockValue = 'false';
            } else if (fieldType.includes('Date') || fieldType.includes('DateTime')) {
              mockValue = 'new Date("2023-01-01")';
            } else if (fieldType.includes('[]') || fieldType.includes('Array')) {
              mockValue = '[]';
            } else if (fieldType.includes('ID')) {
              mockValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
            } else {
              // Check for nested object type reference
              const nestedTypeMatch = fieldType.match(/^([A-Za-z][A-Za-z0-9_]*)$/);
              if (nestedTypeMatch) {
                const nestedTypeName = nestedTypeMatch[1];
                const nestedObjectProps = this.findNestedObjectTypeDefinition(nestedTypeName, typesContent);

                if (nestedObjectProps && Object.keys(nestedObjectProps).length > 0) {
                  // Generate mock object from nested type properties
                  const mockObj = Object.entries(nestedObjectProps)
                    .map(([propName, propType]) => {
                      let propValue: string;
                      if (propType.includes('String')) {
                        propValue = `"mock-${propName}"`;
                      } else if (propType.includes('ID')) {
                        propValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
                      } else if (propType.includes('Int') || propType.includes('Float')) {
                        propValue = `${Math.floor(Math.random() * 100)}`;
                      } else if (propType.includes('Boolean')) {
                        propValue = 'false';
                      } else {
                        propValue = '{}';
                      }
                      return `${propName}: ${propValue}`;
                    })
                    .join(', ');

                  mockValue = `{ ${mockObj} }`;
                } else {
                  mockValue = '{}';
                }
              } else {
                mockValue = '{}';
              }
            }

            mockFields.push({
              name: fieldName,
              mockValue
            });
          }

          // If we found fields from the generated type, return them directly
          if (mockFields.length > 0) {
            console.log(`Using generated type fields for ${typeName}: ${JSON.stringify(mockFields)}`);
            return mockFields;
          }
        } else {
          console.log(`Could not find simple object definition for ${typeName} in ResolversParentTypes`);

          // Try a more comprehensive approach for complex types
          // Some types might be defined with Omit, Pick, etc.
          // Look at the original type definition if we haven't already
          if (!typeDefMatch) {
            const typeDefRegex = new RegExp(`export\\s+type\\s+${typeName}\\s*=\\s*\\{[\\s\\S]*?\\};`, 'i');
            const typeDefMatch = typesContent.match(typeDefRegex);

            if (typeDefMatch) {
              const typeDef = typeDefMatch[0];
              console.log(`Found type definition for ${typeName}`);

              // Extract fields from the type definition
              const fieldsRegex = /([a-zA-Z0-9_]+)(?:\??):\s*([^;]+);/g;
              let fieldMatch;

              while ((fieldMatch = fieldsRegex.exec(typeDef)) !== null) {
                const fieldName = fieldMatch[1].trim();
                // Skip __typename
                if (fieldName === '__typename') continue;

                const fieldType = fieldMatch[2].trim();
                console.log(`Found field in type definition: ${fieldName} of type ${fieldType}`);

                // Generate mock value with similar logic to above
                let mockValue: string;

                if (fieldType.includes('String')) {
                  mockValue = `"mock-${fieldName}"`;
                } else if (fieldType.includes('ID')) {
                  mockValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
                } else if (fieldType.includes('Int') || fieldType.includes('Float')) {
                  mockValue = `${Math.floor(Math.random() * 100)}`;
                } else if (fieldType.includes('Boolean')) {
                  mockValue = 'false';
                } else {
                  // Check if this is a nested object type
                  const nestedTypeName = fieldType.replace(/['output']|['input']/g, '').trim();
                  const nestedObjectProps = this.findNestedObjectTypeDefinition(nestedTypeName, typesContent);

                  if (nestedObjectProps && Object.keys(nestedObjectProps).length > 0) {
                    // Generate nested object
                    const mockObj = Object.entries(nestedObjectProps)
                      .map(([propName, propType]) => {
                        let propValue: string;
                        if (propType.includes('String')) {
                          propValue = `"mock-${propName}"`;
                        } else if (propType.includes('ID')) {
                          propValue = `"mock-id-${Math.floor(Math.random() * 1000)}"`;
                        } else if (propType.includes('Int') || propType.includes('Float')) {
                          propValue = `${Math.floor(Math.random() * 100)}`;
                        } else if (propType.includes('Boolean')) {
                          propValue = 'false';
                        } else {
                          propValue = '{}';
                        }
                        return `${propName}: ${propValue}`;
                      })
                      .join(', ');

                    mockValue = `{ ${mockObj} }`;
                  } else {
                    mockValue = '{}';
                  }
                }

                mockFields.push({
                  name: fieldName,
                  mockValue
                });
              }

              if (mockFields.length > 0) {
                console.log(`Using fields from type definition for ${typeName}: ${JSON.stringify(mockFields)}`);
                return mockFields;
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error parsing generated types file for ${typeName}:`, error);
        // Fall back to schema-based approach
      }
    }

    // If we couldn't extract from generated types, fall back to schema-based approach
    console.log(`Falling back to schema-based approach for ${typeName}`);

    // Get the fields for this type
    const typeFields = type.getFields();

    // For each field, generate a mock value
    for (const fieldName in typeFields) {
      const field = typeFields[fieldName];
      // Only include required fields to avoid unnecessary mock data
      if (field.type instanceof GraphQLNonNull) {
        const mockValue = generateMockValueForType(field.type, this.schema);
        mockFields.push({
          name: fieldName,
          mockValue
        });
      }
    }

    console.log(`Generated schema-based fields for ${typeName}: ${JSON.stringify(mockFields)}`);
    return mockFields;
  }

  /**
   * Find the definition of a nested object type in the generated types file
   * @param typeName The name of the type to find
   * @param typesContent The content of the graphql.ts file
   * @returns An object with property names and their types, or null if not found
   */
  private findNestedObjectTypeDefinition(typeName: string, typesContent: string): Record<string, string> | null {
    console.log(`Looking for nested type definition: ${typeName}`);

    // Clean up the type name - remove any generics, quotes, etc.
    const cleanTypeName = typeName.replace(/['"\s<>]/g, '').split('[')[0].split('(')[0];

    // Skip if the type name doesn't look like a valid identifier
    if (!cleanTypeName.match(/^[A-Za-z][A-Za-z0-9_]*$/)) {
      return null;
    }

    // Try to find the type definition
    const typeDefRegex = new RegExp(`export\\s+type\\s+${cleanTypeName}\\s*=\\s*\\{([\\s\\S]*?)\\};`, 'i');
    const typeDefMatch = typesContent.match(typeDefRegex);

    if (!typeDefMatch || !typeDefMatch[1]) {
      console.log(`Could not find definition for nested type: ${cleanTypeName}`);
      return null;
    }

    const typeDefContent = typeDefMatch[1].trim();
    console.log(`Found definition for nested type ${cleanTypeName}: ${typeDefContent}`);

    // Extract fields from the type definition
    const result: Record<string, string> = {};
    const fieldsRegex = /([a-zA-Z0-9_]+)(?:\??):\s*([^;]+);/g;
    let fieldMatch;

    while ((fieldMatch = fieldsRegex.exec(typeDefContent)) !== null) {
      const fieldName = fieldMatch[1].trim();
      // Skip __typename
      if (fieldName === '__typename') continue;

      const fieldType = fieldMatch[2].trim();
      result[fieldName] = fieldType;
    }

    console.log(`Extracted fields from nested type ${cleanTypeName}:`, result);
    return result;
  }

  /**
   * Get the appropriate return type for assertions based on the GraphQL type
   * @param type GraphQL type
   * @returns JavaScript type name for assertions
   */
  private getReturnTypeForAssertion(type: GraphQLType): string {
    // Unwrap non-null wrapper if present
    if (type instanceof GraphQLNonNull) {
      return this.getReturnTypeForAssertion(type.ofType);
    }

    // Handle list type
    if (type instanceof GraphQLList) {
      return 'Array';
    }

    // Handle scalar types
    if (type instanceof GraphQLScalarType) {
      switch (type.name) {
        case 'String':
          return 'String';
        case 'ID':
          return 'String|Number'; // ID can be either string or number
        case 'Int':
        case 'Float':
          return 'Number';
        case 'Boolean':
          return 'Boolean';
        case 'Date':
        case 'DateTime':
          return 'Date';
        default:
          return 'Object';
      }
    }

    // Handle object types
    if (type instanceof GraphQLObjectType) {
      return 'Object';
    }

    // Default case
    return 'Object';
  }

  /**
   * Get the appropriate return type for a mutation
   * @param type Parent type
   * @param fieldName Field name
   * @returns JavaScript type name for assertions
   */
  private getReturnTypeForMutation(type: GraphQLObjectType, fieldName: string): string {
    const field = type.getFields()[fieldName];
    if (field) {
      return this.getReturnTypeForAssertion(field.type);
    }
    return 'Object';
  }
}