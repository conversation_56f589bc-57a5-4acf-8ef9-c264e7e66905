import type {
  GraphQLType,
  GraphQLSchema,
  GraphQLArgument,
  GraphQLInputField
} from 'graphql';
import {
  GraphQLScalarType,
  GraphQLObjectType,
  GraphQLList,
  GraphQLNonNull,
  GraphQLEnumType,
  GraphQLInputObjectType
} from 'graphql';

/**
 * Generate a JavaScript code string that represents a mock value for a given GraphQL type
 * @param gqlType The GraphQL type to mock
 * @param schema The GraphQL schema 
 * @param depth Current recursion depth (to prevent infinite recursion)
 * @returns A string representation of mock data
 */
export function generateMockValueForType(
  gqlType: GraphQLType,
  schema: GraphQLSchema,
  depth = 0
): string {
  // Prevent infinite recursion
  if (depth > 3) {
    return 'null';
  }

  // Handle non-null wrapper
  if (gqlType instanceof GraphQLNonNull) {
    return generateMockValueForType(gqlType.ofType, schema, depth);
  }

  // Handle list wrapper
  if (gqlType instanceof GraphQLList) {
    const mockItem = generateMockValueForType(gqlType.ofType, schema, depth + 1);
    return `[${mockItem}]`;
  }

  // Handle scalar types
  if (gqlType instanceof GraphQLScalarType) {
    switch (gqlType.name) {
      case 'String':
        return `"mock-string"`;
      case 'ID':
        // Randomly generate either a string or number ID to exercise both types
        return Math.random() > 0.5
          ? `"mock-id-${Math.floor(Math.random() * 1000)}"`
          : `${Math.floor(Math.random() * 1000)}`;
      case 'Int':
        return `${Math.floor(Math.random() * 100)}`;
      case 'Float':
        return `${(Math.random() * 100).toFixed(2)}`;
      case 'Boolean':
        return 'false';
      case 'Date':
      case 'DateTime':
        return `new Date("2023-01-01T00:00:00Z")`;
      default:
        return `"mock-${gqlType.name.toLowerCase()}"`;
    }
  }

  // Handle enum types
  if (gqlType instanceof GraphQLEnumType) {
    const enumValues = gqlType.getValues();
    if (enumValues.length > 0) {
      return `"${enumValues[0].name}"`;
    }
    return 'null';
  }

  // Handle input object types
  if (gqlType instanceof GraphQLInputObjectType) {
    const fields = gqlType.getFields();
    const mockFields: string[] = [];

    for (const fieldName in fields) {
      const field = fields[fieldName];
      // Only include required fields to keep the mock simple
      if (field.type instanceof GraphQLNonNull) {
        mockFields.push(
          `${fieldName}: ${generateMockValueForType(field.type, schema, depth + 1)}`
        );
      }
    }

    if (mockFields.length > 0) {
      return `{ ${mockFields.join(', ')} }`;
    }
    return '{}';
  }

  // Handle object types - properly mock required fields
  if (gqlType instanceof GraphQLObjectType) {
    // Don't recurse too deep for object types
    if (depth > 3) {
      return '{}';
    }

    const fields = gqlType.getFields();
    const mockFields: string[] = [];

    for (const fieldName in fields) {
      const field = fields[fieldName];
      // Include required fields
      if (field.type instanceof GraphQLNonNull) {
        mockFields.push(
          `${fieldName}: ${generateMockValueForType(field.type, schema, depth + 1)}`
        );
      }
    }

    if (mockFields.length > 0) {
      return `{ ${mockFields.join(', ')} }`;
    }
    return '{}';
  }

  // Default case
  return 'null';
}

/**
 * Generate mock data for arguments
 * @param args GraphQL arguments to mock
 * @param schema GraphQL schema
 * @returns Array of mock arguments with their names and mock values
 */
export function generateMockArgs(
  args: readonly GraphQLArgument[],
  schema: GraphQLSchema
): Array<{ name: string; mockValue: string }> {
  return args.map(arg => ({
    name: arg.name,
    mockValue: generateMockValueForType(arg.type, schema)
  }));
}

/**
 * Generate mock data for input object fields
 * @param fields GraphQL input fields to mock
 * @param schema GraphQL schema
 * @returns Array of mock fields with their names and mock values
 */
export function generateMockInputFields(
  fields: { [key: string]: GraphQLInputField },
  schema: GraphQLSchema
): Array<{ name: string; mockValue: string }> {
  return Object.values(fields).map(field => ({
    name: field.name,
    mockValue: generateMockValueForType(field.type, schema)
  }));
}

/**
 * Parse a TypeScript interface or type to extract properties
 * @param content The TypeScript file content
 * @param typeName The name of the type or interface to extract
 * @returns Array of context properties with their names and mock values
 */
export function parseContextInterface(
  content: string,
  typeName: string
): Array<{ name: string; mockValue: string }> {
  const properties: Array<{ name: string; mockValue: string }> = [];

  // Extract the interface or type definition
  const interfaceRegex = new RegExp(
    `(?:interface|type)\\s+${typeName}\\s*(?:extends\\s+[^{]+)?\\s*{([^}]*)}`,
    's'
  );

  const match = content.match(interfaceRegex);
  if (!match) {
    return properties;
  }

  const interfaceBody = match[1];

  // Extract individual properties
  const propertyRegex = /(\w+)(?:\?)?:\s*([^;]+);/g;
  let propertyMatch;

  while ((propertyMatch = propertyRegex.exec(interfaceBody)) !== null) {
    const name = propertyMatch[1];
    const type = propertyMatch[2].trim();

    // Generate an appropriate mock value based on the type
    let mockValue: string;

    if (type.includes('function') || type.includes('=>') || /\([^)]*\)/.test(type)) {
      mockValue = 'jest.fn()';
    } else if (type === 'string' || type.includes('String')) {
      mockValue = '"mock-string"';
    } else if (type === 'number' || type.includes('Number')) {
      mockValue = '123';
    } else if (type === 'boolean' || type.includes('Boolean')) {
      mockValue = 'false';
    } else if (type.includes('[]') || type.includes('Array')) {
      mockValue = '[]';
    } else if (type === 'Date') {
      mockValue = 'new Date("2023-01-01")';
    } else if (type.includes('Map<') || type.includes('Record<')) {
      mockValue = 'new Map()';
    } else if (type.startsWith('{')) {
      mockValue = '{}';
    } else {
      mockValue = '{}'; // Default for objects and other complex types
    }

    properties.push({ name, mockValue });
  }

  return properties;
} 