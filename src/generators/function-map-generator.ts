import * as fs from 'fs-extra';
import * as path from 'path';
import { GraphQLSchema, GraphQLObjectType, GraphQLInterfaceType, isObjectType, isInterfaceType } from 'graphql';
import { DirectiveParser } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';
import { WatchedFileWriter } from '@utils/watched-file-writer';
import { calculateRelativeImportPath, convertToFileName } from '@generators/utils/type-utils';
import type { DirectiveContainer } from '@utils/directive-parser';
import _ from 'lodash';

/**
 * Interface for function map entry
 */
export interface FunctionMapEntry {
  name: string;
  type: 'method' | 'field';
  source: 'directive' | 'decorator' | 'inherited';
  call?: string;
  fieldType?: string;
  interfaceSource?: string;
}

/**
 * Interface for resolver import information
 */
export interface ResolverImportInfo {
  fieldName: string;
  importPath: string;
  exportName: string;
  hasResolver: boolean;
}

/**
 * Interface for type function map
 */
export interface TypeFunctionMap {
  typeName: string;
  typeKind: 'object' | 'interface';
  methods: FunctionMapEntry[];
  fields: FunctionMapEntry[];
  allEntries: FunctionMapEntry[];
  resolverImports: ResolverImportInfo[];
}

/**
 * Generator for function maps that aggregate all available methods and fields for types and interfaces
 */
export class FunctionMapGenerator {
  private schema: GraphQLSchema;
  private outputRoot: string;
  private schemaMapper: any;
  private decoratorDirectiveProvider?: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer;
  private interfaceInheritanceHandler?: any;
  private globalSchemaId?: string;

  constructor(
    schema: GraphQLSchema,
    options: {
      outputRoot: string;
      schemaMapper: any;
    }
  ) {
    this.schema = schema;
    this.outputRoot = options.outputRoot;
    this.schemaMapper = options.schemaMapper;
  }

  /**
   * Set decorator directive provider for integration with decorator system
   * @param directiveProvider Function to get decorator directives for a type/field
   */
  public setDecoratorProvider(
    directiveProvider: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer
  ): void {
    this.decoratorDirectiveProvider = directiveProvider;
  }

  /**
   * Set the global schema identifier for multi-schema support
   * @param schemaId The global schema identifier
   */
  public setGlobalSchemaId(schemaId?: string): void {
    this.globalSchemaId = schemaId;
  }

  /**
   * Set interface inheritance handler for inheritance support
   * @param inheritanceHandler Interface inheritance handler instance
   */
  public setInterfaceInheritanceHandler(inheritanceHandler: any): void {
    this.interfaceInheritanceHandler = inheritanceHandler;
  }

  /**
   * Determine the resolver file path for a given type and field
   * @param typeName The GraphQL type name
   * @param fieldName The field name
   * @param typeKind The type kind ('object' | 'interface')
   * @returns The absolute path to the resolver file or null if not determinable
   */
  private getResolverFilePath(typeName: string, fieldName: string, typeKind: 'object' | 'interface'): string | null {
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    if (!typeLocation) {
      return null;
    }

    const fileName = convertToFileName(fieldName);

    // Handle different type categories
    switch (typeLocation.category) {
      case 'query':
        return path.join(this.outputRoot, 'root-query', `${fileName}.ts`);

      case 'mutation':
        return path.join(this.outputRoot, 'root-mutation', `${fileName}.ts`);

      case 'subscription':
        return path.join(this.outputRoot, 'root-subscription', `${fileName}.ts`);

      case 'interface':
        // For interfaces: types/interface/{interface-name}/{field-name}.ts
        const interfaceDir = _.kebabCase(typeName);
        return path.join(this.outputRoot, 'types', 'interface', interfaceDir, `${fileName}.ts`);

      case 'object':
      default:
        // For object types: {outputRoot}/{relativeSchemaDir}/{kebabCaseFileName}/{kebabCaseTypeName}/{field-name}.ts
        // This matches the logic in object-type-processor.ts
        const schemaFile = typeLocation.sourceFile;
        const schemaDir = path.dirname(schemaFile);
        const schemaBaseName = path.basename(schemaFile, path.extname(schemaFile));

        // Get the relative path from schema root to the directory containing this type
        const schemaRoot = this.schemaMapper.schemaRoot;
        const relativeSchemaDir = path.relative(schemaRoot, path.join(schemaRoot, schemaDir));

        // Create the file name part in kebab-case
        const kebabCaseFileName = _.kebabCase(schemaBaseName);

        // Create output path that mirrors the schema directory structure with kebab-case file names
        // And appends the type name as a subdirectory
        const typeOutputPath = path.join(
          this.outputRoot,
          relativeSchemaDir,
          kebabCaseFileName,
          _.kebabCase(typeName)
        );

        return path.join(typeOutputPath, `${fileName}.ts`);
    }
  }

  /**
   * Get import information for a resolver
   * @param typeName The GraphQL type name
   * @param fieldName The field name
   * @param typeKind The type kind ('object' | 'interface')
   * @param functionMapFilePath The path to the function map file being generated
   * @returns Resolver import information
   */
  private getResolverImportInfo(
    typeName: string,
    fieldName: string,
    typeKind: 'object' | 'interface',
    functionMapFilePath: string
  ): ResolverImportInfo {
    const resolverFilePath = this.getResolverFilePath(typeName, fieldName, typeKind);



    if (!resolverFilePath || !fs.existsSync(resolverFilePath)) {
      return {
        fieldName,
        importPath: '',
        exportName: fieldName,
        hasResolver: false
      };
    }

    // Calculate relative import path from function map file to resolver file
    const importPath = calculateRelativeImportPath(functionMapFilePath, resolverFilePath, this.outputRoot);



    return {
      fieldName,
      importPath: importPath.replace('.ts', ''), // Remove .ts extension for imports
      exportName: fieldName,
      hasResolver: true
    };
  }

  /**
   * Generate function maps for all types and interfaces
   */
  public async generateFunctionMaps(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Generating function maps...');
    }

    const typeMap = this.schema.getTypeMap();
    const functionMaps: TypeFunctionMap[] = [];

    // Process object types
    for (const [typeName, type] of Object.entries(typeMap)) {
      if (isObjectType(type) && !typeName.startsWith('__')) {
        const functionMap = await this.generateTypeMap(type, 'object');
        if (functionMap) {
          functionMaps.push(functionMap);
        }
      }
    }

    // Process interface types
    for (const [typeName, type] of Object.entries(typeMap)) {
      if (isInterfaceType(type) && !typeName.startsWith('__')) {
        const functionMap = await this.generateTypeMap(type, 'interface');
        if (functionMap) {
          functionMaps.push(functionMap);
        }
      }
    }

    // Generate individual function map files
    for (const functionMap of functionMaps) {
      await this.generateFunctionMapFile(functionMap);
    }

    // Generate master index file
    await this.generateMasterIndex(functionMaps);

    if (process.env.DEBUG_PARSER) {
      console.log(`Generated ${functionMaps.length} function maps`);
    }
  }

  /**
   * Generate function map for a specific type
   * @param type GraphQL type (object or interface)
   * @param typeKind Type kind ('object' or 'interface')
   * @returns Type function map or null if no entries found
   */
  private async generateTypeMap(
    type: GraphQLObjectType | GraphQLInterfaceType,
    typeKind: 'object' | 'interface'
  ): Promise<TypeFunctionMap | null> {
    const typeName = type.name;
    const fields = type.getFields();
    const methods: FunctionMapEntry[] = [];
    const fieldEntries: FunctionMapEntry[] = [];
    const resolverImports: ResolverImportInfo[] = [];



    // Get type location for schema file path
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';
    const absoluteSchemaPath = path.resolve(this.schemaMapper.schemaRoot, schemaFilePath);

    // Determine the function map file path for import calculations
    const outputDir = path.join(this.outputRoot, 'function-maps', typeKind === 'interface' ? 'interfaces' : 'types');
    const functionMapFilePath = path.join(outputDir, `${typeName.toLowerCase()}.ts`);

    // Process each field
    for (const [fieldName, field] of Object.entries(fields)) {
      // Get resolver import information for this field (for ALL fields, not just those with directives)
      const resolverImportInfo = this.getResolverImportInfo(typeName, fieldName, typeKind, functionMapFilePath);
      resolverImports.push(resolverImportInfo);

      // Get comment-based directives
      const fieldDirectives = await DirectiveParser.extractDirectivesFromSchema(
        absoluteSchemaPath,
        typeName,
        fieldName
      );

      // Get decorator-based directives
      let decoratorDirectives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };
      if (this.decoratorDirectiveProvider) {
        decoratorDirectives = this.decoratorDirectiveProvider(typeName, fieldName, this.globalSchemaId);
      }

      // Check for method calls
      const commentMethodCall = DirectiveProcessor.extractMethodCall(fieldDirectives);
      const decoratorMethodCall = DirectiveProcessor.extractMethodCall(decoratorDirectives);
      const methodCall = decoratorMethodCall || commentMethodCall;

      if (methodCall) {
        methods.push({
          name: fieldName,
          type: 'method',
          source: decoratorMethodCall ? 'decorator' : 'directive',
          call: methodCall
        });
      }

      // Check for field directives
      const hasFieldDirectives = fieldDirectives.fieldFields.length > 0 || decoratorDirectives.fieldFields.length > 0;
      if (hasFieldDirectives) {
        const allFieldDirectives = [...fieldDirectives.fieldFields, ...decoratorDirectives.fieldFields];
        for (const fieldDirective of allFieldDirectives) {
          fieldEntries.push({
            name: fieldDirective.name,
            type: 'field',
            source: 'directive',
            fieldType: fieldDirective.type
          });
        }
      }

      // Check for inherited method calls (if interface inheritance handler is available)
      if (!methodCall && this.interfaceInheritanceHandler) {
        const inheritanceInfo = await this.interfaceInheritanceHandler.findInheritedMethodCall(typeName, fieldName);
        if (inheritanceInfo) {
          methods.push({
            name: fieldName,
            type: 'method',
            source: 'inherited',
            call: inheritanceInfo.methodCall,
            interfaceSource: inheritanceInfo.interfaceName
          });
        }
      }
    }

    // Return null if no entries found (but include resolver imports)
    if (methods.length === 0 && fieldEntries.length === 0 && resolverImports.filter(r => r.hasResolver).length === 0) {
      return null;
    }

    const allEntries = [...methods, ...fieldEntries];

    return {
      typeName,
      typeKind,
      methods,
      fields: fieldEntries,
      allEntries,
      resolverImports
    };
  }

  /**
   * Generate function map file for a specific type
   * @param functionMap Type function map
   */
  private async generateFunctionMapFile(functionMap: TypeFunctionMap): Promise<void> {
    const { typeName, typeKind, methods, fields, allEntries } = functionMap;
    
    // Create output directory
    const outputDir = path.join(this.outputRoot, 'function-maps', typeKind === 'interface' ? 'interfaces' : 'types');
    await fs.ensureDir(outputDir);

    // Generate file content
    const content = this.generateFunctionMapContent(functionMap);

    // Write file
    const outputFile = path.join(outputDir, `${typeName.toLowerCase()}.ts`);
    WatchedFileWriter.writeFileSync(outputFile, content);

    if (process.env.DEBUG_PARSER) {
      console.log(`Generated function map for ${typeKind} ${typeName}: ${allEntries.length} entries`);
    }
  }

  /**
   * Generate content for function map file
   * @param functionMap Type function map
   * @returns Generated file content
   */
  private generateFunctionMapContent(functionMap: TypeFunctionMap): string {
    const { typeName, typeKind, methods, fields, allEntries, resolverImports } = functionMap;

    // Generate import statements for resolvers that exist
    const availableResolvers = resolverImports.filter(r => r.hasResolver);
    const importStatements = availableResolvers.length > 0
      ? availableResolvers.map(r => `import { ${r.exportName} } from '${r.importPath}';`).join('\n') + '\n\n'
      : '';

    const methodsObject = methods.length > 0
      ? methods.map((m, index) => {
          const comment = m.interfaceSource ? ` // inherited from ${m.interfaceSource}` : '';
          const comma = index < methods.length - 1 ? ',' : '';
          return `  ${m.name}: '${m.call}'${comma}${comment}`;
        }).join('\n')
      : '';

    const fieldsObject = fields.length > 0
      ? fields.map(f => `  ${f.name}: '${f.fieldType}'`).join(',\n')
      : '';

    const allEntriesObject = allEntries.map((e, index) => {
      const comment = e.source === 'inherited' ? ` // inherited from ${e.interfaceSource}` : '';
      const comma = index < allEntries.length - 1 ? ',' : '';
      return `  ${e.name}: { type: '${e.type}', source: '${e.source}'${e.call ? `, call: '${e.call}'` : ''}${e.fieldType ? `, fieldType: '${e.fieldType}'` : ''} }${comma}${comment}`;
    }).join('\n');

    // Generate function reference map with actual function references
    const functionReferencesObject = availableResolvers.length > 0
      ? availableResolvers.map(r => `  ${r.fieldName}`).join(',\n')
      : '';



    return `/**
 * Function map for ${typeKind} ${typeName}
 * Auto-generated - contains all available methods and fields
 */

${importStatements}export const ${typeName}Methods = {
${methodsObject}
};

export const ${typeName}Fields = {
${fieldsObject}
};

export const ${typeName}FunctionMap = {
${allEntriesObject}
};

${availableResolvers.length > 0 ? `/**
 * ${typeName} resolver function references
 * Contains actual function references for idiomatic usage
 */
export const ${typeName}Type = {
${functionReferencesObject}
};

` : ''}export default ${typeName}FunctionMap;
`;
  }

  /**
   * Generate master index file that exports all function maps
   * @param functionMaps All generated function maps
   */
  private async generateMasterIndex(functionMaps: TypeFunctionMap[]): Promise<void> {
    const outputDir = path.join(this.outputRoot, 'function-maps');
    await fs.ensureDir(outputDir);

    const exports = functionMaps.map(fm => {
      const dir = fm.typeKind === 'interface' ? 'interfaces' : 'types';
      const hasResolvers = fm.resolverImports.some(r => r.hasResolver);
      const typeExport = hasResolvers ? `, ${fm.typeName}Type` : '';
      return `export { default as ${fm.typeName}FunctionMap, ${fm.typeName}Methods, ${fm.typeName}Fields${typeExport} } from './${dir}/${fm.typeName.toLowerCase()}';`;
    }).join('\n');

    const content = `/**
 * Master function map index
 * Auto-generated - exports all available function maps
 */

${exports}

// Type definitions
export interface FunctionMapEntry {
  type: 'method' | 'field';
  source: 'directive' | 'decorator' | 'inherited';
  call?: string;
  fieldType?: string;
}

export type FunctionMap = Record<string, FunctionMapEntry>;
`;

    const outputFile = path.join(outputDir, 'index.ts');
    WatchedFileWriter.writeFileSync(outputFile, content);

    if (process.env.DEBUG_PARSER) {
      console.log('Generated master function map index');
    }
  }
}
