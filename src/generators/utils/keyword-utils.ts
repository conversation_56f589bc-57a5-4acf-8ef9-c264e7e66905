/**
 * Utility functions for handling JavaScript keywords
 */

/**
 * List of JavaScript reserved keywords that should not be used as variable or function names
 */
export const JS_KEYWORDS = [
  // JavaScript keywords
  'break', 'case', 'catch', 'class', 'const', 'continue', 'debugger', 'default', 'delete',
  'do', 'else', 'export', 'extends', 'finally', 'for', 'function', 'if', 'import',
  'in', 'instanceof', 'new', 'return', 'super', 'switch', 'this', 'throw', 'try',
  'typeof', 'var', 'void', 'while', 'with', 'yield',

  // Future reserved words
  'enum', 'implements', 'interface', 'let', 'package', 'private', 'protected', 'public', 'static',

  // Null, boolean literals
  'null', 'true', 'false',

  // Common built-in objects that might cause conflicts
  'Object', 'Function', 'Boolean', 'Symbol', 'Error', 'EvalError', 'RangeError',
  'ReferenceError', 'SyntaxError', 'TypeError', 'URIError', 'Number', 'Math',
  'Date', 'String', 'RegExp', 'Array', 'Int8Array', 'Uint8Array', 'Uint8ClampedArray',
  'Int16Array', 'Uint16Array', 'Int32Array', 'Uint32Array', 'Float32Array', 'Float64Array',
  'Map', 'Set', 'WeakMap', 'WeakSet', 'Promise'
];

/**
 * Check if a name is a JavaScript reserved keyword
 * @param name The name to check
 * @returns True if the name is a reserved keyword
 */
export function isJavaScriptKeyword(name: string): boolean {
  return JS_KEYWORDS.includes(name);
}

/**
 * Make a name safe to use as a JavaScript identifier by appending '_handler' if it's a reserved keyword
 * @param name The name to make safe
 * @returns The safe name
 */
export function makeSafeJavaScriptName(name: string): string {
  return isJavaScriptKeyword(name) ? `${name}_handler` : name;
}
