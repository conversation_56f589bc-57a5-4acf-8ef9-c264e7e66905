import type { G<PERSON>h<PERSON><PERSON>ield } from 'graphql';
import * as path from 'path';
import * as fs from 'fs';
import * as glob from 'glob';
import { isJavaScriptKeyword } from './keyword-utils';
import { extractScalarTypesFromSchema } from '../../utils/type-converters';

/**
 * Check if a type name is a scalar type defined in the schema
 * @param typeName The type name to check
 * @param schemaRoot Optional schema root directory
 * @returns True if the type is a scalar type
 */
export function isScalarType(typeName: string, schemaRoot?: string): boolean {
  const scalarTypes = extractScalarTypesFromSchema(schemaRoot);
  return scalarTypes.has(typeName);
}

/**
 * Convert GraphQL return type to TypeScript return type
 * @param graphqlType The GraphQL type string
 * @param projectRoot Optional project root for loading scalar mappings
 * @param schemaRoot Optional schema root for identifying scalar types
 * @returns The equivalent TypeScript type
 */
export function convertToTypeScriptReturnType(
  graphqlType: string,
  projectRoot?: string,
  schemaRoot?: string
): string {
  // Remove "!" for non-null types and convert to TypeScript nullable union
  const nullable = !graphqlType.endsWith('!');
  const baseType = graphqlType.replace(/!/g, '');

  // Handle list types
  if (baseType.startsWith('[') && baseType.endsWith(']')) {
    // Extract the inner type
    const innerType = baseType.substring(1, baseType.length - 1);
    // Convert the inner type to TypeScript and wrap in an array
    const tsInnerType = convertToTypeScriptReturnType(innerType, projectRoot, schemaRoot);
    return nullable ? `Array<${tsInnerType}> | null` : `Array<${tsInnerType}>`;
  }

  // Check if this is a scalar type defined in the schema
  if (isScalarType(baseType, schemaRoot)) {
    // For scalar types, use clean type name (maps to 'unknown' in scalars.ts)
    const scalarTypeRef = baseType;
    return nullable ? `${scalarTypeRef} | null` : scalarTypeRef;
  }

  // For complex types (object types, interfaces, unions), use the generated GraphQL types
  return nullable ? `${baseType} | null` : baseType;
}

/**
 * Generate a suitable default return value based on the GraphQL type
 * @param returnType The GraphQL type to generate a default value for
 * @returns A statement that returns an appropriate default value
 */
export function generateDefaultReturnValue(returnType: string): string {
  // Remove the non-null marker for checking
  const typeForCheck = returnType.replace(/!/g, '');

  // Handle lists - match [Type] or [Type!] patterns
  if (typeForCheck.startsWith('[') && typeForCheck.endsWith(']')) {
    return 'return [];';
  }

  // Handle nullable types - if it doesn't end with ! it's nullable
  if (!returnType.endsWith('!')) {
    return 'return null;';
  }

  // Handle scalar types
  if (typeForCheck === 'String') return 'return "";';
  if (typeForCheck === 'Int') return 'return 0;';
  if (typeForCheck === 'Float') return 'return 0.0;';
  if (typeForCheck === 'Boolean') return 'return false;';
  if (typeForCheck === 'ID') return 'return "";  // Could also be a number';

  // For complex types, we need to return an object with the required fields
  // Since we don't know the required fields, throw an implementation error
  return 'throw new Error("' + returnType + ' resolver not implemented");';
}

/**
 * Format arguments for schema representation
 * @param args The field arguments
 * @returns The formatted argument string
 */
export function formatArgs(args: readonly any[]): string {
  if (!args || args.length === 0) {
    return '';
  }

  const argStrings = args.map(arg =>
    `${arg.name}: ${arg.type.toString()}`
  );

  return `(${argStrings.join(', ')})`;
}

/**
 * Check if a field is a simple scalar field without arguments
 * @param field The GraphQL field
 * @returns Whether the field is a simple scalar field
 */
export function isSimpleField(field: GraphQLField<any, any>): boolean {
  // If it has arguments, it's not a simple field
  if (field.args && field.args.length > 0) {
    return false;
  }

  // Check if the return type is a scalar
  const returnType = field.type.toString();
  const isScalar = (
    returnType.includes('String') ||
    returnType.includes('Int') ||
    returnType.includes('Float') ||
    returnType.includes('Boolean') ||
    returnType.includes('ID')
  );

  // Also check if it's a non-list type
  const isNotList = !returnType.includes('[');

  return isScalar && isNotList;
}

/**
 * Convert a field name to a suitable file name
 * @param name The field name
 * @returns The file name
 */
export function convertToFileName(name: string): string {
  // Convert camelCase to kebab-case
  const kebabCase = name.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();

  // Check if the original name is a JavaScript keyword
  // If it is, append "-handler" to the kebab-case name
  return isJavaScriptKeyword(name) ? `${kebabCase}-handler` : kebabCase;
}

/**
 * Calculate a relative import path from one file to another
 * @param fromFile The source file
 * @param toFile The target file
 * @param outputRoot The root output directory
 * @returns The relative import path
 */
export function calculateRelativeImportPath(fromFile: string, toFile: string, outputRoot: string): string {
  // If toFile starts with an alias (e.g., @context/context), return it as is
  if (toFile.startsWith('@')) {
    return toFile;
  }

  // If toFile is not an absolute path, assume it's relative to the output root
  const absoluteToFile = path.isAbsolute(toFile)
    ? toFile
    : path.join(outputRoot, toFile);

  // Get the directory containing the source file
  const fromDir = path.dirname(fromFile);

  // Calculate the relative path
  let relativePath = path.relative(fromDir, absoluteToFile);

  // Convert backslashes to forward slashes for import statements
  relativePath = relativePath.replace(/\\/g, '/');

  // Ensure the path starts with ./ or ../
  if (!relativePath.startsWith('.')) {
    relativePath = './' + relativePath;
  }

  // Remove the file extension
  relativePath = relativePath.replace(/\.ts$/, '');

  return relativePath;
}