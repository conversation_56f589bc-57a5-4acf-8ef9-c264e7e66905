import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import { hasCustomImplementation } from '@generators/utils/file-utils';
import { cleanupEmptyDirectories } from '@generators/utils/file-utils';
import { WatchedFileWriter } from '../../utils/watched-file-writer';

/**
 * Service for cleaning up obsolete files
 */
export class CleanupService {
  private outputRoot: string;
  private processedFiles: Set<string>;
  private processedDirectories: Set<string>;
  private preservedFiles: Set<string>;
  private deletedFiles: Set<string>;
  private generatedFileTimestamps: Map<string, number>;

  /**
   * Create a new cleanup service
   * @param options Configuration options
   */
  constructor(options: {
    outputRoot: string;
    processedFiles: Set<string>;
    processedDirectories: Set<string>;
    preservedFiles: Set<string>;
    generatedFileTimestamps: Map<string, number>;
  }) {
    this.outputRoot = options.outputRoot;
    this.processedFiles = options.processedFiles;
    this.processedDirectories = options.processedDirectories;
    this.preservedFiles = options.preservedFiles;
    this.deletedFiles = new Set<string>();
    this.generatedFileTimestamps = options.generatedFileTimestamps;
  }

  /**
   * Clean up any obsolete files that are no longer needed
   */
  public async cleanupObsoleteFiles(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Cleaning up obsolete files...');
    }

    // Find all TypeScript files in the output directory
    const allFiles = glob.sync('**/*.ts', {
      cwd: this.outputRoot,
      ignore: ['**/*.d.ts', '**/node_modules/**', '**/graphql.ts', '**/schema.graphql'],
      absolute: true,
      windowsPathsNoEscape: true
    });

    // Create a normalized version of processedFiles for comparison
    // This helps when there's a mismatch between absolute paths and relative paths
    const normalizedProcessedFiles = new Set<string>();
    for (const file of this.processedFiles) {
      // For each processed file, create both normalized absolute path and relative path versions
      const absolutePath = path.isAbsolute(file) ? file : path.resolve(this.outputRoot, file);
      const relativePath = path.relative(this.outputRoot, absolutePath);
      normalizedProcessedFiles.add(absolutePath);
      normalizedProcessedFiles.add(path.join(this.outputRoot, relativePath));
    }

    // Similarly normalize preserved files
    const normalizedPreservedFiles = new Set<string>();
    for (const file of this.preservedFiles) {
      const absolutePath = path.isAbsolute(file) ? file : path.resolve(this.outputRoot, file);
      const relativePath = path.relative(this.outputRoot, absolutePath);
      normalizedPreservedFiles.add(absolutePath);
      normalizedPreservedFiles.add(path.join(this.outputRoot, relativePath));
    }

    for (const file of allFiles) {
      // Normalize the file path for comparison
      const absolutePath = path.resolve(file);
      const relativePath = path.relative(this.outputRoot, file);
      const alternativePath = path.join(this.outputRoot, relativePath);

      // Skip files that we explicitly processed (check both absolute and relative paths)
      if (normalizedProcessedFiles.has(absolutePath) ||
        normalizedProcessedFiles.has(alternativePath) ||
        normalizedProcessedFiles.has(relativePath)) {
        continue;
      }

      // Additional check for files that might be in processedFiles with a different path format
      const fileBaseName = path.basename(file);
      const isProcessedByName = Array.from(this.processedFiles).some(processedFile => {
        const processedBaseName = path.basename(processedFile);
        return processedBaseName === fileBaseName;
      });

      if (isProcessedByName) {
        console.log(`File ${file} matched by basename with a processed file, skipping cleanup.`);
        continue;
      }

      // Skip index files that we might have generated
      if (file.endsWith('index.ts')) {
        continue;
      }

      // Skip files in processed directories
      const fileDir = path.dirname(file);
      if ((this.processedDirectories.has(fileDir) && file.includes('__resolve-type')) ||
        file.includes('interfaces/') || file.includes('unions/')) {
        continue;
      }

      // Skip preserved files (check both absolute and relative paths)
      if (normalizedPreservedFiles.has(absolutePath) ||
        normalizedPreservedFiles.has(alternativePath) ||
        normalizedPreservedFiles.has(relativePath)) {
        continue;
      }

      // Special case for interface and union resolver files
      if (file.includes('__resolve-type.ts')) {
        // Always preserve __resolve-type.ts files
        this.preservedFiles.add(file);
        continue;
      }

      // Check if the file has custom implementation
      const hasCustomCode = await hasCustomImplementation(file);

      if (hasCustomCode) {
        // Preserve file with custom implementation
        this.preservedFiles.add(file);

        // Only add auto-generated comment if the file is truly obsolete
        // (i.e., not in processedFiles but has custom implementation)
        const fileContent = fs.readFileSync(file, 'utf8');
        if (!fileContent.includes('THIS FILE IS AUTO-GENERATED BUT PRESERVED') &&
          // Make sure the file is not within the same generator run's processed files
          !Array.from(this.processedFiles).some(processedFile =>
            path.normalize(processedFile).includes(path.normalize(path.basename(file))))) {
          const autoGeneratedComment = '// THIS FILE IS AUTO-GENERATED BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n';
          const newContent = fileContent.replace(/^((?:\/\/.*\n)*)/m, `$1${autoGeneratedComment}`);
          WatchedFileWriter.writeFileSync(file, newContent);
          console.log(`Added auto-generated comment to preserved file: ${file}`);
        } else if (fileContent.includes('THIS FILE IS AUTO-GENERATED BUT PRESERVED') &&
          Array.from(this.processedFiles).some(processedFile =>
            path.normalize(processedFile).includes(path.normalize(path.basename(file))))) {
          // Remove auto-generated comment from files that are in the current generator run
          const updatedContent = fileContent.replace(/\/\/ THIS FILE IS AUTO-GENERATED BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n/g, '');
          fs.writeFileSync(file, updatedContent);
          console.log(`Removed auto-generated comment from file: ${file}`);
        }
      } else {
        // Delete obsolete file
        console.log(`File check - absolute path: ${absolutePath}`);
        console.log(`File check - alternative path: ${alternativePath}`);
        console.log(`File check - relative path: ${relativePath}`);
        console.log(`Processed files has path: ${this.processedFiles.has(absolutePath)}`);
        fs.unlinkSync(file);
        this.deletedFiles.add(file);
        console.log(`Deleted obsolete file: ${file}`);
      }
    }

    // Clean up empty directories
    cleanupEmptyDirectories(this.outputRoot);
  }

  /**
   * Get the number of deleted files
   */
  public getDeletedFilesCount(): number {
    return this.deletedFiles.size;
  }
} 