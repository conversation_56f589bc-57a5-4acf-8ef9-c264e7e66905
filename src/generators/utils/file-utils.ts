import * as fs from 'fs-extra';
import * as path from 'path';
import _ from 'lodash';
import { WatchedFileWriter } from '../../utils/watched-file-writer';

/**
 * Generate an index file for a directory
 * @param directoryPath Path to the directory
 * @param files List of files in the directory (without extension)
 * @returns The path to the generated index file
 */
export function generateIndexFile(directoryPath: string, files: string[]): string {
  if (!fs.existsSync(directoryPath)) {
    return '';
  }

  const indexPath = path.join(directoryPath, 'index.ts');
  let indexContent = '// Generated index file\n\n';

  // First, add imports for all resolvers we'll reference in the exports object
  const validFiles = files.filter(file => file !== 'index' && !file.startsWith('.'));
  for (const file of validFiles) {
    const camelCaseName = _.camelCase(file);
    indexContent += `import { default as ${camelCaseName} } from './${file}';\n`;
  }

  // Add a blank line after imports if there are any
  if (validFiles.length > 0) {
    indexContent += '\n';
  }

  // Add exports for each file
  for (const file of validFiles) {
    const camelCaseName = _.camelCase(file);
    indexContent += `export { ${camelCaseName} };\n`;
  }

  // Add exports object
  if (validFiles.length > 0) {
    indexContent += '\n// Consolidated exports object\n';
    indexContent += 'export const resolvers = {\n';

    for (const file of validFiles) {
      const camelCaseName = _.camelCase(file);
      indexContent += `  ${camelCaseName},\n`;
    }

    indexContent += '};\n';
  }

  // Write the index file
  WatchedFileWriter.writeFileSync(indexPath, indexContent);

  if (process.env.DEBUG_PARSER) {
    console.log(`Generated index file at ${indexPath}`);
  }

  return indexPath;
}

/**
 * Clean up empty directories recursively
 * @param directoryPath The directory to check
 */
export function cleanupEmptyDirectories(directoryPath: string): void {
  if (!fs.existsSync(directoryPath)) {
    return;
  }

  // Get all items in the directory
  const items = fs.readdirSync(directoryPath);

  // Process subdirectories first
  for (const item of items) {
    const itemPath = path.join(directoryPath, item);

    // Skip hidden files and non-directories
    if (item.startsWith('.') || !fs.statSync(itemPath).isDirectory()) {
      continue;
    }

    // Recursively clean up subdirectories
    cleanupEmptyDirectories(itemPath);
  }

  // Check if directory is empty after cleaning subdirectories
  const remainingItems = fs.readdirSync(directoryPath);

  if (remainingItems.length === 0) {
    // Delete empty directory
    fs.rmdirSync(directoryPath);
    console.log(`Deleted empty directory: ${directoryPath}`);
  }
}

/**
 * Check if a file contains custom implementation code
 * @param filePath Path to the file
 * @returns True if the file contains custom code
 */
export async function hasCustomImplementation(filePath: string): Promise<boolean> {
  try {
    // Read the file content
    const content = fs.readFileSync(filePath, 'utf-8');

    // If the file is empty, it doesn't have custom code
    if (!content.trim()) {
      return false;
    }
    
    // If the file contains a TODO comment, it's likely unmodified boilerplate
    if (content.includes('TODO: Implement') || content.includes('throw new Error("Not implemented")')) {
      console.log(`Skipping file with TODO comment: ${filePath}`);
      return false;
    }

    // Look for markers of custom implementation
    const customCodeIndicators = [
      // Custom logic that doesn't just throw errors
      /console\.log|return [^;]+;(?!.*throw)/,
      // Custom imports beyond the standard ones - FIXED to not exclude Context imports
      /import (?!.*\{.*ResolversParentTypes.*\}|.*Context.*\}).+/,
      // Business logic code
      /if\s*\(.*\)|for\s*\(.*\)|switch\s*\(.*\)|try\s*\{/
    ];

    // Check for each indicator
    for (const indicator of customCodeIndicators) {
      if (indicator.test(content)) {
        return true;
      }
    }

    // If the file doesn't match any of the custom code indicators
    // but also doesn't have TODO comments, consider it potentially modified
    if (!content.includes('TODO') &&
        !content.includes('throw new Error') &&
        !content.includes('// CUSTOM IMPLEMENTATION START')) {
      // Likely modified but not in a way our patterns detect
      if (process.env.DEBUG_PARSER) {
        console.log(`File may have custom implementation: ${filePath}`);
      }
      return true;
    }

    // Default to preserving files with no clear patterns
    return true;
  } catch (error) {
    console.error(`Error checking custom implementation in ${filePath}:`, error);
    return false; // Assume no custom code on error
  }
}

/**
 * Preserve custom implementation in a file while updating the boilerplate
 * @param file Path to the file
 * @param template New template content
 * @returns Merged content with custom implementation preserved
 */
export const preserveCustomImplementation = async (file: string, template: string): Promise<string> => {
  const existingContent = fs.readFileSync(file, 'utf8');
  
  // Extract custom implementation from the file
  const customImplPattern = /\/\/ CUSTOM IMPLEMENTATION START\s*([\s\S]*?)\s*\/\/ CUSTOM IMPLEMENTATION END/;
  const customImplMatch = existingContent.match(customImplPattern);
  
  if (customImplMatch && customImplMatch[1]) {
    // The file has custom implementation, preserve it
    const customImpl = customImplMatch[1].trim();
    
    // Replace the implementation in the template
    const newContent = template.replace(
      /\/\/ CUSTOM IMPLEMENTATION START\s*[\s\S]*?\/\/ CUSTOM IMPLEMENTATION END/,
      `// CUSTOM IMPLEMENTATION START\n// THIS FILE IS OBSOLETE BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n${customImpl}\n// CUSTOM IMPLEMENTATION END`
    );
    
    return newContent;
  }
  
  // No custom implementation, return the original template
  return template;
}; 