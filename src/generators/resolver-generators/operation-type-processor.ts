import * as fs from 'fs-extra';
import * as path from 'path';
import type { GraphQLSchema } from 'graphql';
import { GraphQLObjectType } from 'graphql';
import type { FieldResolverGenerator } from '@resolvers/field-resolver-generator';
import { convertToFileName } from '@generators/utils/type-utils';
import { generateIndexFile } from '@generators/utils/file-utils';

/**
 * Processor for operation types (Query, Mutation, Subscription)
 */
export class OperationTypeProcessor {
  private schema: GraphQLSchema;
  private schemaMapper: any;
  private fieldResolverGenerator: FieldResolverGenerator;

  /**
   * Create a new operation type processor
   * @param schema The GraphQL schema
   * @param fieldResolverGenerator The field resolver generator
   * @param schemaMapper The schema mapper
   */
  constructor(
    schema: GraphQLSchema,
    fieldResolverGenerator: FieldResolverGenerator,
    schemaMapper: any
  ) {
    this.schema = schema;
    this.fieldResolverGenerator = fieldResolverGenerator;
    this.schemaMapper = schemaMapper;
  }

  /**
   * Process query operations
   */
  public async processQueryTypes(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Processing Query types...');
    }
    await this.processOperationTypes('query');
  }

  /**
   * Process mutation operations
   */
  public async processMutationTypes(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Processing Mutation types...');
    }
    await this.processOperationTypes('mutation');
  }

  /**
   * Process subscription operations
   */
  public async processSubscriptionTypes(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Processing Subscription types...');
    }
    await this.processOperationTypes('subscription');
  }

  /**
   * Process operation types based on category
   * @param category The category of operation (query, mutation, subscription)
   */
  private async processOperationTypes(category: 'query' | 'mutation' | 'subscription'): Promise<void> {
    // Get all types categorized as the specified operation
    const typeNames = this.schemaMapper.getTypesByCategory(category);

    for (const typeName of typeNames) {
      console.log(`Processing ${category} type: ${typeName}`);
      const type = this.schema.getType(typeName);

      if (!type || !(type instanceof GraphQLObjectType)) {
        console.log(`Skipping ${typeName} - not an object type`);
        continue;
      }

      // Get the output directory for this operation type
      const outputPath = this.schemaMapper.getOutputPathForType(typeName);
      if (!outputPath) {
        console.warn(`Could not determine output path for ${category} type ${typeName}`);
        continue;
      }

      // Ensure the directory exists
      fs.ensureDirSync(outputPath);

      // Process each field in the operation type
      const fields = type.getFields();
      const processedFiles: string[] = [];

      for (const fieldName in fields) {
        // Skip special fields
        if (fieldName.startsWith('__')) {
          continue;
        }

        const field = fields[fieldName];
        const fileName = convertToFileName(fieldName);
        const outputFile = path.join(outputPath, `${fileName}.ts`);

        // Generate the resolver implementation
        await this.fieldResolverGenerator.generateFieldResolver(typeName, fieldName, field, outputFile);
        processedFiles.push(fileName);
      }

      // Generate an index file for this directory
      if (processedFiles.length > 0) {
        generateIndexFile(outputPath, processedFiles);
      }
    }
  }
} 