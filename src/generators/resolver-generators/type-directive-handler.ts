import * as path from 'path';
import type { DirectiveContainer, FieldDirectiveField } from '@utils/directive-parser';
import { DirectiveParser } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';

/**
 * Handles directive processing at the type level
 */
export class TypeDirectiveHandler {
  private typeDirectivesCache: Map<string, DirectiveContainer> = new Map();
  private schemaRoot: string;

  /**
   * Create a new type directive handler
   * @param schemaRoot The root schema directory
   */
  constructor(schemaRoot: string) {
    this.schemaRoot = schemaRoot;
  }

  /**
   * Get directives for a specific type
   * @param typeName The name of the type
   * @param schemaFilePath The schema file path (relative to schema root)
   * @returns Directive container for the type
   */
  public async getTypeDirectives(
    typeName: string,
    schemaFilePath: string
  ): Promise<DirectiveContainer> {
    // Check cache first
    const cacheKey = `${typeName}:${schemaFilePath}`;
    if (this.typeDirectivesCache.has(cacheKey)) {
      return this.typeDirectivesCache.get(cacheKey)!;
    }

    // Get absolute path to schema file
    const absoluteSchemaPath = path.resolve(this.schemaRoot, schemaFilePath);

    // Extract directives from schema file
    const directives = await DirectiveParser.extractDirectivesFromSchema(
      absoluteSchemaPath,
      typeName
    );

    // Cache for future use
    this.typeDirectivesCache.set(cacheKey, directives);

    return directives;
  }

  /**
   * Get type-level imports that should apply to all fields in the type
   * @param typeName The name of the type
   * @param schemaFilePath The schema file path (relative to schema root)
   * @returns Array of import statements
   */
  public async getTypeImports(typeName: string, schemaFilePath: string): Promise<string[]> {
    const typeDirectives = await this.getTypeDirectives(typeName, schemaFilePath);
    return DirectiveProcessor.extractImportStatements(typeDirectives);
  }

  /**
   * Get type-level imports that should apply to all fields in the type with transformed paths
   * @param typeName The name of the type
   * @param schemaFilePath The schema file path (relative to schema root)
   * @param outputFilePath The absolute path to the output file where imports will be used
   * @returns Array of transformed import statements
   */
  public async getTypeTransformedImports(
    typeName: string,
    schemaFilePath: string,
    outputFilePath: string
  ): Promise<string[]> {
    const typeDirectives = await this.getTypeDirectives(typeName, schemaFilePath);
    const absoluteSchemaPath = path.resolve(this.schemaRoot, schemaFilePath);

    return DirectiveProcessor.extractTransformedImportStatements(
      typeDirectives,
      absoluteSchemaPath,
      outputFilePath
    );
  }

  /**
   * Extract all relevant field directive fields for a type
   * @param typeName The name of the type
   * @param schemaFilePath The path to the schema file containing the type
   * @returns Array of field directive fields for the type
   */
  public async getFieldDirectiveFields(
    typeName: string,
    schemaFilePath: string
  ): Promise<FieldDirectiveField[]> {
    const typeDirectives = await this.getTypeDirectives(typeName, schemaFilePath);
    return typeDirectives.fieldFields;
  }
} 