import * as fs from 'fs-extra';
import * as path from 'path';
import _ from 'lodash';
import * as glob from 'glob';

/**
 * Generator for root index file
 */
export class RootIndexGenerator {
  private outputRoot: string;
  private processedFiles: Set<string>;
  
  /**
   * Create a new root index generator
   * @param outputRoot The root output directory
   * @param processedFiles Set of processed files
   */
  constructor(outputRoot: string, processedFiles: Set<string>) {
    this.outputRoot = outputRoot;
    this.processedFiles = processedFiles;
  }
  
  /**
   * Generate a root index.ts file that imports and re-exports all resolvers
   */
  public async generateRootIndex(): Promise<void> {
    console.log('Generating root index file...');
    
    const indexPath = path.join(this.outputRoot, 'index.ts');
    
    // Get all directories in the output root
    const directories = fs.readdirSync(this.outputRoot)
      .filter(item => {
        const itemPath = path.join(this.outputRoot, item);
        return fs.statSync(itemPath).isDirectory() && !item.startsWith('.');
      });
    
    // Start building index content
    let indexContent = '// Generated root index file\n\n';
    
    // Find all interface and union resolver files throughout the directory structure
    const interfaceFiles = glob.sync(`${this.outputRoot}/**/*/index.ts`, { windowsPathsNoEscape: true });
    
    // Map to keep track of interface and union imports
    const interfaceImports: { [typeName: string]: string } = {};
    const unionImports: { [typeName: string]: string } = {};
    
    // Import from each directory
    for (const dir of directories) {
      const dirPath = path.join(this.outputRoot, dir);
      
      // Skip the old dedicated interface/union directories if they exist but are empty
      if ((dir === 'interfaces' || dir === 'unions') && 
          fs.readdirSync(dirPath).length === 0) {
        continue;
      }
      
      // Check if the directory has an index.ts file
      if (fs.existsSync(path.join(dirPath, 'index.ts'))) {
        const importPath = `./${dir}`;
        const camelCaseName = _.camelCase(dir);
        
        indexContent += `import * as ${camelCaseName} from '${importPath}';\n`;
      }
    }
    
    // Process all interface and union resolver files
    for (const file of interfaceFiles) {
      // Skip the root index.ts
      if (path.relative(this.outputRoot, file) === 'index.ts') {
        continue;
      }
      
      const relativePath = path.relative(this.outputRoot, path.dirname(file));
      const dirParts = relativePath.split(path.sep);
      const lastDir = dirParts[dirParts.length - 1];
      
      // Skip if this isn't a type directory (won't have a hyphen in the name)
      if (!lastDir.includes('-')) {
        continue;
      }
      
      // Convert directory name to type name (kebab-case to PascalCase)
      const typeName = lastDir
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join('');
      
      // Check if this is an interface or union resolver
      try {
        const fileContent = fs.readFileSync(file, 'utf8');
        
        // Simple heuristic to determine if this is an interface or union resolver
        if (fileContent.includes('__resolveType')) {
          // Determine if interface or union based on file content
          const isInterface = fileContent.includes('implements') || fileContent.includes('Interface');
          const isUnion = fileContent.includes('union') || fileContent.includes('Union');
          
          const importPath = './' + relativePath.replace(/\\/g, '/');
          const camelCaseName = _.camelCase(relativePath.replace(/\//g, '_'));
          
          indexContent += `import * as ${camelCaseName} from '${importPath}';\n`;
          
          if (isInterface) {
            interfaceImports[typeName] = camelCaseName;
          } else if (isUnion) {
            unionImports[typeName] = camelCaseName;
          }
        }
      } catch (error) {
        console.error(`Error reading file ${file}: ${error}`);
      }
    }
    
    // Export all resolvers
    indexContent += '\n// Export all resolvers\n';
    indexContent += 'export const resolvers = {\n';
    
    // Add imports from top-level subdirectories
    for (const dir of directories) {
      const dirPath = path.join(this.outputRoot, dir);
      
      // Skip the old dedicated interface/union directories
      if (dir === 'interfaces' || dir === 'unions') {
        continue;
      }
      
      // Check if the directory has an index.ts file
      if (fs.existsSync(path.join(dirPath, 'index.ts'))) {
        const camelCaseName = _.camelCase(dir);
        indexContent += `  ...${camelCaseName}.resolvers,\n`;
      }
    }
    
    // Add interface resolvers
    if (Object.keys(interfaceImports).length > 0) {
      indexContent += '  // Interfaces resolvers\n';
      for (const [typeName, camelCaseName] of Object.entries(interfaceImports)) {
        indexContent += `  ${typeName}: ${camelCaseName}.resolvers,\n`;
      }
    }
    
    // Add union resolvers
    if (Object.keys(unionImports).length > 0) {
      indexContent += '  // Unions resolvers\n';
      for (const [typeName, camelCaseName] of Object.entries(unionImports)) {
        indexContent += `  ${typeName}: ${camelCaseName}.resolvers,\n`;
      }
    }
    
    indexContent += '};\n';
    
    // Add the root index file to processed files
    this.processedFiles.add(path.resolve(indexPath));
    
    // Write the index file
    fs.writeFileSync(indexPath, indexContent);
    if (process.env.DEBUG_PARSER) {
      console.log(`Generated root index file at ${indexPath}`);
    }
  }
} 