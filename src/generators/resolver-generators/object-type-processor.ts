import * as fs from 'fs-extra';
import * as path from 'path';
import type { GraphQLSchema } from 'graphql';
import { GraphQLObjectType } from 'graphql';
import type { FieldResolverGenerator } from '@resolvers/field-resolver-generator';
import type { TypeResolverGenerator } from '@resolvers/type-resolver-generator';
import { convertToFileName } from '@generators/utils/type-utils';
import { generateIndexFile } from '@generators/utils/file-utils';
import { DirectiveParser } from '@utils/directive-parser';
import _ from 'lodash';
/**
 * Processor for object type fields
 */
export class ObjectTypeProcessor {
  private schema: GraphQLSchema;
  private schemaMapper: any;
  private fieldResolverGenerator: FieldResolverGenerator;
  private typeResolverGenerator: TypeResolverGenerator;

  /**
   * Create a new object type processor
   * @param schema The GraphQL schema
   * @param fieldResolverGenerator The field resolver generator
   * @param typeResolverGenerator The type resolver generator
   * @param schemaMapper The schema mapper
   */
  constructor(
    schema: GraphQLSchema,
    fieldResolverGenerator: FieldResolverGenerator,
    typeResolverGenerator: TypeResolverGenerator,
    schemaMapper: any
  ) {
    this.schema = schema;
    this.fieldResolverGenerator = fieldResolverGenerator;
    this.typeResolverGenerator = typeResolverGenerator;
    this.schemaMapper = schemaMapper;
  }

  /**
   * Process regular object types for field resolvers
   */
  public async processObjectTypes(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Processing object types...');
    }

    const typeMap = this.schema.getTypeMap();

    // Find all object types
    for (const typeName in typeMap) {
      // Skip built-in types and already processed query/mutation/subscription types
      if (typeName.startsWith('__') ||
        typeName.endsWith('Query') ||
        typeName.endsWith('Mutation') ||
        typeName.endsWith('Subscription')) {
        continue;
      }

      const type = typeMap[typeName];

      // Process object types
      if (type instanceof GraphQLObjectType) {
        // Get the type location to determine the original schema path
        const typeLocation = this.schemaMapper.getTypeLocation(typeName);
        if (!typeLocation) {
          console.log(`No type location found for ${typeName}, skipping`);
          continue;
        }

        // Get output path preserving the schema's directory structure
        const baseOutputPath = this.schemaMapper.getOutputPathForType(typeName);
        if (!baseOutputPath) {
          console.log(`No output path found for ${typeName}, skipping`);
          continue;
        }

        // Extract source file information
        const schemaFile = typeLocation.sourceFile;
        const schemaDir = path.dirname(schemaFile);
        const schemaBaseName = path.basename(schemaFile, path.extname(schemaFile));

        // Convert schema file name to kebab-case for the output path while preserving directory structure
        // Get the relative path from schema root to the directory containing this type
        const schemaRoot = this.schemaMapper.schemaRoot;
        const relativeSchemaDir = path.relative(schemaRoot, path.join(schemaRoot, schemaDir));

        // Create the file name part in kebab-case
        const kebabCaseFileName = _.kebabCase(schemaBaseName);

        // Create output path that mirrors the schema directory structure with kebab-case file names
        // And appends the type name as a subdirectory
        const typeOutputPath = path.join(
          this.schemaMapper.outputRoot,
          relativeSchemaDir,
          kebabCaseFileName,
          _.kebabCase(typeName)
        );

        if (process.env.DEBUG_PARSER) {
          console.log(`Generating resolvers for type ${typeName} at ${typeOutputPath}`);
        }

        // Ensure the directory exists
        fs.ensureDirSync(typeOutputPath);

        // Check if the object type has a @resolver directive for __resolveType generation
        const absoluteSchemaPath = path.resolve(
          this.schemaMapper.schemaRoot,
          typeLocation.sourceFile
        );

        const directives = await DirectiveParser.extractDirectivesFromSchema(
          absoluteSchemaPath,
          typeName
        );

        // Check if the type has a resolver directive
        const hasResolverDirective = directives.others.resolver && directives.others.resolver.length > 0;

        // Generate __resolveType if the object type has a @resolver directive
        if (hasResolverDirective) {
          console.log(`Object type ${typeName} has @resolver directive, generating __resolveType resolver`);
          await this.typeResolverGenerator.generateObjectResolver(type, typeOutputPath);
        }

        // Process each field in the object type
        const fields = type.getFields();
        const processedFiles: string[] = [];

        for (const fieldName in fields) {
          // Skip special fields
          if (fieldName.startsWith('__')) {
            continue;
          }

          const field = fields[fieldName];

          // Generate the field resolver in the type-specific folder
          const outputFile = path.join(typeOutputPath, `${convertToFileName(fieldName)}.ts`);

          // Generate the resolver implementation
          await this.fieldResolverGenerator.generateFieldResolver(typeName, fieldName, field, outputFile);
          processedFiles.push(convertToFileName(fieldName));
        }

        // Generate an index file for this directory
        if (processedFiles.length > 0) {
          generateIndexFile(typeOutputPath, processedFiles);
        }
      }
    }
  }
} 