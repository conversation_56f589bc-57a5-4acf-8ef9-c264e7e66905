import * as fs from 'fs-extra';
import * as path from 'path';
import _ from 'lodash';
import type {
  GraphQLSchema,
  GraphQLInterfaceType,
  GraphQLUnionType
} from 'graphql';
import {
  GraphQLObjectType
} from 'graphql';
import Handlebars from 'handlebars';
import { RESOLVE_TYPE_TEMPLATE } from '@templates/resolve-type-template';
import { calculateRelativeImportPath } from '@generators/utils/type-utils';
import { hasCustomImplementation, preserveCustomImplementation } from '@generators/utils/file-utils';
import { DirectiveParser, type DirectiveContainer } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';
import type { ResolveTypeAlias} from '@utils/type-converters';
import { generateResolveTypesFile } from '@utils/type-converters';
import { WatchedFileWriter } from '../../utils/watched-file-writer';
import { TemplateSanitizer } from '../../utils/template-sanitizer';
import { getGlobalTemplateCache } from '../../utils/template-compilation-cache';
import { getGlobalBatchFileWriter } from '../../utils/batch-file-writer';

// Register a safe helper for raw content (now redundant since escaping is disabled, but kept for compatibility)
Handlebars.registerHelper('safeRaw', function(content) {
  return new Handlebars.SafeString(content || '');
});

// Get the global template cache for optimized compilation
const templateCache = getGlobalTemplateCache({
  enableLogging: false, // Disable logging in production generators
  enablePerformanceTracking: true,
});

// Configure cached template compilation with escaping disabled for TypeScript code generation
const compile = (template: string) => templateCache.compile(template, { noEscape: true });

/**
 * Generator for type resolvers (interfaces and unions)
 */
export class TypeResolverGenerator {
  private schema: GraphQLSchema;
  private preservedFiles: Set<string>;
  private processedFiles: Set<string>;
  private processedDirectories: Set<string>;
  private outputRoot: string;
  private force: boolean;
  private contextPath: string;
  private contextName: string;
  private schemaMapper: any;
  private resolveTypeAliases: ResolveTypeAlias[] = [];
  private resolveTypeThreshold: number = 2;
  private decoratorDirectiveProvider?: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer;
  private decoratorSmartImportProvider?: (typeName: string, fieldName: string | undefined, targetOutputPath: string, schemaId?: string) => string[];
  private useBatchWriter: boolean;

  /**
   * Create a new type resolver generator
   * @param schema The GraphQL schema
   * @param options Configuration options
   */
  constructor(schema: GraphQLSchema, options: {
    outputRoot: string;
    force: boolean;
    contextPath: string;
    contextName: string;
    preservedFiles: Set<string>;
    processedFiles: Set<string>;
    processedDirectories: Set<string>;
    schemaMapper: any;
  }) {
    this.schema = schema;
    this.outputRoot = options.outputRoot;
    this.force = options.force;
    this.contextPath = options.contextPath;
    this.contextName = options.contextName;
    this.preservedFiles = options.preservedFiles;
    this.processedFiles = options.processedFiles;
    this.processedDirectories = options.processedDirectories;
    this.schemaMapper = options.schemaMapper;

    // PERFORMANCE FIX: Disable batch writing as it's causing 20+ second delays
    // The batch writer has performance issues that need to be resolved
    this.useBatchWriter = false; // process.env.ENABLE_BATCH_IO !== 'false';
  }

  /**
   * Write file using batch writer or direct writing based on configuration
   * @private
   */
  private writeFile(filePath: string, content: string): void {
    if (this.useBatchWriter) {
      const batchWriter = getGlobalBatchFileWriter({
        enableLogging: process.env.DEBUG_PARSER === 'true',
        enablePerformanceMonitoring: true,
      });
      batchWriter.queueWrite(filePath, content);
    } else {
      WatchedFileWriter.writeFileSync(filePath, content);
    }
  }

  /**
   * Set decorator directive providers for integration with decorator system
   * @param directiveProvider Function to get decorator directives for a type/field
   * @param smartImportProvider Function to get smart imports for decorator method calls
   */
  public setDecoratorProviders(
    directiveProvider: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer,
    smartImportProvider: (typeName: string, fieldName: string | undefined, targetOutputPath: string, schemaId?: string) => string[]
  ): void {
    this.decoratorDirectiveProvider = directiveProvider;
    this.decoratorSmartImportProvider = smartImportProvider;
  }

  /**
   * Generate a resolver for an interface
   * @param interfaceType The GraphQL interface type
   * @param outputDir The output directory path
   */
  public async generateInterfaceResolver(
    interfaceType: GraphQLInterfaceType,
    outputDir: string
  ): Promise<void> {
    const typeName = interfaceType.name;
    const outputFile = path.join(outputDir, '__resolve-type.ts');

    // Extract schema info
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

    // Get the directives
    const absoluteSchemaPath = path.resolve(
      this.schemaMapper.schemaRoot,
      schemaFilePath
    );

    const directives = await DirectiveParser.extractDirectivesFromSchema(
      absoluteSchemaPath,
      typeName
    );

    // Get decorator-based directives if provider is available
    let decoratorDirectives: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
    if (this.decoratorDirectiveProvider) {
      decoratorDirectives = this.decoratorDirectiveProvider(typeName);
      if (process.env.DEBUG_PARSER) {
        console.log(`Found ${decoratorDirectives.methodCalls.length} decorator method calls, ${decoratorDirectives.imports.length} decorator imports for ${typeName}`);
      }
    }

    // Check if the type has directives (including both comment-based and decorator-based)
    const hasDirectives = (
      directives.imports.length > 0 ||
      directives.methodCalls.length > 0 ||
      Object.keys(directives.others).length > 0 ||
      decoratorDirectives.imports.length > 0 ||
      decoratorDirectives.methodCalls.length > 0 ||
      Object.keys(decoratorDirectives.others).length > 0
    );

    // Check if the type has a resolver directive
    const hasResolverDirective = directives.others.resolver && directives.others.resolver.length > 0;

    // Check if context info exists in the file and needs updating
    let needsContextUpdate = false;
    if (fs.existsSync(outputFile)) {
      const fileContent = fs.readFileSync(outputFile, 'utf8');
      // Check if the context import in the file doesn't match the current context path
      const contextImportRegex = new RegExp(`import\\s*{\\s*${this.contextName}\\s*}\\s*from\\s*['"]([^'"]+)['"]`);
      const contextMatch = fileContent.match(contextImportRegex);

      if (contextMatch && contextMatch[1] !== this.contextPath) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Context path changed from ${contextMatch[1]} to ${this.contextPath} for ${outputFile}, forcing update`);
        }
        needsContextUpdate = true;
      }
    }

    // Skip if the file exists, force is not set, there are no directives, and context hasn't changed
    if (fs.existsSync(outputFile) && !this.force && !hasDirectives && !needsContextUpdate) {
      const hasCustomCode = await hasCustomImplementation(outputFile);

      if (hasCustomCode) {
        // Preserve file with custom implementation
        this.preservedFiles.add(outputFile);

        // Add auto-generated comment to preserved file
        let fileContent = fs.readFileSync(outputFile, 'utf8');
        if (!fileContent.includes('THIS FILE IS AUTO-GENERATED BUT PRESERVED')) {
          const autoGeneratedComment = '// THIS FILE IS AUTO-GENERATED BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n';
          fileContent = fileContent.replace(/^((?:\/\/.*\n)*)/m, `$1${autoGeneratedComment}`);
          if (process.env.DEBUG_PARSER) {
            console.log(`Added auto-generated comment to preserved file: ${outputFile}`);
          }
        }

        this.writeFile(outputFile, fileContent);
        return;
      }
    }

    // Ensure the directory exists
    fs.ensureDirSync(outputDir);

    // Get the possible types for this interface
    const possibleTypes = this.schema.getPossibleTypes(interfaceType).map(type => type.name);

    // Collect resolve type alias data if this interface has many possible types
    this.collectResolveTypeAlias(typeName, 'interface', possibleTypes);

    // Prepare template data
    const template = compile(RESOLVE_TYPE_TEMPLATE);

    // Determine the import path to the generated types
    const relativePathToGenerated = calculateRelativeImportPath(
      outputFile, 'graphql.ts', this.outputRoot
    );

    // Calculate the relative import path for context if it's not an alias
    let importPathToContext = this.contextPath;
    if (!importPathToContext.startsWith('@')) {
      let absoluteContextPath: string;

      // If contextPath is already absolute (from CLI resolution), use it directly
      if (path.isAbsolute(importPathToContext)) {
        absoluteContextPath = importPathToContext;
      } else {
        // Otherwise resolve relative to current working directory
        absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
      }

      const resolverDir = path.dirname(outputFile);
      let relativeContextPath = path.relative(resolverDir, absoluteContextPath);

      // Convert backslashes to forward slashes for import statements
      relativeContextPath = relativeContextPath.replace(/\\/g, '/');

      // Ensure the path starts with ./ or ../
      if (!relativeContextPath.startsWith('.')) {
        relativeContextPath = './' + relativeContextPath;
      }

      importPathToContext = relativeContextPath;
    }

    // Create example type checks with a sample property for each possible type
    const possibleTypeChecks = possibleTypes.map(type => {
      const typeObj = this.schema.getType(type);
      let typePropertyExample = 'someField';

      if (typeObj instanceof GraphQLObjectType) {
        // Get the first field that's not in the interface as an example
        const typeFields = typeObj.getFields();
        const interfaceFields = interfaceType.getFields();

        for (const fieldName in typeFields) {
          if (!(fieldName in interfaceFields)) {
            typePropertyExample = fieldName;
            break;
          }
        }
      }

      return {
        type,
        typePropertyExample
      };
    });

    // Generate the schema representation
    const fieldDefinitions = Object.values(interfaceType.getFields())
      .map(field => `  ${field.name}: ${field.type.toString()}`)
      .join('\n');

    const schemaRepresentation = `interface ${typeName} {\n${fieldDefinitions}\n}`;

    // Extract custom imports from directives with transformed paths
    const customImports = DirectiveProcessor.extractTransformedImportStatements(
      directives,
      absoluteSchemaPath,
      outputFile
    ).join('\n');

    // Extract resolver call from directive
    const resolverCall = DirectiveProcessor.extractResolverCall(directives);

    // Extract useTypeName flag from directive (defaults to false)
    const useTypeName = DirectiveProcessor.extractUseTypeName(directives);

    // Determine if we need to import the interface type itself
    // This is needed when there are no implementing types (standalone interfaces)
    const needsInterfaceTypeImport = possibleTypes.length === 0;

    // Check if we should use a type alias for this interface
    const shouldUseTypeAlias = possibleTypes.length >= this.resolveTypeThreshold;
    const typeAliasName = shouldUseTypeAlias ? `${typeName}ResolveType` : '';

    // Calculate relative path to resolve-types.ts if needed
    const relativePathToResolveTypes = shouldUseTypeAlias
      ? calculateRelativeImportPath(outputFile, 'resolve-types.ts', this.outputRoot)
      : '';

    // Generate qualified name for testing purposes including full path
    // Extract the relative path from output root to get the directory structure
    const relativePath = path.relative(this.outputRoot, path.dirname(outputFile));
    const pathParts = relativePath.split(path.sep).filter(part => part && part !== '.');

    // Remove duplicate type names from path parts to avoid redundancy
    const typeNameLower = typeName.toLowerCase();
    const cleanedPathParts = pathParts.filter(part =>
      _.kebabCase(part) !== _.kebabCase(typeName) &&
      part.toLowerCase() !== typeNameLower
    );

    // Convert path parts to camelCase and join them, ensuring proper camelCase
    const pathPrefix = cleanedPathParts.map((part, index) => {
      const camelCased = _.camelCase(part);
      // First part should be lowercase, subsequent parts should be PascalCase
      return index === 0 ? camelCased : _.upperFirst(camelCased);
    }).join('');

    // Create the qualified name with full path: pathPrefixTypeNameResolveType (starting with lowercase)
    const typeNamePart = _.upperFirst(_.camelCase(typeName));
    const fullQualifiedName = `${pathPrefix}${typeNamePart}ResolveType`;
    // Ensure the qualified name always starts with lowercase
    const qualifiedName = fullQualifiedName.charAt(0).toLowerCase() + fullQualifiedName.slice(1);

    // Generate the implementation
    const resolverData = {
      typeName,
      typeKind: 'interface',
      resolverName: '__resolveType',
      schemaFilePath,
      schemaRepresentation,
      possibleTypes: possibleTypes.join(', '),
      possibleTypeChecks,
      needsResolversImport: false,
      needsInterfaceTypeImport,
      importPathToGenerated: relativePathToGenerated,
      importPathToContext: importPathToContext,
      contextName: this.contextName,
      hasDirectives,
      customImports,
      resolverCall,
      useTypeName,
      shouldUseTypeAlias,
      typeAliasName,
      importPathToResolveTypes: relativePathToResolveTypes,
      // Add qualified name for testing
      qualifiedName
    };

    const content = template(resolverData);

    // If the file exists and no directives, try to preserve custom implementation
    if (fs.existsSync(outputFile) && !hasDirectives) {
      if (process.env.DEBUG_PARSER) {
        console.log('Preserving custom implementation in', outputFile);
      }
      if (process.env.DEBUG_PARSER) {
        console.log('Content:', content);
      }
      const mergedContent = await preserveCustomImplementation(outputFile, content);
      this.writeFile(outputFile, mergedContent);
    } else {
      // Create a new file or overwrite if directives exist
      this.writeFile(outputFile, content);
    }

    // Generate an index file - with default export style
    const indexFile = path.join(outputDir, 'index.ts');
    const indexContent = `// Generated type resolver index\n\n// __resolveType implementation for the ${typeName} interface\nexport { default } from './__resolve-type';\n\n// Export the resolver map\nexport const resolvers = {\n  __resolveType: require('./__resolve-type').default\n};\n`;
    this.writeFile(indexFile, indexContent);

    // Track the files we've processed
    const absoluteOutputFile = path.resolve(outputFile);
    const absoluteIndexFile = path.resolve(indexFile);
    const absoluteOutputDir = path.resolve(outputDir);

    this.processedFiles.add(absoluteOutputFile);
    this.processedFiles.add(absoluteIndexFile);
    this.processedDirectories.add(absoluteOutputDir);

    const actionVerb = hasDirectives ? "Overwrote" : "Generated";
    if (process.env.DEBUG_PARSER) {
      console.log(`${actionVerb} interface resolver for ${typeName} at ${outputFile}${hasDirectives ? " (has directives)" : ""}`);
    }
  }

  /**
   * Generate a resolver for an object type
   * @param objectType The GraphQL object type
   * @param outputDir The output directory path
   */
  public async generateObjectResolver(
    objectType: GraphQLObjectType,
    outputDir: string
  ): Promise<void> {
    const typeName = objectType.name;
    const outputFile = path.join(outputDir, '__resolve-type.ts');

    // Extract schema info
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

    // Get the directives
    const absoluteSchemaPath = path.resolve(
      this.schemaMapper.schemaRoot,
      schemaFilePath
    );

    const directives = await DirectiveParser.extractDirectivesFromSchema(
      absoluteSchemaPath,
      typeName
    );

    // Check if the type has a resolver directive
    const hasDirectives = directives.others.resolver && directives.others.resolver.length > 0;

    // Only generate if there's a resolver directive
    if (!hasDirectives) {
      if (process.env.DEBUG_PARSER) {
        console.log(`Object type ${typeName} has no @resolver directive, skipping __resolveType generation`);
      }
      return;
    }

    // Determine the import path to the context
    const importPathToContext = calculateRelativeImportPath(
      outputFile, this.contextPath, this.outputRoot
    );

    // Ensure the directory exists
    fs.ensureDirSync(outputDir);

    // For object types, the possible types is just the type itself
    const possibleTypes = [typeName];

    // Collect resolve type alias data if needed (though for single types it won't meet threshold)
    this.collectResolveTypeAlias(typeName, 'object', possibleTypes);

    // Prepare template data
    const template = compile(RESOLVE_TYPE_TEMPLATE);

    // Determine the import path to the generated types
    const relativePathToGenerated = calculateRelativeImportPath(
      outputFile, 'graphql.ts', this.outputRoot
    );

    // Create example type checks - for object types, we can check for specific fields
    const possibleTypeChecks = [{
      type: typeName,
      typePropertyExample: this.getExamplePropertyForObjectType(objectType)
    }];

    // Generate the schema representation
    const fieldDefinitions = Object.values(objectType.getFields())
      .map(field => `  ${field.name}: ${field.type.toString()}`)
      .join('\n');

    const schemaRepresentation = `type ${typeName} {\n${fieldDefinitions}\n}`;

    // Extract custom imports from directives with transformed paths
    const customImports = DirectiveProcessor.extractTransformedImportStatements(
      directives,
      absoluteSchemaPath,
      outputFile
    ).join('\n');

    // Extract resolver call from directive
    const resolverCall = DirectiveProcessor.extractResolverCall(directives);

    // Extract useTypeName flag from directive (defaults to false)
    const useTypeName = DirectiveProcessor.extractUseTypeName(directives);

    // For object types, we don't need to import the type itself as it's not a union/interface
    const needsInterfaceTypeImport = false;

    // Check if we should use a type alias for this object type (unlikely for single type)
    const shouldUseTypeAlias = possibleTypes.length >= this.resolveTypeThreshold;
    const typeAliasName = shouldUseTypeAlias ? `${typeName}ResolveType` : '';

    // Calculate relative path to resolve-types.ts if needed
    const relativePathToResolveTypes = shouldUseTypeAlias
      ? calculateRelativeImportPath(outputFile, 'resolve-types.ts', this.outputRoot)
      : '';

    // Generate qualified name for testing purposes including full path
    // Extract the relative path from output root to get the directory structure
    const relativePath = path.relative(this.outputRoot, path.dirname(outputFile));
    const pathParts = relativePath.split(path.sep).filter(part => part && part !== '.');

    // Remove duplicate type names from path parts to avoid redundancy
    const typeNameLower = typeName.toLowerCase();
    const cleanedPathParts = pathParts.filter(part =>
      _.kebabCase(part) !== _.kebabCase(typeName) &&
      part.toLowerCase() !== typeNameLower
    );

    // Convert path parts to camelCase and join them, ensuring proper camelCase
    const pathPrefix = cleanedPathParts.map((part, index) => {
      const camelCased = _.camelCase(part);
      // First part should be lowercase, subsequent parts should be PascalCase
      return index === 0 ? camelCased : _.upperFirst(camelCased);
    }).join('');

    // Create the qualified name with full path: pathPrefixTypeNameResolveType (starting with lowercase)
    const typeNamePart = _.upperFirst(_.camelCase(typeName));
    const fullQualifiedName = `${pathPrefix}${typeNamePart}ResolveType`;
    // Ensure the qualified name always starts with lowercase
    const qualifiedName = fullQualifiedName.charAt(0).toLowerCase() + fullQualifiedName.slice(1);

    // Generate the implementation
    const resolverData = {
      typeName,
      typeKind: 'object',
      resolverName: '__resolveType',
      schemaFilePath,
      schemaRepresentation,
      possibleTypes: possibleTypes.join(', '),
      possibleTypeChecks,
      needsResolversImport: false,
      needsInterfaceTypeImport,
      importPathToGenerated: relativePathToGenerated,
      importPathToContext: importPathToContext,
      contextName: this.contextName,
      hasDirectives,
      customImports,
      resolverCall,
      useTypeName,
      shouldUseTypeAlias,
      typeAliasName,
      importPathToResolveTypes: relativePathToResolveTypes,
      // Add qualified name for testing
      qualifiedName
    };

    // Generate the resolver file
    const resolverContent = template(resolverData);
    this.writeFile(outputFile, resolverContent);

    // Generate an index file - with default export style
    const indexFile = path.join(outputDir, 'index.ts');
    const indexContent = `// Generated type resolver index\n\n// __resolveType implementation for the ${typeName} object type\nexport { default } from './__resolve-type';\n\n// Export the resolver map\nexport const resolvers = {\n  __resolveType: require('./__resolve-type').default\n};\n`;
    this.writeFile(indexFile, indexContent);

    // Track the files we've processed
    const absoluteOutputFile = path.resolve(outputFile);
    const absoluteIndexFile = path.resolve(indexFile);
    const absoluteOutputDir = path.resolve(outputDir);

    this.processedFiles.add(absoluteOutputFile);
    this.processedFiles.add(absoluteIndexFile);
    this.processedDirectories.add(absoluteOutputDir);

    const actionVerb = hasDirectives ? "Generated" : "Generated";
    if (process.env.DEBUG_PARSER) {
      console.log(`${actionVerb} object resolver for ${typeName} at ${outputFile} (has @resolver directive)`);
    }
  }

  /**
   * Generate a resolver for a union
   * @param unionType The GraphQL union type
   * @param outputDir The output directory path
   */
  public async generateUnionResolver(
    unionType: GraphQLUnionType,
    outputDir: string
  ): Promise<void> {
    const typeName = unionType.name;
    const outputFile = path.join(outputDir, '__resolve-type.ts');

    // Extract schema info
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

    // Get the directives
    const absoluteSchemaPath = path.resolve(
      this.schemaMapper.schemaRoot,
      schemaFilePath
    );

    const directives = await DirectiveParser.extractDirectivesFromSchema(
      absoluteSchemaPath,
      typeName
    );

    // Get decorator-based directives if provider is available
    let decoratorDirectives: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
    if (this.decoratorDirectiveProvider) {
      decoratorDirectives = this.decoratorDirectiveProvider(typeName);
      if (process.env.DEBUG_PARSER) {
        console.log(`Found ${decoratorDirectives.methodCalls.length} decorator method calls, ${decoratorDirectives.imports.length} decorator imports for ${typeName}`);
      }
    }

    // Check if the type has directives (including both comment-based and decorator-based)
    const hasDirectives = (
      directives.imports.length > 0 ||
      directives.methodCalls.length > 0 ||
      Object.keys(directives.others).length > 0 ||
      decoratorDirectives.imports.length > 0 ||
      decoratorDirectives.methodCalls.length > 0 ||
      Object.keys(decoratorDirectives.others).length > 0
    );

    // Check if the type has a resolver directive
    const hasResolverDirective = directives.others.resolver && directives.others.resolver.length > 0;

    // Check if context info exists in the file and needs updating
    let needsContextUpdate = false;
    if (fs.existsSync(outputFile)) {
      const fileContent = fs.readFileSync(outputFile, 'utf8');
      // Check if the context import in the file doesn't match the current context path
      const contextImportRegex = new RegExp(`import\\s*{\\s*${this.contextName}\\s*}\\s*from\\s*['"]([^'"]+)['"]`);
      const contextMatch = fileContent.match(contextImportRegex);

      if (contextMatch && contextMatch[1] !== this.contextPath) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Context path changed from ${contextMatch[1]} to ${this.contextPath} for ${outputFile}, forcing update`);
        }
        needsContextUpdate = true;
      }
    }

    // Skip if the file exists, force is not set, there are no directives, and context hasn't changed
    if (fs.existsSync(outputFile) && !this.force && !hasDirectives && !needsContextUpdate) {
      const hasCustomCode = await hasCustomImplementation(outputFile);

      if (hasCustomCode) {
        // Preserve file with custom implementation
        this.preservedFiles.add(outputFile);

        // Add auto-generated comment to preserved file
        let fileContent = fs.readFileSync(outputFile, 'utf8');
        if (!fileContent.includes('THIS FILE IS AUTO-GENERATED BUT PRESERVED')) {
          const autoGeneratedComment = '// THIS FILE IS AUTO-GENERATED BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n';
          fileContent = fileContent.replace(/^((?:\/\/.*\n)*)/m, `$1${autoGeneratedComment}`);
          if (process.env.DEBUG_PARSER) {
            console.log(`Added auto-generated comment to preserved file: ${outputFile}`);
          }
        }

        this.writeFile(outputFile, fileContent);
        return;
      }
    }

    // Ensure the directory exists
    fs.ensureDirSync(outputDir);

    // Get the possible types for this union
    const possibleTypes = unionType.getTypes().map(type => type.name);

    // Collect resolve type alias data if this union has many possible types
    this.collectResolveTypeAlias(typeName, 'union', possibleTypes);

    // Prepare template data
    const template = compile(RESOLVE_TYPE_TEMPLATE);

    // Determine the import path to the generated types
    const relativePathToGenerated = calculateRelativeImportPath(
      outputFile, 'graphql.ts', this.outputRoot
    );

    // Calculate the relative import path for context if it's not an alias
    let importPathToContext = this.contextPath;
    if (!importPathToContext.startsWith('@')) {
      let absoluteContextPath: string;

      // If contextPath is already absolute (from CLI resolution), use it directly
      if (path.isAbsolute(importPathToContext)) {
        absoluteContextPath = importPathToContext;
      } else {
        // Otherwise resolve relative to current working directory
        absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
      }

      const resolverDir = path.dirname(outputFile);
      let relativeContextPath = path.relative(resolverDir, absoluteContextPath);

      // Convert backslashes to forward slashes for import statements
      relativeContextPath = relativeContextPath.replace(/\\/g, '/');

      // Ensure the path starts with ./ or ../
      if (!relativeContextPath.startsWith('.')) {
        relativeContextPath = './' + relativeContextPath;
      }

      importPathToContext = relativeContextPath;
    }

    // Create example type checks with a sample property for each possible type
    const possibleTypeChecks = possibleTypes.map(type => {
      const typeObj = this.schema.getType(type) as GraphQLObjectType;
      let typePropertyExample = 'someField';

      if (typeObj instanceof GraphQLObjectType) {
        // Get the first field as an example for union member types
        const typeFields = typeObj.getFields();
        const fieldNames = Object.keys(typeFields);

        if (fieldNames.length > 0) {
          typePropertyExample = fieldNames[0];
        }
      }

      return {
        type,
        typePropertyExample
      };
    });

    // Generate the schema representation
    const schemaRepresentation = `union ${typeName} = ${possibleTypes.join(' | ')}`;

    // Extract custom imports from directives with transformed paths
    const customImports = DirectiveProcessor.extractTransformedImportStatements(
      directives,
      absoluteSchemaPath,
      outputFile
    ).join('\n');

    // Extract resolver call from directive
    const resolverCall = DirectiveProcessor.extractResolverCall(directives);

    // Extract useTypeName flag from directive (defaults to false)
    const useTypeName = DirectiveProcessor.extractUseTypeName(directives);

    // Check if we should use a type alias for this union
    const shouldUseTypeAlias = possibleTypes.length >= this.resolveTypeThreshold;
    const typeAliasName = shouldUseTypeAlias ? `${typeName}ResolveType` : '';

    // Calculate relative path to resolve-types.ts if needed
    const relativePathToResolveTypes = shouldUseTypeAlias
      ? calculateRelativeImportPath(outputFile, 'resolve-types.ts', this.outputRoot)
      : '';

    // Generate qualified name for testing purposes including full path
    // Extract the relative path from output root to get the directory structure
    const relativePath = path.relative(this.outputRoot, path.dirname(outputFile));
    const pathParts = relativePath.split(path.sep).filter(part => part && part !== '.');

    // Remove duplicate type names from path parts to avoid redundancy
    const typeNameLower = typeName.toLowerCase();
    const cleanedPathParts = pathParts.filter(part =>
      _.kebabCase(part) !== _.kebabCase(typeName) &&
      part.toLowerCase() !== typeNameLower
    );

    // Convert path parts to camelCase and join them, ensuring proper camelCase
    const pathPrefix = cleanedPathParts.map((part, index) => {
      const camelCased = _.camelCase(part);
      // First part should be lowercase, subsequent parts should be PascalCase
      return index === 0 ? camelCased : _.upperFirst(camelCased);
    }).join('');

    // Create the qualified name with full path: pathPrefixTypeNameResolveType (starting with lowercase)
    const typeNamePart = _.upperFirst(_.camelCase(typeName));
    const fullQualifiedName = `${pathPrefix}${typeNamePart}ResolveType`;
    // Ensure the qualified name always starts with lowercase
    const qualifiedName = fullQualifiedName.charAt(0).toLowerCase() + fullQualifiedName.slice(1);

    // Generate the implementation
    const resolverData = {
      typeName,
      typeKind: 'union',
      resolverName: '__resolveType',
      schemaFilePath,
      schemaRepresentation,
      possibleTypes: possibleTypes.join(', '),
      possibleTypeChecks,
      needsResolversImport: false,
      importPathToGenerated: relativePathToGenerated,
      importPathToContext: importPathToContext,
      contextName: this.contextName,
      hasDirectives,
      customImports,
      resolverCall,
      useTypeName,
      shouldUseTypeAlias,
      typeAliasName,
      importPathToResolveTypes: relativePathToResolveTypes,
      // Add qualified name for testing
      qualifiedName
    };

    const content = template(resolverData);

    // If the file exists and no directives, try to preserve custom implementation
    if (fs.existsSync(outputFile) && !hasDirectives) {
      const mergedContent = await preserveCustomImplementation(outputFile, content);
      this.writeFile(outputFile, mergedContent);
    } else {
      // Create a new file or overwrite if directives exist
      this.writeFile(outputFile, content);
    }

    // Generate an index file - with default export style
    const indexFile = path.join(outputDir, 'index.ts');
    const indexContent = `// Generated type resolver index\n\n// __resolveType implementation for the ${typeName} union\nexport { default } from './__resolve-type';\n\n// Export the resolver map\nexport const resolvers = {\n  __resolveType: require('./__resolve-type').default\n};\n`;
    this.writeFile(indexFile, indexContent);

    // Track the files we've processed
    const absoluteOutputFile = path.resolve(outputFile);
    const absoluteIndexFile = path.resolve(indexFile);
    const absoluteOutputDir = path.resolve(outputDir);

    this.processedFiles.add(absoluteOutputFile);
    this.processedFiles.add(absoluteIndexFile);
    this.processedDirectories.add(absoluteOutputDir);

    const actionVerb = hasDirectives ? "Overwrote" : "Generated";
    if (process.env.DEBUG_PARSER) {
      console.log(`${actionVerb} union resolver for ${typeName} at ${outputFile}${hasDirectives ? " (has directives)" : ""}`);
    }
  }

  /**
   * Collect resolve type alias data for interfaces, unions, and objects with many possible types
   * @param typeName The name of the interface, union, or object type
   * @param typeKind Whether this is an 'interface', 'union', or 'object'
   * @param possibleTypes Array of possible type names
   */
  private collectResolveTypeAlias(typeName: string, typeKind: 'interface' | 'union' | 'object', possibleTypes: string[]): void {
    if (possibleTypes.length >= this.resolveTypeThreshold) {
      const aliasName = `${typeName}ResolveType`;
      this.resolveTypeAliases.push({
        typeName,
        typeKind,
        possibleTypes,
        aliasName
      });
    }
  }

  /**
   * Get an example property name for an object type to use in type checking examples
   * @param objectType The GraphQL object type
   * @returns A property name that can be used for type checking
   */
  private getExamplePropertyForObjectType(objectType: GraphQLObjectType): string {
    const fields = objectType.getFields();
    const fieldNames = Object.keys(fields);

    // Return the first field name, or 'id' if it exists, or a default
    if (fieldNames.includes('id')) {
      return 'id';
    }

    return fieldNames.length > 0 ? fieldNames[0] : 'someField';
  }

  /**
   * Get collected resolve type aliases
   * @returns Array of collected resolve type aliases
   */
  public getResolveTypeAliases(): ResolveTypeAlias[] {
    return this.resolveTypeAliases;
  }

  /**
   * Generate the resolve-types.ts file with collected type aliases
   * @param debug Enable debug logging
   */
  public async generateResolveTypesFile(debug: boolean = false): Promise<void> {
    await generateResolveTypesFile(this.outputRoot, this.resolveTypeAliases, debug);
  }
}