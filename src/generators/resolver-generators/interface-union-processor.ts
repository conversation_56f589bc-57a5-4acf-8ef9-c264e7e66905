import type { GraphQLSchema } from 'graphql';
import { GraphQLInterfaceType, GraphQLUnionType } from 'graphql';
import type { TypeResolverGenerator } from '@resolvers/type-resolver-generator';
import type { FieldResolverGenerator } from '@resolvers/field-resolver-generator';
import { DirectiveParser } from '@utils/directive-parser';
import { convertToFileName } from '@generators/utils/type-utils';
import { generateIndexFile } from '@generators/utils/file-utils';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Processor for interfaces and unions
 */
export class InterfaceUnionProcessor {
  private schema: GraphQLSchema;
  private schemaMapper: any;
  private typeResolverGenerator: TypeResolverGenerator;
  private fieldResolverGenerator: FieldResolverGenerator;

  /**
   * Create a new interface and union processor
   * @param schema The GraphQL schema
   * @param typeResolverGenerator The type resolver generator
   * @param fieldResolverGenerator The field resolver generator
   * @param schemaMapper The schema mapper
   */
  constructor(
    schema: GraphQLSchema,
    typeResolverGenerator: TypeResolverGenerator,
    fieldResolverGenerator: FieldResolverGenerator,
    schemaMapper: any
  ) {
    this.schema = schema;
    this.typeResolverGenerator = typeResolverGenerator;
    this.fieldResolverGenerator = fieldResolverGenerator;
    this.schemaMapper = schemaMapper;
  }

  /**
   * Process interfaces and unions
   */
  public async processInterfacesAndUnions(): Promise<void> {
    if (process.env.DEBUG_PARSER) {
      console.log('Processing interfaces and unions...');
    }

    const typeMap = this.schema.getTypeMap();

    // Find all interface and union types
    for (const typeName in typeMap) {
      // Skip built-in types and query/mutation types
      if (typeName.startsWith('__') ||
        typeName === 'Query' ||
        typeName === 'Mutation' ||
        typeName === 'Subscription') {
        continue;
      }

      const type = typeMap[typeName];
      let outputPath: string | null = null;

      // Process interfaces
      if (type instanceof GraphQLInterfaceType) {
        // Get possible types that implement this interface
        const possibleTypes = this.schema.getPossibleTypes(type);

        outputPath = this.schemaMapper.getOutputPathForType(typeName);
        if (outputPath) {
          // Extract schema info
          const typeLocation = this.schemaMapper.getTypeLocation(typeName);
          const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

          // Get the directives
          const absoluteSchemaPath = path.resolve(
            this.schemaMapper.schemaRoot,
            schemaFilePath
          );

          const directives = await DirectiveParser.extractDirectivesFromSchema(
            absoluteSchemaPath,
            typeName
          );

          // Check if the type has a resolver directive
          const hasResolverDirective = directives.others.resolver && directives.others.resolver.length > 0;

          // Always generate __resolveType for all interfaces
          if (possibleTypes.length > 0) {
            if (process.env.DEBUG_PARSER) {
              console.log(`Interface ${typeName} has ${possibleTypes.length} implementing types: ${possibleTypes.map(t => t.name).join(', ')}`);
              console.log(`Generating __resolveType resolver for interface ${typeName} with ${possibleTypes.length} implementing types`);
            }
          } else {
            if (process.env.DEBUG_PARSER) {
              console.log(`Interface ${typeName} has no implementing types, but generating __resolveType resolver anyway`);
            }
          }
          await this.typeResolverGenerator.generateInterfaceResolver(type, outputPath);

          // Always generate field resolvers for interface fields (regardless of implementing types)
          if (process.env.DEBUG_PARSER) {
            console.log(`Generating field resolvers for interface ${typeName}`);
          }
          await this.generateInterfaceFieldResolvers(type, outputPath);
        } else {
          if (process.env.DEBUG_PARSER) {
            console.log(`Could not determine output path for interface ${typeName}`);
          }
        }
      }
      // Process unions
      else if (type instanceof GraphQLUnionType) {
        // Get possible types in this union
        const possibleTypes = type.getTypes();

        // Skip unions with no member types
        if (possibleTypes.length === 0) {
          if (process.env.DEBUG_PARSER) {
            console.log(`No member types found for union ${typeName}, skipping __resolveType generation`);
          }
          continue;
        }

        console.log(`Union ${typeName} has ${possibleTypes.length} member types: ${possibleTypes.map(t => t.name).join(', ')}`);

        outputPath = this.schemaMapper.getOutputPathForType(typeName);
        if (outputPath) {
          // Extract schema info
          const typeLocation = this.schemaMapper.getTypeLocation(typeName);
          const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

          // Get the directives
          const absoluteSchemaPath = path.resolve(
            this.schemaMapper.schemaRoot,
            schemaFilePath
          );

          const directives = await DirectiveParser.extractDirectivesFromSchema(
            absoluteSchemaPath,
            typeName
          );

          // Check if the type has a resolver directive
          const hasResolverDirective = directives.others.resolver && directives.others.resolver.length > 0;

          // Generate union resolver (always generate for unions with possible types)
          if (process.env.DEBUG_PARSER) {
            console.log(`Generating resolver for union ${typeName} with ${possibleTypes.length} possible types`);
          }
          await this.typeResolverGenerator.generateUnionResolver(type, outputPath);
        } else {
          if (process.env.DEBUG_PARSER) {
            console.log(`Could not determine output path for union ${typeName}`);
          }
        }
      }
    }

    // Generate resolve-types.ts file with collected type aliases
    await this.typeResolverGenerator.generateResolveTypesFile(false);
  }

  /**
   * Generate field resolvers for interface fields
   * @param interfaceType The GraphQL interface type
   * @param outputPath The base output path for the interface
   */
  private async generateInterfaceFieldResolvers(
    interfaceType: GraphQLInterfaceType,
    outputPath: string
  ): Promise<void> {
    const typeName = interfaceType.name;
    const fields = interfaceType.getFields();
    const processedFiles: string[] = [];

    // Ensure the directory exists before generating field resolvers
    fs.ensureDirSync(outputPath);

    for (const fieldName in fields) {
      // Skip special fields
      if (fieldName.startsWith('__')) {
        continue;
      }

      const field = fields[fieldName];

      // Generate the field resolver in the interface-specific folder
      const outputFile = path.join(outputPath, `${convertToFileName(fieldName)}.ts`);

      // Ensure the directory exists for this specific file
      fs.ensureDirSync(path.dirname(outputFile));

      // Generate the resolver implementation
      await this.fieldResolverGenerator.generateFieldResolver(typeName, fieldName, field, outputFile);
      processedFiles.push(convertToFileName(fieldName));
    }

    // Generate an index file for this directory if we have field resolvers
    if (processedFiles.length > 0) {
      generateIndexFile(outputPath, processedFiles);
    }
  }
}