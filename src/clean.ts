import * as fs from 'fs-extra';
import * as path from 'path';

// Define paths to clean up
const GENERATED_PATH = path.join(process.cwd(), 'src', 'generated');

// Optional: list of files to preserve in each directory (if any)
const PRESERVE_FILES: Record<string, string[]> = {
  // Add files to preserve here if needed
  // Example: [GENERATED_PATH]: ['.gitkeep'],
};

/**
 * Cleans a directory by removing all files except those in the preserve list
 * If removeDir is true, removes the directory itself, otherwise just empties it
 */
const cleanDirectory = (dirPath: string, removeDir: boolean = false): void => {
  if (!fs.existsSync(dirPath)) {
    console.log(`Directory does not exist: ${dirPath}`);
    return;
  }

  const filesToPreserve = PRESERVE_FILES[dirPath] ?? [];

  console.log(`Cleaning directory: ${dirPath}`);

  try {
    if (removeDir) {
      // Remove entire directory
      fs.removeSync(dirPath);
      console.log(`Removed directory: ${dirPath}`);
    } else {
      // Clean individual files in directory
      const files = fs.readdirSync(dirPath);

      for (const file of files) {
        if (filesToPreserve.includes(file)) {
          console.log(`Preserving file: ${file}`);
          continue;
        }

        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          // Recursively clean subdirectories
          cleanDirectory(filePath, true);
        } else {
          // Remove file
          fs.removeSync(filePath);
          console.log(`Removed file: ${filePath}`);
        }
      }
    }
  } catch (error) {
    console.error(`Error cleaning directory ${dirPath}:`, error);
  }
}

/**
 * Clean generated files
 */
export const cleanGeneratedFiles = (options: { output?: string }) => {
  console.log('Starting cleanup...');

  // Determine output directory
  const outputDir = options.output ?? GENERATED_PATH;

  // Clean generated directory
  cleanDirectory(outputDir);

  // Ensure the directory exists (in case it was completely removed)
  fs.ensureDirSync(outputDir);

  console.log('Cleanup completed successfully!');
}

// Run the script
cleanGeneratedFiles({}); 