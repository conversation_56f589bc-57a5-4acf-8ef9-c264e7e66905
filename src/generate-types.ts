import * as fs from 'fs-extra';
import * as path from 'path';

// Default generated path
const DEFAULT_GENERATED_PATH = path.join(process.cwd(), 'src', 'generated');

/**
 * Checks if @gql-generator/context is mapped in tsconfig.json
 * If not, generates a context.ts file in the src directory and adds the mapping
 * Also ensures output directory is included in tsconfig.json include array
 * 
 * @param outputDir - The output directory where generated files will be located
 * @returns boolean - Whether a new context file was generated
 */
const ensureContextMapping = (outputDir: string): boolean => {
  const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');

  // Check if tsconfig.json exists
  if (!fs.existsSync(tsconfigPath)) {
    console.log('No tsconfig.json found. Skipping context mapping check.');
    return false;
  }

  try {
    // Read and parse tsconfig.json
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));

    // Check if paths mapping exists
    const hasPaths = tsconfig.compilerOptions && tsconfig.compilerOptions.paths;
    const hasContextMapping = hasPaths && tsconfig.compilerOptions.paths['@gql-generator/context'];

    // Replace any {{output}} placeholder in the path if it exists
    let actualOutputDir = outputDir;
    if (outputDir.includes('{{output}}')) {
      // For path checks, replace with a default but keep the template for the mapping
      actualOutputDir = outputDir.replace('{{output}}', 'src/generated');
    }

    if (hasContextMapping) {
      console.log('Found existing @gql-generator/context mapping in tsconfig.json');
    } else {
      // No mapping found, generate context.ts in src directory
      console.log('No @gql-generator/context mapping found in tsconfig.json');

      // Create src directory if it doesn't exist
      const srcDir = path.join(process.cwd(), 'src');
      fs.ensureDirSync(srcDir);

      // Generate context.ts in src directory
      const contextPath = path.join(srcDir, 'context.ts');
      const contextContent = `/**
 * Context interface for GraphQL resolvers and mutators
 * This file was auto-generated as a default context implementation.
 * You should modify it to fit your application's needs.
 */

/**
 * Context interface for GraphQL resolvers and mutators
 * Contains authentication, data sources, and other context information
 */
export interface Context {
  userId?: string;
  isAuthenticated: boolean;
  // Add any other context properties needed for your application
  // Example: dataSources, services, etc.
  // For example: user?: User;
  // Or data source clients: db: Database;
}

/**
 * Create a Context instance with default values
 * @param options - Optional partial Context properties
 * @returns A complete Context object
 */
export const createContext = (options?: Partial<Context>): Context => {
  return {
    isAuthenticated: false,
    ...options
  };
};
`;

      fs.writeFileSync(contextPath, contextContent);
      console.log(`Generated default context.ts at ${contextPath}`);

      // Add or update paths in tsconfig.json
      tsconfig.compilerOptions ??= {};

      tsconfig.compilerOptions.paths ??= {};

      // Set mapping to src/context.ts
      tsconfig.compilerOptions.paths['@gql-generator/context'] = ['./src/context.ts'];
      console.log(`Updated tsconfig.json with mapping for @gql-generator/context -> ./src/context.ts`);
    }

    // Ensure output directory is in tsconfig.json include array
    // Extract relative output path for include
    let includePattern: string;

    if (outputDir.includes('{{output}}')) {
      // Using the template pattern, include the expected structure
      includePattern = outputDir.replace('{{output}}', '*');
    } else {
      // Using a direct path, make it relative to tsconfig.json location
      const relativeOutputDir = path.relative(process.cwd(), actualOutputDir).replace(/\\/g, '/');
      includePattern = `${relativeOutputDir}/**/*`;

      // If relativeOutputDir is just "src", no need to add it separately
      if (relativeOutputDir === 'src') {
        includePattern = ''; // Will be skipped below
      }
    }

    // Add to include array if not empty and not already included
    if (includePattern && (!tsconfig.include || !tsconfig.include.includes(includePattern))) {
      tsconfig.include ??= [];

      if (!tsconfig.include.some((pattern: string) =>
        pattern.includes(includePattern) || includePattern.includes(pattern))) {
        tsconfig.include.push(includePattern);
        console.log(`Added ${includePattern} to tsconfig.json include array`);
      }
    }

    // Write the updated tsconfig.json
    fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));

    return !hasContextMapping; // Return true if we created a new mapping
  } catch (error) {
    console.error('Error ensuring context mapping:', error);
    return false;
  }
};

/**
 * Sets up the context file and tsconfig mapping
 * 
 * @param outputDir - Optional output directory path (defaults to src/generated)
 * @param contextPath - Optional path to a custom Context interface
 * @param contextName - Optional name for the Context import (defaults to 'Context')
 */
const setupContext = (outputDir?: string, contextPath?: string, contextName: string = 'Context') => {
  // Use the provided output directory or the default
  const generatedPath = outputDir ?? DEFAULT_GENERATED_PATH;

  // For actual file operations, we need to replace {{output}} with a concrete path
  let actualOutputPath = generatedPath;
  if (generatedPath.includes('{{output}}')) {
    actualOutputPath = generatedPath.replace('{{output}}', 'src/generated');
    console.log(`Using actual output path: ${actualOutputPath} (from template: ${generatedPath})`);
  }

  console.log(`Setting up context for resolvers and mutators...`);

  // Ensure the generated directory exists
  fs.ensureDirSync(actualOutputPath);

  // If no custom context path is provided, ensure @gql-generator/context is mapped
  if (!contextPath) {
    // Pass the original template path with {{output}} to ensure consistent mapping
    ensureContextMapping(generatedPath);
  }

  return {
    contextPath,
    contextName
  };
}

/**
 * Main function to run the context setup
 */
const main = () => {
  try {
    setupContext();
  } catch (error) {
    console.error('Error setting up context:', error);
    process.exit(1);
  }
}

// Run the context setup if this file is executed directly
if (require.main === module) {
  main();
}

// Export for use in other scripts
export { setupContext, ensureContextMapping }; 