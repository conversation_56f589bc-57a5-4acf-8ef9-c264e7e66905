import type { DetectedPattern } from '../utils/code-pattern-analyzer';
import type { SchemaUpdater, UpdateResult } from '../utils/schema-updater';

/**
 * Configuration options for sync coordination
 */
export interface SyncCoordinatorOptions {
  debounceMs?: number;
  maxAttempts?: number;
  preventLoops?: boolean;
  batchSize?: number;
}

/**
 * Sync state tracking
 */
export type SyncState = 'idle' | 'processing' | 'error' | 'paused';

/**
 * Change event for queuing
 */
export interface ChangeEvent {
  id: string;
  type: 'schema-to-code' | 'code-to-schema';
  timestamp: number;
  data: any;
  attempts: number;
}

/**
 * Sync operation result
 */
export interface SyncResult {
  success: boolean;
  message: string;
  patternsProcessed: number;
  errors: string[];
}

/**
 * Coordinates bidirectional synchronization between schema and code
 */
export class BidirectionalSyncCoordinator {
  private options: Required<SyncCoordinatorOptions>;
  private syncState: SyncState = 'idle';
  private changeQueue: ChangeEvent[] = [];
  private debounceTimer: NodeJS.Timeout | null = null;
  private processingPromise: Promise<SyncResult> | null = null;

  constructor(options: SyncCoordinatorOptions = {}) {
    this.options = {
      debounceMs: 500,
      maxAttempts: 3,
      preventLoops: true,
      batchSize: 10,
      ...options,
    };
  }

  /**
   * Handle code-to-schema synchronization
   */
  async handleCodeToSchemaSync(
    pattern: DetectedPattern,
    schemaUpdater: SchemaUpdater
  ): Promise<SyncResult> {
    const eventId = this.generateEventId();

    console.log(`🔄 Syncing pattern to schema: ${pattern.type} - ${pattern.content}`);

    try {
      // Queue the change event
      const changeEvent: ChangeEvent = {
        id: eventId,
        type: 'code-to-schema',
        timestamp: Date.now(),
        data: pattern,
        attempts: 0,
      };

      this.changeQueue.push(changeEvent);

      // Process the queue with debouncing
      return await this.processQueueDebounced(schemaUpdater);
    } catch (error) {
      console.error('Error in code-to-schema sync:', error);
      return {
        success: false,
        message: `Sync failed: ${error instanceof Error ? error.message : String(error)}`,
        patternsProcessed: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  /**
   * Handle batched code-to-schema synchronization for multiple patterns from the same file change
   */
  async handleBatchedCodeToSchemaSync(
    patterns: DetectedPattern[],
    schemaUpdater: SchemaUpdater
  ): Promise<SyncResult> {
    if (patterns.length === 0) {
      return {
        success: true,
        message: 'No patterns to process',
        patternsProcessed: 0,
        errors: [],
      };
    }

    console.log(`🔄 Batching ${patterns.length} patterns for schema sync`);

    try {
      // Create change events for all patterns
      const changeEvents: ChangeEvent[] = patterns.map(pattern => ({
        id: this.generateEventId(),
        type: 'code-to-schema' as const,
        timestamp: Date.now(),
        data: pattern,
        attempts: 0,
      }));

      // Add all events to the queue at once
      this.changeQueue.push(...changeEvents);

      // Process the queue with debouncing (all patterns will be processed in the same session)
      return await this.processQueueDebounced(schemaUpdater);
    } catch (error) {
      console.error('Error in batched code-to-schema sync:', error);
      return {
        success: false,
        message: `Batched sync failed: ${error instanceof Error ? error.message : String(error)}`,
        patternsProcessed: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  /**
   * Handle schema-to-code synchronization
   */
  async handleSchemaToCodeSync(schemaPath: string): Promise<SyncResult> {
    const eventId = this.generateEventId();
    
    console.log(`🔄 Schema changed, triggering code regeneration: ${schemaPath}`);

    try {
      const changeEvent: ChangeEvent = {
        id: eventId,
        type: 'schema-to-code',
        timestamp: Date.now(),
        data: { schemaPath },
        attempts: 0,
      };

      this.changeQueue.push(changeEvent);

      // For schema changes, we typically want immediate processing
      return await this.processQueue();
    } catch (error) {
      console.error('Error in schema-to-code sync:', error);
      return {
        success: false,
        message: `Sync failed: ${error instanceof Error ? error.message : String(error)}`,
        patternsProcessed: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  /**
   * Process the change queue with debouncing
   */
  private async processQueueDebounced(schemaUpdater: SchemaUpdater): Promise<SyncResult> {
    return new Promise((resolve, reject) => {
      // Clear existing timer
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // Set new timer
      this.debounceTimer = setTimeout(async () => {
        try {
          const result = await this.processQueue(schemaUpdater);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, this.options.debounceMs);
    });
  }

  /**
   * Process the change queue
   */
  private async processQueue(schemaUpdater?: SchemaUpdater): Promise<SyncResult> {
    // Prevent concurrent processing
    if (this.processingPromise) {
      await this.processingPromise;
    }

    this.processingPromise = this.doProcessQueue(schemaUpdater);
    return await this.processingPromise;
  }

  /**
   * Actually process the queue
   */
  private async doProcessQueue(schemaUpdater?: SchemaUpdater): Promise<SyncResult> {
    if (this.syncState === 'processing') {
      return {
        success: false,
        message: 'Sync already in progress',
        patternsProcessed: 0,
        errors: [],
      };
    }

    this.syncState = 'processing';
    const errors: string[] = [];
    let patternsProcessed = 0;
    let backupSessionId: string | null = null;

    try {
      // Start a backup session if we have a schema updater
      if (schemaUpdater) {
        backupSessionId = schemaUpdater.startBackupSession();
        console.log(`🔄 Started backup session: ${backupSessionId}`);
      }

      // Process events in batches
      while (this.changeQueue.length > 0) {
        const batch = this.changeQueue.splice(0, this.options.batchSize);

        for (const event of batch) {
          try {
            if (event.type === 'code-to-schema' && schemaUpdater) {
              await this.processCodeToSchemaEvent(event, schemaUpdater);
              patternsProcessed++;
            } else if (event.type === 'schema-to-code') {
              await this.processSchemaToCodeEvent(event);
              patternsProcessed++;
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            errors.push(`Event ${event.id}: ${errorMessage}`);

            // Retry logic
            if (event.attempts < this.options.maxAttempts) {
              event.attempts++;
              this.changeQueue.push(event);
              console.log(`   ↳ Retrying event ${event.id} (attempt ${event.attempts})`);
            } else {
              console.error(`   ↳ Max attempts reached for event ${event.id}`);
            }
          }
        }
      }

      this.syncState = 'idle';

      return {
        success: errors.length === 0,
        message: errors.length === 0
          ? `Successfully processed ${patternsProcessed} patterns`
          : `Processed ${patternsProcessed} patterns with ${errors.length} errors`,
        patternsProcessed,
        errors,
      };
    } catch (error) {
      this.syncState = 'error';
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        success: false,
        message: `Queue processing failed: ${errorMessage}`,
        patternsProcessed,
        errors: [...errors, errorMessage],
      };
    } finally {
      // End the backup session
      if (schemaUpdater && backupSessionId) {
        schemaUpdater.endBackupSession();
        console.log(`✅ Ended backup session: ${backupSessionId}`);
      }
    }
  }

  /**
   * Process a code-to-schema event
   */
  private async processCodeToSchemaEvent(
    event: ChangeEvent,
    schemaUpdater: SchemaUpdater
  ): Promise<void> {
    const pattern = event.data as DetectedPattern;
    
    console.log(`   ↳ Processing ${pattern.type}: ${pattern.content}`);

    let result: UpdateResult;

    if (pattern.type === 'methodCall') {
      result = await schemaUpdater.addMethodCallDirective(
        pattern.fieldContext.schemaFilePath,
        pattern.fieldContext.typeName,
        pattern.fieldContext.fieldName,
        pattern.content
      );
    } else if (pattern.type === 'import') {
      result = await schemaUpdater.addImportDirective(
        pattern.fieldContext.schemaFilePath,
        pattern.fieldContext.typeName,
        pattern.content,
        pattern.fieldContext.fieldName
      );
    } else {
      throw new Error(`Unknown pattern type: ${pattern.type}`);
    }

    if (!result.success) {
      throw new Error(result.message);
    }

    console.log(`   ↳ ${result.message}`);
  }

  /**
   * Process a schema-to-code event
   */
  private async processSchemaToCodeEvent(event: ChangeEvent): Promise<void> {
    const { schemaPath } = event.data;
    
    console.log(`   ↳ Processing schema change: ${schemaPath}`);
    
    // This would trigger the existing code generation pipeline
    // For now, we'll just log it as the actual regeneration
    // is handled by the existing WatchService
    console.log(`   ↳ Schema change will trigger code regeneration`);
  }

  /**
   * Get current sync state
   */
  getSyncState(): SyncState {
    return this.syncState;
  }

  /**
   * Get queue length
   */
  getQueueLength(): number {
    return this.changeQueue.length;
  }

  /**
   * Clear the queue (useful for testing or error recovery)
   */
  clearQueue(): void {
    this.changeQueue = [];
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  /**
   * Pause sync operations
   */
  pause(): void {
    this.syncState = 'paused';
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  /**
   * Resume sync operations
   */
  resume(): void {
    if (this.syncState === 'paused') {
      this.syncState = 'idle';
    }
  }

  /**
   * Generate a unique event ID
   */
  private generateEventId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
