import fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';
import { printSchema } from 'graphql';
import * as glob from 'glob';
import { loadAndMergeSchemas, saveMergedSchema, normalizePath, SchemaLoadingError } from '@utils/schema-loader';
import { postProcessTypes } from '@utils/post-process-types';
import { getScalarTypeMappings, generateScalarsFileContent } from '@utils/type-converters';
import { PathSanitizer, CommonSanitizers } from '@utils/path-sanitizer';
import { TemplateSanitizer } from '@utils/template-sanitizer';
import { getGlobalOptimizedCodeGenService, type OptimizedCodeGenOptions } from './optimized-codegen';

// Debug logger function
export const debugLog = (debug: boolean, message: string, ...data: any[]) => {
  if (debug) {
    console.log(`[DEBUG] ${message}`, ...data);
  }
};

/**
 * Phase 2 Optimization: Run GraphQL CodeGen using direct API integration
 * Replaces child process execution for 10-20% performance improvement
 */
export const runOptimizedGraphQLCodegen = async (options: OptimizedCodeGenOptions): Promise<void> => {
  const { debug = false } = options;

  if (debug) {
    console.log('🚀 Running optimized GraphQL CodeGen with direct API integration...');
  }

  try {
    const optimizedService = getGlobalOptimizedCodeGenService({
      maxParallelJobs: 4,
      cacheDir: path.join(process.cwd(), '.gql-generator-cache')
    });

    const result = await optimizedService.generateTypes({
      ...options,
      enableCaching: process.env.ENABLE_CODEGEN_CACHING !== 'false',
      enableParallelProcessing: process.env.ENABLE_PARALLEL_PROCESSING !== 'false'
    });

    if (result.success) {
      const cacheStatus = result.fromCache ? '(from cache)' : '(generated)';
      console.log(`✅ GraphQL CodeGen completed successfully ${cacheStatus} in ${result.duration}ms`);

      if (debug) {
        console.log(`Output path: ${result.outputPath}`);
        const cacheStats = optimizedService.getCacheStats();
        console.log(`Cache entries: ${cacheStats.entryCount}`);
      }
    } else {
      throw result.error || new Error('CodeGen generation failed');
    }

    // Generate scalars.ts file
    const sanitizedOutputPath = normalizePath(options.output);
    await generateScalarsFile(sanitizedOutputPath, options.schema, debug);

  } catch (error) {
    console.error('❌ Optimized GraphQL CodeGen failed:', error);

    if (debug) {
      console.log('🔄 Falling back to legacy CodeGen implementation...');
      await runGraphQLCodegen(options);
    } else {
      throw error;
    }
  }
};

/**
 * Generate scalar configuration string for codegen
 * @param schemaRoot The schema root directory
 * @returns Formatted scalar configuration string
 */
function generateScalarConfig(schemaRoot: string): string {
  const scalarMappings = getScalarTypeMappings(schemaRoot);

  const scalarEntries = Object.entries(scalarMappings)
    .map(([key, value]) => `                  ${key}: "${value.replace(/"/g, '\\"')}"`)
    .join(',\n');

  return scalarEntries;
}

/**
 * Generate scalars.ts file with clean type aliases
 * @param outputDir The output directory where scalars.ts should be generated
 * @param schemaRoot The schema root directory for detecting scalar types
 * @param debug Enable debug logging
 */
async function generateScalarsFile(outputDir: string, schemaRoot: string, debug: boolean = false): Promise<void> {
  try {
    debugLog(debug, 'Generating scalars.ts file...');

    const scalarsContent = generateScalarsFileContent(schemaRoot);
    const scalarsFilePath = path.join(outputDir, 'scalars.ts');

    fs.writeFileSync(scalarsFilePath, scalarsContent);

    if (debug) {
      console.log(`Generated clean scalar type aliases at: ${scalarsFilePath}`);
    }
    debugLog(debug, 'Scalars.ts file generated successfully');
  } catch (error: any) {
    console.error(`Error generating scalars.ts file: ${error.message}`);
    debugLog(debug, 'Failed to generate scalars.ts file:', error);
  }
}

interface CodegenOptions {
  schema: string;
  output: string;
  force?: boolean;
  context?: string;
  contextName?: string;
  debug?: boolean;
}

// Run GraphQL CodeGen
export const runGraphQLCodegen = async (options: CodegenOptions): Promise<void> => {
  const { debug = false } = options;
  if (debug) {
    console.log('Running GraphQL Code Generator...');
  }
  debugLog(debug, 'Debug mode enabled for graphql.ts file generation');
  debugLog(debug, 'Options:', JSON.stringify(options, null, 2));

  // Sanitize and validate schema path
  const schemaSanitizationResult = CommonSanitizers.schemaFiles(options.schema, {
    baseDirectory: process.cwd(),
    allowAbsolute: true,
  });

  if (!schemaSanitizationResult.isValid) {
    throw new Error(`Invalid schema path: ${schemaSanitizationResult.error}`);
  }

  const schemaPath = schemaSanitizationResult.sanitizedPath!;
  debugLog(debug, `Sanitized schema path: ${schemaPath}`);

  // Sanitize and validate output path
  const outputSanitizationResult = CommonSanitizers.outputDirectories(options.output, {
    baseDirectory: process.cwd(),
    allowAbsolute: true,
  });

  if (!outputSanitizationResult.isValid) {
    throw new Error(`Invalid output path: ${outputSanitizationResult.error}`);
  }

  const sanitizedOutputPath = outputSanitizationResult.sanitizedPath!;
  debugLog(debug, `Sanitized output path: ${sanitizedOutputPath}`);

  // Normalize the schema path for cross-platform compatibility
  const normalizedSchemaPath = normalizePath(schemaPath);
  debugLog(debug, `Normalized schema path: ${normalizedSchemaPath}`);

  // Determine if the schema path is a directory, file, or pattern
  let schemaGlob: string;

  // Check if the schema path exists and is a directory
  if (fs.existsSync(normalizedSchemaPath) && fs.statSync(normalizedSchemaPath).isDirectory()) {
    debugLog(debug, `Schema path is a directory: ${normalizedSchemaPath}`);
    schemaGlob = path.join(normalizedSchemaPath, '**', '*.gql').replace(/\\/g, '/');
    debugLog(debug, `Using glob pattern for directory: ${schemaGlob}`);
  }
  // Check if the schema path exists and is a file
  else if (fs.existsSync(normalizedSchemaPath) && fs.statSync(normalizedSchemaPath).isFile()) {
    debugLog(debug, `Schema path is a file: ${normalizedSchemaPath}`);
    schemaGlob = normalizedSchemaPath.replace(/\\/g, '/');
    debugLog(debug, `Using direct file path: ${schemaGlob}`);
  }
  // Check if it's a glob pattern
  else if (options.schema.includes('*')) {
    debugLog(debug, `Schema path is a glob pattern: ${options.schema}`);
    schemaGlob = options.schema.replace(/\\/g, '/');
    debugLog(debug, `Using provided glob pattern: ${schemaGlob}`);
  }
  // Otherwise it's an invalid path
  else {
    // Try checking if adding .gql or .graphql extension works
    if (fs.existsSync(`${normalizedSchemaPath}.gql`)) {
      debugLog(debug, `Found schema file with .gql extension: ${normalizedSchemaPath}.gql`);
      schemaGlob = normalizePath(`${normalizedSchemaPath}.gql`);
    } else if (fs.existsSync(`${normalizedSchemaPath}.graphql`)) {
      debugLog(debug, `Found schema file with .graphql extension: ${normalizedSchemaPath}.graphql`);
      schemaGlob = normalizePath(`${normalizedSchemaPath}.graphql`);
    } else {
      // The schema path is invalid - throw an error
      console.error(`Schema path does not exist: ${normalizedSchemaPath}`);
      throw new Error(`Invalid schema path: ${normalizedSchemaPath}. Please provide a valid path to your GraphQL schema files.`);
    }
  }

  // Notify user we're running codegen
  debugLog(debug, `Running GraphQL codegen with schema glob: ${schemaGlob}`);

  // If a custom context path is provided, update the codegen.ts file
  if (options.context) {
    // Sanitize context path if it's not a module reference
    let sanitizedContextPath = options.context;
    if (!options.context.startsWith('@') && !options.context.startsWith('node_modules')) {
      const contextSanitizationResult = CommonSanitizers.typescriptFiles(options.context, {
        baseDirectory: process.cwd(),
        allowAbsolute: true,
      });

      if (!contextSanitizationResult.isValid) {
        throw new Error(`Invalid context path: ${contextSanitizationResult.error}`);
      }

      sanitizedContextPath = contextSanitizationResult.sanitizedPath!;
    }

    const codegenPath = path.join(process.cwd(), 'codegen.ts');

    if (fs.existsSync(codegenPath)) {
      debugLog(debug, `Updating codegen.ts with custom context...`);

      try {
        // Read the codegen.ts file
        let codegenContent = fs.readFileSync(codegenPath, 'utf8');

        // Get the context path and name
        let contextPath = sanitizedContextPath;

        // If it's a relative path (not starting with @), resolve it relative to the workspace root
        if (!contextPath.startsWith('@')) {
          const absolutePath = path.resolve(process.cwd(), contextPath);
          contextPath = path.relative(process.cwd(), absolutePath).replace(/\\/g, '/');
        }

        // Get the context name (default to 'Context')
        const contextName = options.contextName ?? 'Context';

        // Replace the contextType in the codegen.ts file to point directly to the context file
        const contextTypePattern = /contextType:\s*["'].*?["']/g;
        const contextTypeReplacement = `contextType: "${contextPath}#${contextName}"`;

        // Check if the pattern exists in the content
        if (codegenContent.match(contextTypePattern)) {
          // Replace all instances of the pattern
          codegenContent = codegenContent.replace(contextTypePattern, contextTypeReplacement);

          // Write the updated codegen.ts file
          fs.writeFileSync(codegenPath, codegenContent);

          debugLog(debug, `Updated codegen.ts to import ${contextName} directly from ${contextPath}`);
        } else {
          if (debug) {
            console.warn(
              `Could not find contextType pattern in codegen.ts. Custom context may not be applied correctly.`
            );
          }
        }
      } catch (error: any) {
        console.error(`Error updating codegen.ts: ${error.message}`);
        if (debug) {
          console.warn(`Continuing with default context.`);
        }
      }
    } else {
      if (debug) {
        console.warn(
          'codegen.ts file not found. Custom context type will not be applied to GraphQL codegen.'
        );
      }
    }
  }

  // Run GraphQL codegen via child process
  try {
    // Install dependencies if needed
    try {
      debugLog(debug, 'Checking for GraphQL CodeGen dependencies...');
      // Try to require the package to see if it's installed
      require.resolve('@graphql-codegen/cli');
    } catch (_e) {
      if (debug) {
        console.log('Installing GraphQL CodeGen dependencies...');
      }
      execSync(
        'npm install --no-save @graphql-codegen/cli @graphql-codegen/typescript @graphql-codegen/typescript-resolvers',
        { stdio: 'inherit' }
      );
    }

    // Create the output directory if it doesn't exist
    fs.ensureDirSync(sanitizedOutputPath);

    // Use the loadAndMergeSchemas utility to load and merge the schema
    debugLog(debug, `Loading and merging schema files using pattern: ${schemaGlob}`);

    let executableSchema;
    try {
      const result = loadAndMergeSchemas(normalizedSchemaPath, debug);
      executableSchema = result.executableSchema;
    } catch (error) {
      if (error instanceof SchemaLoadingError) {
        // Schema loading errors are already formatted and logged
        throw new Error(`Schema loading failed: ${error.message}`);
      }
      // Re-throw other errors as-is
      throw error;
    }

    // Write merged schema to output (with @field field filtering)
    const schemaOutputPath = path.join(sanitizedOutputPath, 'schema.graphql');
    await saveMergedSchema(printSchema(executableSchema), schemaOutputPath, normalizedSchemaPath, debug);
    if (debug) {
      console.log(`Generated merged schema at: ${schemaOutputPath}`);
    }

    // Format the generated schema file
    try {
      // Properly escape the path for cross-platform compatibility
      const escapedPath = process.platform === 'win32'
        ? `"${schemaOutputPath.replace(/\\/g, '\\\\')}"`
        : `"${schemaOutputPath}"`;
      execSync(`npx prettier --write ${escapedPath}`, { stdio: 'inherit' });
      debugLog(debug, `Formatted generated schema: ${schemaOutputPath}`);
    } catch (error) {
      if (debug) {
        console.warn(`Warning: Could not format schema file: ${error}`);
      }
    }

    // Now run the graphql-codegen programmatically using the direct Node.js API
    debugLog(debug, 'Running GraphQL CodeGen programmatically...');

    // Dynamic import of graphql-codegen
    try {
      // Create a temporary file just for the generation
      const tempCodegenFile = path.join(process.cwd(), 'temp-codegen-script.js');

      // Determine the context type to use
      let contextTypeConfig = `contextType: "@gql-generator/context#Context"`;

      // Get context information from options or schema directive
      if (options.context) {
        // Get the context path and name from command line options
        let contextPath = options.context;
        const contextName = options.contextName ?? 'Context';

        // If it's a relative path (not starting with @), resolve it properly
        if (!contextPath.startsWith('@')) {
          let absolutePath: string;

          // If contextPath is already absolute (from CLI resolution), use it directly
          if (path.isAbsolute(contextPath)) {
            absolutePath = contextPath;
          } else {
            // Otherwise resolve relative to current working directory
            absolutePath = path.resolve(process.cwd(), contextPath);
          }

          contextPath = path.relative(process.cwd(), absolutePath).replace(/\\/g, '/');

          // For contextType, we need to make sure the path is relative to the generated file
          // Let's calculate the proper relative path
          const outputDir = path.isAbsolute(sanitizedOutputPath) ? sanitizedOutputPath : path.resolve(process.cwd(), sanitizedOutputPath);
          const relativeToOutput = path.relative(outputDir, absolutePath);

          // Use the relative path for the contextType
          contextTypeConfig = `contextType: "${relativeToOutput.replace(/\\/g, '/')}#${contextName}"`;
        } else {
          // For alias paths, use as is
          contextTypeConfig = `contextType: "${contextPath}#${contextName}"`;
        }
      } else {
        // If no context provided via options, try to extract it from schema directives
        try {
          // Find all GraphQL schema files
          const schemaFiles = glob.sync(path.join(normalizedSchemaPath, '**', '*.{gql,graphql}'), {
            windowsPathsNoEscape: true,
          });

          // Extract context from schema directives
          let contextFromDirective = null;
          for (const schemaFile of schemaFiles) {
            // Use dynamic import to avoid circular dependency issues
            const { DirectiveParser } = require('@utils/directive-parser');
            const { DirectiveProcessor } = require('@utils/directive-processor');

            const directives = await DirectiveParser.extractSchemaDirectives(schemaFile);
            const contextInfo = DirectiveProcessor.extractContextInfo(directives);

            if (contextInfo) {
              debugLog(debug,
                `Found context directive in schema: path=${contextInfo.path}, name=${contextInfo.name}`
              );

              let contextPath = contextInfo.path;
              const contextName = contextInfo.name;

              // If it's a relative path (not starting with @), resolve it properly
              if (!contextPath.startsWith('@')) {
                let absolutePath: string;

                // If contextPath is already absolute (from CLI resolution), use it directly
                if (path.isAbsolute(contextPath)) {
                  absolutePath = contextPath;
                } else {
                  // Otherwise resolve relative to current working directory
                  absolutePath = path.resolve(process.cwd(), contextPath);
                }

                contextPath = path.relative(process.cwd(), absolutePath).replace(/\\/g, '/');

                // For contextType, we need to make sure the path is relative to the generated file
                // Let's calculate the proper relative path
                const outputDir = path.isAbsolute(sanitizedOutputPath) ? sanitizedOutputPath : path.resolve(process.cwd(), sanitizedOutputPath);
                const relativeToOutput = path.relative(outputDir, absolutePath);

                // Use the relative path for the contextType
                contextTypeConfig = `contextType: "${relativeToOutput.replace(/\\/g, '/')}#${contextName}"`;
              } else {
                // For alias paths, use as is
                contextTypeConfig = `contextType: "${contextPath}#${contextName}"`;
              }

              contextFromDirective = { path: contextPath, name: contextName };
              break;
            }
          }

          if (!contextFromDirective) {
            // When no custom context is provided, use the @gql-generator/context alias
            // This must be mapped by the user in their tsconfig.json to their own context implementation
            contextTypeConfig = `contextType: "@gql-generator/context#Context"`;
          }
        } catch (directiveError) {
          console.error('Error extracting context from schema directives:', directiveError);
          // Fallback to default context
          contextTypeConfig = `contextType: "@gql-generator/context#Context"`;
        }
      }

      // Generate scalar configuration from schema
      const scalarConfig = generateScalarConfig(options.schema);

      // Sanitize paths for safe interpolation into JavaScript
      // Normalize paths before sanitization to handle Windows paths correctly
      const normalizedSchemaOutputPath = path.normalize(schemaOutputPath);
      const normalizedGraphQLOutputPath = path.normalize(path.join(sanitizedOutputPath, 'graphql.ts'));
      const sanitizedSchemaPath = TemplateSanitizer.escapeForJavaScript(normalizedSchemaOutputPath);
      const sanitizedGraphQLPath = TemplateSanitizer.escapeForJavaScript(normalizedGraphQLOutputPath);

      const tempCodegenContent = `
        const { generate } = require('@graphql-codegen/cli');

        generate({
          schema: "${sanitizedSchemaPath}",
          generates: {
            "${sanitizedGraphQLPath}": {
              plugins: ['typescript', 'typescript-resolvers'],
              config: {
                useIndexSignature: true,
                ${contextTypeConfig},
                namingConvention: {
                  typeNames: './src/utils/custom-naming-convention',
                  enumValues: 'keep',
                  transformUnderscore: false
                },
                enumsAsTypes: false,
                scalars: {
                  ${scalarConfig}
                }
              }
            }
          }
        }, true).then(() => {
          console.log('GraphQL types generated successfully');
          process.exit(0);
        }).catch((error) => {
          console.error('Error generating GraphQL types:', error);
          process.exit(1);
        });
      `;

      debugLog(debug, 'Writing temporary codegen script to:', tempCodegenFile);
      if (debug) {
        console.log(`\n===== TEMPORARY CODEGEN SCRIPT CONTENT =====`);
        console.log(tempCodegenContent);
        console.log(`===== END SCRIPT CONTENT =====\n`);
      }

      fs.writeFileSync(tempCodegenFile, tempCodegenContent);

      console.log(`Running node script: ${tempCodegenFile}`);
      debugLog(debug, 'Executing codegen script with Node');

      // Add a pre-generation check to see if the file already exists and its state
      const graphqlTsPath = path.join(sanitizedOutputPath, 'graphql.ts');
      if (fs.existsSync(graphqlTsPath)) {
        debugLog(debug, `GraphQL types file already exists at: ${graphqlTsPath}`);
        const stats = fs.statSync(graphqlTsPath);
        debugLog(debug, `Existing file size: ${stats.size} bytes`);
      } else {
        debugLog(
          debug,
          `GraphQL types file doesn't exist yet, will be created at: ${graphqlTsPath}`
        );
      }

      try {
        execSync(`node "${tempCodegenFile}"`, { stdio: 'inherit' });
        debugLog(debug, 'Codegen script execution completed');
      } catch (error) {
        console.error('Error executing codegen script:', error);
        throw error;
      }

      // Clean up the temporary file
      debugLog(debug, 'Removing temporary codegen script');
      fs.unlinkSync(tempCodegenFile);

      // Verify the file was generated properly
      if (fs.existsSync(graphqlTsPath)) {
        const stats = fs.statSync(graphqlTsPath);
        debugLog(debug, `Generated file size: ${stats.size} bytes`);

        if (stats.size === 0) {
          console.error(`ERROR: The generated graphql.ts file is empty (0 bytes)!`);
        } else if (stats.size < 1000) {
          console.warn(
            `WARNING: The generated graphql.ts file is very small (${stats.size} bytes). It might be truncated.`
          );
        } else {
          debugLog(debug, `Generated file seems to have an appropriate size (${stats.size} bytes)`);
        }
      } else {
        console.error(`ERROR: GraphQL types file was not generated at: ${graphqlTsPath}`);
      }

      // Post-process the graphql.ts file to fix imports if a custom context is provided
      if (options.context) {
        const graphqlFilePath = path.join(sanitizedOutputPath, 'graphql.ts');

        if (fs.existsSync(graphqlFilePath)) {
          console.log('Post-processing graphql.ts to fix Context import...');

          try {
            // Read the generated graphql.ts file
            let graphqlContent = fs.readFileSync(graphqlFilePath, 'utf8');

            // Get the context path and name
            let contextPath = options.context;
            const contextName = options.contextName ?? 'Context';

            // Calculate the correct relative path from graphql.ts to the context file
            if (!contextPath.startsWith('@')) {
              const absoluteContextPath = path.resolve(process.cwd(), contextPath);
              const graphqlDirPath = path.dirname(path.resolve(process.cwd(), graphqlFilePath));
              const relativeContextPath = path
                .relative(graphqlDirPath, absoluteContextPath)
                .replace(/\\/g, '/');

              // Ensure path starts with ./ if it's not going up directories
              contextPath = relativeContextPath.startsWith('.')
                ? relativeContextPath
                : `./${relativeContextPath}`;
            }

            // Check for both possible import patterns
            const contextImportPatterns = [
              // Check for import from types
              /import\s*{\s*Context\s*}\s*from\s*['"]\.\/types['"];?/,
              // Check for direct import but with incorrect path
              new RegExp(
                `import\\s*{\\s*${contextName}\\s*}\\s*from\\s*['"][^.][^/][^'"]*.+?['"];?`
              ),
            ];

            let importFound = false;
            for (const pattern of contextImportPatterns) {
              if (graphqlContent.match(pattern)) {
                // Replace the import statement
                const importReplacement = `import { ${contextName} as Context } from '${contextPath}';`;
                graphqlContent = graphqlContent.replace(pattern, importReplacement);
                importFound = true;

                // Write the updated graphql.ts file
                fs.writeFileSync(graphqlFilePath, graphqlContent);
                console.log(
                  `Successfully updated graphql.ts to import ${contextName} as Context directly from ${contextPath}`
                );
                break;
              }
            }

            if (!importFound) {
              console.warn(
                'Could not find Context import in graphql.ts file. Manual adjustment may be needed.'
              );
              console.warn('Current imports in graphql.ts:');

              // Extract the first few lines to show the imports
              const firstLines = graphqlContent.split('\n').slice(0, 10).join('\n');
              console.warn(firstLines);
            }
          } catch (error: any) {
            console.error(`Error updating graphql.ts imports: ${error.message}`);
          }
        }
      }

      // Note: Post-processing of types is now handled by the schema-based generator
      // to ensure decorator metadata is available for @GQLField processing

      // Generate scalars.ts file with clean type aliases
      await generateScalarsFile(sanitizedOutputPath, options.schema, debug);

      console.log('GraphQL codegen completed successfully.');
    } catch (codegenError) {
      console.error('Failed to run GraphQL CodeGen programmatically:', codegenError);

      // Fallback method - use the TypeScript directly
      console.log('Attempting fallback TypeScript generation method...');

      // Generate scalar types from schema for fallback
      const scalarMappings = getScalarTypeMappings(options.schema);
      const scalarTypeEntries = Object.entries(scalarMappings)
        .map(([key, value]) => `  ${key}: ${value};`)
        .join('\n');

      // Simplified TypeScript generation - create a basic TypeScript interface file
      const basicTsContent = `
/**
 * This is a basic TypeScript interface generated from the GraphQL schema.
 * For more detailed types, please ensure GraphQL CodeGen is properly installed.
 */

export interface Context {
  [key: string]: any;
}

export type Maybe<T> = T | null;

export type Scalars = {
${scalarTypeEntries}
};

// Basic Query interface
export interface Query {
  [key: string]: any;
}

// Basic Mutation interface
export interface Mutation {
  [key: string]: any;
}

// Basic Resolvers interface
export interface Resolvers {
  Query?: QueryResolvers;
  Mutation?: MutationResolvers;
  [key: string]: any;
}

export interface QueryResolvers {
  [key: string]: (root: any, args: any, context: Context) => any;
}

export interface MutationResolvers {
  [key: string]: (root: any, args: any, context: Context) => any;
}
`;

      const typesOutputPath = path.join(sanitizedOutputPath, 'graphql.ts');
      fs.writeFileSync(typesOutputPath, basicTsContent);
      console.log(`Generated basic TypeScript types at: ${typesOutputPath}`);

      // Generate scalars.ts file even in fallback mode
      await generateScalarsFile(sanitizedOutputPath, options.schema, debug);
    }

    console.log('GraphQL Code Generator completed successfully');
  } catch (error) {
    console.error('Error during GraphQL schema processing:', error);
    throw error;
  }
};
