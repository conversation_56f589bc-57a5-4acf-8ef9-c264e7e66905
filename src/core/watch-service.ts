import chokidar from 'chokidar';
import path from 'path';
import { generateAll, generateAllMultiple } from './generator-orchestrator';
import type { GeneratorOptions, MultiGeneratorOptions, SchemaOutputMapping } from './generator';
import { UI, FileChangeTracker } from '../utils/ui';

/**
 * Extended options for watch functionality
 */
export interface WatchGeneratorOptions extends GeneratorOptions {
  /** Debounce delay in milliseconds (default: 300) */
  debounce?: number;
  /** Run initial generation on startup (default: true) */
  initial?: boolean;
  /** Additional paths to watch beyond schema */
  watchPaths?: string[];
}

/**
 * Extended options for multi-mapping watch functionality
 */
export interface MultiWatchGeneratorOptions extends Omit<WatchGeneratorOptions, 'schema' | 'output'> {
  /** Schema-output mappings to watch */
  mappings: SchemaOutputMapping[];
}

/**
 * Service for watching schema files and automatically regenerating code
 */
export class WatchService {
  private watcher: ReturnType<typeof chokidar.watch> | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;
  private options: WatchGeneratorOptions;
  private isGenerating = false;
  private isShuttingDown = false;
  protected fileChangeTracker: FileChangeTracker;

  constructor(options: WatchGeneratorOptions) {
    this.options = {
      debounce: 300,
      initial: true,
      ...options,
    };
    this.fileChangeTracker = new FileChangeTracker(100);
  }

  /**
   * Start watching files and run initial generation if enabled
   */
  async start(): Promise<void> {
    if (this.watcher) {
      UI.warning('Watch service is already running');
      return;
    }

    UI.info('Starting GraphQL Generator Watch Service...');

    // Run initial generation if enabled
    if (this.options.initial) {
      UI.spinner('Running initial generation...');
      await this.regenerate();
    }

    // Determine paths to watch
    const watchPaths = this.getWatchPaths();
    UI.info(`Watching paths: ${watchPaths.join(', ')}`);

    // Initialize chokidar watcher
    this.watcher = chokidar.watch(watchPaths, {
      ignored: this.getIgnoredPatterns(),
      persistent: true,
      ignoreInitial: true, // We handle initial generation separately
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 50,
      },
    });

    // Set up event listeners
    this.setupEventListeners();

    // Set up graceful shutdown
    this.setupShutdownHandlers();

    UI.success('Watch service started successfully');
    UI.info('Press Ctrl+C to stop watching');
  }

  /**
   * Stop the watch service
   */
  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    console.log('\n🛑 Stopping watch service...');

    // Clear any pending debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    // Close the watcher
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = null;
    }

    console.log('✅ Watch service stopped');
  }

  /**
   * Get paths to watch based on schema and additional watch paths
   */
  private getWatchPaths(): string[] {
    const paths: string[] = [];

    // Add schema path
    if (this.options.schema) {
      // Convert glob pattern to directory for watching
      const schemaDir = this.extractDirectoryFromGlob(this.options.schema);
      paths.push(schemaDir);
    }

    // Add codebase directory for decorator scanning
    if (this.options.enableDecorators && this.options.codebaseDir) {
      const codebaseDir = path.resolve(this.options.codebaseDir);
      paths.push(codebaseDir);
      console.log(`📁 Adding codebase directory to watch: ${codebaseDir}`);
    }

    // Add additional watch paths
    if (this.options.watchPaths) {
      paths.push(...this.options.watchPaths);
    }

    return paths;
  }

  /**
   * Extract directory path from glob pattern
   */
  private extractDirectoryFromGlob(globPattern: string): string {
    // Remove glob patterns and get base directory
    const cleanPath = globPattern
      .replace(/\/\*\*\/\*\.\w+$/, '') // Remove /**/*.ext
      .replace(/\/\*\.\w+$/, '') // Remove /*.ext
      .replace(/\*\*\/\*\.\w+$/, '.') // Handle **/*.ext at root
      .replace(/\*\.\w+$/, '.'); // Handle *.ext at root

    return path.resolve(cleanPath);
  }

  /**
   * Get patterns to ignore during watching
   */
  private getIgnoredPatterns(): (path: string, stats?: any) => boolean {
    return (filePath: string, stats?: any) => {
      const relativePath = path.relative(process.cwd(), filePath);
      
      // Ignore directories and files we don't want to watch
      const ignorePatterns = [
        'node_modules',
        '.git',
        'dist',
        'build',
        'coverage',
        '.nyc_output',
        this.options.output, // Don't watch our own output directory
      ];

      // Check if path contains any ignore patterns
      for (const pattern of ignorePatterns) {
        if (relativePath.includes(pattern)) {
          return true;
        }
      }

      // Only watch relevant file types
      if (stats?.isFile()) {
        const ext = path.extname(filePath);
        const relevantExtensions = ['.gql', '.graphql'];

        // Include TypeScript files if decorator scanning is enabled
        if (this.options.enableDecorators) {
          relevantExtensions.push('.ts', '.tsx');
        }

        // Always include JS files for backward compatibility
        relevantExtensions.push('.js');

        return !relevantExtensions.includes(ext);
      }

      return false;
    };
  }

  /**
   * Set up file system event listeners
   */
  private setupEventListeners(): void {
    if (!this.watcher) return;

    this.watcher
      .on('add', (filePath: string) => {
        const relativePath = path.relative(process.cwd(), filePath);
        this.fileChangeTracker.addChange('add', relativePath);
        this.logFileChange('added', filePath);
        this.debounceRegeneration();
      })
      .on('change', (filePath: string) => {
        const relativePath = path.relative(process.cwd(), filePath);
        this.fileChangeTracker.addChange('change', relativePath);
        this.logFileChange('changed', filePath);
        this.debounceRegeneration();
      })
      .on('unlink', (filePath: string) => {
        const relativePath = path.relative(process.cwd(), filePath);
        this.fileChangeTracker.addChange('unlink', relativePath);
        this.logFileChange('removed', filePath);
        this.debounceRegeneration();
      })
      .on('addDir', (dirPath: string) => {
        // Directory changes don't need batching
        UI.info(`Directory added: ${path.relative(process.cwd(), dirPath)}`);
      })
      .on('unlinkDir', (dirPath: string) => {
        UI.info(`Directory removed: ${path.relative(process.cwd(), dirPath)}`);
        this.debounceRegeneration();
      })
      .on('error', (error: unknown) => {
        UI.error(`Watcher error: ${error}`);
      })
      .on('ready', () => {
        UI.success('Initial scan complete. Ready for changes');
      });
  }

  /**
   * Log file changes with appropriate context
   * @param action The action performed (added, changed, removed)
   * @param filePath The file path
   */
  private logFileChange(action: string, filePath: string): void {
    const relativePath = path.relative(process.cwd(), filePath);
    const ext = path.extname(filePath);

    // Determine file type for better logging
    let fileType = 'file';
    if (['.gql', '.graphql'].includes(ext)) {
      fileType = 'schema file';
    } else if (['.ts', '.tsx'].includes(ext) && this.options.enableDecorators) {
      // Check if it's in the codebase directory
      if (this.options.codebaseDir && filePath.includes(this.options.codebaseDir)) {
        fileType = 'decorator file';
      } else {
        fileType = 'TypeScript file';
      }
    }

    UI.info(`📝 ${fileType} ${action}: ${relativePath}`);
  }

  /**
   * Debounce regeneration to avoid excessive calls
   */
  private debounceRegeneration(): void {
    if (this.isShuttingDown) return;

    // Clear existing timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set new timer
    this.debounceTimer = setTimeout(() => {
      // Flush file changes before regenerating
      this.fileChangeTracker.flush();
      UI.spinner('Regenerating...');
      this.regenerate().catch((error) => {
        UI.error(`Regeneration failed: ${error}`);
      });
    }, this.options.debounce);
  }

  /**
   * Run the code generation process
   */
  protected async regenerate(): Promise<void> {
    if (this.isGenerating || this.isShuttingDown) {
      return;
    }

    this.isGenerating = true;
    const startTime = Date.now();

    try {
      // Use existing generateAll function
      await generateAll(this.options);

      const duration = Date.now() - startTime;
      UI.success(`Code regenerated successfully in ${duration}ms`);
      UI.updateStats(duration);
      UI.statusLine();
      UI.newLine();
    } catch (error) {
      UI.error(`Code regeneration failed: ${error}`);
      UI.info('Continuing to watch for changes...');
      UI.newLine();
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * Set up graceful shutdown handlers
   */
  private setupShutdownHandlers(): void {
    const shutdown = async (signal: string) => {
      UI.clearLine();
      UI.newLine();
      UI.info(`Received ${signal}, shutting down gracefully...`);
      UI.shutdown();
      await this.stop();
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }
}

/**
 * Service for watching multiple schema-output mappings and automatically regenerating code
 */
export class MultiWatchService {
  private watchServices: WatchService[] = [];
  private options: MultiWatchGeneratorOptions;
  private isShuttingDown = false;

  constructor(options: MultiWatchGeneratorOptions) {
    this.options = options;

    // Create individual watch services for each mapping
    this.watchServices = options.mappings.map((mapping, index) => {
      const singleOptions: WatchGeneratorOptions = {
        ...options,
        schema: mapping.schema,
        output: mapping.output,
      };

      return new WatchService(singleOptions);
    });
  }

  /**
   * Start watching all schema-output mappings
   */
  async start(): Promise<void> {
    if (this.watchServices.length === 0) {
      console.log('No schema-output mappings to watch');
      return;
    }

    console.log(`🔍 Starting Multi-Watch Service for ${this.watchServices.length} mapping(s)...`);

    // Start all watch services concurrently
    await Promise.all(this.watchServices.map(async (service, index) => {
      const mapping = this.options.mappings[index];
      const mappingName = mapping.name || `mapping-${index + 1}`;

      try {
        console.log(`   Starting ${mappingName}...`);
        await service.start();
        console.log(`   ✅ ${mappingName} started`);
      } catch (error) {
        console.error(`   ❌ Failed to start ${mappingName}:`, error);
        throw error;
      }
    }));

    // Set up graceful shutdown
    this.setupShutdownHandlers();

    console.log(`✅ Multi-Watch Service started successfully for ${this.watchServices.length} mapping(s)`);
    console.log('   Press Ctrl+C to stop watching');
  }

  /**
   * Stop watching all mappings
   */
  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    console.log('\n🛑 Stopping Multi-Watch Service...');

    // Stop all watch services
    await Promise.all(this.watchServices.map(async (service, index) => {
      const mapping = this.options.mappings[index];
      const mappingName = mapping.name || `mapping-${index + 1}`;

      try {
        await service.stop();
        console.log(`   ${mappingName} stopped`);
      } catch (error) {
        console.error(`   Error stopping ${mappingName}:`, error);
      }
    }));

    console.log('👋 Multi-Watch Service stopped');
  }

  /**
   * Set up graceful shutdown handlers
   */
  private setupShutdownHandlers(): void {
    this.shutdownHandler = this.shutdownHandler.bind(this);
    process.on('SIGINT', this.shutdownHandler);
    process.on('SIGTERM', this.shutdownHandler);
  }

  /**
   * Handle shutdown signals
   */
  private async shutdownHandler(): Promise<void> {
    await this.stop();
    process.exit(0);
  }
}
