import chokidar from 'chokidar';
import path from 'path';
import crypto from 'crypto';
import * as fs from 'fs-extra';
import type { WatchGeneratorOptions} from './watch-service';
import { WatchService, MultiWatchService, MultiWatchGeneratorOptions } from './watch-service';
import { CodePatternAnalyzer } from '../utils/code-pattern-analyzer';
import { SchemaUpdater } from '../utils/schema-updater';
import { BidirectionalSyncCoordinator } from './bidirectional-sync-coordinator';
import { WatchedFileWriter } from '../utils/watched-file-writer';
import { UI } from '../utils/ui';

/**
 * Enhanced options for bidirectional watch functionality
 */
export interface EnhancedWatchOptions extends WatchGeneratorOptions {
  /** Enable bidirectional sync between schema and code (default: true) */
  enableBidirectionalSync?: boolean;
  /** Patterns for watching generated code files */
  codeWatchPatterns?: string[];
  /** Patterns to exclude from bidirectional watching */
  bidirectionalIgnorePatterns?: string[];
  /** Watch only specific resolver directories (default: true) */
  selectiveWatching?: boolean;
  /** Custom resolver directories to watch (overrides default selective watching) */
  customResolverDirectories?: string[];
  /** Watch infrastructure files like index.ts, graphql.ts (default: false) */
  watchInfrastructureFiles?: boolean;
  /** Debounce delay for sync operations in milliseconds (default: 500) */
  syncDebounce?: number;
  /** Detect await patterns in return statements (default: true) */
  detectAwaitPatterns?: boolean;
  /** Detect cast patterns in return statements (default: true) */
  detectCastPatterns?: boolean;
  /** Detect import patterns (default: true) */
  detectImportPatterns?: boolean;
  /** Prevent infinite loops (default: true) */
  preventInfiniteLoops?: boolean;
  /** Maximum sync attempts before giving up (default: 3) */
  maxSyncAttempts?: number;
  /** Backup schema files before modification (default: true) */
  backupSchemaFiles?: boolean;
}

/**
 * Change source tracking for loop prevention
 */
export interface ChangeFingerprint {
  filePath: string;
  contentHash: string;
  timestamp: number;
  source: 'user' | 'generator';
}

/**
 * Enhanced watch service that supports bidirectional sync between schema and generated code
 */
export class EnhancedWatchService extends WatchService {
  private codeWatcher: ReturnType<typeof chokidar.watch> | null = null;
  private syncCoordinator: BidirectionalSyncCoordinator;
  private patternAnalyzer: CodePatternAnalyzer;
  private schemaUpdater: SchemaUpdater;
  private enhancedOptions: EnhancedWatchOptions;
  private changeFingerprints: Map<string, ChangeFingerprint> = new Map();
  private isSyncing = false;
  private generationSessionId: string | null = null;
  private generationInProgress = false;

  constructor(options: EnhancedWatchOptions) {
    super(options);
    
    this.enhancedOptions = {
      enableBidirectionalSync: true,
      selectiveWatching: true,
      watchInfrastructureFiles: false,
      syncDebounce: 500,
      detectAwaitPatterns: true,
      detectCastPatterns: true,
      detectImportPatterns: true,
      preventInfiniteLoops: true,
      maxSyncAttempts: 3,
      backupSchemaFiles: false,
      ...options,
    };

    // Initialize components
    this.patternAnalyzer = new CodePatternAnalyzer({
      detectAwaitPatterns: this.enhancedOptions.detectAwaitPatterns!,
      detectCastPatterns: this.enhancedOptions.detectCastPatterns!,
      detectImportPatterns: this.enhancedOptions.detectImportPatterns!,
    });

    this.schemaUpdater = new SchemaUpdater({
      backupFiles: this.enhancedOptions.backupSchemaFiles!,
      preserveComments: true,
    });

    this.syncCoordinator = new BidirectionalSyncCoordinator({
      debounceMs: this.enhancedOptions.syncDebounce!,
      maxAttempts: this.enhancedOptions.maxSyncAttempts!,
      preventLoops: this.enhancedOptions.preventInfiniteLoops!,
    });

    // Register this service with the WatchedFileWriter
    WatchedFileWriter.setWatchService(this);
  }

  /**
   * Start enhanced watching with bidirectional sync
   */
  async start(): Promise<void> {
    // Start the base schema watching
    await super.start();

    // Start code watching if bidirectional sync is enabled
    if (this.enhancedOptions.enableBidirectionalSync) {
      await this.startCodeWatching();
    }

    UI.success('Enhanced watch service with bidirectional sync started');
  }

  /**
   * Stop enhanced watching
   */
  async stop(): Promise<void> {
    // Stop code watching first
    if (this.codeWatcher) {
      await this.codeWatcher.close();
      this.codeWatcher = null;
    }

    // Clear the WatchedFileWriter service reference
    await WatchedFileWriter.clearWatchService();

    // Stop the base service
    await super.stop();

    if (process.env.DEBUG_PARSER) {
      console.log('🛑 Enhanced watch service stopped');
    }
  }

  /**
   * Start watching generated code files for changes
   */
  private async startCodeWatching(): Promise<void> {
    const codeWatchPaths = this.getCodeWatchPaths();
    
    if (codeWatchPaths.length === 0) {
      if (process.env.DEBUG_PARSER) {
        console.log('⚠️  No code watch paths configured, skipping code watching');
      }
      return;
    }

    if (process.env.DEBUG_PARSER) {
      console.log(`👀 Watching generated code paths: ${codeWatchPaths.join(', ')}`);
    }

    this.codeWatcher = chokidar.watch(codeWatchPaths, {
      ignored: this.getCodeIgnoredPatterns(),
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 50,
      },
    });

    this.setupCodeEventListeners();
  }

  /**
   * Get paths to watch for generated code changes
   */
  private getCodeWatchPaths(): string[] {
    const paths: string[] = [];

    if (this.enhancedOptions.output) {
      if (this.enhancedOptions.selectiveWatching) {
        // Use selective watching - only watch specific resolver directories
        const defaultResolverDirs = [
          'root-query',
          'root-mutation',
          'root-subscription',
          'types'
        ];

        const resolverDirs = this.enhancedOptions.customResolverDirectories || defaultResolverDirs;
        const resolverPaths = resolverDirs
          .map(dir => path.join(this.enhancedOptions.output!, dir))
          .filter(p => fs.existsSync(p));

        paths.push(...resolverPaths);
        if (process.env.DEBUG_PARSER) {
          console.log(`📁 Selective watching enabled for resolver directories: ${resolverPaths.map(p => path.relative(process.cwd(), p)).join(', ')}`);
        }
      } else {
        // Watch the entire output directory (legacy behavior)
        paths.push(this.enhancedOptions.output);
        if (process.env.DEBUG_PARSER) {
          console.log(`📁 Full directory watching enabled for: ${path.relative(process.cwd(), this.enhancedOptions.output)}`);
        }
      }
    }

    // Add custom code watch patterns
    if (this.enhancedOptions.codeWatchPatterns) {
      paths.push(...this.enhancedOptions.codeWatchPatterns);
      if (process.env.DEBUG_PARSER) {
        console.log(`📁 Custom watch patterns added: ${this.enhancedOptions.codeWatchPatterns.join(', ')}`);
      }
    }

    return paths.map(p => path.resolve(p));
  }

  /**
   * Get patterns to ignore when watching code files
   */
  private getCodeIgnoredPatterns(): (path: string, stats?: any) => boolean {
    return (filePath: string, stats?: any) => {
      const relativePath = path.relative(process.cwd(), filePath);

      // Only watch TypeScript files
      if (stats?.isFile()) {
        const ext = path.extname(filePath);
        if (ext !== '.ts') {
          return true; // Ignore non-TypeScript files
        }

        // Check if we should ignore infrastructure files
        const fileName = path.basename(filePath, '.ts');
        const infrastructureFiles = [
          'index',           // Index files (auto-generated)
          'graphql',         // Generated GraphQL types
          'scalars',         // Generated scalar types
          'resolve-types',   // Generated resolve types
          'schema',          // Generated schema files
        ];

        if (!this.enhancedOptions.watchInfrastructureFiles && infrastructureFiles.includes(fileName)) {
          if (process.env.DEBUG_PARSER) {
            console.log(`   ↳ Ignoring infrastructure file: ${relativePath}`);
          }
          return true; // Ignore these infrastructure files
        }

        // Check custom ignore patterns
        if (this.enhancedOptions.bidirectionalIgnorePatterns) {
          for (const pattern of this.enhancedOptions.bidirectionalIgnorePatterns) {
            if (relativePath.includes(pattern) || fileName.includes(pattern)) {
              if (process.env.DEBUG_PARSER) {
                console.log(`   ↳ Ignoring file matching custom pattern '${pattern}': ${relativePath}`);
              }
              return true;
            }
          }
        }

        // Only watch resolver implementation files
        // These are files that users typically modify
        const isResolverImplementation =
          // Field resolvers (kebab-case names)
          /^[a-z][a-z0-9]*(-[a-z0-9]+)*$/.test(fileName) ||
          // Type resolvers
          fileName === '__resolve-type' ||
          // Test files
          fileName.includes('.test') ||
          fileName.includes('.spec');

        if (!isResolverImplementation) {
          if (process.env.DEBUG_PARSER) {
            console.log(`   ↳ Ignoring non-resolver file: ${relativePath}`);
          }
          return true; // Only watch resolver implementation files
        }
      }

      // Ignore common directories
      const ignorePatterns = [
        'node_modules',
        '.git',
        'dist',
        'build',
        'coverage',
        '__test__',
        '.nyc_output',
      ];

      return ignorePatterns.some(pattern => relativePath.includes(pattern));
    };
  }

  /**
   * Set up event listeners for code file changes
   */
  private setupCodeEventListeners(): void {
    if (!this.codeWatcher) return;

    this.codeWatcher
      .on('change', async (filePath: string) => {
        const relativePath = path.relative(process.cwd(), filePath);
        if (process.env.DEBUG_PARSER) {
          console.log(`📝 Code file changed: ${relativePath}`);
        }
        
        // Check if this is a user-initiated change
        if (this.isUserInitiatedChange(filePath)) {
          await this.handleCodeChange(filePath);
        } else {
          if (process.env.DEBUG_PARSER) {
            console.log(`   ↳ Skipping generator-initiated change`);
          }
        }
      })
      .on('error', (error: unknown) => {
        console.error('❌ Code watcher error:', error);
      });
  }

  /**
   * Handle code file changes and extract patterns
   */
  private async handleCodeChange(filePath: string): Promise<void> {
    if (this.isSyncing) {
      if (process.env.DEBUG_PARSER) {
        console.log('   ↳ Sync in progress, queuing change');
      }
      return;
    }

    try {
      this.isSyncing = true;

      // Analyze the file for patterns
      const patterns = await this.patternAnalyzer.analyzeFile(filePath);

      if (patterns.length === 0) {
        if (process.env.DEBUG_PARSER) {
          console.log('   ↳ No patterns detected');
        }
        return;
      }

      if (process.env.DEBUG_PARSER) {
        console.log(`   ↳ Found ${patterns.length} pattern(s) to sync`);
      }

      // Batch all patterns from this file change together
      await this.syncCoordinator.handleBatchedCodeToSchemaSync(patterns, this.schemaUpdater);

    } catch (error) {
      console.error('❌ Error handling code change:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Override regenerate to add generation session tracking
   */
  protected async regenerate(): Promise<void> {
    // Start generation session
    this.generationSessionId = crypto.randomUUID();
    this.generationInProgress = true;

    if (this.enhancedOptions.debug) {
      UI.info(`Starting generation session: ${this.generationSessionId}`);
    }

    try {
      // Call parent regenerate method
      await super.regenerate();

      // Mark all files in output directory as generator-modified
      await this.markOutputDirectoryAsGenerated();

    } finally {
      // End generation session
      this.generationInProgress = false;
      if (this.enhancedOptions.debug) {
        UI.success(`Generation session completed: ${this.generationSessionId}`);
      }
      this.generationSessionId = null;
    }
  }

  /**
   * Mark all files in the output directory as generator-modified
   */
  private async markOutputDirectoryAsGenerated(): Promise<void> {
    if (!this.enhancedOptions.output || !fs.existsSync(this.enhancedOptions.output)) {
      return;
    }

    try {
      const files = await this.getAllFilesInDirectory(this.enhancedOptions.output);
      this.markAsGeneratorModified(files);
      if (process.env.DEBUG_PARSER) {
        console.log(`📝 Marked ${files.length} files as generator-modified`);
      }
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn('Warning: Could not mark output files as generator-modified:', error);
      }
    }
  }

  /**
   * Get all files in a directory recursively
   */
  private async getAllFilesInDirectory(dirPath: string): Promise<string[]> {
    const files: string[] = [];

    const items = await fs.readdir(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = await fs.stat(fullPath);

      if (stat.isDirectory()) {
        const subFiles = await this.getAllFilesInDirectory(fullPath);
        files.push(...subFiles);
      } else if (stat.isFile()) {
        files.push(fullPath);
      }
    }

    return files;
  }

  /**
   * Check if a file change was initiated by the user (not the generator)
   */
  private isUserInitiatedChange(filePath: string): boolean {
    if (!this.enhancedOptions.preventInfiniteLoops) {
      return true; // Always process if loop prevention is disabled
    }

    // Skip if generation is currently in progress
    if (this.generationInProgress) {
      if (process.env.DEBUG_PARSER) {
        console.log(`   ↳ Skipping change during generation session: ${this.generationSessionId}`);
      }
      return false;
    }

    const now = Date.now();
    const existing = this.changeFingerprints.get(filePath);

    // If we don't have a record, assume it's user-initiated
    if (!existing) {
      this.recordChange(filePath, 'user');
      return true;
    }

    // If the change was recent and from generator, skip it
    const timeDiff = now - existing.timestamp;
    if (timeDiff < 2000 && existing.source === 'generator') {
      return false;
    }

    // Update the record and process as user change
    this.recordChange(filePath, 'user');
    return true;
  }

  /**
   * Record a file change for tracking purposes
   */
  private recordChange(filePath: string, source: 'user' | 'generator'): void {
    try {
      const fs = require('fs');
      const content = fs.readFileSync(filePath, 'utf8');
      const contentHash = crypto.createHash('md5').update(content).digest('hex');

      this.changeFingerprints.set(filePath, {
        filePath,
        contentHash,
        timestamp: Date.now(),
        source,
      });
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Warning: Could not record change for ${filePath}:`, error);
      }
    }
  }

  /**
   * Mark files as generator-modified to prevent processing
   */
  public markAsGeneratorModified(filePaths: string[]): void {
    for (const filePath of filePaths) {
      this.recordChange(filePath, 'generator');
    }
  }
}

/**
 * Enhanced options for multi-mapping bidirectional watch functionality
 */
export interface MultiEnhancedWatchOptions extends Omit<EnhancedWatchOptions, 'schema' | 'output'> {
  /** Schema-output mappings to watch */
  mappings: Array<{
    schema: string;
    output: string;
    name?: string;
  }>;
}

/**
 * Enhanced service for watching multiple schema-output mappings with bidirectional sync
 */
export class MultiEnhancedWatchService {
  private enhancedWatchServices: EnhancedWatchService[] = [];
  private options: MultiEnhancedWatchOptions;
  private isShuttingDown = false;

  constructor(options: MultiEnhancedWatchOptions) {
    this.options = options;

    // Create individual enhanced watch services for each mapping
    this.enhancedWatchServices = options.mappings.map((mapping, index) => {
      const singleOptions: EnhancedWatchOptions = {
        ...options,
        schema: mapping.schema,
        output: mapping.output,
      };

      return new EnhancedWatchService(singleOptions);
    });
  }

  /**
   * Start enhanced watching for all schema-output mappings
   */
  async start(): Promise<void> {
    if (this.enhancedWatchServices.length === 0) {
      if (process.env.DEBUG_PARSER) {
        console.log('No schema-output mappings to watch');
      }
      return;
    }

    if (process.env.DEBUG_PARSER) {
      console.log(`🔄 Starting Multi-Enhanced Watch Service for ${this.enhancedWatchServices.length} mapping(s)...`);
    }

    // Start all enhanced watch services concurrently
    await Promise.all(this.enhancedWatchServices.map(async (service, index) => {
      const mapping = this.options.mappings[index];
      const mappingName = mapping.name || `mapping-${index + 1}`;

      try {
        if (process.env.DEBUG_PARSER) {
          console.log(`   Starting enhanced watch for ${mappingName}...`);
        }
        await service.start();
        if (process.env.DEBUG_PARSER) {
          console.log(`   ✅ ${mappingName} enhanced watch started`);
        }
      } catch (error) {
        console.error(`   ❌ Failed to start enhanced watch for ${mappingName}:`, error);
        throw error;
      }
    }));

    // Set up graceful shutdown
    this.setupShutdownHandlers();

    console.log(`✅ Multi-Enhanced Watch Service started successfully for ${this.enhancedWatchServices.length} mapping(s)`);
    if (process.env.DEBUG_PARSER) {
      console.log('   Press Ctrl+C to stop watching');
    }
  }

  /**
   * Stop enhanced watching for all mappings
   */
  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    if (process.env.DEBUG_PARSER) {
      console.log('\n🛑 Stopping Multi-Enhanced Watch Service...');
    }

    // Stop all enhanced watch services
    await Promise.all(this.enhancedWatchServices.map(async (service, index) => {
      const mapping = this.options.mappings[index];
      const mappingName = mapping.name || `mapping-${index + 1}`;

      try {
        await service.stop();
        if (process.env.DEBUG_PARSER) {
          console.log(`   ${mappingName} enhanced watch stopped`);
        }
      } catch (error) {
        console.error(`   Error stopping enhanced watch for ${mappingName}:`, error);
      }
    }));

    if (process.env.DEBUG_PARSER) {
      console.log('👋 Multi-Enhanced Watch Service stopped');
    }
  }

  /**
   * Set up graceful shutdown handlers
   */
  private setupShutdownHandlers(): void {
    this.shutdownHandler = this.shutdownHandler.bind(this);
    process.on('SIGINT', this.shutdownHandler);
    process.on('SIGTERM', this.shutdownHandler);
  }

  /**
   * Handle shutdown signals
   */
  private async shutdownHandler(): Promise<void> {
    await this.stop();
    process.exit(0);
  }
}
