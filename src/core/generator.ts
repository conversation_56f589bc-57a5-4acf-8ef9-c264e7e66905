import path from 'path';
import { verifyGeneratedFile } from '@utils/file-verifier';
import { loadAndMergeSchemas, SchemaLoadingError } from '@utils/schema-loader';
import { SchemaBasedGenerator } from '@generators/index';

export interface GeneratorOptions {
    schema: string;
    output: string;
    force?: boolean;
    skipCodegen?: boolean;
    context?: string;
    contextName?: string;
    test?: boolean;
    testOutput?: string;
    testStyle?: 'grouped' | 'individual';
    debug?: boolean;
    applyAliases?: boolean;
    // Watch-specific options (optional for backward compatibility)
    debounce?: number;
    initial?: boolean;
    watchPaths?: string[];
    // Decorator options
    codebaseDir?: string;
    enableDecorators?: boolean;
    // Schema identifier for multi-schema support
    schemaId?: string;
    // Import alias configuration
    aliasCodebase?: string;
}

/**
 * Schema-output mapping for multiple directory support
 */
export interface SchemaOutputMapping {
    schema: string;
    output: string;
    name?: string; // Optional identifier for the mapping
}

/**
 * Extended options for multiple schema-output mappings
 */
export interface MultiGeneratorOptions extends Omit<GeneratorOptions, 'schema' | 'output'> {
    mappings: SchemaOutputMapping[];
}

// Function for the schema-based generation
export const generateSchemaBasedCode = async (options: GeneratorOptions): Promise<void> => {
    console.log('Running schema-based code generation...');
    const { debug = false } = options;

    // Load and merge schemas
    let executableSchema;
    try {
        const result = loadAndMergeSchemas(options.schema, debug);
        executableSchema = result.executableSchema;
    } catch (error) {
        if (error instanceof SchemaLoadingError) {
            // Schema loading errors are already formatted and logged
            throw new Error(`Schema loading failed: ${error.message}`);
        }
        // Re-throw other errors as-is
        throw error;
    }

    // Process test output path - replace {{output}} placeholder
    let testOutputPath = options.testOutput;
    if (testOutputPath && testOutputPath.includes('{{output}}')) {
        testOutputPath = testOutputPath.replace('{{output}}', options.output);
    }

    // Create the schema-based generator
    const generator = new SchemaBasedGenerator(executableSchema, {
        schema: options.schema,
        output: options.output,
        force: options.force,
        context: options.context,
        contextName: options.contextName,
        generateTests: options.test,
        testOutput: testOutputPath,
        individualTests: options.testStyle === 'individual',
        debug: options.debug,
        applyAliases: options.applyAliases,
        codebaseDir: options.codebaseDir,
        enableDecorators: options.enableDecorators,
        schemaId: options.schemaId,
        aliasCodebase: options.aliasCodebase
    });

    // Generate the code
    await generator.generate();

    // Verify the generated graphql.ts file
    const graphqlTsPath = path.join(options.output, 'graphql.ts');
    verifyGeneratedFile(graphqlTsPath, debug);

    console.log('Schema-based code generation completed successfully!');
};

/**
 * Generate code for multiple schema-output mappings
 */
export const generateMultipleSchemas = async (options: MultiGeneratorOptions): Promise<void> => {
    console.log(`🚀 Starting generation for ${options.mappings.length} schema-output mapping(s)...`);

    for (let i = 0; i < options.mappings.length; i++) {
        const mapping = options.mappings[i];
        const mappingName = mapping.name || `mapping-${i + 1}`;

        console.log(`\n📦 Processing ${mappingName} (${i + 1}/${options.mappings.length}):`);
        console.log(`   Schema: ${mapping.schema}`);
        console.log(`   Output: ${mapping.output}`);

        // Create single generator options for this mapping
        const singleOptions: GeneratorOptions = {
            ...options,
            schema: mapping.schema,
            output: mapping.output,
        };

        try {
            await generateSchemaBasedCode(singleOptions);
            console.log(`✅ ${mappingName} completed successfully`);
        } catch (error) {
            console.error(`❌ ${mappingName} failed:`, error);
            throw error;
        }
    }

    console.log(`\n🎉 All ${options.mappings.length} schema-output mapping(s) completed successfully!`);
};