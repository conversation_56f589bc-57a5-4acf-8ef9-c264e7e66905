import path from 'path';
import { verifyGeneratedFile } from '@utils/file-verifier';
import { runGraphQLCodegen, runOptimizedGraphQLCodegen, debugLog } from './codegen';
import type { GeneratorOptions, MultiGeneratorOptions } from './generator';
import { generateSchemaBasedCode, generateMultipleSchemas } from './generator';

/**
 * Main function to run all generation steps
 * @param options Generator options
 */
export const generateAll = async (options: GeneratorOptions) => {
    const { debug = false } = options;
    if (debug) {
        console.log('Generating all artifacts...');
    }
    debugLog(debug, 'Starting generation with options:', JSON.stringify(options, null, 2));

    try {
        // Run GraphQL CodeGen if not skipped - use optimized version by default
        if (!options.skipCodegen) {
            const useOptimizedCodegen = process.env.ENABLE_OPTIMIZED_CODEGEN !== 'false';

            if (useOptimizedCodegen) {
                if (debug) {
                    console.log('🚀 Using Phase 2 optimized CodeGen with direct API integration...');
                }
                await runOptimizedGraphQLCodegen(options);
            } else {
                if (debug) {
                    console.log('📦 Using legacy CodeGen with child process...');
                }
                await runGraphQLCodegen(options);
            }
        }

        // Always use the schema-based generator
        await generateSchemaBasedCode(options);

        // Final verification of all output files
        const graphqlTsPath = path.join(options.output, 'graphql.ts');
        verifyGeneratedFile(graphqlTsPath, debug);

        if (debug) {
            console.log('All artifacts generated successfully!');
        }
    } catch (error: any) {
        console.error('Error generating artifacts:', error);
        throw error;
    }
}

/**
 * Main function to run generation for multiple schema-output mappings
 * @param options Multi-generator options with mappings
 */
export const generateAllMultiple = async (options: MultiGeneratorOptions) => {
    const { debug = false } = options;
    if (debug) {
        console.log('Generating all artifacts for multiple mappings...');
    }
    debugLog(debug, 'Starting multi-generation with options:', JSON.stringify(options, null, 2));

    try {
        const useOptimizedCodegen = process.env.ENABLE_OPTIMIZED_CODEGEN !== 'false';
        const useParallelProcessing = process.env.ENABLE_PARALLEL_PROCESSING !== 'false' && options.mappings.length > 1;

        if (useOptimizedCodegen && useParallelProcessing && !options.skipCodegen) {
            // Phase 2 Optimization: Parallel CodeGen processing for multiple schemas
            if (debug) {
                console.log(`🚀 Processing ${options.mappings.length} schemas in parallel with optimized CodeGen...`);
            }

            const { getGlobalOptimizedCodeGenService } = require('./optimized-codegen');
            const optimizedService = getGlobalOptimizedCodeGenService();

            // Prepare all CodeGen options
            const codegenOptions = options.mappings.map(mapping => ({
                ...options,
                schema: mapping.schema,
                output: mapping.output,
                enableCaching: process.env.ENABLE_CODEGEN_CACHING !== 'false',
                enableParallelProcessing: true
            }));

            // Run parallel CodeGen
            const codegenResults = await optimizedService.generateMultipleTypes(codegenOptions);

            // Report results
            const successCount = codegenResults.filter((r: any) => r.success).length;
            const cacheHits = codegenResults.filter((r: any) => r.fromCache).length;

            if (debug) {
                console.log(`✅ CodeGen completed: ${successCount}/${options.mappings.length} successful, ${cacheHits} from cache`);
            }
        }

        // Process each mapping for schema-based generation
        for (let i = 0; i < options.mappings.length; i++) {
            const mapping = options.mappings[i];
            const mappingName = mapping.name || `mapping-${i + 1}`;

            if (debug) {
                console.log(`\n🔄 Processing ${mappingName} (${i + 1}/${options.mappings.length})...`);
            }

            // Create single generator options for this mapping
            const singleOptions: GeneratorOptions = {
                ...options,
                schema: mapping.schema,
                output: mapping.output,
            };

            // Run GraphQL CodeGen if not skipped and not already processed in parallel
            if (!options.skipCodegen && !(useOptimizedCodegen && useParallelProcessing)) {
                if (useOptimizedCodegen) {
                    await runOptimizedGraphQLCodegen(singleOptions);
                } else {
                    await runGraphQLCodegen(singleOptions);
                }
            }

            // Always use the schema-based generator
            await generateSchemaBasedCode(singleOptions);

            // Final verification of output files
            const graphqlTsPath = path.join(mapping.output, 'graphql.ts');
            verifyGeneratedFile(graphqlTsPath, debug);

            if (debug) {
                console.log(`✅ ${mappingName} artifacts generated successfully!`);
            }
        }

        if (debug) {
            console.log(`\n🎉 All ${options.mappings.length} mapping(s) completed successfully!`);
        }
    } catch (error: any) {
        console.error('Error generating artifacts for multiple mappings:', error);
        throw error;
    }
}