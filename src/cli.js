#!/usr/bin/env node

// This is a cross-platform wrapper script that ensures the CLI tool works on all platforms
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Log helpful diagnostic information in verbose mode or when there are issues
const isVerbose = process.argv.includes('--verbose') || process.env.GQL_GENERATOR_VERBOSE === 'true';

function logDebug(message) {
  if (isVerbose) {
    console.log(`[DEBUG] ${message}`);
  }
}

// Platform detection for platform-specific handling
const isWindows = os.platform() === 'win32';
logDebug(`Operating System: ${os.platform()} (${os.release()})`);
logDebug(`Node.js Version: ${process.version}`);

// Determine the path to the actual index.js file
// Check multiple possible locations in priority order
const possiblePaths = [
  path.resolve(__dirname, '../dist/src/index.js'),       // npm installed globally or locally
  path.resolve(__dirname, './index.js'),                 // local development
  path.resolve(__dirname, '../lib/index.js'),            // alternate build directory
  path.resolve(__dirname, '../build/src/index.js'),      // alternate build directory
  path.resolve(__dirname, '../index.js'),                // alternative local structure
  path.resolve(__dirname, '../src/index.js')             // source directory
];

// Find the first path that exists
let targetPath = null;
let checkedPaths = [];

for (const checkPath of possiblePaths) {
  checkedPaths.push(checkPath);
  logDebug(`Checking for CLI script at: ${checkPath}`);
  
  if (fs.existsSync(checkPath)) {
    targetPath = checkPath;
    logDebug(`Found CLI script at: ${checkPath}`);
    break;
  }
}

// If no valid path found, report error with diagnostic information
if (!targetPath) {
  console.error('Error: Could not find index.js file. Please ensure the package is installed correctly.');
  console.error('Checked the following locations:');
  checkedPaths.forEach(p => console.error(` - ${p}`));
  console.error('\nTry running "npm run build" before executing the CLI.');
  process.exit(1);
}

// Additional check to ensure the file is actually executable/readable
try {
  fs.accessSync(targetPath, fs.constants.R_OK);
  logDebug(`Verified read access to: ${targetPath}`);
} catch (err) {
  console.error(`Error: Cannot read the CLI script at ${targetPath}`);
  console.error(`Details: ${err.message}`);
  process.exit(1);
}

// Use the cross-platform spawn method to run the CLI
// On Windows this doesn't require the file to be executable
logDebug(`Executing CLI from: ${targetPath}`);
logDebug(`CLI arguments: ${process.argv.slice(2).join(' ')}`);

const cli = spawn(process.execPath, [targetPath, ...process.argv.slice(2)], {
  stdio: 'inherit',
  shell: false, // More secure, use shell:true only if necessary for Windows compatibility
  windowsHide: true // Prevent command window from showing on Windows
});

// Forward exit code properly
cli.on('close', (code, signal) => {
  if (signal) {
    logDebug(`Process exited due to signal: ${signal}`);
    // On Unix platforms, add 128 to the signal code by convention
    process.exit(isWindows ? 1 : 128 + (typeof signal === 'number' ? signal : 0));
  } else {
    logDebug(`Process exited with code: ${code}`);
    process.exit(code || 0);
  }
});

// Handle errors with specific error codes for different failure modes
cli.on('error', (err) => {
  console.error(`Failed to start CLI process: ${err.message}`);
  
  // Provide specific exit codes for different error types
  if (err.code === 'ENOENT') {
    console.error('Node executable not found. Please ensure Node.js is installed correctly.');
    process.exit(127); // Command not found
  } else if (err.code === 'EACCES') {
    console.error('Permission denied when executing the CLI script.');
    process.exit(126); // Permission problem
  } else {
    process.exit(1);
  }
});

// Forward signals properly with safety checks
process.on('SIGINT', () => {
  try {
    cli.kill('SIGINT');
  } catch (e) {
    logDebug(`Error sending SIGINT: ${e.message}`);
    process.exit(1);
  }
});

process.on('SIGTERM', () => {
  try {
    cli.kill('SIGTERM');
  } catch (e) {
    logDebug(`Error sending SIGTERM: ${e.message}`);
    process.exit(1);
  }
});

// Add handler for Windows-specific signals
if (isWindows) {
  process.on('SIGBREAK', () => {
    try {
      cli.kill('SIGBREAK');
    } catch (e) {
      logDebug(`Error sending SIGBREAK: ${e.message}`);
      process.exit(1);
    }
  });
} 