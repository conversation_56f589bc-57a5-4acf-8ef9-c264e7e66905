/**
 * Context interface for GraphQL resolvers and mutators
 * This file was auto-generated as a default context implementation.
 * You should modify it to fit your application's needs.
 */

/**
 * Context interface for GraphQL resolvers and mutators
 * Contains authentication, data sources, and other context information
 */
export interface Context {
  userId?: string;
  isAuthenticated: boolean;
  // Add any other context properties needed for your application
  // Example: dataSources, services, etc.
  // For example: user?: User;
  // Or data source clients: db: Database;
}

/**
 * Create a Context instance with default values
 * @param options - Optional partial Context properties
 * @returns A complete Context object
 */
export const createContext = (options?: Partial<Context>): Context => {
  return {
    isAuthenticated: false,
    ...options
  };
};
