import 'module-alias/register';

// Export functions for use as a module
export { setupContext } from './generate-types';
export { parseTypeScriptFile, generatePreservedImplementation } from './utils/ts-parser';
export { Context, createContext } from './context';

// Export core functionality
export { generateAll, generateAllMultiple } from './core/generator-orchestrator';
export { generateSchemaBasedCode, generateMultipleSchemas } from './core/generator';
export { runGraphQLCodegen } from './core/codegen';
export { WatchService, MultiWatchService } from './core/watch-service';
export { EnhancedWatchService, MultiEnhancedWatchService } from './core/enhanced-watch-service';

// Export template optimization functionality
export {
  initializeTemplateOptimization,
  getGlobalTemplateOptimizationIntegration,
  TemplateOptimizationConfig,
  TemplateOptimizationResult
} from './utils/template-optimization-integration';
export {
  getGlobalTemplateCache,
  TemplateCompilationCache,
  CacheOptions,
  CacheStatistics
} from './utils/template-compilation-cache';
export {
  getGlobalTemplatePerformanceMonitor,
  TemplatePerformanceMonitor,
  TemplatePerformanceMetrics
} from './utils/template-performance-monitor';
export {
  getGlobalTemplateDataOptimizer,
  TemplateDataOptimizer,
  TemplateDataOptimizerConfig
} from './utils/template-data-optimizer';

// Export Phase 2 optimization functionality
export {
  getGlobalOptimizedCodeGenService,
  OptimizedCodeGenService,
  type OptimizedCodeGenOptions,
  type CodeGenResult
} from './core/optimized-codegen';
export {
  getGlobalStreamingRenderer,
  StreamingTemplateRenderer,
  type StreamingRenderOptions,
  type TemplateChunk
} from './utils/streaming-template-renderer';
export {
  getGlobalMemoryMonitor,
  MemoryPressureMonitor,
  type MemoryPressureConfig,
  type MemoryMetrics
} from './utils/memory-pressure-monitor';
export {
  getGlobalStreamingBatchProcessor,
  StreamingBatchProcessor,
  type StreamingBatchConfig
} from './utils/streaming-batch-integration';
export {
  getGlobalLargeSchemaOptimizer,
  LargeSchemaOptimizer,
  type LargeSchemaConfig
} from './utils/large-schema-optimizer';
export {
  getGlobalDependencyGraph,
  DependencyGraph,
  type DependencyNode,
  type DependencyChange
} from './utils/dependency-graph';
export {
  getGlobalSmartCacheInvalidation,
  SmartCacheInvalidation,
  type CacheInvalidationConfig
} from './utils/smart-cache-invalidation';
export {
  getGlobalIncrementalTypeProcessor,
  IncrementalTypeProcessor,
  type IncrementalConfig,
  type TypeDefinition
} from './utils/incremental-type-processor';

// Export performance optimization functionality
export {
  getGlobalPerformanceOrchestrator,
  PerformanceOrchestrator,
  type PerformanceConfig,
  type PerformanceMetrics,
  type PerformanceReport,
  // Backward compatibility - deprecated
  getGlobalPhase3Optimizer
} from './utils/performance-orchestrator';
export {
  getGlobalWASMBridge,
  WASMBridge,
  type WASMConfig,
  type WASMPerformanceMetrics
} from './utils/wasm-bridge';
export {
  getGlobalWASMPerformanceMonitor,
  WASMPerformanceMonitor,
  type WASMPerformanceConfig
} from './utils/wasm-performance-monitor';
export {
  getGlobalMemoryMappedFileReader,
  MemoryMappedFileReader,
  type MemoryMappedConfig
} from './utils/memory-mapped-file-reader';
export {
  getGlobalPlatformCompatibility,
  PlatformCompatibility,
  type PlatformInfo,
  type CompatibilityFeatures
} from './utils/platform-compatibility';
export {
  runPerformanceIntegrationTests,
  PerformanceIntegrationTest,
  type PerformanceTestReport,
  // Backward compatibility - deprecated
  runPhase3IntegrationTests
} from './utils/performance-integration-test';

// Default CLI export
export default async function cli() {
  const { createProgram } = require('./cli/commands');
  const program = createProgram();
  program.parse();
}

// Run CLI if this module is executed directly
if (require.main === module) {
  cli();
} 