import type { FieldDirectiveField } from './directive-parser';
import { DirectiveParser } from './directive-parser';
import { convertGraphQLTypeToTypeScript } from './type-converters';

/**
 * Processes @field directives
 */
export class FieldDirectiveProcessor {
  /**
   * Extract field directive fields from directives
   * @param directives The directive container
   * @returns Array of field directive fields
   */
  public static extractFieldDirectiveFields(directives: {
    fieldFields: FieldDirectiveField[];
  }): FieldDirectiveField[] {
    return directives.fieldFields || [];
  }

  /**
   * Get field-level @field override for a specific field
   * @param typeName The name of the type
   * @param fieldName The name of the field
   * @param schemaFilePath The path to the schema file
   * @param debug Enable debug logging
   * @returns The field override or null if none exists
   */
  public static async getFieldOverride(
    typeName: string,
    fieldName: string,
    schemaFilePath: string,
    debug: boolean = false
  ): Promise<FieldDirectiveField | null> {
    try {
      const directives = await DirectiveParser.extractDirectivesFromSchema(
        schemaFilePath,
        typeName,
        fieldName,
        debug
      );

      if (directives.fieldFields.length > 0) {
        return directives.fieldFields[0];
      }
      return null;
    } catch (error) {
      console.error(`Error getting field @field override: ${error}`);
      return null;
    }
  }

  /**
   * Generate import statements for field directive fields
   * @param fieldDirectiveFields Array of field directive fields
   * @returns Array of import statements
   */
  public static generateImports(fieldDirectiveFields: FieldDirectiveField[]): string[] {
    const imports: string[] = [];
    const typesByImportPath: Record<string, Set<string>> = {};

    for (const field of fieldDirectiveFields) {
      // Handle both importPath and path properties
      const importPath = field.importPath || field.path;

      if (importPath) {
        // Use Set to automatically deduplicate types for each import path
        if (!typesByImportPath[importPath]) {
          typesByImportPath[importPath] = new Set<string>();
        }

        if (field.exportedName && field.exportedName !== field.type) {
          // Named export with different name - extract base type name for import
          const baseExportedName = this.extractBaseTypeName(field.exportedName);
          const baseTypeName = this.extractBaseTypeName(field.type);
          typesByImportPath[importPath].add(`${baseExportedName} as ${baseTypeName}`);
        } else {
          // Named export with same name - extract base type name for import
          const baseTypeName = this.extractBaseTypeName(field.type);
          typesByImportPath[importPath].add(baseTypeName);
        }
      }
    }

    // Generate import statements
    for (const importPath in typesByImportPath) {
      const typesSet = typesByImportPath[importPath];
      const typesArray = Array.from(typesSet).sort(); // Convert Set to sorted array
      // Clean up the import path by removing quotes
      const cleanImportPath = importPath.replace(/^["']|["']$/g, '');
      imports.push(`import { ${typesArray.join(', ')} } from '${cleanImportPath}';`);
    }

    return imports;
  }

  /**
   * Extract the base type name from a complex type specification
   * Removes array brackets, generic parameters, and quotes to get the importable type name
   * @param typeName The full type specification (e.g., "CustomJSON[]", "ComplexType<T>", "string | null")
   * @returns The base type name suitable for importing (e.g., "CustomJSON", "ComplexType")
   */
  private static extractBaseTypeName(typeName: string): string {
    // Remove quotes
    let cleanName = typeName.replace(/^["']|["']$/g, '');

    // Remove array brackets from the end (e.g., "CustomJSON[]" -> "CustomJSON")
    cleanName = cleanName.replace(/\[\]$/, '');

    // Remove generic type parameters (e.g., "ComplexType<T>" -> "ComplexType")
    cleanName = cleanName.replace(/<[^>]*>/, '');

    // For union types, take the first non-null type (e.g., "string | null" -> "string")
    if (cleanName.includes('|')) {
      const parts = cleanName.split('|').map(part => part.trim());
      // Find the first part that's not 'null', 'undefined', or empty
      const nonNullPart = parts.find(part => part && part !== 'null' && part !== 'undefined');
      if (nonNullPart) {
        cleanName = nonNullPart;
      }
    }

    // Remove any remaining whitespace
    cleanName = cleanName.trim();

    return cleanName;
  }

  /**
   * Generate TypeScript field definitions from field directive fields
   * @param fieldDirectiveFields Array of field directive fields
   * @returns String containing TypeScript field definitions
   */
  public static generateTypeScriptFields(fieldDirectiveFields: FieldDirectiveField[]): string {
    const fieldDefs: string[] = [];

    for (const field of fieldDirectiveFields) {
      const tsType = convertGraphQLTypeToTypeScript(field.type);
      fieldDefs.push(`  ${field.name}: ${tsType};`);
    }

    return fieldDefs.join('\n');
  }

  // Backward compatibility methods
  /**
   * @deprecated Use extractFieldDirectiveFields instead
   */
  public static extractHiddenFields(directives: {
    fieldFields: FieldDirectiveField[];
  }): FieldDirectiveField[] {
    return this.extractFieldDirectiveFields(directives);
  }
}
