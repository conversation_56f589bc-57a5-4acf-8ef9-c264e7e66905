import type { GraphQLSchema} from 'graphql';
import { GraphQLObjectType, GraphQLInterfaceType } from 'graphql';
import { DirectiveParser, type DirectiveContainer } from './directive-parser';
import { DirectiveProcessor } from './directive-processor';
import type { SchemaMapper } from './schema-mapper';
import type { DecoratorContainer } from './decorator-parser';
import { DecoratorProcessor } from './decorator-processor';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from './decorator-types';
import { AliasConfig, AliasConfigUtils } from './alias-config';
import * as path from 'path';

/**
 * Interface inheritance information
 */
export interface InterfaceInheritanceInfo {
  interfaceName: string;
  fieldName: string;
  methodCall: string;
  directives: DirectiveContainer;
  imports?: string[];
}

/**
 * Utility class for handling interface inheritance in GraphQL schema
 */
export class InterfaceInheritanceHandler {
  private methodCallCache = new Map<string, string | null>();
  private inheritanceInfoCache = new Map<string, InterfaceInheritanceInfo | null>();
  // New caches for multi-deep inheritance support
  private allInterfacesCache = new Map<string, string[]>();
  private circularDependencyCache = new Set<string>();

  // Decorator support
  private decoratorMetadata: DecoratorContainer | null = null;
  private decoratorProcessor: DecoratorProcessor | null = null;

  constructor(
    private schema: GraphQLSchema,
    private schemaMapper: SchemaMapper
  ) {}

  /**
   * Set decorator metadata for inheritance processing
   * @param decoratorMetadata The decorator metadata from codebase scanning
   */
  public setDecoratorMetadata(decoratorMetadata: DecoratorContainer): void {
    this.decoratorMetadata = decoratorMetadata;

    // Initialize decorator processor if not already done
    if (!this.decoratorProcessor) {
      this.decoratorProcessor = new DecoratorProcessor(
        DEFAULT_DECORATOR_CONFIG,
        DEFAULT_PRECEDENCE_RULES
      );
    }
  }

  /**
   * Set alias configuration for import path generation
   * @param codebaseDir The codebase directory path
   * @param aliasCodebase The alias for codebase imports
   */
  public setAliasConfig(codebaseDir?: string, aliasCodebase?: string): void {
    if (this.decoratorProcessor) {
      const aliasConfig = AliasConfigUtils.createAliasConfig(codebaseDir, aliasCodebase);
      this.decoratorProcessor.setAliasConfig(aliasConfig || undefined);
    }
  }

  /**
   * Get all interfaces that a given type implements (direct interfaces only)
   * @param typeName The name of the type to check
   * @returns Array of interface names that the type implements directly
   */
  public getImplementedInterfaces(typeName: string): string[] {
    const type = this.schema.getType(typeName);

    if (!type || !(type instanceof GraphQLObjectType)) {
      return [];
    }

    return type.getInterfaces().map(iface => iface.name);
  }

  /**
   * Get all interfaces that a given type or interface implements recursively
   * This includes interface-to-interface inheritance chains
   * @param typeName The name of the type or interface to check
   * @param maxDepth Maximum depth to traverse (default: 10, prevents infinite loops)
   * @returns Array of interface names including all inherited interfaces
   */
  public getAllInterfacesRecursively(typeName: string, maxDepth: number = 10): string[] {
    const cacheKey = `${typeName}:${maxDepth}`;

    // Check cache first
    if (this.allInterfacesCache.has(cacheKey)) {
      return this.allInterfacesCache.get(cacheKey) || [];
    }

    const result = this._getAllInterfacesRecursivelyInternal(typeName, maxDepth, new Set(), 0);

    // Cache the result
    this.allInterfacesCache.set(cacheKey, result);

    return result;
  }

  /**
   * Internal recursive method for interface discovery
   * @param typeName Current type or interface name
   * @param maxDepth Maximum depth allowed
   * @param visited Set of visited types to detect cycles
   * @param currentDepth Current recursion depth
   * @returns Array of all interface names in the inheritance chain
   */
  private _getAllInterfacesRecursivelyInternal(
    typeName: string,
    maxDepth: number,
    visited: Set<string>,
    currentDepth: number
  ): string[] {
    // Prevent infinite recursion
    if (currentDepth >= maxDepth) {
      console.warn(`⚠️  Maximum inheritance depth (${maxDepth}) reached for ${typeName}`);
      return [];
    }

    // Detect circular dependencies
    if (visited.has(typeName)) {
      console.warn(`⚠️  Circular dependency detected in interface inheritance: ${typeName}`);
      this.circularDependencyCache.add(typeName);
      return [];
    }

    // Add current type to visited set
    const newVisited = new Set(visited);
    newVisited.add(typeName);

    const allInterfaces: string[] = [];
    const type = this.schema.getType(typeName);

    if (!type) {
      return [];
    }

    // Handle object types (get their direct interfaces)
    if (type instanceof GraphQLObjectType) {
      const directInterfaces = type.getInterfaces().map(iface => iface.name);
      allInterfaces.push(...directInterfaces);

      // Recursively get interfaces that these interfaces implement
      for (const interfaceName of directInterfaces) {
        const nestedInterfaces = this._getAllInterfacesRecursivelyInternal(
          interfaceName,
          maxDepth,
          newVisited,
          currentDepth + 1
        );
        allInterfaces.push(...nestedInterfaces);
      }
    }
    // Handle interface types (get interfaces they implement)
    else if (type instanceof GraphQLInterfaceType) {
      const implementedInterfaces = type.getInterfaces().map(iface => iface.name);
      allInterfaces.push(...implementedInterfaces);

      // Recursively get interfaces that these interfaces implement
      for (const interfaceName of implementedInterfaces) {
        const nestedInterfaces = this._getAllInterfacesRecursivelyInternal(
          interfaceName,
          maxDepth,
          newVisited,
          currentDepth + 1
        );
        allInterfaces.push(...nestedInterfaces);
      }
    }

    // Remove duplicates and return
    return [...new Set(allInterfaces)];
  }

  /**
   * Check if a type has circular dependencies in its interface inheritance
   * @param typeName The name of the type to check
   * @returns True if circular dependencies are detected
   */
  public hasCircularDependency(typeName: string): boolean {
    return this.circularDependencyCache.has(typeName);
  }

  /**
   * Get all types with detected circular dependencies
   * @returns Array of type names with circular dependencies
   */
  public getTypesWithCircularDependencies(): string[] {
    return Array.from(this.circularDependencyCache);
  }

  /**
   * Validate interface inheritance chain for a type
   * @param typeName The name of the type to validate
   * @returns Validation result with details
   */
  public validateInterfaceInheritance(typeName: string): {
    isValid: boolean;
    hasCircularDependency: boolean;
    maxDepthExceeded: boolean;
    allInterfaces: string[];
    errors: string[];
  } {
    const errors: string[] = [];
    let hasCircularDependency = false;
    let maxDepthExceeded = false;

    // Clear any previous circular dependency cache for this type
    this.circularDependencyCache.delete(typeName);

    // Try to get all interfaces with a reasonable depth limit
    const allInterfaces = this.getAllInterfacesRecursively(typeName, 20);

    // Check if circular dependency was detected during traversal
    if (this.circularDependencyCache.has(typeName)) {
      hasCircularDependency = true;
      errors.push(`Circular dependency detected in interface inheritance for ${typeName}`);
    }

    // Check if we might have hit depth limits (heuristic)
    if (allInterfaces.length > 15) {
      maxDepthExceeded = true;
      errors.push(`Possible maximum depth exceeded for ${typeName} (${allInterfaces.length} interfaces found)`);
    }

    const isValid = !hasCircularDependency && !maxDepthExceeded;

    return {
      isValid,
      hasCircularDependency,
      maxDepthExceeded,
      allInterfaces,
      errors
    };
  }

  /**
   * Check if a type implements a specific interface
   * @param typeName The name of the type to check
   * @param interfaceName The name of the interface to check for
   * @returns True if the type implements the interface
   */
  public implementsInterface(typeName: string, interfaceName: string): boolean {
    const implementedInterfaces = this.getImplementedInterfaces(typeName);
    return implementedInterfaces.includes(interfaceName);
  }

  /**
   * Get method call from an interface field that can be inherited
   * @param interfaceName The name of the interface
   * @param fieldName The name of the field
   * @returns The method call string if found, null otherwise
   */
  public async getInterfaceFieldMethodCall(
    interfaceName: string,
    fieldName: string
  ): Promise<string | null> {
    const cacheKey = `${interfaceName}.${fieldName}`;

    // Check cache first
    if (this.methodCallCache.has(cacheKey)) {
      return this.methodCallCache.get(cacheKey) || null;
    }

    try {
      // Get the interface type location
      const typeLocation = this.schemaMapper.getTypeLocation(interfaceName);
      if (!typeLocation) {
        this.methodCallCache.set(cacheKey, null);
        return null;
      }

      // Get absolute path to the schema file
      const absoluteSchemaPath = path.resolve(
        this.schemaMapper.schemaRoot,
        typeLocation.sourceFile
      );

      // Extract field-level directives from the interface
      const fieldDirectives = await DirectiveParser.extractDirectivesFromSchema(
        absoluteSchemaPath,
        interfaceName,
        fieldName
      );

      // Extract method call from directives
      const methodCall = DirectiveProcessor.extractMethodCall(fieldDirectives);

      // Cache the result
      this.methodCallCache.set(cacheKey, methodCall);

      return methodCall;
    } catch (error) {
      console.error(`Error getting interface field method call: ${error}`);
      this.methodCallCache.set(cacheKey, null);
      return null;
    }
  }

  /**
   * Get complete interface inheritance information for a field
   * @param interfaceName The name of the interface
   * @param fieldName The name of the field
   * @returns Interface inheritance info if method call exists, null otherwise
   */
  public async getInterfaceInheritanceInfo(
    interfaceName: string,
    fieldName: string
  ): Promise<InterfaceInheritanceInfo | null> {
    const cacheKey = `${interfaceName}.${fieldName}.info`;

    // Check cache first
    if (this.inheritanceInfoCache.has(cacheKey)) {
      return this.inheritanceInfoCache.get(cacheKey) || null;
    }

    try {
      // Get the interface type location
      const typeLocation = this.schemaMapper.getTypeLocation(interfaceName);
      if (!typeLocation) {
        this.inheritanceInfoCache.set(cacheKey, null);
        return null;
      }

      // Get absolute path to the schema file
      const absoluteSchemaPath = path.resolve(
        this.schemaMapper.schemaRoot,
        typeLocation.sourceFile
      );

      // Extract field-level directives from the interface
      const fieldDirectives = await DirectiveParser.extractDirectivesFromSchema(
        absoluteSchemaPath,
        interfaceName,
        fieldName
      );

      // Extract method call from directives
      const methodCall = DirectiveProcessor.extractMethodCall(fieldDirectives);

      if (!methodCall) {
        this.inheritanceInfoCache.set(cacheKey, null);
        return null;
      }

      const inheritanceInfo: InterfaceInheritanceInfo = {
        interfaceName,
        fieldName,
        methodCall,
        directives: fieldDirectives
      };

      // Cache the result
      this.inheritanceInfoCache.set(cacheKey, inheritanceInfo);

      return inheritanceInfo;
    } catch (error) {
      console.error(`Error getting interface inheritance info: ${error}`);
      this.inheritanceInfoCache.set(cacheKey, null);
      return null;
    }
  }

  /**
   * Get interface field directives for inheritance (supports @import-only inheritance)
   * Now supports both comment-based and decorator-sourced directives
   * @param interfaceName The name of the interface
   * @param fieldName The name of the field
   * @returns Interface field directives if any directives exist, null otherwise
   */
  public async getInterfaceFieldDirectives(
    interfaceName: string,
    fieldName: string
  ): Promise<DirectiveContainer | null> {
    try {
      // Get comment-based directives from schema files
      const commentDirectives = await this.getCommentBasedDirectives(interfaceName, fieldName);

      // Get decorator-sourced directives
      const decoratorDirectives = this.getDecoratorBasedDirectives(interfaceName, fieldName);

      // Merge directives with precedence rules (decorators take precedence)
      const mergedDirectives = this.mergeDirectivesWithPrecedence(decoratorDirectives, commentDirectives);

      // Return directives if any exist
      const hasAnyDirectives =
        mergedDirectives.imports.length > 0 ||
        mergedDirectives.methodCalls.length > 0 ||
        mergedDirectives.fieldFields.length > 0 ||
        Object.keys(mergedDirectives.others).length > 0;

      return hasAnyDirectives ? mergedDirectives : null;
    } catch (error) {
      console.error(`Error getting interface field directives: ${error}`);
      return null;
    }
  }

  /**
   * Get comment-based directives from schema files
   * @param interfaceName The name of the interface
   * @param fieldName The name of the field
   * @returns Comment-based directives
   */
  private async getCommentBasedDirectives(
    interfaceName: string,
    fieldName: string
  ): Promise<DirectiveContainer> {
    try {
      // Get the interface type location
      const typeLocation = this.schemaMapper.getTypeLocation(interfaceName);
      if (!typeLocation) {
        return this.createEmptyDirectiveContainer();
      }

      // Get absolute path to the schema file
      const absoluteSchemaPath = path.resolve(
        this.schemaMapper.schemaRoot,
        typeLocation.sourceFile
      );

      // Extract field-level directives from the interface
      return await DirectiveParser.extractDirectivesFromSchema(
        absoluteSchemaPath,
        interfaceName,
        fieldName
      );
    } catch (error) {
      console.error(`Error getting comment-based directives: ${error}`);
      return this.createEmptyDirectiveContainer();
    }
  }

  /**
   * Get decorator-based directives for an interface field
   * @param interfaceName The name of the interface
   * @param fieldName The name of the field
   * @returns Decorator-based directives
   */
  private getDecoratorBasedDirectives(
    interfaceName: string,
    fieldName: string
  ): DirectiveContainer {
    if (!this.decoratorMetadata || !this.decoratorProcessor) {
      return this.createEmptyDirectiveContainer();
    }

    try {
      // Convert decorator metadata to directive container
      const allDecoratorDirectives = this.decoratorProcessor.convertToDirectiveContainer(
        this.decoratorMetadata
      );

      // Filter for the specific interface and field
      return this.filterDirectivesForInterfaceField(allDecoratorDirectives, interfaceName, fieldName);
    } catch (error) {
      console.error(`Error getting decorator-based directives: ${error}`);
      return this.createEmptyDirectiveContainer();
    }
  }

  /**
   * Filter directives for a specific interface field
   * @param directives The directive container to filter
   * @param interfaceName The interface name
   * @param fieldName The field name
   * @returns Filtered directives
   */
  private filterDirectivesForInterfaceField(
    directives: DirectiveContainer,
    interfaceName: string,
    fieldName: string
  ): DirectiveContainer {
    const filtered: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {},
    };

    // Filter method calls for this interface/field
    for (const methodCall of directives.methodCalls) {
      if (this.isMethodCallForInterfaceField(methodCall, interfaceName, fieldName)) {
        filtered.methodCalls.push(methodCall);
      }
    }

    // Filter imports for this interface/field
    for (const importDirective of directives.imports) {
      if (this.isImportForInterfaceField(importDirective, interfaceName, fieldName)) {
        filtered.imports.push(importDirective);
      }
    }

    // Filter field directives - for @field directives, we include all as they define additional fields
    // The filtering is more about context rather than specific field matching
    filtered.fieldFields = [...directives.fieldFields];

    // Filter other directives
    for (const [key, otherDirectives] of Object.entries(directives.others)) {
      const filteredOthers = otherDirectives.filter(directive =>
        this.isOtherDirectiveForInterfaceField(directive, interfaceName, fieldName)
      );
      if (filteredOthers.length > 0) {
        filtered.others[key] = filteredOthers;
      }
    }

    return filtered;
  }

  /**
   * Merge directives with precedence rules (decorators take precedence)
   * @param decoratorDirectives Directives from decorators
   * @param commentDirectives Directives from comments
   * @returns Merged directives
   */
  private mergeDirectivesWithPrecedence(
    decoratorDirectives: DirectiveContainer,
    commentDirectives: DirectiveContainer
  ): DirectiveContainer {
    if (!this.decoratorProcessor) {
      return commentDirectives;
    }

    return this.decoratorProcessor.mergeDirectives(decoratorDirectives, commentDirectives);
  }

  /**
   * Create an empty directive container
   * @returns Empty directive container
   */
  private createEmptyDirectiveContainer(): DirectiveContainer {
    return {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {},
    };
  }

  /**
   * Find inherited method call for a type field from any implemented interfaces
   * Now supports multi-deep inheritance with proper precedence resolution
   * @param typeName The name of the implementing type
   * @param fieldName The name of the field
   * @returns Interface inheritance info if found, null otherwise
   */
  public async findInheritedMethodCall(
    typeName: string,
    fieldName: string
  ): Promise<InterfaceInheritanceInfo | null> {
    // Get all interfaces implemented by this type (including deep inheritance)
    const allInterfaces = this.getAllInterfacesRecursively(typeName);

    // Check for circular dependencies
    if (this.hasCircularDependency(typeName)) {
      console.warn(`⚠️  Skipping method call inheritance for ${typeName}.${fieldName} due to circular dependency`);
      return null;
    }

    // Get direct interfaces first (they have highest precedence)
    const directInterfaces = this.getImplementedInterfaces(typeName);

    // Create precedence-ordered list: direct interfaces first, then inherited interfaces
    const orderedInterfaces = [
      ...directInterfaces,
      ...allInterfaces.filter(iface => !directInterfaces.includes(iface))
    ];

    // Check each interface for method calls on this field in precedence order
    for (const interfaceName of orderedInterfaces) {
      const inheritanceInfo = await this.getInterfaceInheritanceInfo(
        interfaceName,
        fieldName
      );

      if (inheritanceInfo) {
        console.log(`✓ Multi-deep inheritance: ${typeName}.${fieldName} inherits method call from ${interfaceName} (depth: ${this._getInterfaceDepth(typeName, interfaceName)})`);

        // Return the first interface method call found (highest precedence)
        return inheritanceInfo;
      }
    }

    return null;
  }

  /**
   * Calculate the inheritance depth of an interface for a given type
   * @param typeName The implementing type
   * @param interfaceName The interface to find depth for
   * @returns Depth level (0 = direct interface, 1+ = inherited through other interfaces)
   */
  private _getInterfaceDepth(typeName: string, interfaceName: string): number {
    const directInterfaces = this.getImplementedInterfaces(typeName);

    // If it's a direct interface, depth is 0
    if (directInterfaces.includes(interfaceName)) {
      return 0;
    }

    // Otherwise, find the minimum depth through any direct interface
    let minDepth = Infinity;

    for (const directInterface of directInterfaces) {
      const depth = this._findInterfaceDepthRecursive(directInterface, interfaceName, 1, new Set());
      if (depth !== -1 && depth < minDepth) {
        minDepth = depth;
      }
    }

    return minDepth === Infinity ? -1 : minDepth;
  }

  /**
   * Recursively find the depth of an interface in the inheritance chain
   * @param currentInterface Current interface being checked
   * @param targetInterface Interface we're looking for
   * @param currentDepth Current depth in the search
   * @param visited Set of visited interfaces to prevent cycles
   * @returns Depth if found, -1 if not found
   */
  private _findInterfaceDepthRecursive(
    currentInterface: string,
    targetInterface: string,
    currentDepth: number,
    visited: Set<string>
  ): number {
    if (visited.has(currentInterface)) {
      return -1; // Circular dependency
    }

    if (currentInterface === targetInterface) {
      return currentDepth;
    }

    const interfaceType = this.schema.getType(currentInterface);
    if (!interfaceType || !(interfaceType instanceof GraphQLInterfaceType)) {
      return -1;
    }

    const newVisited = new Set(visited);
    newVisited.add(currentInterface);

    const implementedInterfaces = interfaceType.getInterfaces().map(iface => iface.name);

    for (const implementedInterface of implementedInterfaces) {
      const depth = this._findInterfaceDepthRecursive(
        implementedInterface,
        targetInterface,
        currentDepth + 1,
        newVisited
      );

      if (depth !== -1) {
        return depth;
      }
    }

    return -1;
  }

  /**
   * Find inherited directives for a type field from any implemented interfaces
   * Now supports multi-deep inheritance with proper precedence resolution
   * This supports inheritance of @import directives even without @methodCall
   * @param typeName The name of the implementing type
   * @param fieldName The name of the field
   * @returns Interface directives if found, null otherwise
   */
  public async findInheritedDirectives(
    typeName: string,
    fieldName: string
  ): Promise<{ interfaceName: string; directives: DirectiveContainer } | null> {
    // Get all interfaces implemented by this type (including deep inheritance)
    const allInterfaces = this.getAllInterfacesRecursively(typeName);

    // Check for circular dependencies
    if (this.hasCircularDependency(typeName)) {
      console.warn(`⚠️  Skipping directive inheritance for ${typeName}.${fieldName} due to circular dependency`);
      return null;
    }

    // Get direct interfaces first (they have highest precedence)
    const directInterfaces = this.getImplementedInterfaces(typeName);

    // Create precedence-ordered list: direct interfaces first, then inherited interfaces
    const orderedInterfaces = [
      ...directInterfaces,
      ...allInterfaces.filter(iface => !directInterfaces.includes(iface))
    ];

    // Check each interface for directives on this field in precedence order
    for (const interfaceName of orderedInterfaces) {
      const directives = await this.getInterfaceFieldDirectives(
        interfaceName,
        fieldName
      );

      if (directives) {
        console.log(`✓ Multi-deep directive inheritance: ${typeName}.${fieldName} inherits directives from ${interfaceName} (depth: ${this._getInterfaceDepth(typeName, interfaceName)})`);

        // Return the first interface directives found (highest precedence)
        return {
          interfaceName,
          directives
        };
      }
    }

    return null;
  }

  /**
   * Check if a type field should inherit method call from interfaces
   * @param typeName The name of the implementing type
   * @param _fieldName The name of the field (unused but kept for API consistency)
   * @param typeMethodCall The method call defined directly on the type (if any)
   * @returns True if inheritance should be applied
   */
  public shouldInheritMethodCall(
    typeName: string,
    _fieldName: string,
    typeMethodCall: string | null
  ): boolean {
    // Only inherit if the type doesn't have its own method call
    if (typeMethodCall) {
      return false;
    }

    // Check if the type implements any interfaces
    const implementedInterfaces = this.getImplementedInterfaces(typeName);
    return implementedInterfaces.length > 0;
  }

  /**
   * Get all types that implement a specific interface
   * @param interfaceName The name of the interface
   * @returns Array of type names that implement the interface
   */
  public getImplementingTypes(interfaceName: string): string[] {
    const interfaceType = this.schema.getType(interfaceName);

    if (!interfaceType || !(interfaceType instanceof GraphQLInterfaceType)) {
      return [];
    }

    return this.schema.getPossibleTypes(interfaceType).map(type => type.name);
  }

  /**
   * Get detailed information about interface implementations
   * @param typeName The name of the type to analyze
   * @returns Object with interface implementation details
   */
  public getInterfaceImplementationDetails(typeName: string): {
    implementsInterfaces: boolean;
    interfaceNames: string[];
    interfaceCount: number;
  } {
    const interfaceNames = this.getImplementedInterfaces(typeName);

    return {
      implementsInterfaces: interfaceNames.length > 0,
      interfaceNames,
      interfaceCount: interfaceNames.length
    };
  }

  /**
   * Check if multiple types implement the same interface
   * @param interfaceName The name of the interface
   * @returns True if multiple types implement this interface
   */
  public hasMultipleImplementations(interfaceName: string): boolean {
    const implementingTypes = this.getImplementingTypes(interfaceName);
    return implementingTypes.length > 1;
  }

  /**
   * Get interface field information for debugging
   * @param interfaceName The name of the interface
   * @returns Object with interface field details
   */
  public getInterfaceFieldInfo(interfaceName: string): {
    interfaceExists: boolean;
    fieldNames: string[];
    fieldCount: number;
  } {
    const interfaceType = this.schema.getType(interfaceName);

    if (!interfaceType || !(interfaceType instanceof GraphQLInterfaceType)) {
      return {
        interfaceExists: false,
        fieldNames: [],
        fieldCount: 0
      };
    }

    const fields = interfaceType.getFields();
    const fieldNames = Object.keys(fields);

    return {
      interfaceExists: true,
      fieldNames,
      fieldCount: fieldNames.length
    };
  }

  /**
   * Get all method calls defined in an interface
   * @param interfaceName The name of the interface
   * @returns Map of field names to method calls
   */
  public async getAllInterfaceMethodCalls(interfaceName: string): Promise<Map<string, string>> {
    const methodCalls = new Map<string, string>();

    const interfaceFieldInfo = this.getInterfaceFieldInfo(interfaceName);
    if (!interfaceFieldInfo.interfaceExists) {
      return methodCalls;
    }

    // Check each field for method calls
    for (const fieldName of interfaceFieldInfo.fieldNames) {
      const methodCall = await this.getInterfaceFieldMethodCall(interfaceName, fieldName);
      if (methodCall) {
        methodCalls.set(fieldName, methodCall);
      }
    }

    return methodCalls;
  }

  /**
   * Clear all caches (useful for testing or when schema changes)
   */
  public clearCaches(): void {
    this.methodCallCache.clear();
    this.inheritanceInfoCache.clear();
    this.allInterfacesCache.clear();
    this.circularDependencyCache.clear();
  }

  /**
   * Clear caches for a specific type (useful when a type is modified)
   * @param typeName The name of the type to clear caches for
   */
  public clearCachesForType(typeName: string): void {
    // Clear method call cache entries for this type
    for (const key of this.methodCallCache.keys()) {
      if (key.startsWith(`${typeName}.`)) {
        this.methodCallCache.delete(key);
      }
    }

    // Clear inheritance info cache entries for this type
    for (const key of this.inheritanceInfoCache.keys()) {
      if (key.startsWith(`${typeName}.`)) {
        this.inheritanceInfoCache.delete(key);
      }
    }

    // Clear all interfaces cache entries for this type
    for (const key of this.allInterfacesCache.keys()) {
      if (key.startsWith(`${typeName}:`)) {
        this.allInterfacesCache.delete(key);
      }
    }

    // Remove from circular dependency cache
    this.circularDependencyCache.delete(typeName);
  }

  /**
   * Get comprehensive cache statistics for debugging
   */
  public getCacheStats(): {
    methodCallCacheSize: number;
    inheritanceInfoCacheSize: number;
    allInterfacesCacheSize: number;
    circularDependencyCacheSize: number;
    totalCacheEntries: number;
  } {
    const methodCallCacheSize = this.methodCallCache.size;
    const inheritanceInfoCacheSize = this.inheritanceInfoCache.size;
    const allInterfacesCacheSize = this.allInterfacesCache.size;
    const circularDependencyCacheSize = this.circularDependencyCache.size;

    return {
      methodCallCacheSize,
      inheritanceInfoCacheSize,
      allInterfacesCacheSize,
      circularDependencyCacheSize,
      totalCacheEntries: methodCallCacheSize + inheritanceInfoCacheSize + allInterfacesCacheSize + circularDependencyCacheSize
    };
  }

  /**
   * Warm up caches for a list of types (useful for performance optimization)
   * @param typeNames Array of type names to pre-populate caches for
   */
  public warmUpCaches(typeNames: string[]): void {
    console.log(`🔥 Warming up interface inheritance caches for ${typeNames.length} types...`);

    for (const typeName of typeNames) {
      try {
        // Pre-populate all interfaces cache
        this.getAllInterfacesRecursively(typeName);

        // Pre-populate validation cache
        this.validateInterfaceInheritance(typeName);
      } catch (error) {
        console.warn(`⚠️  Failed to warm up cache for ${typeName}: ${error}`);
      }
    }

    const stats = this.getCacheStats();
    console.log(`✅ Cache warm-up complete. Total entries: ${stats.totalCacheEntries}`);
  }

  /**
   * Get detailed inheritance analysis for a type
   * @param typeName The name of the type to analyze
   * @returns Comprehensive inheritance analysis
   */
  public getInheritanceAnalysis(typeName: string): {
    typeName: string;
    directInterfaces: string[];
    allInterfaces: string[];
    inheritanceDepth: number;
    hasCircularDependency: boolean;
    interfaceDepths: { [interfaceName: string]: number };
    validation: {
      isValid: boolean;
      hasCircularDependency: boolean;
      maxDepthExceeded: boolean;
      allInterfaces: string[];
      errors: string[];
    };
  } {
    const directInterfaces = this.getImplementedInterfaces(typeName);
    const allInterfaces = this.getAllInterfacesRecursively(typeName);
    const hasCircularDependency = this.hasCircularDependency(typeName);
    const validation = this.validateInterfaceInheritance(typeName);

    // Calculate depths for all interfaces
    const interfaceDepths: { [interfaceName: string]: number } = {};
    for (const interfaceName of allInterfaces) {
      interfaceDepths[interfaceName] = this._getInterfaceDepth(typeName, interfaceName);
    }

    // Calculate maximum inheritance depth
    const inheritanceDepth = Math.max(0, ...Object.values(interfaceDepths));

    return {
      typeName,
      directInterfaces,
      allInterfaces,
      inheritanceDepth,
      hasCircularDependency,
      interfaceDepths,
      validation
    };
  }

  /**
   * Get inheritance tree visualization for a type
   * @param typeName The name of the type to visualize
   * @returns String representation of the inheritance tree
   */
  public getInheritanceTree(typeName: string): string {
    const analysis = this.getInheritanceAnalysis(typeName);
    const lines: string[] = [];

    lines.push(`📊 Inheritance Tree for ${typeName}`);
    lines.push(`${'='.repeat(40)}`);

    if (analysis.hasCircularDependency) {
      lines.push(`⚠️  CIRCULAR DEPENDENCY DETECTED`);
    }

    lines.push(`📈 Max Depth: ${analysis.inheritanceDepth}`);
    lines.push(`🔗 Total Interfaces: ${analysis.allInterfaces.length}`);
    lines.push('');

    // Group interfaces by depth
    const interfacesByDepth: { [depth: number]: string[] } = {};
    for (const [interfaceName, depth] of Object.entries(analysis.interfaceDepths)) {
      if (!interfacesByDepth[depth]) {
        interfacesByDepth[depth] = [];
      }
      interfacesByDepth[depth].push(interfaceName);
    }

    // Display tree structure
    const sortedDepths = Object.keys(interfacesByDepth).map(Number).sort((a, b) => a - b);

    for (const depth of sortedDepths) {
      const interfaces = interfacesByDepth[depth];
      const indent = '  '.repeat(depth);
      const prefix = depth === 0 ? '🔗' : '↳';

      for (const interfaceName of interfaces) {
        lines.push(`${indent}${prefix} ${interfaceName} (depth: ${depth})`);
      }
    }

    if (analysis.validation.errors.length > 0) {
      lines.push('');
      lines.push('❌ Validation Errors:');
      for (const error of analysis.validation.errors) {
        lines.push(`   • ${error}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Analyze inheritance conflicts for a specific field across all interfaces
   * @param typeName The name of the type
   * @param fieldName The name of the field
   * @returns Analysis of potential conflicts
   */
  public async analyzeFieldInheritanceConflicts(
    typeName: string,
    fieldName: string
  ): Promise<{
    fieldName: string;
    hasConflicts: boolean;
    interfacesWithDirectives: Array<{
      interfaceName: string;
      depth: number;
      hasMethodCall: boolean;
      hasImports: boolean;
      methodCall?: string;
      imports?: string[];
    }>;
    selectedInterface?: string;
    conflictDetails: string[];
  }> {
    const allInterfaces = this.getAllInterfacesRecursively(typeName);
    const interfacesWithDirectives: Array<{
      interfaceName: string;
      depth: number;
      hasMethodCall: boolean;
      hasImports: boolean;
      methodCall?: string;
      imports?: string[];
    }> = [];

    // Check each interface for directives on this field
    for (const interfaceName of allInterfaces) {
      const inheritanceInfo = await this.getInterfaceInheritanceInfo(interfaceName, fieldName);

      if (inheritanceInfo) {
        interfacesWithDirectives.push({
          interfaceName,
          depth: this._getInterfaceDepth(typeName, interfaceName),
          hasMethodCall: !!inheritanceInfo.methodCall,
          hasImports: !!(inheritanceInfo.imports && inheritanceInfo.imports.length > 0),
          methodCall: inheritanceInfo.methodCall,
          imports: inheritanceInfo.imports
        });
      }
    }

    // Sort by depth (direct interfaces first)
    interfacesWithDirectives.sort((a, b) => a.depth - b.depth);

    // Analyze conflicts
    const hasConflicts = interfacesWithDirectives.length > 1;
    const conflictDetails: string[] = [];
    let selectedInterface: string | undefined;

    if (interfacesWithDirectives.length > 0) {
      selectedInterface = interfacesWithDirectives[0].interfaceName;

      if (hasConflicts) {
        conflictDetails.push(`Multiple interfaces define directives for ${fieldName}:`);
        for (const iface of interfacesWithDirectives) {
          const directives = [];
          if (iface.hasMethodCall) directives.push('methodCall');
          if (iface.hasImports) directives.push('imports');

          conflictDetails.push(
            `  • ${iface.interfaceName} (depth: ${iface.depth}) - ${directives.join(', ')}`
          );
        }
        conflictDetails.push(`Selected: ${selectedInterface} (highest precedence)`);
      }
    }

    return {
      fieldName,
      hasConflicts,
      interfacesWithDirectives,
      selectedInterface,
      conflictDetails
    };
  }

  /**
   * Check if a method call directive is for a specific interface field
   * @param methodCall The method call directive
   * @param interfaceName The interface name
   * @param fieldName The field name
   * @returns True if the method call is for this interface field
   */
  private isMethodCallForInterfaceField(
    methodCall: any,
    interfaceName: string,
    fieldName: string
  ): boolean {
    // This is a simplified check - in practice, we'd need to parse the decorator metadata
    // to extract the type and field information from the original decorator
    // For now, we'll use a basic string matching approach
    const content = methodCall.content || '';
    return content.includes(`type: "${interfaceName}"`) && content.includes(`field: "${fieldName}"`);
  }

  /**
   * Check if an import directive is for a specific interface field
   * @param importDirective The import directive
   * @param interfaceName The interface name
   * @param fieldName The field name
   * @returns True if the import is for this interface field
   */
  private isImportForInterfaceField(
    importDirective: any,
    interfaceName: string,
    fieldName: string
  ): boolean {
    // For imports, we typically associate them with the same scope as method calls
    // This is a simplified approach - in practice, we'd need more sophisticated logic
    return true; // For now, include all imports as they might be needed
  }

  /**
   * Check if an other directive is for a specific interface field
   * @param directive The directive
   * @param interfaceName The interface name
   * @param fieldName The field name
   * @returns True if the directive is for this interface field
   */
  private isOtherDirectiveForInterfaceField(
    directive: any,
    interfaceName: string,
    fieldName: string
  ): boolean {
    // This is a simplified check - in practice, we'd need more sophisticated logic
    const content = directive.content || '';
    return content.includes(interfaceName) || content.includes(fieldName);
  }
}
