import * as fs from 'fs-extra';
import * as path from 'path';
import { parse, DocumentNode, FieldDefinitionNode, ObjectTypeDefinitionNode } from 'graphql';
import { WatchedFileWriter } from './watched-file-writer';
import { BackupNamingUtils, type BackupNamingOptions } from './backup-naming';

/**
 * Configuration options for schema updating
 */
export interface SchemaUpdaterOptions {
  backupFiles?: boolean;
  preserveComments?: boolean;
  validateSyntax?: boolean;
  /** Backup naming options */
  backupOptions?: BackupNamingOptions;
  /** Context information for better backup naming */
  context?: {
    codebasePath?: string;
    schemaPath?: string;
    operation?: string;
  };
}

/**
 * Location of a field in a schema file
 */
export interface FieldLocation {
  filePath: string;
  typeName: string;
  fieldName: string;
  lineNumber: number;
  fieldStartIndex: number;
  fieldEndIndex: number;
}

/**
 * Result of a schema update operation
 */
export interface UpdateResult {
  success: boolean;
  message: string;
  backupPath?: string;
  error?: Error;
}

/**
 * Safely updates GraphQL schema files by adding directives while preserving structure
 */
export class SchemaUpdater {
  private options: Required<Pick<SchemaUpdaterOptions, 'backupFiles' | 'preserveComments' | 'validateSyntax'>> &
    Pick<SchemaUpdaterOptions, 'backupOptions' | 'context'>;
  private backupSession: Map<string, string> = new Map(); // filePath -> backupPath
  private currentSessionId: string | null = null;

  constructor(options: SchemaUpdaterOptions = {}) {
    this.options = {
      backupFiles: true,
      preserveComments: true,
      validateSyntax: true,
      ...options,
    };
  }

  /**
   * Start a new backup session to prevent duplicate backups
   */
  startBackupSession(): string {
    // Use new session ID generation if available, otherwise fallback to legacy
    this.currentSessionId = BackupNamingUtils.generateSessionId('schema-update');
    this.backupSession.clear();
    return this.currentSessionId;
  }

  /**
   * Start a backup session with custom prefix
   */
  startBackupSessionWithPrefix(prefix: string): string {
    this.currentSessionId = BackupNamingUtils.generateSessionId(prefix);
    this.backupSession.clear();
    return this.currentSessionId;
  }



  /**
   * Get the current session ID
   */
  getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * Add a methodCall directive to a field in a schema file
   */
  async addMethodCallDirective(
    schemaFilePath: string,
    typeName: string,
    fieldName: string,
    methodCall: string
  ): Promise<UpdateResult> {
    try {
      // Check if schema file exists
      if (!fs.existsSync(schemaFilePath)) {
        return {
          success: false,
          message: `Schema file not found: ${schemaFilePath}`,
        };
      }

      // Read the schema file
      const originalContent = await fs.readFile(schemaFilePath, 'utf8');

      // Create backup if enabled
      let backupPath: string | undefined;
      if (this.options.backupFiles) {
        backupPath = await this.createBackup(schemaFilePath, originalContent);
      }

      // Find the field location
      const fieldLocation = this.findFieldInSchema(originalContent, typeName, fieldName);
      if (!fieldLocation) {
        return {
          success: false,
          message: `Field ${typeName}.${fieldName} not found in ${schemaFilePath}`,
        };
      }

      // Add the methodCall directive
      const updatedContent = this.addDirectiveToField(
        originalContent,
        fieldLocation,
        'methodCall',
        methodCall
      );

      // Validate syntax if enabled
      if (this.options.validateSyntax) {
        const validationResult = this.validateGraphQLSyntax(updatedContent);
        if (!validationResult.valid) {
          return {
            success: false,
            message: `Syntax validation failed: ${validationResult.error}`,
            backupPath,
          };
        }
      }

      // Write the updated content with automatic formatting
      await WatchedFileWriter.writeFile(schemaFilePath, updatedContent);

      return {
        success: true,
        message: `Added methodCall directive to ${typeName}.${fieldName}`,
        backupPath,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error updating schema: ${error instanceof Error ? error.message : String(error)}`,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }

  /**
   * Add an import directive to a type or field in a schema file
   */
  async addImportDirective(
    schemaFilePath: string,
    typeName: string,
    importStatement: string,
    fieldName?: string
  ): Promise<UpdateResult> {
    try {
      if (!fs.existsSync(schemaFilePath)) {
        return {
          success: false,
          message: `Schema file not found: ${schemaFilePath}`,
        };
      }

      const originalContent = await fs.readFile(schemaFilePath, 'utf8');

      // Create backup if enabled
      let backupPath: string | undefined;
      if (this.options.backupFiles) {
        backupPath = await this.createBackup(schemaFilePath, originalContent);
      }

      // Check if this import already exists anywhere in the file to avoid duplicates
      if (this.importAlreadyExists(originalContent, importStatement)) {
        return {
          success: true,
          message: fieldName
            ? `Import directive already exists for ${typeName}.${fieldName}`
            : `Import directive already exists for ${typeName}`,
          backupPath,
        };
      }

      let updatedContent: string;

      if (fieldName) {
        // Field-level import directive
        const fieldLocation = this.findFieldInSchema(originalContent, typeName, fieldName);
        if (!fieldLocation) {
          return {
            success: false,
            message: `Field ${typeName}.${fieldName} not found in ${schemaFilePath}`,
          };
        }

        updatedContent = this.addDirectiveToField(
          originalContent,
          fieldLocation,
          'import',
          importStatement
        );
      } else {
        // Type-level import directive (legacy behavior)
        const typeLocation = this.findTypeInSchema(originalContent, typeName);
        if (!typeLocation) {
          return {
            success: false,
            message: `Type ${typeName} not found in ${schemaFilePath}`,
          };
        }

        updatedContent = this.addDirectiveToType(
          originalContent,
          typeLocation,
          'import',
          importStatement
        );
      }

      // Validate syntax if enabled
      if (this.options.validateSyntax) {
        const validationResult = this.validateGraphQLSyntax(updatedContent);
        if (!validationResult.valid) {
          return {
            success: false,
            message: `Syntax validation failed: ${validationResult.error}`,
            backupPath,
          };
        }
      }

      // Write the updated content with automatic formatting
      await WatchedFileWriter.writeFile(schemaFilePath, updatedContent);

      return {
        success: true,
        message: fieldName
          ? `Added import directive to ${typeName}.${fieldName}`
          : `Added import directive to ${typeName}`,
        backupPath,
      };
    } catch (error) {
      return {
        success: false,
        message: `Error updating schema: ${error instanceof Error ? error.message : String(error)}`,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  }

  /**
   * Find a field in a schema file and return its location
   */
  private findFieldInSchema(content: string, typeName: string, fieldName: string): FieldLocation | null {
    const lines = content.split('\n');
    let inType = false;
    let typeStartLine = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Look for type definition
      if (line.startsWith(`type ${typeName}`) || line.startsWith(`extend type ${typeName}`)) {
        inType = true;
        typeStartLine = i;
        continue;
      }

      // End of type definition
      if (inType && line === '}') {
        break;
      }

      // Look for field within the type (handle both simple fields and fields with parameters)
      if (inType && (line.includes(`${fieldName}:`) || line.includes(`${fieldName}(`))) {
        // Calculate character indices
        const fieldStartIndex = content.indexOf(line, content.split('\n', i).join('\n').length);
        const fieldEndIndex = fieldStartIndex + line.length;

        return {
          filePath: '',
          typeName,
          fieldName,
          lineNumber: i + 1,
          fieldStartIndex,
          fieldEndIndex,
        };
      }
    }

    return null;
  }

  /**
   * Find a type definition in a schema file
   */
  private findTypeInSchema(content: string, typeName: string): { lineNumber: number; startIndex: number } | null {
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line.startsWith(`type ${typeName}`) || line.startsWith(`extend type ${typeName}`)) {
        const startIndex = content.split('\n', i).join('\n').length;
        return {
          lineNumber: i + 1,
          startIndex,
        };
      }
    }

    return null;
  }

  /**
   * Add a directive to a field, preserving existing directives and comments
   */
  private addDirectiveToField(
    content: string,
    fieldLocation: FieldLocation,
    directiveName: string,
    directiveContent: string
  ): string {
    const lines = content.split('\n');
    const fieldLineIndex = fieldLocation.lineNumber - 1;

    // Check if directive already exists
    const existingDirective = this.findExistingDirective(lines, fieldLineIndex, directiveName);
    if (existingDirective) {
      // Update existing directive
      lines[existingDirective.lineIndex] = `  # @${directiveName}(${directiveContent})`;
    } else {
      // Add new directive before the field
      const directiveLine = `  # @${directiveName}(${directiveContent})`;
      lines.splice(fieldLineIndex, 0, directiveLine);
    }

    return lines.join('\n');
  }

  /**
   * Add a directive to a type definition
   */
  private addDirectiveToType(
    content: string,
    typeLocation: { lineNumber: number; startIndex: number },
    directiveName: string,
    directiveContent: string
  ): string {
    const lines = content.split('\n');
    const typeLineIndex = typeLocation.lineNumber - 1;

    // Check if directive already exists
    const existingDirective = this.findExistingDirective(lines, typeLineIndex, directiveName);
    if (existingDirective) {
      // Update existing directive
      lines[existingDirective.lineIndex] = `# @${directiveName}(${directiveContent})`;
    } else {
      // Add new directive before the type
      const directiveLine = `# @${directiveName}(${directiveContent})`;
      lines.splice(typeLineIndex, 0, directiveLine);
    }

    return lines.join('\n');
  }

  /**
   * Check if an import directive already exists in the schema content
   */
  private importAlreadyExists(content: string, importStatement: string): boolean {
    // Extract the core import content (what's inside the parentheses)
    const importMatch = importStatement.match(/import\s+(.+?)\s+from\s+['"]([^'"]+)['"]/);
    if (!importMatch) {
      return false;
    }

    const [, specifier, modulePath] = importMatch;

    // Look for existing @import directives with the same module path and specifier
    const existingImportPattern = new RegExp(
      `#\\s*@import\\s*\\(\\s*import\\s+${specifier.replace(/[{}]/g, '\\$&')}\\s+from\\s+['"]${modulePath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]\\s*[;]?\\s*\\)`,
      'i'
    );

    return existingImportPattern.test(content);
  }

  /**
   * Find an existing directive near a given line
   */
  private findExistingDirective(
    lines: string[],
    startLineIndex: number,
    directiveName: string
  ): { lineIndex: number; content: string } | null {
    // Look backwards from the target line for existing directives
    for (let i = startLineIndex - 1; i >= 0; i--) {
      const line = lines[i].trim();

      // Stop if we hit a non-comment line
      if (line && !line.startsWith('#')) {
        break;
      }

      // Check if this is the directive we're looking for
      if (line.includes(`@${directiveName}(`)) {
        return {
          lineIndex: i,
          content: line,
        };
      }
    }

    return null;
  }

  /**
   * Validate GraphQL syntax
   */
  private validateGraphQLSyntax(content: string): { valid: boolean; error?: string } {
    try {
      parse(content);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Create a backup of the schema file (session-aware to prevent duplicates)
   */
  private async createBackup(filePath: string, content: string): Promise<string> {
    // Check if we already have a backup for this file in the current session
    const existingBackup = this.backupSession.get(filePath);
    if (existingBackup) {
      console.log(`   ↳ Using existing backup for session: ${existingBackup}`);
      return existingBackup;
    }

    let backupPath: string;

    // Use new backup naming system if options are provided
    if (this.options.backupOptions && this.options.context?.codebasePath && this.options.context?.schemaPath) {
      try {
        // Create a session-specific backup directory
        const sessionId = this.currentSessionId || `session-${Date.now()}`;
        const backupInfo = await BackupNamingUtils.createBackupDirectory(
          this.options.context.codebasePath,
          this.options.context.schemaPath,
          {
            ...this.options.backupOptions,
            customPrefix: `schema-update-${sessionId}`,
            useSubdirectories: true
          }
        );

        // Generate structured backup path
        backupPath = BackupNamingUtils.generateStructuredBackupPath(
          filePath,
          backupInfo.backupDir,
          {
            ...this.options.backupOptions,
            organizeByType: true,
            preserveStructure: true
          }
        );

        // Ensure directory exists
        await fs.ensureDir(path.dirname(backupPath));

      } catch (error) {
        console.warn(`Failed to use new backup naming system: ${error}`);
        // Fallback to legacy naming
        backupPath = this.createLegacyBackupPath(filePath);
      }
    } else {
      // Use legacy backup naming
      backupPath = this.createLegacyBackupPath(filePath);
    }

    // Handle naming collisions
    if (await fs.pathExists(backupPath)) {
      backupPath = await BackupNamingUtils.resolveNamingCollision(backupPath);
    }

    await fs.writeFile(backupPath, content, 'utf8');

    // Track this backup in the current session
    this.backupSession.set(filePath, backupPath);

    console.log(`   ↳ Created backup: ${backupPath}`);
    return backupPath;
  }

  /**
   * Create legacy backup path for backward compatibility
   */
  private createLegacyBackupPath(filePath: string): string {
    const timestamp = BackupNamingUtils.generateTimestamp();
    const sessionSuffix = this.currentSessionId ? `.${this.currentSessionId}` : '';
    return `${filePath}.backup.${timestamp}${sessionSuffix}`;
  }

  /**
   * End the current backup session and optionally clean up
   */
  endBackupSession(cleanup: boolean = false): void {
    if (cleanup && this.currentSessionId) {
      this.cleanupSessionBackups();
    }

    this.currentSessionId = null;
    this.backupSession.clear();
  }

  /**
   * Clean up all backups for the current session
   */
  async cleanupSessionBackups(): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    try {
      // Clean up new-style session backups
      if (this.options.backupOptions?.baseBackupDir) {
        const cleanedCount = await BackupNamingUtils.cleanupSessionBackups(
          this.currentSessionId,
          this.options.backupOptions.baseBackupDir
        );
        console.log(`Cleaned up ${cleanedCount} session backup directories`);
      }

      // Clean up legacy session backups
      for (const [filePath, backupPath] of this.backupSession.entries()) {
        try {
          if (await fs.pathExists(backupPath)) {
            await fs.remove(backupPath);
            console.log(`Cleaned up legacy backup: ${backupPath}`);
          }
        } catch (error) {
          console.warn(`Failed to clean up legacy backup ${backupPath}:`, error);
        }
      }

    } catch (error) {
      console.warn(`Failed to clean up session backups: ${error}`);
    }
  }

  /**
   * Get statistics for the current session
   */
  async getSessionStatistics(): Promise<any> {
    if (!this.currentSessionId) {
      return {
        sessionId: null,
        backupCount: this.backupSession.size,
        legacyBackups: Array.from(this.backupSession.values())
      };
    }

    try {
      const stats = await BackupNamingUtils.getSessionStatistics(
        this.currentSessionId,
        this.options.backupOptions?.baseBackupDir || '.backup'
      );

      return {
        sessionId: this.currentSessionId,
        ...stats,
        legacyBackupCount: this.backupSession.size,
        legacyBackups: Array.from(this.backupSession.values())
      };
    } catch (error) {
      console.warn(`Failed to get session statistics: ${error}`);
      return {
        sessionId: this.currentSessionId,
        backupCount: this.backupSession.size,
        legacyBackups: Array.from(this.backupSession.values()),
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * List all backups for the current session
   */
  async listSessionBackups(): Promise<string[]> {
    const backups: string[] = [];

    // Add legacy backups
    backups.push(...Array.from(this.backupSession.values()));

    // Add new-style backups if session ID exists
    if (this.currentSessionId && this.options.backupOptions?.baseBackupDir) {
      try {
        const sessionBackups = await BackupNamingUtils.listSessionBackups(
          this.currentSessionId,
          this.options.backupOptions.baseBackupDir
        );
        backups.push(...sessionBackups.map(b => b.backupDir));
      } catch (error) {
        console.warn(`Failed to list session backups: ${error}`);
      }
    }

    return backups;
  }
}
