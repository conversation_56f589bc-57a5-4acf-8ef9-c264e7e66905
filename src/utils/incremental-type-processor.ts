import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import { getGlobalDependencyGraph } from './dependency-graph';
import { getGlobalSmartCacheInvalidation } from './smart-cache-invalidation';

/**
 * Phase 2 Optimization: Incremental Type Processing Engine
 * Skip processing of unchanged type definitions for massive performance gains
 */

export interface TypeDefinition {
  id: string;
  name: string;
  kind: 'object' | 'interface' | 'union' | 'enum' | 'input' | 'scalar';
  sourceFile: string;
  hash: string;
  lastProcessed: number;
  dependencies: string[];
  outputFiles: string[];
  metadata: Record<string, any>;
  // Phase 3: Type-level tracking
  fieldHashes?: Map<string, string>; // field name -> hash
  directiveHashes?: Map<string, string>; // directive type -> hash
  typeSignature?: string; // Complete type signature hash
  changeLevel?: 'type' | 'field' | 'directive';
  processingPriority?: number;
}

export interface ProcessingPlan {
  totalTypes: number;
  typesToProcess: string[];
  typesToSkip: string[];
  estimatedTimeReduction: number;
  processingOrder: string[];
  batchGroups: string[][];
}

export interface ProcessingResult {
  processedTypes: number;
  skippedTypes: number;
  totalTime: number;
  timePerType: number;
  cacheHitRate: number;
  errors: Array<{ typeId: string; error: string }>;
}

export interface IncrementalConfig {
  enableIncrementalProcessing: boolean;
  enableDependencyTracking: boolean;
  enableBatchOptimization: boolean;
  maxBatchSize: number;
  forceReprocessThreshold: number; // Hours after which to force reprocess
  enableMetrics: boolean;
  debug: boolean;
  // Phase 3: Enhanced file-level processing
  enableFileLevelTracking: boolean;
  enableSmartBatching: boolean;
  enableContentHashing: boolean;
  fileBatchSize: number;
  enableParallelFileProcessing: boolean;
}

/**
 * Incremental type processing engine
 * Phase 3: Enhanced with file-level granular processing
 */
export class IncrementalTypeProcessor extends EventEmitter {
  private config: Required<IncrementalConfig>;
  private typeDefinitions = new Map<string, TypeDefinition>();
  private dependencyGraph = getGlobalDependencyGraph();
  private cacheInvalidation = getGlobalSmartCacheInvalidation();
  private persistencePath: string;
  private processingMetrics: ProcessingResult[] = [];

  // Phase 3: File-level tracking
  private fileContentHashes = new Map<string, string>();
  private fileProcessingGroups = new Map<string, string[]>(); // file -> type IDs
  private fileDependencies = new Map<string, Set<string>>(); // file -> dependent files
  private fileProcessingMetrics = new Map<string, { lastProcessed: number; processingTime: number; changeCount: number }>();

  constructor(config: Partial<IncrementalConfig> = {}, persistencePath?: string) {
    super();

    this.config = {
      enableIncrementalProcessing: true,
      enableDependencyTracking: true,
      enableBatchOptimization: true,
      maxBatchSize: 20,
      forceReprocessThreshold: 24, // 24 hours
      enableMetrics: true,
      debug: false,
      // Phase 3: Enhanced defaults
      enableFileLevelTracking: true,
      enableSmartBatching: true,
      enableContentHashing: true,
      fileBatchSize: 10,
      enableParallelFileProcessing: true,
      ...config
    };

    this.persistencePath = persistencePath || path.join(process.cwd(), '.gql-generator-cache', 'incremental-types.json');
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.cacheInvalidation.on('invalidationComplete', (result) => {
      this.handleCacheInvalidation(result.affectedFiles);
    });
  }

  /**
   * Register a type definition for incremental processing
   */
  registerType(
    id: string,
    name: string,
    kind: TypeDefinition['kind'],
    sourceFile: string,
    dependencies: string[] = [],
    outputFiles: string[] = [],
    metadata: Record<string, any> = {}
  ): TypeDefinition {
    const hash = this.calculateTypeHash(sourceFile, name, dependencies);
    
    const existingType = this.typeDefinitions.get(id);
    const isChanged = !existingType || existingType.hash !== hash;

    const typeDef: TypeDefinition = {
      id,
      name,
      kind,
      sourceFile,
      hash,
      lastProcessed: existingType?.lastProcessed || 0,
      dependencies,
      outputFiles,
      metadata: { ...existingType?.metadata, ...metadata }
    };

    this.typeDefinitions.set(id, typeDef);

    // Update dependency graph
    if (this.config.enableDependencyTracking) {
      this.dependencyGraph.addNode(id, 'generated', sourceFile, dependencies, {
        typeName: name,
        typeKind: kind,
        outputFiles
      });
    }

    if (isChanged && this.config.debug) {
      console.log(`📝 Registered ${isChanged ? 'changed' : 'unchanged'} type: ${name} (${kind})`);
    }

    return typeDef;
  }

  /**
   * Create processing plan based on changes and dependencies
   */
  async createProcessingPlan(allTypeIds: string[]): Promise<ProcessingPlan> {
    const startTime = Date.now();
    
    if (this.config.debug) {
      console.log(`📋 Creating processing plan for ${allTypeIds.length} types...`);
    }

    // Load existing type definitions
    await this.load();

    const typesToProcess: string[] = [];
    const typesToSkip: string[] = [];
    const now = Date.now();
    const forceReprocessTime = this.config.forceReprocessThreshold * 60 * 60 * 1000; // Convert hours to ms

    // Determine which types need processing
    for (const typeId of allTypeIds) {
      const typeDef = this.typeDefinitions.get(typeId);
      
      if (!typeDef) {
        // New type, must process
        typesToProcess.push(typeId);
        continue;
      }

      // Check if type needs reprocessing
      const needsReprocessing = this.needsReprocessing(typeDef, now, forceReprocessTime);
      
      if (needsReprocessing) {
        typesToProcess.push(typeId);
      } else {
        typesToSkip.push(typeId);
      }
    }

    // Calculate processing order based on dependencies
    const processingOrder = this.calculateProcessingOrder(typesToProcess);

    // Create batch groups for optimized processing
    const batchGroups = this.config.enableBatchOptimization ? 
      this.createBatchGroups(processingOrder) : 
      [processingOrder];

    // Estimate time reduction
    const estimatedTimeReduction = this.estimateTimeReduction(typesToSkip.length, allTypeIds.length);

    const plan: ProcessingPlan = {
      totalTypes: allTypeIds.length,
      typesToProcess,
      typesToSkip,
      estimatedTimeReduction,
      processingOrder,
      batchGroups
    };

    if (this.config.debug) {
      console.log(`📊 Processing plan created in ${Date.now() - startTime}ms:`);
      console.log(`   - Process: ${typesToProcess.length} types`);
      console.log(`   - Skip: ${typesToSkip.length} types`);
      console.log(`   - Time reduction: ~${Math.round(estimatedTimeReduction)}%`);
      console.log(`   - Batches: ${batchGroups.length}`);
    }

    this.emit('planCreated', plan);
    return plan;
  }

  /**
   * Execute processing plan
   */
  async executeProcessingPlan(
    plan: ProcessingPlan,
    processingFunction: (typeIds: string[]) => Promise<void>
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    const errors: Array<{ typeId: string; error: string }> = [];
    let processedCount = 0;

    if (this.config.debug) {
      console.log(`🚀 Executing processing plan: ${plan.batchGroups.length} batches`);
    }

    try {
      // Process each batch
      for (let i = 0; i < plan.batchGroups.length; i++) {
        const batch = plan.batchGroups[i];
        
        if (this.config.debug) {
          console.log(`📦 Processing batch ${i + 1}/${plan.batchGroups.length} (${batch.length} types)`);
        }

        try {
          await processingFunction(batch);
          
          // Mark types as processed
          const now = Date.now();
          for (const typeId of batch) {
            const typeDef = this.typeDefinitions.get(typeId);
            if (typeDef) {
              typeDef.lastProcessed = now;
            }
          }
          
          processedCount += batch.length;
          
          this.emit('batchProcessed', {
            batch: i + 1,
            totalBatches: plan.batchGroups.length,
            typesInBatch: batch.length,
            totalProcessed: processedCount
          });
          
        } catch (error) {
          // Record errors but continue with other batches
          for (const typeId of batch) {
            errors.push({ typeId, error: (error as Error).message });
          }
          
          if (this.config.debug) {
            console.error(`❌ Batch ${i + 1} failed:`, error);
          }
        }
      }

      // Save updated type definitions
      await this.save();

    } catch (error) {
      this.emit('processingError', error);
      throw error;
    }

    const totalTime = Date.now() - startTime;
    const result: ProcessingResult = {
      processedTypes: processedCount,
      skippedTypes: plan.typesToSkip.length,
      totalTime,
      timePerType: processedCount > 0 ? totalTime / processedCount : 0,
      cacheHitRate: plan.typesToSkip.length / plan.totalTypes,
      errors
    };

    // Record metrics
    if (this.config.enableMetrics) {
      this.processingMetrics.push(result);
      if (this.processingMetrics.length > 50) {
        this.processingMetrics = this.processingMetrics.slice(-50);
      }
    }

    if (this.config.debug) {
      console.log(`✅ Processing completed in ${totalTime}ms:`);
      console.log(`   - Processed: ${processedCount} types`);
      console.log(`   - Skipped: ${plan.typesToSkip.length} types`);
      console.log(`   - Cache hit rate: ${Math.round(result.cacheHitRate * 100)}%`);
      console.log(`   - Errors: ${errors.length}`);
    }

    this.emit('processingComplete', result);
    return result;
  }

  /**
   * Check if a type needs reprocessing
   */
  private needsReprocessing(
    typeDef: TypeDefinition,
    now: number,
    forceReprocessTime: number
  ): boolean {
    // Force reprocess if too old
    if (now - typeDef.lastProcessed > forceReprocessTime) {
      return true;
    }

    // Check if source file changed
    const currentHash = this.calculateTypeHash(typeDef.sourceFile, typeDef.name, typeDef.dependencies);
    if (currentHash !== typeDef.hash) {
      return true;
    }

    // Check if any dependencies changed
    if (this.config.enableDependencyTracking) {
      const dependents = this.dependencyGraph.getDependents(typeDef.id);
      for (const dependentId of dependents) {
        const dependentNode = this.dependencyGraph.getNode(dependentId);
        if (dependentNode && dependentNode.lastModified > typeDef.lastProcessed) {
          return true;
        }
      }
    }

    // Check if output files are missing
    for (const outputFile of typeDef.outputFiles) {
      if (!fs.existsSync(outputFile)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Calculate processing order based on dependencies
   */
  private calculateProcessingOrder(typeIds: string[]): string[] {
    if (!this.config.enableDependencyTracking) {
      return typeIds; // No dependency tracking, use original order
    }

    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: string[] = [];

    const visit = (typeId: string): void => {
      if (visited.has(typeId)) return;
      if (visiting.has(typeId)) {
        // Circular dependency detected, add to result anyway
        result.push(typeId);
        return;
      }

      visiting.add(typeId);
      
      const typeDef = this.typeDefinitions.get(typeId);
      if (typeDef) {
        // Visit dependencies first
        for (const depId of typeDef.dependencies) {
          if (typeIds.includes(depId)) {
            visit(depId);
          }
        }
      }

      visiting.delete(typeId);
      visited.add(typeId);
      result.push(typeId);
    };

    for (const typeId of typeIds) {
      visit(typeId);
    }

    return result;
  }

  /**
   * Create batch groups for optimized processing
   */
  private createBatchGroups(typeIds: string[]): string[][] {
    const batches: string[][] = [];
    
    for (let i = 0; i < typeIds.length; i += this.config.maxBatchSize) {
      batches.push(typeIds.slice(i, i + this.config.maxBatchSize));
    }
    
    return batches;
  }

  /**
   * Calculate type hash for change detection
   */
  private calculateTypeHash(sourceFile: string, typeName: string, dependencies: string[]): string {
    try {
      const content = fs.existsSync(sourceFile) ? fs.readFileSync(sourceFile, 'utf8') : '';
      const hashInput = `${content}:${typeName}:${dependencies.sort().join(',')}`;
      return crypto.createHash('sha256').update(hashInput).digest('hex').substring(0, 16);
    } catch (error) {
      return '';
    }
  }

  /**
   * Estimate time reduction percentage
   */
  private estimateTimeReduction(skippedCount: number, totalCount: number): number {
    if (totalCount === 0) return 0;
    return (skippedCount / totalCount) * 100;
  }

  /**
   * Handle cache invalidation events
   */
  private handleCacheInvalidation(affectedFiles: string[]): void {
    // Mark types from affected files as needing reprocessing
    for (const [typeId, typeDef] of this.typeDefinitions) {
      if (affectedFiles.includes(typeDef.sourceFile)) {
        typeDef.lastProcessed = 0; // Force reprocessing
        
        if (this.config.debug) {
          console.log(`🔄 Marked type ${typeDef.name} for reprocessing due to cache invalidation`);
        }
      }
    }
  }

  /**
   * Load type definitions from persistence
   */
  async load(): Promise<void> {
    try {
      if (fs.existsSync(this.persistencePath)) {
        const data = await fs.readJson(this.persistencePath);
        
        this.typeDefinitions.clear();
        for (const typeData of data.types || []) {
          this.typeDefinitions.set(typeData.id, typeData);
        }
        
        this.emit('loaded', { typeCount: this.typeDefinitions.size });
      }
    } catch (error) {
      this.emit('loadError', error);
    }
  }

  /**
   * Save type definitions to persistence
   */
  async save(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.persistencePath));
      
      const data = {
        version: '1.0',
        timestamp: Date.now(),
        types: Array.from(this.typeDefinitions.values())
      };
      
      await fs.writeJson(this.persistencePath, data, { spaces: 2 });
      this.emit('saved', { typeCount: this.typeDefinitions.size });
    } catch (error) {
      this.emit('saveError', error);
    }
  }

  /**
   * Get processing statistics
   * Phase 3: Enhanced with file-level metrics
   */
  getStats() {
    const recentMetrics = this.processingMetrics.slice(-10);
    const avgCacheHitRate = recentMetrics.length > 0 ?
      recentMetrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / recentMetrics.length : 0;

    return {
      config: this.config,
      typeCount: this.typeDefinitions.size,
      recentMetrics,
      averageCacheHitRate: avgCacheHitRate,
      persistencePath: this.persistencePath,
      // Phase 3: File-level stats
      totalFiles: this.fileContentHashes.size,
      fileProcessingGroups: this.fileProcessingGroups.size,
      fileDependencies: this.fileDependencies.size
    };
  }

  /**
   * Clear all type definitions
   */
  clear(): void {
    this.typeDefinitions.clear();
    this.processingMetrics = [];
    this.emit('cleared');
  }

  /**
   * Get all type definitions
   */
  getAllTypes(): TypeDefinition[] {
    return Array.from(this.typeDefinitions.values());
  }

  // Phase 3: Enhanced File-Level Processing Methods

  /**
   * Register file-level tracking for a source file
   */
  registerFile(filePath: string, typeIds: string[]): void {
    if (!this.config.enableFileLevelTracking) return;

    // Calculate file content hash
    if (this.config.enableContentHashing && fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hash = crypto.createHash('sha256').update(content).digest('hex');
      this.fileContentHashes.set(filePath, hash);
    }

    // Track which types belong to this file
    this.fileProcessingGroups.set(filePath, typeIds);

    // Initialize file dependencies
    if (!this.fileDependencies.has(filePath)) {
      this.fileDependencies.set(filePath, new Set());
    }

    // Initialize file processing metrics
    if (!this.fileProcessingMetrics.has(filePath)) {
      this.fileProcessingMetrics.set(filePath, {
        lastProcessed: 0,
        processingTime: 0,
        changeCount: 0
      });
    }

    if (this.config.debug) {
      console.log(`📁 Registered file: ${filePath} with ${typeIds.length} types`);
    }
  }

  /**
   * Detect file-level changes with content hashing
   */
  async detectFileChanges(): Promise<Array<{ filePath: string; changeType: 'modified' | 'added' | 'removed'; affectedTypes: string[] }>> {
    const changes: Array<{ filePath: string; changeType: 'modified' | 'added' | 'removed'; affectedTypes: string[] }> = [];

    if (!this.config.enableFileLevelTracking) return changes;

    // Check for modified files
    for (const [filePath, oldHash] of this.fileContentHashes) {
      try {
        if (!fs.existsSync(filePath)) {
          // File removed
          const affectedTypes = this.fileProcessingGroups.get(filePath) || [];
          changes.push({
            filePath,
            changeType: 'removed',
            affectedTypes
          });

          // Clean up tracking data
          this.fileContentHashes.delete(filePath);
          this.fileProcessingGroups.delete(filePath);
          this.fileDependencies.delete(filePath);
          this.fileProcessingMetrics.delete(filePath);

          continue;
        }

        if (this.config.enableContentHashing) {
          const content = fs.readFileSync(filePath, 'utf8');
          const newHash = crypto.createHash('sha256').update(content).digest('hex');

          if (newHash !== oldHash) {
            // File modified
            const affectedTypes = this.fileProcessingGroups.get(filePath) || [];
            changes.push({
              filePath,
              changeType: 'modified',
              affectedTypes
            });

            // Update hash
            this.fileContentHashes.set(filePath, newHash);

            // Update metrics
            const metrics = this.fileProcessingMetrics.get(filePath);
            if (metrics) {
              metrics.changeCount++;
              this.fileProcessingMetrics.set(filePath, metrics);
            }
          }
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn(`⚠️ Error checking file ${filePath}:`, error);
        }
      }
    }

    return changes;
  }

  /**
   * Create optimized file processing batches
   */
  createFileProcessingBatches(changedFiles: string[]): Array<{ batchId: string; files: string[]; estimatedProcessingTime: number; priority: number }> {
    if (!this.config.enableSmartBatching) {
      // Simple batching
      const batches: Array<{ batchId: string; files: string[]; estimatedProcessingTime: number; priority: number }> = [];
      for (let i = 0; i < changedFiles.length; i += this.config.fileBatchSize) {
        const batchFiles = changedFiles.slice(i, i + this.config.fileBatchSize);
        batches.push({
          batchId: `batch-${Math.floor(i / this.config.fileBatchSize)}`,
          files: batchFiles,
          estimatedProcessingTime: batchFiles.length * 1000, // 1 second per file estimate
          priority: 1
        });
      }
      return batches;
    }

    // Smart batching based on dependencies and processing history
    const batches: Array<{ batchId: string; files: string[]; estimatedProcessingTime: number; priority: number }> = [];
    const processedFiles = new Set<string>();
    let batchCounter = 0;

    // Group files by dependency relationships
    const dependencyGroups = this.groupFilesByDependencies(changedFiles);

    for (const group of dependencyGroups) {
      const unprocessedFiles = group.filter(file => !processedFiles.has(file));
      if (unprocessedFiles.length === 0) continue;

      // Calculate batch priority based on file metrics
      const priority = this.calculateFileBatchPriority(unprocessedFiles);
      const estimatedTime = this.estimateBatchProcessingTime(unprocessedFiles);

      // Split large groups into smaller batches
      for (let i = 0; i < unprocessedFiles.length; i += this.config.fileBatchSize) {
        const batchFiles = unprocessedFiles.slice(i, i + this.config.fileBatchSize);
        batches.push({
          batchId: `smart-batch-${batchCounter++}`,
          files: batchFiles,
          estimatedProcessingTime: estimatedTime / Math.ceil(unprocessedFiles.length / this.config.fileBatchSize),
          priority
        });

        batchFiles.forEach(file => processedFiles.add(file));
      }
    }

    // Sort batches by priority (higher priority first)
    return batches.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Group files by their dependency relationships
   */
  private groupFilesByDependencies(files: string[]): string[][] {
    const groups: string[][] = [];
    const visited = new Set<string>();

    for (const file of files) {
      if (visited.has(file)) continue;

      const group: string[] = [];
      const toVisit = [file];

      while (toVisit.length > 0) {
        const currentFile = toVisit.pop()!;
        if (visited.has(currentFile)) continue;

        visited.add(currentFile);
        group.push(currentFile);

        // Add dependent files to the group
        const dependencies = this.fileDependencies.get(currentFile);
        if (dependencies) {
          for (const dep of dependencies) {
            if (files.includes(dep) && !visited.has(dep)) {
              toVisit.push(dep);
            }
          }
        }
      }

      if (group.length > 0) {
        groups.push(group);
      }
    }

    return groups;
  }

  /**
   * Calculate priority for a file batch based on processing history
   */
  private calculateFileBatchPriority(files: string[]): number {
    let totalPriority = 0;

    for (const file of files) {
      const metrics = this.fileProcessingMetrics.get(file);
      if (metrics) {
        // Higher priority for frequently changing files and files with many dependent types
        const changeFrequency = metrics.changeCount;
        const typeCount = this.fileProcessingGroups.get(file)?.length || 1;
        const dependentCount = this.fileDependencies.get(file)?.size || 0;

        totalPriority += (changeFrequency * 0.4) + (typeCount * 0.3) + (dependentCount * 0.3);
      } else {
        totalPriority += 1; // Default priority for new files
      }
    }

    return Math.round(totalPriority / files.length);
  }

  /**
   * Estimate processing time for a batch of files
   */
  private estimateBatchProcessingTime(files: string[]): number {
    let totalTime = 0;

    for (const file of files) {
      const metrics = this.fileProcessingMetrics.get(file);
      if (metrics && metrics.processingTime > 0) {
        totalTime += metrics.processingTime;
      } else {
        // Default estimate: 1 second per file
        totalTime += 1000;
      }
    }

    return totalTime;
  }

  /**
   * Record file processing metrics
   */
  recordFileProcessingTime(filePath: string, processingTime: number): void {
    const metrics = this.fileProcessingMetrics.get(filePath) || {
      lastProcessed: 0,
      processingTime: 0,
      changeCount: 0
    };

    metrics.lastProcessed = Date.now();
    metrics.processingTime = processingTime;
    this.fileProcessingMetrics.set(filePath, metrics);

    if (this.config.debug) {
      console.log(`⏱️ File ${filePath} processed in ${processingTime}ms`);
    }
  }

  /**
   * Process files in parallel batches
   */
  async processFileBatchesInParallel(
    batches: Array<{ batchId: string; files: string[]; estimatedProcessingTime: number; priority: number }>,
    processingFunction: (files: string[]) => Promise<void>
  ): Promise<void> {
    if (!this.config.enableParallelFileProcessing) {
      // Sequential processing
      for (const batch of batches) {
        const startTime = Date.now();
        await processingFunction(batch.files);
        const processingTime = Date.now() - startTime;

        // Record metrics for each file in the batch
        for (const file of batch.files) {
          this.recordFileProcessingTime(file, processingTime / batch.files.length);
        }
      }
      return;
    }

    // Parallel processing with concurrency control
    const maxConcurrency = Math.min(4, batches.length); // Limit to 4 concurrent batches

    const processBatch = async (batch: { batchId: string; files: string[]; estimatedProcessingTime: number; priority: number }) => {
      const startTime = Date.now();

      try {
        await processingFunction(batch.files);
        const processingTime = Date.now() - startTime;

        // Record metrics for each file in the batch
        for (const file of batch.files) {
          this.recordFileProcessingTime(file, processingTime / batch.files.length);
        }

        if (this.config.debug) {
          console.log(`✅ Batch ${batch.batchId} completed in ${processingTime}ms`);
        }
      } catch (error) {
        if (this.config.debug) {
          console.error(`❌ Batch ${batch.batchId} failed:`, error);
        }
        throw error;
      }
    };

    // Process batches with controlled concurrency
    const promises: Promise<void>[] = [];
    let batchIndex = 0;

    const processNext = async (): Promise<void> => {
      if (batchIndex >= batches.length) return;

      const batch = batches[batchIndex++];
      await processBatch(batch);

      // Process next batch
      await processNext();
    };

    // Start initial batches
    for (let i = 0; i < maxConcurrency && i < batches.length; i++) {
      promises.push(processNext());
    }

    await Promise.all(promises);
  }

  // Phase 3: Type-Level Incremental Processing Methods

  /**
   * Register type with granular field and directive tracking
   */
  registerTypeWithGranularTracking(
    id: string,
    name: string,
    kind: TypeDefinition['kind'],
    sourceFile: string,
    dependencies: string[] = [],
    outputFiles: string[] = [],
    metadata: Record<string, any> = {},
    fields: Array<{ name: string; type: string; directives: string[] }> = [],
    directives: Array<{ type: string; args: Record<string, any> }> = []
  ): TypeDefinition {
    // Calculate field-level hashes
    const fieldHashes = new Map<string, string>();
    for (const field of fields) {
      const fieldSignature = `${field.name}:${field.type}:${field.directives.join(',')}`;
      const fieldHash = crypto.createHash('sha256').update(fieldSignature).digest('hex');
      fieldHashes.set(field.name, fieldHash);
    }

    // Calculate directive-level hashes
    const directiveHashes = new Map<string, string>();
    for (const directive of directives) {
      const directiveSignature = `${directive.type}:${JSON.stringify(directive.args)}`;
      const directiveHash = crypto.createHash('sha256').update(directiveSignature).digest('hex');
      directiveHashes.set(directive.type, directiveHash);
    }

    // Calculate complete type signature
    const typeSignature = this.calculateTypeSignature(name, kind, fields, directives, dependencies);

    const existingType = this.typeDefinitions.get(id);
    const changeLevel = this.determineTypeChangeLevel(existingType, fieldHashes, directiveHashes, typeSignature);
    const processingPriority = this.calculateTypeProcessingPriority(id, changeLevel, dependencies.length);

    const typeDef: TypeDefinition = {
      id,
      name,
      kind,
      sourceFile,
      hash: typeSignature,
      lastProcessed: existingType?.lastProcessed || 0,
      dependencies,
      outputFiles,
      metadata: { ...existingType?.metadata, ...metadata },
      fieldHashes,
      directiveHashes,
      typeSignature,
      changeLevel,
      processingPriority
    };

    this.typeDefinitions.set(id, typeDef);

    // Update dependency graph with enhanced metadata
    if (this.config.enableDependencyTracking) {
      this.dependencyGraph.addNode(id, 'generated', sourceFile, dependencies, {
        typeName: name,
        typeKind: kind,
        outputFiles,
        changeLevel,
        processingPriority,
        fieldCount: fields.length,
        directiveCount: directives.length
      });
    }

    if (this.config.debug && changeLevel) {
      console.log(`🔍 Type ${name} registered with ${changeLevel}-level changes (priority: ${processingPriority})`);
    }

    return typeDef;
  }

  /**
   * Calculate complete type signature for change detection
   */
  private calculateTypeSignature(
    name: string,
    kind: TypeDefinition['kind'],
    fields: Array<{ name: string; type: string; directives: string[] }>,
    directives: Array<{ type: string; args: Record<string, any> }>,
    dependencies: string[]
  ): string {
    const components = [
      `name:${name}`,
      `kind:${kind}`,
      `fields:${fields.map(f => `${f.name}:${f.type}:${f.directives.join(',')}`).join('|')}`,
      `directives:${directives.map(d => `${d.type}:${JSON.stringify(d.args)}`).join('|')}`,
      `dependencies:${dependencies.sort().join(',')}`
    ];

    return crypto.createHash('sha256').update(components.join(':::')).digest('hex');
  }

  /**
   * Determine the granular level of change for a type
   */
  private determineTypeChangeLevel(
    existingType: TypeDefinition | undefined,
    newFieldHashes: Map<string, string>,
    newDirectiveHashes: Map<string, string>,
    newTypeSignature: string
  ): 'type' | 'field' | 'directive' | undefined {
    if (!existingType) return 'type'; // New type

    // Check if type signature changed
    if (existingType.typeSignature !== newTypeSignature) {
      // Determine granular level of change

      // Check directive-level changes first (most granular)
      if (existingType.directiveHashes) {
        for (const [directiveType, newHash] of newDirectiveHashes) {
          const oldHash = existingType.directiveHashes.get(directiveType);
          if (oldHash !== newHash) {
            return 'directive';
          }
        }

        // Check for removed directives
        for (const directiveType of existingType.directiveHashes.keys()) {
          if (!newDirectiveHashes.has(directiveType)) {
            return 'directive';
          }
        }
      }

      // Check field-level changes
      if (existingType.fieldHashes) {
        for (const [fieldName, newHash] of newFieldHashes) {
          const oldHash = existingType.fieldHashes.get(fieldName);
          if (oldHash !== newHash) {
            return 'field';
          }
        }

        // Check for removed fields
        for (const fieldName of existingType.fieldHashes.keys()) {
          if (!newFieldHashes.has(fieldName)) {
            return 'field';
          }
        }
      }

      // If we reach here, it's a type-level change
      return 'type';
    }

    return undefined; // No changes
  }

  /**
   * Calculate processing priority based on change level and impact
   */
  private calculateTypeProcessingPriority(
    typeId: string,
    changeLevel: 'type' | 'field' | 'directive' | undefined,
    dependencyCount: number
  ): number {
    if (!changeLevel) return 0; // No changes, no priority

    let basePriority = 0;

    // Priority based on change level (type changes have highest priority)
    switch (changeLevel) {
      case 'type':
        basePriority = 100;
        break;
      case 'field':
        basePriority = 50;
        break;
      case 'directive':
        basePriority = 25;
        break;
    }

    // Adjust based on dependency count (more dependencies = higher priority)
    const dependencyMultiplier = Math.min(dependencyCount * 0.1, 2.0); // Cap at 2x multiplier

    // Adjust based on dependent count from dependency graph
    const dependents = this.dependencyGraph.getDependents(typeId);
    const dependentMultiplier = Math.min(dependents.length * 0.05, 1.5); // Cap at 1.5x multiplier

    return Math.round(basePriority * (1 + dependencyMultiplier + dependentMultiplier));
  }

  /**
   * Create optimized type processing order based on dependencies and priorities
   */
  createOptimizedTypeProcessingOrder(typeIds: string[]): Array<{ typeId: string; priority: number; batchGroup: number }> {
    const typeData = typeIds.map(id => {
      const typeDef = this.typeDefinitions.get(id);
      return {
        typeId: id,
        priority: typeDef?.processingPriority || 0,
        dependencies: typeDef?.dependencies || [],
        changeLevel: typeDef?.changeLevel
      };
    });

    // Sort by priority first
    typeData.sort((a, b) => b.priority - a.priority);

    // Group types into processing batches based on dependencies
    const processingOrder: Array<{ typeId: string; priority: number; batchGroup: number }> = [];
    const processed = new Set<string>();
    let batchGroup = 0;

    while (processed.size < typeData.length) {
      const currentBatch: Array<{ typeId: string; priority: number; batchGroup: number }> = [];

      for (const type of typeData) {
        if (processed.has(type.typeId)) continue;

        // Check if all dependencies are already processed
        const dependenciesProcessed = type.dependencies.every(dep =>
          processed.has(dep) || !typeIds.includes(dep)
        );

        if (dependenciesProcessed) {
          currentBatch.push({
            typeId: type.typeId,
            priority: type.priority,
            batchGroup
          });
          processed.add(type.typeId);
        }
      }

      // Add current batch to processing order
      processingOrder.push(...currentBatch);
      batchGroup++;

      // Safety check to prevent infinite loops
      if (currentBatch.length === 0 && processed.size < typeData.length) {
        // Add remaining types (might have circular dependencies)
        for (const type of typeData) {
          if (!processed.has(type.typeId)) {
            processingOrder.push({
              typeId: type.typeId,
              priority: type.priority,
              batchGroup
            });
            processed.add(type.typeId);
          }
        }
        break;
      }
    }

    return processingOrder;
  }

  /**
   * Process types with minimal regeneration based on change levels
   */
  async processTypesWithMinimalRegeneration(
    typeIds: string[],
    processingFunction: (typeId: string, changeLevel: string | undefined) => Promise<void>
  ): Promise<{ processedTypes: number; skippedTypes: number; totalTime: number }> {
    const startTime = Date.now();
    let processedTypes = 0;
    let skippedTypes = 0;

    // Get optimized processing order
    const processingOrder = this.createOptimizedTypeProcessingOrder(typeIds);

    if (this.config.debug) {
      console.log(`🔄 Processing ${typeIds.length} types in optimized order with ${Math.max(...processingOrder.map(p => p.batchGroup)) + 1} batch groups`);
    }

    // Group by batch for potential parallel processing
    const batchGroups = new Map<number, Array<{ typeId: string; priority: number; batchGroup: number }>>();
    for (const item of processingOrder) {
      if (!batchGroups.has(item.batchGroup)) {
        batchGroups.set(item.batchGroup, []);
      }
      batchGroups.get(item.batchGroup)!.push(item);
    }

    // Process each batch group sequentially (dependencies), but types within batch can be parallel
    for (const [batchGroup, types] of batchGroups) {
      if (this.config.debug) {
        console.log(`📦 Processing batch group ${batchGroup} with ${types.length} types`);
      }

      // Process types in current batch (can be done in parallel)
      const batchPromises = types.map(async ({ typeId }) => {
        const typeDef = this.typeDefinitions.get(typeId);
        const changeLevel = typeDef?.changeLevel;

        if (!changeLevel) {
          // No changes detected, skip processing
          skippedTypes++;
          if (this.config.debug) {
            console.log(`⏭️ Skipping unchanged type: ${typeDef?.name || typeId}`);
          }
          return;
        }

        try {
          const typeStartTime = Date.now();
          await processingFunction(typeId, changeLevel);
          const typeProcessingTime = Date.now() - typeStartTime;

          // Record processing metrics in dependency graph
          this.dependencyGraph.recordProcessingMetrics(typeId, typeProcessingTime);

          processedTypes++;

          if (this.config.debug) {
            console.log(`✅ Processed type ${typeDef?.name || typeId} (${changeLevel}-level change) in ${typeProcessingTime}ms`);
          }
        } catch (error) {
          if (this.config.debug) {
            console.error(`❌ Failed to process type ${typeDef?.name || typeId}:`, error);
          }
          throw error;
        }
      });

      // Wait for current batch to complete before moving to next batch
      await Promise.all(batchPromises);
    }

    const totalTime = Date.now() - startTime;

    if (this.config.debug) {
      console.log(`🎯 Type processing complete: ${processedTypes} processed, ${skippedTypes} skipped in ${totalTime}ms`);
    }

    return { processedTypes, skippedTypes, totalTime };
  }
}

/**
 * Global incremental type processor instance
 */
let globalIncrementalTypeProcessor: IncrementalTypeProcessor | null = null;

/**
 * Get or create global incremental type processor
 */
export function getGlobalIncrementalTypeProcessor(
  config?: Partial<IncrementalConfig>,
  persistencePath?: string
): IncrementalTypeProcessor {
  if (!globalIncrementalTypeProcessor) {
    globalIncrementalTypeProcessor = new IncrementalTypeProcessor(config, persistencePath);
  }
  return globalIncrementalTypeProcessor;
}
