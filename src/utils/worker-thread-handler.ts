import { parentPort, workerData } from 'worker_threads';
import { DecoratorParser } from './decorator-parser';
import { parseTypeScriptFile } from './ts-parser';
import type { WorkerTask, WorkerResult } from './worker-thread-pool';

/**
 * Worker thread handler for processing tasks
 */
class WorkerThreadHandler {
  private decoratorParser: DecoratorParser;

  constructor() {
    this.decoratorParser = new DecoratorParser();
  }

  /**
   * Process a task
   */
  public async processTask(task: WorkerTask): Promise<WorkerResult> {
    const startTime = Date.now();

    try {
      let result: any;

      switch (task.type) {
        case 'decorator-scan':
          result = await this.processDecoratorScan(task);
          break;
        case 'ts-parse':
          result = await this.processTypeScriptParse(task);
          break;
        default:
          throw new Error(`Unknown task type: ${task.type}`);
      }

      const duration = Date.now() - startTime;

      return {
        taskId: task.id,
        success: true,
        result,
        duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        taskId: task.id,
        success: false,
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * Process decorator scanning task
   */
  private async processDecoratorScan(task: WorkerTask): Promise<any> {
    const { filePath } = task;

    // Scan the single file using the public method
    const container = await this.decoratorParser.scanSingleFile(filePath);

    return {
      filePath,
      decorators: {
        methodCalls: container.methodCalls,
        imports: container.imports,
        fields: container.fields,
        contexts: container.contexts
      }
    };
  }

  /**
   * Process TypeScript parsing task
   */
  private async processTypeScriptParse(task: WorkerTask): Promise<any> {
    const { filePath } = task;
    
    const result = parseTypeScriptFile(filePath);
    
    return {
      filePath,
      parsed: result
    };
  }
}

// Initialize worker handler
const handler = new WorkerThreadHandler();

// Listen for messages from main thread
if (parentPort) {
  parentPort.on('message', async (task: WorkerTask) => {
    try {
      const result = await handler.processTask(task);
      parentPort!.postMessage(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const result: WorkerResult = {
        taskId: task.id,
        success: false,
        error: errorMessage,
        duration: 0
      };
      parentPort!.postMessage(result);
    }
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught exception in worker thread:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason) => {
    console.error('Unhandled rejection in worker thread:', reason);
    process.exit(1);
  });
} else {
  console.error('Worker thread: parentPort is not available');
  process.exit(1);
}
