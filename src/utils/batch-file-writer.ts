import * as fs from 'fs-extra';
import * as path from 'path';
import { WatchedFileWriter } from './watched-file-writer';
import { getGlobalMemoryMappedFileReader, type MemoryMappedConfig } from './memory-mapped-file-reader';

/**
 * Configuration for BatchFileWriter
 */
export interface BatchFileWriterConfig {
  /** Maximum number of files to batch before flushing (default: 50) */
  batchSize?: number;
  /** Maximum memory usage in bytes before flushing (default: 100MB) */
  maxMemoryUsage?: number;
  /** Enable performance monitoring (default: true) */
  enablePerformanceMonitoring?: boolean;
  /** Enable logging (default: false) */
  enableLogging?: boolean;
  /** Auto-flush interval in milliseconds (default: 5000ms) */
  autoFlushInterval?: number;
  // Phase 3: Scalable I/O integration
  /** Enable hybrid I/O strategy with memory-mapped reads (default: true) */
  enableHybridIO?: boolean;
  /** Enable intelligent batching based on file size and type (default: true) */
  enableIntelligentBatching?: boolean;
  /** Threshold for large files that should use streaming (default: 10MB) */
  largeFileThreshold?: number;
  /** Maximum concurrent write operations (default: 10) */
  maxConcurrentWrites?: number;
}

/**
 * Represents a queued file write operation
 */
interface QueuedWrite {
  filePath: string;
  content: string;
  contentSize: number;
  queuedAt: number;
}

/**
 * Performance metrics for batch operations
 */
export interface BatchWriteMetrics {
  totalBatches: number;
  totalFiles: number;
  totalBytes: number;
  averageBatchSize: number;
  averageFlushTime: number;
  memoryPeakUsage: number;
  timesSaved: number;
  totalErrors: number;
  errorRate: number;
  lastErrorTime?: number;
}

/**
 * Error information for failed writes
 */
interface WriteError {
  filePath: string;
  error: Error;
  timestamp: number;
  retryCount: number;
}

/**
 * BatchFileWriter - Queues file writes and executes them in batches for improved I/O performance
 * Phase 3: Enhanced with scalable I/O architecture and hybrid memory-mapped operations
 */
export class BatchFileWriter {
  private config: Required<BatchFileWriterConfig>;
  private writeQueue: QueuedWrite[] = [];
  private currentMemoryUsage: number = 0;
  private metrics: BatchWriteMetrics;
  private autoFlushTimer?: NodeJS.Timeout;
  private isShuttingDown: boolean = false;
  private dynamicBatchSize: number;
  private memoryPressureLevel: number = 0; // 0-1 scale
  private errors: WriteError[] = [];
  private maxErrorHistory: number = 100;

  // Phase 3: Scalable I/O architecture
  private memoryReader = getGlobalMemoryMappedFileReader();
  private concurrentWrites = 0;
  private writeQueue_large: QueuedWrite[] = []; // Separate queue for large files
  private intelligentBatches = new Map<string, QueuedWrite[]>(); // Batches by file type/size

  constructor(config: BatchFileWriterConfig = {}) {
    this.config = {
      batchSize: config.batchSize ?? 50,
      maxMemoryUsage: config.maxMemoryUsage ?? 100 * 1024 * 1024, // 100MB
      enablePerformanceMonitoring: config.enablePerformanceMonitoring ?? true,
      enableLogging: config.enableLogging ?? false,
      autoFlushInterval: config.autoFlushInterval ?? 5000, // 5 seconds
      // Phase 3: Scalable I/O defaults
      enableHybridIO: config.enableHybridIO ?? true,
      enableIntelligentBatching: config.enableIntelligentBatching ?? true,
      largeFileThreshold: config.largeFileThreshold ?? 10 * 1024 * 1024, // 10MB
      maxConcurrentWrites: config.maxConcurrentWrites ?? 10,
    };

    this.metrics = {
      totalBatches: 0,
      totalFiles: 0,
      totalBytes: 0,
      averageBatchSize: 0,
      averageFlushTime: 0,
      memoryPeakUsage: 0,
      timesSaved: 0,
      totalErrors: 0,
      errorRate: 0,
    };

    // Initialize dynamic batch size
    this.dynamicBatchSize = this.config.batchSize;

    // Start auto-flush timer
    this.startAutoFlush();

    if (this.config.enableLogging) {
      console.log(`📦 BatchFileWriter initialized (batchSize: ${this.config.batchSize}, maxMemory: ${Math.round(this.config.maxMemoryUsage / 1024 / 1024)}MB)`);
    }
  }

  /**
   * Queue a file write operation
   * @param filePath Path to the file
   * @param content File content
   */
  public queueWrite(filePath: string, content: string): void {
    if (this.isShuttingDown) {
      throw new Error('BatchFileWriter is shutting down, cannot queue new writes');
    }

    const contentSize = Buffer.byteLength(content, 'utf8');
    const queuedWrite: QueuedWrite = {
      filePath,
      content,
      contentSize,
      queuedAt: Date.now(),
    };

    // Phase 3: Intelligent batching based on file characteristics
    if (this.config.enableIntelligentBatching) {
      this.queueWriteIntelligent(queuedWrite);
    } else {
      this.writeQueue.push(queuedWrite);
    }

    this.currentMemoryUsage += contentSize;

    // Update peak memory usage
    if (this.currentMemoryUsage > this.metrics.memoryPeakUsage) {
      this.metrics.memoryPeakUsage = this.currentMemoryUsage;
    }

    // Update memory pressure and adjust batch size dynamically
    this.updateMemoryPressure();
    this.adjustDynamicBatchSize();

    // Check if we need to flush due to batch size or memory pressure
    if (this.shouldFlush()) {
      // Don't await here to avoid blocking the caller
      this.flushAsync().catch(error => {
        console.error('❌ Error during async flush:', error);
      });
    }
  }

  /**
   * Flush all queued writes immediately
   * @returns Promise that resolves when all writes are complete
   */
  public async flushAll(): Promise<void> {
    // Check all queues when intelligent batching is enabled
    const totalQueuedFiles = this.getTotalQueuedFiles();
    if (totalQueuedFiles === 0) {
      return;
    }

    // Use hybrid flush if intelligent batching is enabled, otherwise use legacy flush
    if (this.config.enableIntelligentBatching) {
      return this.flushAllHybrid();
    }

    const startTime = Date.now();
    const batchSize = this.writeQueue.length;
    const totalBytes = this.currentMemoryUsage;

    if (this.config.enableLogging) {
      console.log(`🔄 Flushing ${batchSize} files (${Math.round(totalBytes / 1024)}KB)...`);
    }

    try {
      // Process all writes in parallel for maximum performance
      const writePromises = this.writeQueue.map(async (queuedWrite) => {
        try {
          // Use WatchedFileWriter to maintain compatibility with existing infrastructure
          await WatchedFileWriter.writeFile(queuedWrite.filePath, queuedWrite.content);
          return { success: true, filePath: queuedWrite.filePath };
        } catch (error) {
          this.recordError(queuedWrite.filePath, error as Error);
          return { success: false, filePath: queuedWrite.filePath, error: error as Error };
        }
      });

      const results = await Promise.all(writePromises);

      // Aggregate errors
      const errors = results.filter(r => !r.success);
      if (errors.length > 0) {
        const errorMessage = `Batch write failed for ${errors.length}/${results.length} files: ${errors.map(e => e.filePath).join(', ')}`;
        console.error(`❌ ${errorMessage}`);

        // Still throw error to maintain existing error handling behavior
        throw new Error(errorMessage);
      }

      // Update metrics
      if (this.config.enablePerformanceMonitoring) {
        const flushTime = Date.now() - startTime;
        this.updateMetrics(batchSize, totalBytes, flushTime);
      }

      // Clear the queue and reset memory usage
      this.writeQueue = [];
      this.currentMemoryUsage = 0;

      if (this.config.enableLogging) {
        const flushTime = Date.now() - startTime;
        console.log(`✅ Flushed ${batchSize} files in ${flushTime}ms`);
      }

    } catch (error) {
      console.error('❌ Batch flush failed:', error);
      throw error;
    }
  }

  /**
   * Get current performance metrics
   * @returns Current metrics
   */
  public getMetrics(): BatchWriteMetrics {
    return { ...this.metrics };
  }

  /**
   * Get current queue status
   * @returns Queue information
   */
  public getQueueStatus(): { queuedFiles: number; memoryUsage: number; oldestQueueTime: number } {
    // Get oldest queue time across all queues
    let oldestQueueTime = 0;
    const allWrites: QueuedWrite[] = [...this.writeQueue];

    if (this.config.enableIntelligentBatching) {
      allWrites.push(...this.writeQueue_large);
      for (const batch of this.intelligentBatches.values()) {
        allWrites.push(...batch);
      }
    }

    if (allWrites.length > 0) {
      oldestQueueTime = Math.min(...allWrites.map(w => w.queuedAt));
    }

    return {
      queuedFiles: this.getTotalQueuedFiles(),
      memoryUsage: this.currentMemoryUsage,
      oldestQueueTime,
    };
  }

  /**
   * Get recent errors
   * @param limit Maximum number of errors to return (default: 10)
   * @returns Recent error information
   */
  public getRecentErrors(limit: number = 10): WriteError[] {
    return this.errors
      .slice(-limit)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Shutdown the batch writer and flush all remaining writes
   */
  public async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop auto-flush timer
    if (this.autoFlushTimer) {
      clearInterval(this.autoFlushTimer);
      this.autoFlushTimer = undefined;
    }

    // Flush any remaining writes
    await this.flushAll();

    if (this.config.enableLogging) {
      console.log('📦 BatchFileWriter shutdown complete');
    }
  }

  /**
   * Check if we should flush based on current conditions
   * @private
   */
  private shouldFlush(): boolean {
    return (
      this.writeQueue.length >= this.dynamicBatchSize ||
      this.currentMemoryUsage >= this.config.maxMemoryUsage ||
      this.memoryPressureLevel > 0.8 // Flush early under high memory pressure
    );
  }

  /**
   * Update memory pressure level based on current usage
   * @private
   */
  private updateMemoryPressure(): void {
    this.memoryPressureLevel = this.currentMemoryUsage / this.config.maxMemoryUsage;
  }

  /**
   * Dynamically adjust batch size based on memory pressure
   * @private
   */
  private adjustDynamicBatchSize(): void {
    const baseBatchSize = this.config.batchSize;

    if (this.memoryPressureLevel > 0.8) {
      // High memory pressure: reduce batch size significantly
      this.dynamicBatchSize = Math.max(5, Math.floor(baseBatchSize * 0.3));
    } else if (this.memoryPressureLevel > 0.6) {
      // Medium memory pressure: reduce batch size moderately
      this.dynamicBatchSize = Math.max(10, Math.floor(baseBatchSize * 0.6));
    } else if (this.memoryPressureLevel > 0.4) {
      // Low memory pressure: slight reduction
      this.dynamicBatchSize = Math.max(15, Math.floor(baseBatchSize * 0.8));
    } else {
      // Low memory usage: use full batch size
      this.dynamicBatchSize = baseBatchSize;
    }

    // Log dynamic adjustments in debug mode
    if (this.config.enableLogging && this.dynamicBatchSize !== baseBatchSize) {
      console.log(`📊 Dynamic batch size adjusted to ${this.dynamicBatchSize} (pressure: ${Math.round(this.memoryPressureLevel * 100)}%)`);
    }
  }

  /**
   * Async flush without blocking the caller
   * @private
   */
  private async flushAsync(): Promise<void> {
    try {
      await this.flushAll();
    } catch (error) {
      console.error('❌ Async flush failed:', error);
    }
  }

  /**
   * Record an error for monitoring and aggregation
   * @private
   */
  private recordError(filePath: string, error: Error): void {
    const writeError: WriteError = {
      filePath,
      error,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.errors.push(writeError);

    // Limit error history to prevent memory growth
    if (this.errors.length > this.maxErrorHistory) {
      this.errors.shift();
    }

    // Update metrics
    this.metrics.totalErrors++;
    this.metrics.errorRate = this.metrics.totalErrors / Math.max(1, this.metrics.totalFiles);
    this.metrics.lastErrorTime = writeError.timestamp;
  }

  /**
   * Update performance metrics
   * @private
   */
  private updateMetrics(batchSize: number, totalBytes: number, flushTime: number): void {
    this.metrics.totalBatches++;
    this.metrics.totalFiles += batchSize;
    this.metrics.totalBytes += totalBytes;

    // Update averages
    this.metrics.averageBatchSize = this.metrics.totalFiles / this.metrics.totalBatches;
    this.metrics.averageFlushTime =
      (this.metrics.averageFlushTime * (this.metrics.totalBatches - 1) + flushTime) / this.metrics.totalBatches;

    // Update error rate
    this.metrics.errorRate = this.metrics.totalErrors / Math.max(1, this.metrics.totalFiles);

    // Estimate time saved (assuming individual writes would take 10ms each)
    const estimatedIndividualTime = batchSize * 10; // 10ms per individual write
    const timeSaved = Math.max(0, estimatedIndividualTime - flushTime);
    this.metrics.timesSaved += timeSaved;
  }

  /**
   * Start auto-flush timer
   * @private
   */
  private startAutoFlush(): void {
    this.autoFlushTimer = setInterval(() => {
      if (this.writeQueue.length > 0 && !this.isShuttingDown) {
        this.flushAsync().catch(error => {
          console.error('❌ Auto-flush failed:', error);
        });
      }
    }, this.config.autoFlushInterval);
  }

  // Phase 3: Scalable I/O Architecture Methods

  /**
   * Intelligent batching based on file characteristics
   */
  private queueWriteIntelligent(queuedWrite: QueuedWrite): void {
    const fileExt = path.extname(queuedWrite.filePath).toLowerCase();
    const isLargeFile = queuedWrite.contentSize > this.config.largeFileThreshold;

    if (isLargeFile) {
      // Large files go to separate queue for streaming writes
      this.writeQueue_large.push(queuedWrite);
    } else {
      // Group by file type for better batching efficiency
      const batchKey = this.getBatchKey(queuedWrite);

      if (!this.intelligentBatches.has(batchKey)) {
        this.intelligentBatches.set(batchKey, []);
      }

      this.intelligentBatches.get(batchKey)!.push(queuedWrite);
    }
  }

  /**
   * Get batch key for intelligent grouping
   */
  private getBatchKey(queuedWrite: QueuedWrite): string {
    const fileExt = path.extname(queuedWrite.filePath).toLowerCase();
    const sizeCategory = this.getSizeCategory(queuedWrite.contentSize);
    const dirPath = path.dirname(queuedWrite.filePath);

    // Group by: file extension + size category + directory (for locality)
    return `${fileExt}:${sizeCategory}:${dirPath}`;
  }

  /**
   * Categorize file size for batching
   */
  private getSizeCategory(size: number): string {
    if (size < 1024) return 'tiny';           // < 1KB
    if (size < 10 * 1024) return 'small';    // < 10KB
    if (size < 100 * 1024) return 'medium';  // < 100KB
    if (size < 1024 * 1024) return 'large';  // < 1MB
    return 'xlarge';                          // >= 1MB
  }

  /**
   * Flush all queues with hybrid I/O strategy
   */
  public async flushAllHybrid(): Promise<void> {
    if (!this.config.enableHybridIO) {
      return this.flushAll();
    }

    const startTime = Date.now();
    const allWrites: QueuedWrite[] = [];

    // Collect all writes from different queues
    allWrites.push(...this.writeQueue);
    allWrites.push(...this.writeQueue_large);

    for (const batch of this.intelligentBatches.values()) {
      allWrites.push(...batch);
    }

    if (allWrites.length === 0) return;

    try {
      // Process large files with streaming
      const largeFiles = allWrites.filter(w => w.contentSize > this.config.largeFileThreshold);
      const regularFiles = allWrites.filter(w => w.contentSize <= this.config.largeFileThreshold);

      // Process large files sequentially with streaming
      for (const largeFile of largeFiles) {
        await this.writeFileWithStreaming(largeFile);
      }

      // Process regular files in parallel batches
      await this.writeFilesBatched(regularFiles);

      // Clear all queues
      this.writeQueue = [];
      this.writeQueue_large = [];
      this.intelligentBatches.clear();
      this.currentMemoryUsage = 0;

      // Update metrics
      const flushTime = Date.now() - startTime;
      this.updateMetrics(allWrites.length, allWrites.reduce((sum, w) => sum + w.contentSize, 0), flushTime);

      if (this.config.enableLogging) {
        console.log(`✅ Hybrid flush completed: ${allWrites.length} files in ${flushTime}ms`);
      }

    } catch (error) {
      if (this.config.enableLogging) {
        console.error('❌ Hybrid flush failed:', error);
      }
      throw error;
    }
  }

  /**
   * Write large file with streaming to reduce memory pressure
   */
  private async writeFileWithStreaming(queuedWrite: QueuedWrite): Promise<void> {
    try {
      // Ensure directory exists
      await fs.ensureDir(path.dirname(queuedWrite.filePath));

      // Use streaming write for large files
      const writeStream = fs.createWriteStream(queuedWrite.filePath);

      return new Promise((resolve, reject) => {
        writeStream.on('error', reject);
        writeStream.on('finish', resolve);

        // Write content in chunks to reduce memory pressure
        const chunkSize = 64 * 1024; // 64KB chunks
        let offset = 0;

        const writeNextChunk = () => {
          if (offset >= queuedWrite.content.length) {
            writeStream.end();
            return;
          }

          const chunk = queuedWrite.content.slice(offset, offset + chunkSize);
          writeStream.write(chunk);
          offset += chunkSize;

          // Use setImmediate to avoid blocking the event loop
          setImmediate(writeNextChunk);
        };

        writeNextChunk();
      });

    } catch (error) {
      this.recordError(queuedWrite.filePath, error as Error);
      throw error;
    }
  }

  /**
   * Write regular files in optimized batches
   */
  private async writeFilesBatched(writes: QueuedWrite[]): Promise<void> {
    const batchSize = Math.min(this.config.maxConcurrentWrites, writes.length);

    for (let i = 0; i < writes.length; i += batchSize) {
      const batch = writes.slice(i, i + batchSize);

      // Process batch in parallel with concurrency control
      await Promise.all(
        batch.map(async (queuedWrite) => {
          if (this.concurrentWrites >= this.config.maxConcurrentWrites) {
            // Wait for a slot to become available
            await new Promise(resolve => setTimeout(resolve, 10));
          }

          this.concurrentWrites++;

          try {
            await WatchedFileWriter.writeFile(queuedWrite.filePath, queuedWrite.content);
          } finally {
            this.concurrentWrites--;
          }
        })
      );
    }
  }

  /**
   * Get total number of files queued across all queues
   * @private
   */
  private getTotalQueuedFiles(): number {
    let total = this.writeQueue.length;

    if (this.config.enableIntelligentBatching) {
      total += this.writeQueue_large.length;

      for (const batch of this.intelligentBatches.values()) {
        total += batch.length;
      }
    }

    return total;
  }

  /**
   * Get enhanced statistics including Phase 3 metrics
   */
  public getEnhancedStats() {
    const baseStats = {
      queueSize: this.writeQueue.length,
      currentMemoryUsage: this.currentMemoryUsage,
      memoryPeakUsage: this.metrics.memoryPeakUsage,
      totalBatches: this.metrics.totalBatches,
      totalFiles: this.metrics.totalFiles,
      totalBytes: this.metrics.totalBytes,
      averageFlushTime: this.metrics.averageFlushTime,
      errors: this.errors
    };

    return {
      ...baseStats,
      // Phase 3 metrics
      hybridIOEnabled: this.config.enableHybridIO,
      intelligentBatchingEnabled: this.config.enableIntelligentBatching,
      largeFileThreshold: this.config.largeFileThreshold,
      maxConcurrentWrites: this.config.maxConcurrentWrites,
      currentConcurrentWrites: this.concurrentWrites,
      largeFileQueueSize: this.writeQueue_large.length,
      intelligentBatchCount: this.intelligentBatches.size,
      memoryReaderStats: this.memoryReader.getStats()
    };
  }
}

// Global instance for easy access
let globalBatchWriter: BatchFileWriter | null = null;

/**
 * Get the global BatchFileWriter instance
 * @param config Optional configuration for first-time initialization
 * @returns Global BatchFileWriter instance
 */
export function getGlobalBatchFileWriter(config?: BatchFileWriterConfig): BatchFileWriter {
  if (!globalBatchWriter) {
    globalBatchWriter = new BatchFileWriter(config);
  }
  return globalBatchWriter;
}

/**
 * Shutdown the global BatchFileWriter instance
 */
export async function shutdownGlobalBatchFileWriter(): Promise<void> {
  if (globalBatchWriter) {
    await globalBatchWriter.shutdown();
    globalBatchWriter = null;
  }
}
