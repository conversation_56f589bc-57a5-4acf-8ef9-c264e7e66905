import * as fs from 'fs-extra';
import type { FieldDirectiveField, DirectiveContainer } from './directive-parser';
import { DirectiveParser } from './directive-parser';
import { findClosingBraceIndex } from './type-converters';

/**
 * Special processing for all types to ensure we catch top-level field directives
 * @param schemaFiles All schema files
 * @param typeToFieldFields Map to populate with field directive fields
 * @param debug Enable debug logging
 */
export async function processAllTypesWithFieldDirectives(
    schemaFiles: string[],
    typeToFieldFields: Map<string, FieldDirectiveField[]>,
    debug: boolean = false
): Promise<void> {
    if (schemaFiles.length === 0) {
        if (debug) {
            console.log('No schema files found');
        }
        return;
    }

    // Process each schema file
    for (const schemaFile of schemaFiles) {
        if (process.env.DEBUG_PARSER) {
            console.log(`Processing schema file for field directives: ${schemaFile}`);
        }

        try {
            // Read the file content
            const content = fs.readFileSync(schemaFile, 'utf8');
            const lines = content.split('\n');

            // Find all type, interface, and union definitions in the file
            const typeDefRegex = /(?:type|interface)\s+(\w+)\s*(?:implements\s+[\w\s,&]+)?\s*\{|union\s+(\w+)\s*=/g;
            const typeDefs: { name: string; index: number }[] = [];
            let match;
            const fileContent = content.toString();

            while ((match = typeDefRegex.exec(fileContent)) !== null) {
                // Union types are captured in group 2, type/interface in group 1
                const typeName = match[1] || match[2];
                const lineIndex = content.substring(0, match.index).split('\n').length - 1;
                typeDefs.push({ name: typeName, index: lineIndex });

                if (process.env.DEBUG_PARSER) {
                    console.log(`Found type definition: ${typeName} at line ${lineIndex}`);
                }
            }

            // Process each type definition
            for (const typeDef of typeDefs) {
                const typeName = typeDef.name;
                const typeDefIndex = typeDef.index;

                if (process.env.DEBUG_PARSER) {
                    console.log(`Processing type: ${typeName} at line ${typeDefIndex}`);
                }

                // Create a container for directives
                const directives: DirectiveContainer = {
                    imports: [],
                    methodCalls: [],
                    fieldFields: [],
                    others: {},
                };

                // Find directives immediately before the type definition
                // Look backwards from the type definition line, stopping at the first non-comment, non-empty line
                const directiveLines: number[] = [];

                for (let i = typeDefIndex - 1; i >= 0; i--) {
                    const line = lines[i].trim();

                    if (line.startsWith('#')) {
                        // This is a comment line, add it to the list
                        directiveLines.unshift(i); // Add to beginning to maintain order
                    } else if (line === '') {
                        // Empty line, continue looking backwards
                        continue;
                    } else {
                        // Non-empty, non-comment line - stop here
                        break;
                    }
                }

                // Process the directive lines we found
                for (const i of directiveLines) {
                    const line = lines[i].trim();
                    // Try to parse as a directive
                    const directiveRegex = /^\s*#\s*@([a-zA-Z][a-zA-Z0-9_]*)\s*(?:\((.*)\))??\s*$/;
                    const match = line.match(directiveRegex);

                    if (match) {
                        const [_, name, content = ''] = match;

                        if (process.env.DEBUG_PARSER) {
                            console.log(`Found directive at line ${i}: @${name}(${content})`);
                        }

                        if (name === 'field') {
                            // Use the proper DirectiveParser to handle multiple fields and complex types
                            DirectiveParser.parseFieldDirectiveFields(content, directives, debug);
                        }
                    }
                }

                // Find @field directives within the type body as well
                const endTypeIndex = findClosingBraceIndex(lines, typeDefIndex);
                if (endTypeIndex > typeDefIndex) {
                    for (let i = typeDefIndex + 1; i < endTypeIndex; i++) {
                        const line = lines[i].trim();
                        if (line.startsWith('#')) {
                            // Try to parse as a directive
                            const directiveRegex = /^\s*#\s*@([a-zA-Z][a-zA-Z0-9_]*)\s*(?:\((.*)\))??\s*$/;
                            const match = line.match(directiveRegex);

                            if (match) {
                                const [_, name, content = ''] = match;

                                if (process.env.DEBUG_PARSER) {
                                    console.log(`Found directive at line ${i}: @${name}(${content})`);
                                }

                                if (name === 'field') {
                                    // For field-level directives, we need to find the field name
                                    // Look for the next field definition after this directive
                                    let fieldName: string | undefined;
                                    for (let j = i + 1; j < endTypeIndex; j++) {
                                        const nextLine = lines[j].trim();
                                        if (nextLine && !nextLine.startsWith('#') && !nextLine.startsWith('//')) {
                                            // Check if this is a field definition
                                            const fieldMatch = nextLine.match(/^\s*([a-zA-Z][a-zA-Z0-9_]*)\s*[:(]/);
                                            if (fieldMatch) {
                                                fieldName = fieldMatch[1];
                                                if (debug) {
                                                    console.log(`Found field ${fieldName} after directive at line ${j}: ${nextLine}`);
                                                }
                                                break;
                                            }
                                        }
                                    }

                                    // Use the proper DirectiveParser to handle multiple fields and complex types
                                    DirectiveParser.parseFieldDirectiveFields(content, directives, debug, fieldName);
                                }
                            }
                        }
                    }
                }

                // If we found any field directive fields, add them to the map
                if (directives.fieldFields.length > 0) {
                    if (debug) {
                        console.log(`Found ${directives.fieldFields.length} field directive fields for type ${typeName}:`);
                        directives.fieldFields.forEach((field, index) => {
                            console.log(`  ${index + 1}. ${field.name}: ${field.type}${field.importPath ? ` (from ${field.importPath})` : ''}`);
                        });
                    }

                    // Add to the map (merge with existing if any)
                    const existingFields = typeToFieldFields.get(typeName) || [];
                    typeToFieldFields.set(typeName, [...existingFields, ...directives.fieldFields]);
                }
            }
        } catch (error) {
            console.error(`Error processing schema file ${schemaFile}:`, error);
        }
    }

    if (process.env.DEBUG_PARSER) {
        console.log(`Finished processing ${schemaFiles.length} schema files for field directives`);
        console.log(`Found field directive fields for ${typeToFieldFields.size} types`);
    }
}