import fs from 'fs-extra';

// Debug logger function
const debugLog = (debug: boolean, message: string, ...data: any[]) => {
    if (debug) {
        if (process.env.DEBUG_PARSER) {
          console.log(`[DEBUG] ${message}`, ...data);
        }
    }
};

/**
 * Verifies a generated file for existence and valid content
 * @param filePath Path to the file to verify
 * @param debug Whether to enable debug logging
 */
export const verifyGeneratedFile = (filePath: string, debug: boolean): void => {
    try {
        if (!fs.existsSync(filePath)) {
            if (process.env.DEBUG_PARSER) {
              console.warn(`[VERIFY] Generated file does not exist: ${filePath}`);
            }
            return;
        }

        const stats = fs.statSync(filePath);
        debugLog(debug, `[VERIFY] File size: ${stats.size} bytes for ${filePath}`);

        if (filePath.endsWith('graphql.ts')) {
            // For graphql.ts specifically, log more information
            const content = fs.readFileSync(filePath, 'utf8');
            debugLog(debug, `[VERIFY] graphql.ts content length: ${content.length} characters`);
            debugLog(debug, `[VERIFY] graphql.ts line count: ${content.split('\n').length} lines`);

            // Only show detailed verification in debug mode
            if (debug) {
                // Log beginning and end of file to check for truncation
                const lines = content.split('\n');
                const numLinesToShow = 10;

                if (process.env.DEBUG_PARSER) {
                  console.log(`\n===== GRAPHQL.TS FILE VERIFICATION =====`);
                }
                if (process.env.DEBUG_PARSER) {
                  console.log(`File path: ${filePath}`);
                }
                if (process.env.DEBUG_PARSER) {
                  console.log(`File size: ${stats.size} bytes`);
                }
                if (process.env.DEBUG_PARSER) {
                  console.log(`Line count: ${lines.length} lines`);
                }

                if (process.env.DEBUG_PARSER) {
                  console.log(`\n----- First ${numLinesToShow} lines -----`);
                }
                if (process.env.DEBUG_PARSER) {
                  console.log(lines.slice(0, numLinesToShow).join('\n'));
                }

                if (process.env.DEBUG_PARSER) {
                  console.log(`\n----- Last ${numLinesToShow} lines -----`);
                }
                if (process.env.DEBUG_PARSER) {
                  console.log(lines.slice(-numLinesToShow).join('\n'));
                }

                if (process.env.DEBUG_PARSER) {
                  console.log(`\n===== END VERIFICATION =====\n`);
                }
            }
        }
    } catch (error: any) {
        console.error(`[VERIFY] Error verifying generated file ${filePath}:`, error.message);
    }
}; 