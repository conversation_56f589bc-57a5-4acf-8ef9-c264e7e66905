import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Represents a parsed directive from a comment
 */
export interface ParsedDirective {
  /** Name of the directive (e.g., 'import', 'methodCall') */
  name: string;
  /** The content inside the directive parentheses */
  content: string;
  /** The raw directive string including @ symbol and parentheses */
  raw: string;
}

/**
 * Represents parsed parameters for the @default directive
 */
export interface DefaultDirectiveParams {
  /** Whether to return the fallback value directly (true) or use it as fallback (false) */
  directReturn: boolean;
  /** The fallback value or expression to use */
  fallback?: string;
}

/**
 * Represents a parsed field directive field
 */
export interface FieldDirectiveField {
  /** Name of the field */
  name: string;
  /** Type of the field */
  type: string;
  /** Raw string of the field definition */
  raw: string;
  /** Whether the field is optional (uses ? syntax) */
  optional?: boolean;
  /** Import path for the type (if provided) */
  importPath?: string;
  /** Path for custom types */
  path?: string;
  /** Exported name of the type (if different from type) */
  exportedName?: string;
  /** Whether this field should be hidden from schema generation */
  hidden?: boolean;
}

/**
 * @deprecated Use FieldDirectiveField instead
 * Type alias for backward compatibility
 */
export type DefField = FieldDirectiveField;

/**
 * Stores directive information for a GraphQL type or field
 */
export interface DirectiveContainer {
  /** Import directives */
  imports: ParsedDirective[];
  /** Method call directives */
  methodCalls: ParsedDirective[];
  /** Fields from @field directive */
  fieldFields: FieldDirectiveField[];
  /** Other directives */
  others: Record<string, ParsedDirective[]>;
}

/**
 * Parses directives from GraphQL schema comments
 */
export class DirectiveParser {
  /**
   * Regular expression to match directive patterns in comments
   * Format: # @directiveName(directiveContent) or # @directiveName
   */
  private static DIRECTIVE_REGEX = /^\s*#\s*@([a-zA-Z][a-zA-Z0-9_]*)\s*(?:\((.*)\))??\s*$/;

  /**
   * Regular expression to match field directive fields in parentheses
   * This regex handles complex types with nested structures, commas, brackets, and union types
   */
  private static FIELD_DIRECTIVE_REGEX =
    /\s*([a-zA-Z][a-zA-Z0-9_]*)\s*(\??)?\s*:\s*((?:[^,<\[\{]|\||<[^>]*>|\[[^\]]*\]|\{[^}]*\})+)(?:,|$)/g;

  /**
   * Extracts directives from a GraphQL schema file for a specific type or field
   * @param schemaFilePath Path to the GraphQL schema file
   * @param typeName The name of the type to extract directives for
   * @param fieldName Optional field name to extract directives for
   * @param debug Enable debug logging
   * @returns Parsed directives for the type or field
   */
  public static async extractDirectivesFromSchema(
    schemaFilePath: string,
    typeName: string,
    fieldName?: string,
    debug: boolean = false
  ): Promise<DirectiveContainer> {
    console.log(`[DEBUG] DirectiveParser.extractDirectivesFromSchema called for: ${schemaFilePath}, type: ${typeName}, field: ${fieldName}`);
    // Use legacy parser for robust stability
    // The optimized parser has known issues with interface field directive parsing
    // and complex state management that can cause inconsistent behavior
    try {
      // Initialize directive container
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };

      // Check if the schema file exists
      if (!fs.existsSync(schemaFilePath)) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Schema file not found: ${schemaFilePath}`);
        }

        // Try to find the schema file by checking common combinations
        const possiblePaths = [
          schemaFilePath,
          `${schemaFilePath}.gql`,
          `${schemaFilePath}.graphql`,
        ];

        // If the path contains a glob pattern, try to extract a proper path
        if (schemaFilePath.includes('*')) {
          const parts = schemaFilePath.split('/');
          const nonGlobParts = [];

          for (const part of parts) {
            if (part.includes('*')) {
              break;
            }
            nonGlobParts.push(part);
          }

          const schemaDir = nonGlobParts.join('/');

          // Add paths relative to the schema directory
          const relativePath = schemaFilePath.substring(schemaDir.length + 1);
          if (relativePath) {
            possiblePaths.push(path.join(process.cwd(), relativePath));
            possiblePaths.push(path.join('schema', relativePath));
            possiblePaths.push(path.join(process.cwd(), 'schema', relativePath));
          }
        } else {
          // Add additional paths
          possiblePaths.push(path.join('schema', schemaFilePath));
          possiblePaths.push(path.join(process.cwd(), 'schema', schemaFilePath));
          possiblePaths.push(path.join(process.cwd(), 'schema', `${schemaFilePath}.gql`));
          possiblePaths.push(path.join(process.cwd(), 'schema', `${schemaFilePath}.graphql`));
        }

        let found = false;
        for (const testPath of possiblePaths) {
          if (fs.existsSync(testPath)) {
            if (debug) {
              if (process.env.DEBUG_PARSER) {
                console.log(`Found schema file at alternative path: ${testPath}`);
              }
            }
            schemaFilePath = testPath;
            found = true;
            break;
          }
        }

        if (!found) {
          return directives;
        }
      } else if (debug) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Schema file found: ${schemaFilePath}`);
        }
      }

      // Read the schema file - ensure we're reading the latest content
      // Use fs.promises.readFile with no caching options
      const schemaContent = await fs.promises.readFile(schemaFilePath, 'utf8');
      const lines = schemaContent.split('\n');

      // If looking for type directives
      if (!fieldName) {
        // Find the type definition
        const typeDefIndex = lines.findIndex(
          line =>
            line.includes(`type ${typeName}`) ||
            line.includes(`interface ${typeName}`) ||
            line.includes(`union ${typeName}`)
        );

        if (debug) {
          console.log(
            `Looking for type ${typeName} in ${schemaFilePath} - found at line ${typeDefIndex}`
          );
        }

        if (typeDefIndex >= 0) {
          // Look for comment directives before the type definition
          let lineIndex = typeDefIndex - 1;

          // Continue searching through empty lines to find directive comments
          while (lineIndex >= 0) {
            const line = lines[lineIndex].trim();

            if (line.startsWith('#')) {
              this.parseDirectiveLine(lines[lineIndex], directives, debug);
            } else if (line !== '') {
              // Stop if we hit a non-empty, non-comment line
              break;
            }
            // Continue if it's an empty line
            lineIndex--;
          }

          // Also look for comment directives inside the type definition
          // But exclude directives that are positioned right before field definitions
          for (let i = typeDefIndex + 1; i < lines.length; i++) {
            const line = lines[i].trim();

            // Stop if we reach the end of the type definition
            if (line === '}') break;

            // If this is a comment directive
            if (line.startsWith('#')) {
              // Check if this directive is positioned right before a field definition
              let isFieldDirective = false;

              // Look ahead to see if the next non-empty line is a field definition
              for (let j = i + 1; j < lines.length; j++) {
                const nextLine = lines[j].trim();

                // Skip empty lines
                if (nextLine === '') continue;

                // If we hit the end of the type or another directive, it's not a field directive
                if (nextLine === '}' || nextLine.startsWith('#')) break;

                // If this looks like a field definition (fieldName: Type), it's a field directive
                if (nextLine.includes(':') && !nextLine.includes('//') && !nextLine.includes('/*')) {
                  isFieldDirective = true;
                  break;
                }

                // If we hit any other content, stop looking
                break;
              }

              // Only process as type-level directive if it's not a field directive
              if (!isFieldDirective) {
                if (debug) {
                  if (process.env.DEBUG_PARSER) {
                    console.log(`Found type-level directive comment inside type ${typeName} at line ${i}: ${line}`);
                  }
                }
                this.parseDirectiveLine(lines[i], directives, debug);
              } else {
                if (debug) {
                  if (process.env.DEBUG_PARSER) {
                    console.log(`Skipping field-level directive at line ${i} (will be processed as field directive): ${line}`);
                  }
                }
              }
            }
          }
        }
      }
      // If looking for field directives
      else {
        // Find the field definition within the type
        const typeDefIndex = lines.findIndex(
          line =>
            line.includes(`type ${typeName}`) ||
            line.includes(`interface ${typeName}`) ||
            line.includes(`union ${typeName}`)
        );

        if (debug) {
          console.log(
            `Looking for field ${fieldName} in type ${typeName} - type found at line ${typeDefIndex}`
          );
        }

        if (typeDefIndex >= 0) {
          // Find the field definition
          for (let i = typeDefIndex + 1; i < lines.length; i++) {
            const line = lines[i].trim();

            // Stop if we reach the end of the type definition
            if (line === '}') break;

            // If this is the field definition line
            if (
              line.startsWith(fieldName) &&
              (line.includes(`${fieldName}:`) || line.includes(`${fieldName}(`))
            ) {
              if (debug) {
                if (process.env.DEBUG_PARSER) {
                  console.log(`Found field ${fieldName} at line ${i}: ${line}`);
                }
              }

              // Look for comment directives before the field definition
              let lineIndex = i - 1;

              // Continue searching through empty lines to find directive comments
              while (lineIndex >= 0) {
                const line = lines[lineIndex].trim();

                if (line.startsWith('#')) {
                  if (debug) {
                    if (process.env.DEBUG_PARSER) {
                      console.log(`Found directive comment at line ${lineIndex}: ${line}`);
                    }
                  }
                  this.parseDirectiveLine(lines[lineIndex], directives, debug, fieldName);
                } else if (line !== '') {
                  // Stop if we hit a non-empty, non-comment line
                  break;
                }
                // Continue if it's an empty line
                lineIndex--;
              }
              break;
            }
          }
        }
      }

      return directives;
    } catch (error) {
      console.error(`Error extracting directives from schema: ${error}`);
      return {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };
    }
  }

  /**
   * Parse a single directive line and add to directive container
   * @param line The comment line to parse
   * @param directives Container to add parsed directives to
   * @param debug Enable debug logging
   * @param fieldName Optional field name for field-level directive context
   */
  private static parseDirectiveLine(
    line: string,
    directives: DirectiveContainer,
    debug: boolean = false,
    fieldName?: string
  ): void {
    console.log(`[DEBUG] Processing line: "${line}"`);
    const match = line.match(this.DIRECTIVE_REGEX);
    if (!match) {
      console.log(`[DEBUG] No regex match for line: "${line}"`);
      return;
    }
    console.log(`[DEBUG] Regex matched: ${match[0]}`);

    if (debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`Parsed directive: ${match[0]}`);
      }
    }

    const [raw, name, content = ''] = match;
    const directive: ParsedDirective = { name, content, raw };

    // Sort directive by type
    if (name === 'import') {
      console.log(`[DEBUG] Found import directive: ${raw}`);
      directives.imports.push(directive);
    } else if (name === 'methodCall') {
      console.log(`[DEBUG] Found methodCall directive: ${raw}`);
      directives.methodCalls.push(directive);
    } else if (name === 'field') {
      console.log(`[DEBUG] Found field directive: ${raw}`);
      // Parse field directive fields
      this.parseFieldDirectiveFields(content, directives, debug, fieldName);
    } else {
      // Store other directives by name
      directives.others[name] ??= [];
      directives.others[name].push(directive);
    }
  }

  /**
   * Parse @default directive parameters
   * @param content The content inside the @default directive parentheses
   * @returns Parsed default directive parameters
   */
  public static parseDefaultDirective(content: string): DefaultDirectiveParams {
    // Default values
    const params: DefaultDirectiveParams = {
      directReturn: false,
      fallback: undefined,
    };

    // If no content, return defaults (backward compatibility)
    if (!content || content.trim() === '') {
      return params;
    }

    const trimmedContent = content.trim();

    // Check for new direct syntax: @default(direct: "something")
    const directMatch = trimmedContent.match(/^direct\s*:\s*(.+)$/);
    if (directMatch) {
      params.directReturn = true;
      params.fallback = directMatch[1].trim();
      return params;
    }

    // Check for legacy direct return syntax: @default(true, fallback) or @default(false, fallback)
    // Keep for backward compatibility
    const directReturnMatch = trimmedContent.match(/^(true|false)\s*,\s*(.+)$/);
    if (directReturnMatch) {
      params.directReturn = directReturnMatch[1] === 'true';
      params.fallback = directReturnMatch[2].trim();
      return params;
    }

    // Check for simple fallback syntax: @default(fallback)
    if (trimmedContent) {
      params.directReturn = false;
      params.fallback = trimmedContent;
      return params;
    }

    return params;
  }

  /**
   * Parse field directive fields and add to directive container
   * @param content The content inside field directive parentheses
   * @param directives Container to add parsed fields to
   * @param debug Enable debug logging
   * @param fieldName Optional field name for field-level directive context
   */
  public static parseFieldDirectiveFields(
    content: string,
    directives: DirectiveContainer,
    debug: boolean = false,
    fieldName?: string
  ): void {
    if (debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`Parsing field directive content: ${content}`);
      }
    }

    // Handle field-level @field directives first (type overrides without field names)
    // Format: @field(Type) or @field(Type "importPath") or @field(ComplexType<T> "importPath")
    if (!content.includes(',') && !content.includes(':')) {
      // This is a field-level directive for type override
      const fieldLevelRegex = /^\s*([^"]+?)(?:\s+"([^"]+)"\s*)?$/;
      const match = content.match(fieldLevelRegex);

      if (match) {
        const [, typeStr, importPath] = match;
        const cleanType = typeStr.trim();

        if (cleanType) {
          const fieldDirectiveField: FieldDirectiveField = {
            name: fieldName || cleanType, // Use field name if available, otherwise use type name
            type: cleanType,
            raw: content.trim(),
            path: importPath?.trim(),
          };

          // Set importPath if path is provided (for backward compatibility)
          if (importPath?.trim()) {
            fieldDirectiveField.importPath = importPath.trim();
          }

          directives.fieldFields.push(fieldDirectiveField);

          if (debug) {
            const pathInfo =
              fieldDirectiveField.importPath || fieldDirectiveField.path
                ? ` (path: ${fieldDirectiveField.importPath || fieldDirectiveField.path})`
                : '';
            if (fieldName) {
              if (process.env.DEBUG_PARSER) {
                console.log(`Added field-level override for ${fieldName}: ${cleanType}${pathInfo}`);
              }
            } else {
              if (process.env.DEBUG_PARSER) {
                console.log(`Added field-level override: ${cleanType}${pathInfo}`);
              }
            }
          }
          return;
        }
      }
    }

    // Handle enhanced format with imports: fieldName: Type "importPath"
    const hasImportPaths = content.includes('"');
    let foundImports = false;

    if (hasImportPaths) {
      // Enhanced format regex: fieldName: Type "importPath" or fieldName: Type "ExportedName:ImportPath"
      const enhancedFieldRegex =
        /\s*([a-zA-Z][a-zA-Z0-9_]*)\s*(\??)\s*:\s*([a-zA-Z][a-zA-Z0-9_![\]]*)\s*"([^"]+)"\s*(?:,|$)/g;

      let match;
      while ((match = enhancedFieldRegex.exec(content)) !== null) {
        foundImports = true;
        const [raw, name, optional, type, importInfo] = match;

        if (importInfo.includes(':')) {
          // Format: fieldName: Type "ExportedName:ImportPath"
          const [exportedName, importPath] = importInfo.split(':').map(s => s.trim());
          directives.fieldFields.push({
            name: name.trim(),
            type: type.trim(),
            raw: raw.trim(),
            optional: optional === '?',
            importPath,
            exportedName,
          });
        } else {
          // Format: fieldName: Type "ImportPath"
          directives.fieldFields.push({
            name: name.trim(),
            type: type.trim(),
            raw: raw.trim(),
            optional: optional === '?',
            importPath: importInfo.trim(),
          });
        }
      }

      // If we found import fields and that's all there is, we're done
      if (foundImports && !content.includes(',')) {
        return;
      }
    }

    // Special case for single type field override with import paths
    // Format: @field(CustomType?: "@/types/CustomType")
    // This is typically used to override a field's return type
    // BUT we need to distinguish this from union types like: status: "Active | Inactive | Pending"
    if (!content.includes(',') && content.includes(':') && content.includes('"')) {
      // Check if this looks like an import path (starts with @ or / or contains :)
      // vs a quoted union type (contains | or other type operators)
      const match = content.match(/\s*([a-zA-Z][a-zA-Z0-9_]*)\s*(\??)\s*:\s*"([^"]+)"\s*/);
      if (match) {
        const [raw, typeName, optional, quotedContent] = match;

        // Check if there are more quotes after this match (indicating this is part of a larger type expression)
        const remainingContent = content.slice(match.index! + match[0].length);
        const hasMoreQuotes = remainingContent.includes('"');

        // Check if the quoted content looks like an import path vs a type expression
        const looksLikeImportPath = !hasMoreQuotes && (
          quotedContent.startsWith('@') ||
          quotedContent.startsWith('/') ||
          quotedContent.includes(':') ||
          (!quotedContent.includes('|') && !quotedContent.includes(' '))
        );

        if (looksLikeImportPath) {
          // This is a type override with import path
          const importInfo = quotedContent;

          // Check if it's in the format Type?: "Path" or Type?: "ExportedName:Path"
          if (importInfo.includes(':')) {
            // Format: CustomType?: "ExportedName:ImportPath"
            const [exportedName, importPath] = importInfo.split(':').map(s => s.trim());
            directives.fieldFields.push({
              name: typeName.trim(), // Field name will be same as type name for overrides
              type: typeName.trim(),
              raw: raw.trim(),
              optional: optional === '?',
              importPath,
              exportedName,
            });
          } else {
            // Format: CustomType?: "ImportPath"
            directives.fieldFields.push({
              name: typeName.trim(), // Field name will be same as type name for overrides
              type: typeName.trim(),
              raw: raw.trim(),
              optional: optional === '?',
              importPath: importInfo.trim(),
            });
          }
          return; // We've handled the field override, no need to process further
        } else {
          // This is a quoted type expression (like union types), not an import path
          // Let it fall through to the single field parsing logic
        }
      }
    }

    // Handle single field format for @field directive with complex types
    // Format: @field(fieldName: ComplexType "path") or @field(fieldName: ComplexType)
    if (!content.includes(',') && content.includes(':')) {
      // Updated regex to handle union types with quotes properly
      // This regex captures the entire type expression without trying to extract import paths
      const fieldDirectiveRegex = /\s*([a-zA-Z][a-zA-Z0-9_]*)\s*:\s*(.+)$/;

      // Check if there's an import path at the end
      // Import path pattern: field:type followed by space and quoted string that looks like a path
      // This should match: "result: Success | Error "@/types/results""
      // But NOT match: "mode: "light" | "dark" | "auto""
      const importPathMatch = content.match(/^([a-zA-Z][a-zA-Z0-9_]*)\s*:\s*([^"]+?)\s+"([^"]+)"\s*$/);
      let fieldType: string;
      let importPath: string | undefined;

      if (importPathMatch) {
        // There's an import path at the end
        const [, fieldName, typePart, importPart] = importPathMatch;

        // Check if the import part looks like an actual import path
        const looksLikeImportPath = importPart.startsWith('@') ||
                                   importPart.startsWith('/') ||
                                   importPart.includes(':');

        if (looksLikeImportPath) {
          fieldType = typePart.trim();
          importPath = importPart;

          const fieldDirectiveField: FieldDirectiveField = {
            name: fieldName.trim(),
            type: fieldType,
            raw: content.trim(),
            importPath: importPath.trim(),
          };

          directives.fieldFields.push(fieldDirectiveField);

          if (debug) {
            if (process.env.DEBUG_PARSER) {
              console.log(`Added field: ${fieldName.trim()}: ${fieldType} (path: ${importPath})`);
            }
          }
          return;
        }
      } else {
        // No import path, just parse field name and type
        const match = content.match(fieldDirectiveRegex);

        if (match) {
          const [, fieldName, fieldType] = match;

          // Clean up the field type by trimming whitespace
          const cleanFieldType = fieldType.trim();

          const fieldDirectiveField: FieldDirectiveField = {
            name: fieldName.trim(),
            type: cleanFieldType,
            raw: content.trim(),
          };

          directives.fieldFields.push(fieldDirectiveField);

          if (debug) {
            if (process.env.DEBUG_PARSER) {
              console.log(`Added field: ${fieldName.trim()}: ${cleanFieldType} (original: ${cleanFieldType})`);
            }
          }
          return;
        }
      }
    }

    // Parse multiple fields with complex type support
    // This handles cases like: fieldName: ComplexType, anotherField: AnotherType
    const fields = DirectiveParser.parseComplexFieldDefinitions(content);

    for (const field of fields) {
      // Skip fields that have already been processed with import paths
      if (hasImportPaths && field.raw.includes('"') && field.raw.includes(':')) {
        continue;
      }

      directives.fieldFields.push(field);
    }
  }

  /**
   * Parse field definitions that may contain complex types with nested structures
   * @param content The content to parse
   * @returns Array of parsed field directive fields
   */
  private static parseComplexFieldDefinitions(content: string): FieldDirectiveField[] {
    const fields: FieldDirectiveField[] = [];
    let currentPos = 0;

    while (currentPos < content.length) {
      // Skip whitespace
      while (currentPos < content.length && /\s/.test(content[currentPos])) {
        currentPos++;
      }

      if (currentPos >= content.length) break;

      // Parse field name
      const fieldNameMatch = content
        .slice(currentPos)
        .match(/^([a-zA-Z][a-zA-Z0-9_]*)\s*(\??)?\s*:/);
      if (!fieldNameMatch) break;

      const fieldName = fieldNameMatch[1];
      const optional = fieldNameMatch[2] === '?';
      currentPos += fieldNameMatch[0].length;

      // Skip whitespace after colon
      while (currentPos < content.length && /\s/.test(content[currentPos])) {
        currentPos++;
      }

      // Parse field type with bracket/brace counting
      const typeStart = currentPos;
      let bracketCount = 0;
      let braceCount = 0;
      let angleCount = 0;
      let parenCount = 0;
      let inString = false;
      let stringChar = '';

      while (currentPos < content.length) {
        const char = content[currentPos];

        if (!inString) {
          if (char === '"' || char === "'" || char === '`') {
            inString = true;
            stringChar = char;
          } else if (char === '[') {
            bracketCount++;
          } else if (char === ']') {
            bracketCount--;
          } else if (char === '{') {
            braceCount++;
          } else if (char === '}') {
            braceCount--;
          } else if (char === '<') {
            angleCount++;
          } else if (char === '>') {
            angleCount--;
          } else if (char === '(') {
            parenCount++;
          } else if (char === ')') {
            parenCount--;
          } else if (
            char === ',' &&
            bracketCount === 0 &&
            braceCount === 0 &&
            angleCount === 0 &&
            parenCount === 0
          ) {
            // Found a field separator
            break;
          }
        } else {
          if (char === stringChar && (currentPos === 0 || content[currentPos - 1] !== '\\')) {
            inString = false;
            stringChar = '';
          }
        }

        currentPos++;
      }

      const fieldType = content.slice(typeStart, currentPos).trim();

      if (fieldName && fieldType) {
        fields.push({
          name: fieldName,
          type: fieldType,
          raw: `${fieldName}${optional ? '?' : ''}: ${fieldType}`,
          optional,
        });
      }

      // Skip comma if present
      if (currentPos < content.length && content[currentPos] === ',') {
        currentPos++;
      }
    }

    return fields;
  }

  /**
   * Extracts schema directives from a GraphQL schema file
   * Specifically looks for directives applied to the schema declaration
   * @param schemaFilePath Path to the GraphQL schema file
   * @returns Parsed directives for the schema
   */
  public static async extractSchemaDirectives(schemaFilePath: string): Promise<DirectiveContainer> {
    try {
      // Initialize directive container
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };

      // Check if the schema file exists
      if (!fs.existsSync(schemaFilePath)) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Schema file not found: ${schemaFilePath}`);
        }
        return directives;
      }

      // Read the schema file
      const schemaContent = await fs.readFile(schemaFilePath, 'utf8');
      const lines = schemaContent.split('\n');

      // Find the schema declaration
      const schemaDefIndex = lines.findIndex(line => line.trim().startsWith('schema {'));

      if (schemaDefIndex >= 0) {
        // Look for comment directives before the schema definition
        let lineIndex = schemaDefIndex - 1;

        // Continue searching through empty lines to find directive comments
        while (lineIndex >= 0) {
          const line = lines[lineIndex].trim();

          if (line.startsWith('#')) {
            this.parseDirectiveLine(lines[lineIndex], directives);
          } else if (line !== '') {
            // Stop if we hit a non-empty, non-comment line
            break;
          }
          // Continue if it's an empty line
          lineIndex--;
        }
      }

      return directives;
    } catch (error) {
      console.error(`Error extracting schema directives: ${error}`);
      return {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {},
      };
    }
  }
}
