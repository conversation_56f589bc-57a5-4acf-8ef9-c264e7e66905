import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import { DirectiveParser, type DirectiveContainer } from './directive-parser';
import { DecoratorParser, type DecoratorContainer } from './decorator-parser';
import { WatchedFileWriter } from './watched-file-writer';
import { UI } from './ui';
import { TypeScriptMethodResolver, type ResolvedMethod } from './typescript-method-resolver';
import { SafeFileModifier, type FileModificationResult } from './safe-file-modifier';
import { DecoratorInjector, type DecoratorInjectionResult } from './decorator-injector';
import { AliasConfigUtils, type AliasConfig } from './alias-config';
import { BackupNamingUtils, type BackupNamingOptions, type BackupMetadata } from './backup-naming';

/**
 * Configuration options for the migration service
 */
export interface MigrationOptions {
  /** Path to GraphQL schema directory */
  schemaPath: string;
  /** Path to TypeScript codebase directory */
  codebasePath: string;
  /** Optional schema identifier for multi-schema support */
  schemaId?: string;
  /** Enable dry-run mode (preview only, no file changes) */
  dryRun?: boolean;
  /** Enable verbose logging */
  verbose?: boolean;
  /** Create backups before migration */
  createBackups?: boolean;
  /** Patterns to include when scanning TypeScript files */
  includePatterns?: string[];
  /** Patterns to exclude when scanning TypeScript files */
  excludePatterns?: string[];
  /** Alias configuration for import path handling */
  aliasConfig?: AliasConfig;
  /** Backup naming configuration */
  backupOptions?: BackupNamingOptions;
}

/**
 * Result of a migration operation
 */
export interface MigrationResult {
  /** Whether the migration was successful */
  success: boolean;
  /** Number of GraphQL files processed */
  graphqlFilesProcessed: number;
  /** Number of TypeScript files modified */
  typescriptFilesModified: number;
  /** Number of directives migrated */
  directivesMigrated: number;
  /** Number of decorators added */
  decoratorsAdded: number;
  /** List of errors encountered */
  errors: string[];
  /** List of warnings */
  warnings: string[];
  /** Detailed migration report */
  report: MigrationReport[];
}

/**
 * Detailed report for a single file migration
 */
export interface MigrationReport {
  /** File path */
  filePath: string;
  /** Type of file (graphql or typescript) */
  fileType: 'graphql' | 'typescript';
  /** Actions performed */
  actions: string[];
  /** Errors for this file */
  errors: string[];
  /** Warnings for this file */
  warnings: string[];
}

/**
 * Mapping between GraphQL directive and TypeScript method
 */
export interface DirectiveMethodMapping {
  /** GraphQL type name */
  typeName: string;
  /** GraphQL field name (if applicable) */
  fieldName?: string;
  /** Directive type */
  directiveType: 'methodCall' | 'import' | 'field';
  /** Directive content */
  directiveContent: string;
  /** Resolved TypeScript file path */
  typescriptFilePath?: string;
  /** Resolved method/function name */
  methodName?: string;
  /** Whether resolution was successful */
  resolved: boolean;
  /** Error message if resolution failed */
  error?: string;
  /** Resolved method details from TypeScript method resolver */
  resolvedMethod?: ResolvedMethod;
}

/**
 * Core migration service for transforming comment-based directives to TypeScript decorators
 */
export class MigrationService {
  private options: MigrationOptions;
  private directiveParser: DirectiveParser;
  private decoratorParser: DecoratorParser;
  private methodResolver: TypeScriptMethodResolver;
  private fileModifier: SafeFileModifier;
  private decoratorInjector: DecoratorInjector;
  private aliasConfig?: AliasConfig;
  private migrationReport: MigrationReport[] = [];
  private errors: string[] = [];
  private warnings: string[] = [];

  constructor(options: MigrationOptions) {
    this.options = {
      dryRun: false,
      verbose: false,
      createBackups: false,
      includePatterns: ['**/*.ts', '**/*.tsx'],
      excludePatterns: [
        '**/node_modules/**',
        '**/dist/**',
        '**/*.d.ts',
        '**/__tests__/**',
        '**/*.test.ts',
        '**/*.spec.ts'
      ],
      ...options
    };

    this.directiveParser = new DirectiveParser();
    this.decoratorParser = new DecoratorParser();
    this.methodResolver = new TypeScriptMethodResolver({
      verbose: this.options.verbose,
      enableFuzzyMatching: true,
      minConfidence: 0.6,
      maxResults: 5
    });
    this.fileModifier = new SafeFileModifier({
      createBackup: this.options.createBackups,
      verbose: this.options.verbose,
      validateAfterModification: true
    });
    this.decoratorInjector = new DecoratorInjector({
      verbose: this.options.verbose,
      schemaId: this.options.schemaId,
      preventDuplicates: true,
      aliasConfig: this.options.aliasConfig,
      createBackups: this.options.createBackups
    });

    // Set up alias configuration if provided
    this.aliasConfig = this.options.aliasConfig;
  }

  /**
   * Execute the migration process
   * @returns Migration result with detailed information
   */
  public async migrate(): Promise<MigrationResult> {
    try {
      UI.info('🚀 Starting GraphQL directive to TypeScript decorator migration...');
      
      if (this.options.dryRun) {
        UI.info('📋 Running in dry-run mode - no files will be modified');
      }

      // Validate input paths
      await this.validatePaths();

      // Create backups if enabled
      if (this.options.createBackups && !this.options.dryRun) {
        await this.createBackups();
      }

      // Scan GraphQL files for directives
      const graphqlFiles = await this.findGraphQLFiles();
      UI.info(`📄 Found ${graphqlFiles.length} GraphQL files to process`);

      // Scan TypeScript files for existing decorators
      const typescriptFiles = await this.findTypeScriptFiles();
      UI.info(`📄 Found ${typescriptFiles.length} TypeScript files to scan`);

      // Extract directives from GraphQL files
      const directiveMappings = await this.extractDirectiveMappings(graphqlFiles);
      UI.info(`🔍 Found ${directiveMappings.length} directives to migrate`);

      // Resolve method references in TypeScript files
      const resolvedMappings = await this.resolveMethodReferences(directiveMappings, typescriptFiles);
      const successfulMappings = resolvedMappings.filter(m => m.resolved);
      UI.info(`✅ Successfully resolved ${successfulMappings.length} method references`);

      // Apply migrations (add decorators and remove directives)
      let decoratorsAdded = 0;
      let typescriptFilesModified = 0;

      if (!this.options.dryRun) {
        const decoratorResult = await this.addDecorators(successfulMappings);
        decoratorsAdded = decoratorResult.decoratorsAdded;
        typescriptFilesModified = decoratorResult.filesModified;

        await this.removeDirectives(graphqlFiles, directiveMappings);
      } else {
        UI.info('📋 Dry-run mode: Would add decorators and remove directives');
        decoratorsAdded = successfulMappings.length;
        typescriptFilesModified = new Set(successfulMappings.map(m => m.typescriptFilePath)).size;
      }

      const result: MigrationResult = {
        success: this.errors.length === 0,
        graphqlFilesProcessed: graphqlFiles.length,
        typescriptFilesModified,
        directivesMigrated: successfulMappings.length,
        decoratorsAdded,
        errors: this.errors,
        warnings: this.warnings,
        report: this.migrationReport
      };

      this.logMigrationSummary(result);
      return result;

    } catch (error) {
      const errorMessage = `Migration failed: ${error}`;
      this.errors.push(errorMessage);
      UI.error(errorMessage);

      return {
        success: false,
        graphqlFilesProcessed: 0,
        typescriptFilesModified: 0,
        directivesMigrated: 0,
        decoratorsAdded: 0,
        errors: this.errors,
        warnings: this.warnings,
        report: this.migrationReport
      };
    }
  }

  /**
   * Validate that input paths exist and are accessible
   */
  private async validatePaths(): Promise<void> {
    if (!await fs.pathExists(this.options.schemaPath)) {
      throw new Error(`Schema path does not exist: ${this.options.schemaPath}`);
    }

    if (!await fs.pathExists(this.options.codebasePath)) {
      throw new Error(`Codebase path does not exist: ${this.options.codebasePath}`);
    }

    const schemaStats = await fs.stat(this.options.schemaPath);
    if (!schemaStats.isDirectory()) {
      throw new Error(`Schema path must be a directory: ${this.options.schemaPath}`);
    }

    const codebaseStats = await fs.stat(this.options.codebasePath);
    if (!codebaseStats.isDirectory()) {
      throw new Error(`Codebase path must be a directory: ${this.options.codebasePath}`);
    }
  }

  /**
   * Create backups of files before migration
   */
  private async createBackups(): Promise<void> {
    UI.info('💾 Creating backups...');

    try {
      // Create backup directory with improved naming
      const backupInfo = await BackupNamingUtils.createBackupDirectory(
        this.options.codebasePath,
        this.options.schemaPath,
        this.options.backupOptions
      );

      if (backupInfo.exists) {
        UI.warning(`Backup directory already exists: ${backupInfo.backupDir}`);

        // Resolve naming collision if needed
        const resolvedPath = await BackupNamingUtils.resolveNamingCollision(backupInfo.backupDir);
        backupInfo.backupDir = resolvedPath;
        await fs.ensureDir(backupInfo.backupDir);

        if (this.options.backupOptions?.useSubdirectories !== false) {
          await fs.ensureDir(path.join(backupInfo.backupDir, 'schema'));
          await fs.ensureDir(path.join(backupInfo.backupDir, 'codebase'));
        }
      }

      // Backup schema directory
      const schemaBackupDir = this.options.backupOptions?.useSubdirectories !== false
        ? path.join(backupInfo.backupDir, 'schema')
        : backupInfo.backupDir;
      await fs.copy(this.options.schemaPath, schemaBackupDir);

      // Backup codebase directory
      const codebaseBackupDir = this.options.backupOptions?.useSubdirectories !== false
        ? path.join(backupInfo.backupDir, 'codebase')
        : backupInfo.backupDir;
      await fs.copy(this.options.codebasePath, codebaseBackupDir);

      // Create backup metadata if enabled
      if (this.options.backupOptions?.createMetadata !== false) {
        await this.createBackupMetadata(backupInfo);
      }

      // Clean up old backups if configured
      if (this.options.backupOptions?.maxBackupsToKeep && this.options.backupOptions.maxBackupsToKeep > 0) {
        await BackupNamingUtils.cleanupOldBackups(this.options.backupOptions);
      }

      UI.info(`💾 Backups created in: ${backupInfo.backupDir}`);
      UI.info(`   Schema: ${backupInfo.schemaName}, Codebase: ${backupInfo.codebaseName}`);

    } catch (error) {
      UI.error(`Failed to create backups: ${error}`);
      throw error;
    }
  }

  /**
   * Create backup metadata file with migration information
   */
  private async createBackupMetadata(backupInfo: any): Promise<void> {
    try {
      // Calculate statistics
      const schemaFiles = glob.sync(path.join(this.options.schemaPath, '**/*.{gql,graphql}'));
      const codebaseFiles = await this.findTypeScriptFiles();

      // Calculate total size
      let totalSize = 0;
      const allFiles = [...schemaFiles, ...codebaseFiles];
      for (const file of allFiles) {
        try {
          const stats = await fs.stat(file);
          totalSize += stats.size;
        } catch (error) {
          // Ignore files that can't be read
        }
      }

      const metadata: BackupMetadata = {
        createdAt: new Date().toISOString(),
        version: '1.0.0', // TODO: Get from package.json
        originalPaths: {
          codebasePath: this.options.codebasePath,
          schemaPath: this.options.schemaPath
        },
        migrationOptions: {
          schemaId: this.options.schemaId,
          dryRun: this.options.dryRun,
          includePatterns: this.options.includePatterns,
          excludePatterns: this.options.excludePatterns,
          aliasConfig: this.options.aliasConfig
        },
        statistics: {
          fileCount: allFiles.length,
          totalSize,
          schemaFiles: schemaFiles.length,
          codebaseFiles: codebaseFiles.length
        }
      };

      await BackupNamingUtils.createBackupMetadata(backupInfo.backupDir, metadata);

      if (this.options.verbose) {
        UI.info(`📊 Backup metadata created:`);
        UI.info(`   Files: ${metadata.statistics.fileCount} (${Math.round(totalSize / 1024)}KB)`);
        UI.info(`   Schema files: ${metadata.statistics.schemaFiles}`);
        UI.info(`   Codebase files: ${metadata.statistics.codebaseFiles}`);
      }

    } catch (error) {
      UI.warning(`Failed to create backup metadata: ${error}`);
      // Don't throw - metadata creation is not critical
    }
  }

  /**
   * Find all GraphQL files in the schema directory
   */
  private async findGraphQLFiles(): Promise<string[]> {
    const pattern = path.join(this.options.schemaPath, '**/*.{gql,graphql}');
    return glob.sync(pattern, { windowsPathsNoEscape: true });
  }

  /**
   * Find all TypeScript files in the codebase directory
   */
  private async findTypeScriptFiles(): Promise<string[]> {
    const files: string[] = [];
    
    for (const includePattern of this.options.includePatterns!) {
      const pattern = path.join(this.options.codebasePath, includePattern);
      const matchedFiles = glob.sync(pattern, {
        ignore: this.options.excludePatterns!.map(p => path.join(this.options.codebasePath, p)),
        windowsPathsNoEscape: true
      });
      files.push(...matchedFiles);
    }
    
    return [...new Set(files)]; // Remove duplicates
  }

  /**
   * Extract directive mappings from GraphQL files
   */
  private async extractDirectiveMappings(graphqlFiles: string[]): Promise<DirectiveMethodMapping[]> {
    const mappings: DirectiveMethodMapping[] = [];

    for (const graphqlFile of graphqlFiles) {
      try {
        if (this.options.verbose) {
          UI.info(`📄 Processing GraphQL file: ${path.relative(process.cwd(), graphqlFile)}`);
        }

        const fileReport: MigrationReport = {
          filePath: graphqlFile,
          fileType: 'graphql',
          actions: [],
          errors: [],
          warnings: []
        };

        // Extract all types from the GraphQL file
        const types = await this.extractTypesFromGraphQLFile(graphqlFile);
        console.log(`[DEBUG] Extracted types from ${path.relative(process.cwd(), graphqlFile)}: ${types.join(', ')}`);

        for (const typeName of types) {
          // Extract type-level directives
          const typeDirectives = await DirectiveParser.extractDirectivesFromSchema(
            graphqlFile,
            typeName,
            undefined,
            this.options.verbose
          );

          // Process type-level directives
          this.processDirectives(typeDirectives, typeName, undefined, graphqlFile, mappings, fileReport);

          // Extract field-level directives
          const fields = await this.extractFieldsFromType(graphqlFile, typeName);
          for (const fieldName of fields) {
            const fieldDirectives = await DirectiveParser.extractDirectivesFromSchema(
              graphqlFile,
              typeName,
              fieldName,
              this.options.verbose
            );

            // Process field-level directives
            this.processDirectives(fieldDirectives, typeName, fieldName, graphqlFile, mappings, fileReport);
          }
        }

        this.migrationReport.push(fileReport);

      } catch (error) {
        const errorMessage = `Error processing GraphQL file ${graphqlFile}: ${error}`;
        this.errors.push(errorMessage);
        if (this.options.verbose) {
          UI.error(errorMessage);
        }
      }
    }

    return mappings;
  }

  /**
   * Resolve method references in TypeScript files
   */
  private async resolveMethodReferences(
    mappings: DirectiveMethodMapping[],
    typescriptFiles: string[]
  ): Promise<DirectiveMethodMapping[]> {
    UI.info('🔍 Resolving method references in TypeScript files...');

    for (const mapping of mappings) {
      try {
        if (mapping.directiveType === 'methodCall') {
          // Only methodCall directives need method resolution
          const resolvedMethods = await this.methodResolver.resolveMethodCall(
            mapping.directiveContent,
            typescriptFiles
          );

          if (resolvedMethods.length > 0) {
            // Use the best match (highest confidence)
            const bestMatch = resolvedMethods[0];
            mapping.typescriptFilePath = bestMatch.filePath;
            mapping.methodName = bestMatch.methodName;
            mapping.resolvedMethod = bestMatch;
            mapping.resolved = true;

            if (this.options.verbose) {
              UI.success(`✅ Resolved ${mapping.directiveContent} → ${path.relative(process.cwd(), bestMatch.filePath)}:${bestMatch.lineNumber}`);
            }
          } else {
            mapping.error = `No matching method found for: ${mapping.directiveContent}`;
            this.warnings.push(mapping.error);

            if (this.options.verbose) {
              UI.warning(`⚠️ ${mapping.error}`);
            }
          }
        } else if (mapping.directiveType === 'field') {
          // @field directives don't need method resolution, but they need to be processed
          // They will be converted to @GQLField decorators that can be added to any TypeScript file
          // Assign them to the first available TypeScript file or a types file
          const targetFile = await this.findBestFileForDirective(mapping, typescriptFiles);
          if (targetFile) {
            mapping.typescriptFilePath = targetFile;
            mapping.resolved = true;

            if (this.options.verbose) {
              const location = mapping.fieldName ? `${mapping.typeName}.${mapping.fieldName}` : mapping.typeName;
              UI.success(`✅ Processed @field directive in ${location}: ${mapping.directiveContent} → ${path.relative(process.cwd(), targetFile)}`);
            }
          } else {
            mapping.error = `No suitable TypeScript file found for @field directive`;
            this.warnings.push(mapping.error);

            if (this.options.verbose) {
              UI.warning(`⚠️ ${mapping.error}`);
            }
          }
        } else if (mapping.directiveType === 'import') {
          // @import directives don't need method resolution
          // Assign them to the first available TypeScript file
          const targetFile = await this.findBestFileForDirective(mapping, typescriptFiles);
          if (targetFile) {
            // Check if the import is redundant (importing from the same file)
            const isRedundantImport = await this.isRedundantImport(mapping.directiveContent, targetFile);

            if (isRedundantImport) {
              mapping.error = `Skipping redundant import: function already defined in target file`;
              mapping.resolved = false;

              if (this.options.verbose) {
                const location = mapping.fieldName ? `${mapping.typeName}.${mapping.fieldName}` : mapping.typeName;
                UI.info(`ℹ️ Skipped redundant @import in ${location}: ${mapping.directiveContent} (function already in ${path.relative(process.cwd(), targetFile)})`);
              }
            } else {
              mapping.typescriptFilePath = targetFile;
              mapping.resolved = true;

              if (this.options.verbose) {
                const location = mapping.fieldName ? `${mapping.typeName}.${mapping.fieldName}` : mapping.typeName;
                UI.success(`✅ Processed @import directive in ${location}: ${mapping.directiveContent} → ${path.relative(process.cwd(), targetFile)}`);
              }
            }
          } else {
            mapping.error = `No suitable TypeScript file found for @import directive`;
            this.warnings.push(mapping.error);

            if (this.options.verbose) {
              UI.warning(`⚠️ ${mapping.error}`);
            }
          }
        }
      } catch (error) {
        mapping.error = `Error resolving method: ${error}`;
        this.errors.push(mapping.error);

        if (this.options.verbose) {
          UI.error(`❌ ${mapping.error}`);
        }
      }
    }

    const resolvedCount = mappings.filter(m => m.resolved).length;
    UI.info(`✅ Resolved ${resolvedCount}/${mappings.length} directive references`);

    return mappings;
  }

  /**
   * Add decorators to TypeScript files
   */
  private async addDecorators(mappings: DirectiveMethodMapping[]): Promise<{
    decoratorsAdded: number;
    filesModified: number;
  }> {
    UI.info('🎯 Adding decorators to TypeScript files...');

    // Group mappings by file
    const fileGroups = new Map<string, DirectiveMethodMapping[]>();
    for (const mapping of mappings) {
      if (mapping.typescriptFilePath && mapping.resolved) {
        const filePath = mapping.typescriptFilePath;
        if (!fileGroups.has(filePath)) {
          fileGroups.set(filePath, []);
        }
        fileGroups.get(filePath)!.push(mapping);
      }
    }

    let totalDecoratorsAdded = 0;
    let filesModified = 0;

    // Process each file
    for (const [filePath, fileMappings] of fileGroups) {
      try {
        const result = await this.decoratorInjector.injectDecorators(filePath, fileMappings);

        if (result.success) {
          totalDecoratorsAdded += result.decoratorsAdded;
          if (result.decoratorsAdded > 0) {
            filesModified++;
          }

          if (this.options.verbose && result.decoratorsAdded > 0) {
            UI.success(`✅ Added ${result.decoratorsAdded} decorators to ${path.relative(process.cwd(), filePath)}`);
          }
        } else {
          const errorMessage = `Failed to add decorators to ${filePath}: ${result.error}`;
          this.errors.push(errorMessage);
          if (this.options.verbose) {
            UI.error(errorMessage);
          }
        }
      } catch (error) {
        const errorMessage = `Error processing ${filePath}: ${error}`;
        this.errors.push(errorMessage);
        if (this.options.verbose) {
          UI.error(errorMessage);
        }
      }
    }

    UI.info(`✅ Added ${totalDecoratorsAdded} decorators to ${filesModified} files`);
    return { decoratorsAdded: totalDecoratorsAdded, filesModified };
  }

  /**
   * Remove directives from GraphQL files
   */
  private async removeDirectives(
    graphqlFiles: string[],
    mappings: DirectiveMethodMapping[]
  ): Promise<void> {
    UI.info('🧹 Removing directives from GraphQL files...');

    let totalDirectivesRemoved = 0;
    let filesModified = 0;

    // Process each GraphQL file
    for (const graphqlFile of graphqlFiles) {
      try {
        // Find directives to remove in this file
        const fileReport: MigrationReport = {
          filePath: graphqlFile,
          fileType: 'graphql',
          actions: [],
          errors: [],
          warnings: []
        };

        // Read file content
        const content = await fs.readFile(graphqlFile, 'utf8');
        const lines = content.split('\n');
        const modifiedLines = [...lines];

        // Find directive comment lines
        const directiveLines: number[] = [];
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.startsWith('#') &&
              (line.includes('@methodCall') ||
               line.includes('@import') ||
               line.includes('@field'))) {
            directiveLines.push(i);
          }
        }

        if (directiveLines.length === 0) {
          continue; // No directives to remove
        }

        // Remove directive lines (in reverse order to maintain line numbers)
        directiveLines.sort((a, b) => b - a);
        for (const lineIndex of directiveLines) {
          modifiedLines.splice(lineIndex, 1);
          totalDirectivesRemoved++;
          fileReport.actions.push(`Removed directive: ${lines[lineIndex].trim()}`);
        }

        // Write modified content
        const modificationResult = await this.fileModifier.modifyFile(graphqlFile, () => {
          return modifiedLines.join('\n');
        });

        if (modificationResult.success) {
          filesModified++;
          if (this.options.verbose) {
            UI.success(`✅ Removed ${directiveLines.length} directives from ${path.relative(process.cwd(), graphqlFile)}`);
          }
        } else {
          const errorMessage = `Failed to remove directives from ${graphqlFile}: ${modificationResult.error}`;
          this.errors.push(errorMessage);
          fileReport.errors.push(errorMessage);
          if (this.options.verbose) {
            UI.error(errorMessage);
          }
        }

        this.migrationReport.push(fileReport);

      } catch (error) {
        const errorMessage = `Error processing ${graphqlFile}: ${error}`;
        this.errors.push(errorMessage);
        if (this.options.verbose) {
          UI.error(errorMessage);
        }
      }
    }

    UI.info(`✅ Removed ${totalDirectivesRemoved} directives from ${filesModified} files`);
  }

  /**
   * Extract all type names from a GraphQL file
   */
  private async extractTypesFromGraphQLFile(graphqlFile: string): Promise<string[]> {
    try {
      const content = await fs.readFile(graphqlFile, 'utf8');
      const types: string[] = [];

      // Regex to match type, interface, union, input, and enum declarations
      const typeRegex = /^\s*(?:type|interface|union|input|enum)\s+([A-Za-z][A-Za-z0-9_]*)/gm;
      let match;

      while ((match = typeRegex.exec(content)) !== null) {
        const typeName = match[1];
        if (!types.includes(typeName)) {
          types.push(typeName);
        }
      }

      return types;
    } catch (error) {
      throw new Error(`Failed to extract types from ${graphqlFile}: ${error}`);
    }
  }

  /**
   * Extract field names from a specific type in a GraphQL file
   */
  private async extractFieldsFromType(graphqlFile: string, typeName: string): Promise<string[]> {
    try {
      const content = await fs.readFile(graphqlFile, 'utf8');
      const fields: string[] = [];

      // Find the type definition
      const typeRegex = new RegExp(`^\\s*(?:type|interface)\\s+${typeName}\\s*(?:implements\\s+[^{]*)?\\s*{([^}]*)}`, 'ms');
      const typeMatch = content.match(typeRegex);

      if (typeMatch) {
        const typeBody = typeMatch[1];

        // Extract field names from the type body
        const fieldRegex = /^\s*([A-Za-z][A-Za-z0-9_]*)\s*(?:\([^)]*\))?\s*:\s*[^#\n]+/gm;
        let fieldMatch;

        while ((fieldMatch = fieldRegex.exec(typeBody)) !== null) {
          const fieldName = fieldMatch[1];
          if (!fields.includes(fieldName)) {
            fields.push(fieldName);
          }
        }
      }

      return fields;
    } catch (error) {
      throw new Error(`Failed to extract fields from type ${typeName} in ${graphqlFile}: ${error}`);
    }
  }

  /**
   * Process directives and create mappings
   */
  private processDirectives(
    directives: DirectiveContainer,
    typeName: string,
    fieldName: string | undefined,
    graphqlFile: string,
    mappings: DirectiveMethodMapping[],
    fileReport: MigrationReport
  ): void {

    // Process @methodCall directives
    for (const methodCall of directives.methodCalls) {
      const mapping: DirectiveMethodMapping = {
        typeName,
        fieldName,
        directiveType: 'methodCall',
        directiveContent: methodCall.content,
        resolved: false
      };

      mappings.push(mapping);
      fileReport.actions.push(`Found @methodCall directive: ${methodCall.content}`);

      if (this.options.verbose) {
        const location = fieldName ? `${typeName}.${fieldName}` : typeName;
        UI.info(`   📍 Found @methodCall in ${location}: ${methodCall.content}`);
      }
    }

    // Process @import directives
    for (const importDirective of directives.imports) {
      const mapping: DirectiveMethodMapping = {
        typeName,
        fieldName,
        directiveType: 'import',
        directiveContent: importDirective.content,
        resolved: false
      };

      mappings.push(mapping);
      fileReport.actions.push(`Found @import directive: ${importDirective.content}`);

      if (this.options.verbose) {
        const location = fieldName ? `${typeName}.${fieldName}` : typeName;
        UI.info(`   📍 Found @import in ${location}: ${importDirective.content}`);
      }
    }

    // Process @field directives
    for (const fieldDirective of directives.fieldFields) {
      const mapping: DirectiveMethodMapping = {
        typeName,
        fieldName,
        directiveType: 'field',
        directiveContent: `${fieldDirective.name}: ${fieldDirective.type}`,
        resolved: false
      };

      mappings.push(mapping);
      fileReport.actions.push(`Found @field directive: ${fieldDirective.name}: ${fieldDirective.type}`);

      if (this.options.verbose) {
        const location = fieldName ? `${typeName}.${fieldName}` : typeName;
        UI.info(`   📍 Found @field in ${location}: ${fieldDirective.name}: ${fieldDirective.type}`);
      }
    }
  }

  /**
   * Check if an import directive is redundant (importing a function from the same file where it's defined)
   */
  private async isRedundantImport(importContent: string, targetFile: string): Promise<boolean> {
    try {
      // Extract function names from import statement
      // Example: "import { getAdminName, otherFunc } from '@services/admin-functions'"
      const importMatch = importContent.match(/import\s*\{\s*([^}]+)\s*\}\s*from/);
      if (!importMatch) {
        return false; // Can't parse import, assume not redundant
      }

      const importedNames = importMatch[1]
        .split(',')
        .map(name => name.trim())
        .filter(name => name.length > 0);

      if (importedNames.length === 0) {
        return false;
      }

      // Read the target file content
      const fileContent = await fs.readFile(targetFile, 'utf-8');

      // Check if any of the imported functions are already defined in the target file
      for (const functionName of importedNames) {
        // Look for function declarations: "export function functionName" or "function functionName"
        const functionRegex = new RegExp(`\\b(?:export\\s+)?function\\s+${functionName}\\s*\\(`, 'g');
        if (functionRegex.test(fileContent)) {
          return true; // Found at least one function that's already defined
        }
      }

      return false;
    } catch (error) {
      // If we can't read the file or parse the import, assume not redundant
      return false;
    }
  }

  /**
   * Find the best TypeScript file to add a directive to
   */
  private async findBestFileForDirective(mapping: DirectiveMethodMapping, typescriptFiles: string[]): Promise<string | null> {
    if (typescriptFiles.length === 0) {
      return null;
    }

    if (mapping.directiveType === 'field') {
      // For @field directives, we need to find the file that contains the referenced type
      const fieldContent = mapping.directiveContent;

      // Extract the type name from the field content (format: "fieldName: TypeName" or "TypeName")
      let referencedTypeName = '';
      if (fieldContent.includes(':')) {
        // Format: "metadata: BookMetadata" or "auditInfo: AuditInfo"
        const parts = fieldContent.split(':');
        if (parts.length >= 2) {
          referencedTypeName = parts[1].trim();
          // Remove any import path information if present
          if (referencedTypeName.includes('"')) {
            referencedTypeName = referencedTypeName.split('"')[0].trim();
          }
        }
      } else {
        // Format: just "TypeName"
        referencedTypeName = fieldContent.trim();
      }

      if (referencedTypeName) {
        // First, try to find files that actually contain the type definition
        for (const file of typescriptFiles) {
          try {
            const content = await fs.readFile(file, 'utf8');
            // Check if this file contains the type definition
            const typeRegex = new RegExp(`(interface|type|class)\\s+${referencedTypeName}\\b`, 'i');
            if (typeRegex.test(content)) {
              if (this.options.verbose) {
                UI.info(`📍 Found type ${referencedTypeName} in ${path.relative(process.cwd(), file)}`);
              }
              return file;
            }
          } catch (error) {
            // Skip files that can't be read
            continue;
          }
        }

        // If not found by content, fall back to filename matching
        const typeFiles = typescriptFiles.filter(file => {
          const fileName = path.basename(file, '.ts').toLowerCase();
          const typeLower = referencedTypeName.toLowerCase();

          // Check if filename matches the type name or contains it
          return fileName.includes(typeLower) ||
                 fileName.includes('types') ||
                 fileName.includes(typeLower.replace(/([A-Z])/g, '-$1').toLowerCase()) ||
                 file.toLowerCase().includes('/types/');
        });

        if (typeFiles.length > 0) {
          // Prefer files that more closely match the type name
          const exactMatches = typeFiles.filter(file => {
            const fileName = path.basename(file, '.ts').toLowerCase();
            const typeLower = referencedTypeName.toLowerCase();
            return fileName.includes(typeLower) || fileName === typeLower;
          });

          if (exactMatches.length > 0) {
            return exactMatches[0];
          }

          return typeFiles[0];
        }
      }
    }

    // For @import directives or when no specific type file found, prefer general type files
    const typeName = mapping.typeName.toLowerCase();

    // Look for files that might be related to the GraphQL type name or are type files
    const relatedFiles = typescriptFiles.filter(file => {
      const fileName = path.basename(file, '.ts').toLowerCase();
      return fileName.includes(typeName) ||
             fileName.includes('types') ||
             fileName.includes('schema') ||
             fileName.includes('decorators') ||
             file.toLowerCase().includes('/types/');
    });

    if (relatedFiles.length > 0) {
      // Prefer actual type files over other files
      const typeFiles = relatedFiles.filter(file =>
        file.toLowerCase().includes('/types/') ||
        path.basename(file, '.ts').toLowerCase().includes('types')
      );

      if (typeFiles.length > 0) {
        return typeFiles[0];
      }

      return relatedFiles[0];
    }

    // If no related files found, use the first available TypeScript file
    return typescriptFiles[0];
  }

  /**
   * Log migration summary
   */
  private logMigrationSummary(result: MigrationResult): void {
    UI.info('\n📊 Migration Summary:');
    UI.info(`   GraphQL files processed: ${result.graphqlFilesProcessed}`);
    UI.info(`   TypeScript files modified: ${result.typescriptFilesModified}`);
    UI.info(`   Directives migrated: ${result.directivesMigrated}`);
    UI.info(`   Decorators added: ${result.decoratorsAdded}`);

    if (result.errors.length > 0) {
      UI.error(`   Errors: ${result.errors.length}`);
    }

    if (result.warnings.length > 0) {
      UI.warning(`   Warnings: ${result.warnings.length}`);
    }

    if (result.success) {
      UI.success('✅ Migration completed successfully!');
    } else {
      UI.error('❌ Migration completed with errors');
    }
  }
}
