import { getGlobalTemplateCache, CacheStatistics } from './template-compilation-cache';
import { getGlobalInitializationService } from './template-initialization-service';

/**
 * Template performance metrics for monitoring
 */
export interface TemplatePerformanceMetrics {
  /** Cache performance statistics */
  cacheStats: CacheStatistics;
  /** Template initialization metrics */
  initializationMetrics?: {
    isInitialized: boolean;
    totalTemplatesRegistered: number;
    initializationTime: number;
    estimatedTimeSavings: number;
  };
  /** Real-time performance tracking */
  realtimeMetrics: {
    compilationsPerSecond: number;
    averageCompilationTime: number;
    cacheEfficiency: number;
    memoryUsagePercentage: number;
  };
  /** Performance trends over time */
  trends: {
    hitRateTrend: number[]; // Last 10 measurements
    compilationTimeTrend: number[]; // Last 10 measurements
    memoryUsageTrend: number[]; // Last 10 measurements
  };
  /** Performance alerts */
  alerts: TemplatePerformanceAlert[];
}

/**
 * Performance alert for template system
 */
export interface TemplatePerformanceAlert {
  /** Alert severity level */
  level: 'info' | 'warning' | 'error';
  /** Alert message */
  message: string;
  /** Metric that triggered the alert */
  metric: string;
  /** Current value */
  currentValue: number;
  /** Threshold value */
  threshold: number;
  /** Timestamp when alert was triggered */
  timestamp: number;
}

/**
 * Configuration for template performance monitoring
 */
export interface TemplatePerformanceMonitorConfig {
  /** Whether to enable real-time monitoring (default: true) */
  enableRealtimeMonitoring?: boolean;
  /** Monitoring interval in milliseconds (default: 5000) */
  monitoringInterval?: number;
  /** Maximum number of trend data points to keep (default: 10) */
  maxTrendDataPoints?: number;
  /** Performance alert thresholds */
  alertThresholds?: {
    /** Minimum acceptable cache hit rate percentage (default: 70) */
    minHitRate?: number;
    /** Maximum acceptable average compilation time in ms (default: 10) */
    maxCompilationTime?: number;
    /** Maximum acceptable memory usage in bytes (default: 100MB) */
    maxMemoryUsage?: number;
    /** Maximum acceptable cache efficiency percentage (default: 80) */
    minCacheEfficiency?: number;
  };
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
}

/**
 * Template performance monitoring service
 * Provides real-time monitoring and alerting for template compilation performance
 */
export class TemplatePerformanceMonitor {
  private config: Required<TemplatePerformanceMonitorConfig>;
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private trends: TemplatePerformanceMetrics['trends'] = {
    hitRateTrend: [],
    compilationTimeTrend: [],
    memoryUsageTrend: [],
  };
  private alerts: TemplatePerformanceAlert[] = [];
  private lastMetricsTimestamp = 0;
  private lastCompilationCount = 0;

  constructor(config: TemplatePerformanceMonitorConfig = {}) {
    this.config = {
      enableRealtimeMonitoring: true,
      monitoringInterval: 5000, // 5 seconds
      maxTrendDataPoints: 10,
      enableLogging: false,
      ...config,
      alertThresholds: {
        minHitRate: 70,
        maxCompilationTime: 10,
        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
        minCacheEfficiency: 80,
        ...(config.alertThresholds || {}),
      },
    };

    if (this.config.enableLogging) {
      console.log('🚀 TemplatePerformanceMonitor initialized');
    }
  }

  /**
   * Start real-time performance monitoring
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      if (this.config.enableLogging) {
        console.warn('⚠️  Template performance monitoring is already running');
      }
      return;
    }

    if (!this.config.enableRealtimeMonitoring) {
      if (this.config.enableLogging) {
        console.log('📊 Real-time monitoring is disabled');
      }
      return;
    }

    this.isMonitoring = true;
    this.lastMetricsTimestamp = Date.now();
    
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.monitoringInterval);

    if (this.config.enableLogging) {
      console.log(`📊 Template performance monitoring started (interval: ${this.config.monitoringInterval}ms)`);
    }
  }

  /**
   * Stop real-time performance monitoring
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.config.enableLogging) {
      console.log('📊 Template performance monitoring stopped');
    }
  }

  /**
   * Get current template performance metrics
   * @returns Current performance metrics
   */
  public getMetrics(): TemplatePerformanceMetrics {
    const templateCache = getGlobalTemplateCache();
    const initService = getGlobalInitializationService();
    const cacheStats = templateCache.getStatistics();

    // Calculate real-time metrics
    const now = Date.now();
    const timeDelta = (now - this.lastMetricsTimestamp) / 1000; // seconds
    const compilationDelta = (cacheStats.hits + cacheStats.misses) - this.lastCompilationCount;
    const compilationsPerSecond = timeDelta > 0 ? compilationDelta / timeDelta : 0;

    // Calculate cache efficiency (hit rate weighted by time saved)
    const cacheEfficiency = cacheStats.hitRate * (cacheStats.totalTimeSaved / Math.max(1, cacheStats.hits));

    const realtimeMetrics = {
      compilationsPerSecond,
      averageCompilationTime: cacheStats.averageCompilationTime,
      cacheEfficiency: Math.min(100, cacheEfficiency / 10), // Normalize to percentage
      memoryUsagePercentage: (cacheStats.estimatedMemoryUsage / (this.config.alertThresholds.maxMemoryUsage || 1)) * 100,
    };

    // Get initialization metrics if available
    let initializationMetrics;
    try {
      const isInitialized = initService.isTemplateSystemInitialized();
      if (isInitialized) {
        // Note: We'd need to store initialization results to get these metrics
        // For now, we'll provide basic information
        initializationMetrics = {
          isInitialized: true,
          totalTemplatesRegistered: 0, // Would need to be tracked
          initializationTime: 0, // Would need to be stored
          estimatedTimeSavings: cacheStats.totalTimeSaved,
        };
      }
    } catch (error) {
      // Initialization service might not be available
    }

    return {
      cacheStats,
      initializationMetrics,
      realtimeMetrics,
      trends: { ...this.trends },
      alerts: [...this.alerts],
    };
  }

  /**
   * Collect metrics and update trends
   */
  private collectMetrics(): void {
    const metrics = this.getMetrics();
    
    // Update trends
    this.updateTrend(this.trends.hitRateTrend, metrics.cacheStats.hitRate);
    this.updateTrend(this.trends.compilationTimeTrend, metrics.cacheStats.averageCompilationTime);
    this.updateTrend(this.trends.memoryUsageTrend, metrics.cacheStats.estimatedMemoryUsage);

    // Check for performance alerts
    this.checkPerformanceAlerts(metrics);

    // Update tracking variables
    this.lastMetricsTimestamp = Date.now();
    this.lastCompilationCount = metrics.cacheStats.hits + metrics.cacheStats.misses;

    if (this.config.enableLogging) {
      this.logMetrics(metrics);
    }
  }

  /**
   * Update a trend array with a new value
   * @param trend Trend array to update
   * @param value New value to add
   */
  private updateTrend(trend: number[], value: number): void {
    trend.push(value);
    if (trend.length > this.config.maxTrendDataPoints) {
      trend.shift();
    }
  }

  /**
   * Check for performance alerts
   * @param metrics Current metrics
   */
  private checkPerformanceAlerts(metrics: TemplatePerformanceMetrics): void {
    const now = Date.now();
    const thresholds = this.config.alertThresholds;

    // Clear old alerts (older than 1 minute)
    this.alerts = this.alerts.filter(alert => now - alert.timestamp < 60000);

    // Check cache hit rate
    if (thresholds.minHitRate && metrics.cacheStats.hitRate < thresholds.minHitRate) {
      this.addAlert('warning', 'Low cache hit rate', 'hitRate',
        metrics.cacheStats.hitRate, thresholds.minHitRate);
    }

    // Check compilation time
    if (thresholds.maxCompilationTime && metrics.cacheStats.averageCompilationTime > thresholds.maxCompilationTime) {
      this.addAlert('warning', 'High average compilation time', 'compilationTime',
        metrics.cacheStats.averageCompilationTime, thresholds.maxCompilationTime);
    }

    // Check memory usage
    if (thresholds.maxMemoryUsage && metrics.cacheStats.estimatedMemoryUsage > thresholds.maxMemoryUsage) {
      this.addAlert('error', 'High memory usage', 'memoryUsage',
        metrics.cacheStats.estimatedMemoryUsage, thresholds.maxMemoryUsage);
    }

    // Check cache efficiency
    if (thresholds.minCacheEfficiency && metrics.realtimeMetrics.cacheEfficiency < thresholds.minCacheEfficiency) {
      this.addAlert('info', 'Low cache efficiency', 'cacheEfficiency',
        metrics.realtimeMetrics.cacheEfficiency, thresholds.minCacheEfficiency);
    }
  }

  /**
   * Add a performance alert
   * @param level Alert level
   * @param message Alert message
   * @param metric Metric name
   * @param currentValue Current value
   * @param threshold Threshold value
   */
  private addAlert(
    level: TemplatePerformanceAlert['level'],
    message: string,
    metric: string,
    currentValue: number,
    threshold: number
  ): void {
    // Don't add duplicate alerts
    const existingAlert = this.alerts.find(alert => 
      alert.metric === metric && alert.level === level
    );
    
    if (existingAlert) {
      existingAlert.currentValue = currentValue;
      existingAlert.timestamp = Date.now();
      return;
    }

    const alert: TemplatePerformanceAlert = {
      level,
      message,
      metric,
      currentValue,
      threshold,
      timestamp: Date.now(),
    };

    this.alerts.push(alert);

    // Only log alerts in production, but keep them concise
    if (this.config.enableLogging) {
      const emoji = level === 'error' ? '❌' : level === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${emoji} ${message}: ${currentValue.toFixed(1)} vs ${threshold}`);
    }
  }

  /**
   * Log current metrics (only in development mode to avoid performance impact)
   * @param metrics Metrics to log
   */
  private logMetrics(metrics: TemplatePerformanceMetrics): void {
    // Only log detailed metrics in development mode
    if (process.env.NODE_ENV !== 'development') return;

    console.log(`📊 Template: ${metrics.cacheStats.hitRate.toFixed(1)}% hit rate, ${Math.round(metrics.cacheStats.estimatedMemoryUsage / 1024)}KB`);

    if (metrics.alerts.length > 0) {
      console.log(`   ⚠️  ${metrics.alerts.length} alerts`);
    }
  }

  /**
   * Get performance summary for reporting
   * @returns Performance summary
   */
  public getPerformanceSummary(): {
    status: 'excellent' | 'good' | 'warning' | 'critical';
    summary: string;
    recommendations: string[];
  } {
    const metrics = this.getMetrics();
    const alerts = metrics.alerts;
    const errorAlerts = alerts.filter(a => a.level === 'error');
    const warningAlerts = alerts.filter(a => a.level === 'warning');

    let status: 'excellent' | 'good' | 'warning' | 'critical';
    let summary: string;
    const recommendations: string[] = [];

    if (errorAlerts.length > 0) {
      status = 'critical';
      summary = `Template performance is critical with ${errorAlerts.length} error(s)`;
      recommendations.push('Immediate attention required for critical performance issues');
    } else if (warningAlerts.length > 0) {
      status = 'warning';
      summary = `Template performance has ${warningAlerts.length} warning(s)`;
      recommendations.push('Performance optimization recommended');
    } else if (metrics.cacheStats.hitRate > 90) {
      status = 'excellent';
      summary = 'Template performance is excellent';
    } else {
      status = 'good';
      summary = 'Template performance is good';
    }

    // Add specific recommendations based on metrics
    if (metrics.cacheStats.hitRate < 80) {
      recommendations.push('Consider pre-compiling more templates to improve cache hit rate');
    }
    
    if (metrics.cacheStats.averageCompilationTime > 5) {
      recommendations.push('Template compilation time is high, consider template optimization');
    }
    
    if (metrics.realtimeMetrics.memoryUsagePercentage > 80) {
      recommendations.push('Memory usage is high, consider reducing cache size or implementing more aggressive eviction');
    }

    return { status, summary, recommendations };
  }

  /**
   * Reset all monitoring data
   */
  public reset(): void {
    this.trends = {
      hitRateTrend: [],
      compilationTimeTrend: [],
      memoryUsageTrend: [],
    };
    this.alerts = [];
    this.lastMetricsTimestamp = Date.now();
    this.lastCompilationCount = 0;

    if (this.config.enableLogging) {
      console.log('🔄 Template performance monitor reset');
    }
  }
}

// Global singleton instance
let globalPerformanceMonitor: TemplatePerformanceMonitor | null = null;

/**
 * Get the global template performance monitor instance
 * @param config Optional monitor configuration (only used on first call)
 * @returns Global monitor instance
 */
export function getGlobalTemplatePerformanceMonitor(
  config?: TemplatePerformanceMonitorConfig
): TemplatePerformanceMonitor {
  if (!globalPerformanceMonitor) {
    globalPerformanceMonitor = new TemplatePerformanceMonitor(config);
  }
  return globalPerformanceMonitor;
}

/**
 * Reset the global template performance monitor
 * Useful for testing or when configuration changes
 */
export function resetGlobalTemplatePerformanceMonitor(): void {
  if (globalPerformanceMonitor) {
    globalPerformanceMonitor.stopMonitoring();
  }
  globalPerformanceMonitor = null;
}
