import type {
  Decorator<PERSON>ontainer,
  ParsedDecorator,
  GQLMethodCallData,
  GQLImportData,
  GQLFieldData,
  GQLContextData,
} from './decorator-parser';
import type {
  DirectiveContainer,
  ParsedDirective,
  FieldDirectiveField,
} from './directive-parser';
import type {
  DecoratorProcessingConfig,
  DecoratorPrecedenceRules,
  MultiSchemaDecoratorContainer} from './decorator-types';
import {
  DEFAULT_PRECEDENCE_RULES
} from './decorator-types';
import { DecoratorValidator } from './decorator-validator';
import type { AliasConfig } from './alias-config';
import { AliasConfigUtils } from './alias-config';
import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * Converts decorator metadata into existing DirectiveContainer format
 * for seamless integration with the current directive processing pipeline
 */
export class DecoratorProcessor {
  private config: DecoratorProcessingConfig;
  private precedenceRules: DecoratorPrecedenceRules;
  private decoratorMetadata?: DecoratorContainer;
  private aliasConfig?: AliasConfig;

  constructor(
    config: DecoratorProcessingConfig,
    precedenceRules: DecoratorPrecedenceRules = DEFAULT_PRECEDENCE_RULES
  ) {
    this.config = config;
    this.precedenceRules = precedenceRules;
  }

  /**
   * Set the decorator metadata for smart import generation
   * @param metadata The decorator metadata container
   */
  public setDecoratorMetadata(metadata: DecoratorContainer): void {
    this.decoratorMetadata = metadata;
  }

  /**
   * Set the alias configuration for import path generation
   * @param aliasConfig The alias configuration
   */
  public setAliasConfig(aliasConfig: AliasConfig | undefined): void {
    this.aliasConfig = aliasConfig;
  }

  /**
   * Convert decorator container to directive container
   * @param decoratorContainer The decorator container to convert
   * @param schemaId Optional schema identifier for multi-schema support
   * @returns Converted directive container
   */
  public convertToDirectiveContainer(
    decoratorContainer: DecoratorContainer,
    schemaId?: string
  ): DirectiveContainer {
    const directiveContainer: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {},
    };

    // Convert @GQLMethodCall decorators to methodCall directives
    for (const { decorator, data } of decoratorContainer.methodCalls) {
      // Schema filtering logic
      if (schemaId) {
        // If a specific schema is requested, only include decorators for that schema
        if (data.schema !== schemaId) {
          continue;
        }
      } else {
        // If no schema specified, include decorators without schema or with 'default' schema
        if (data.schema && data.schema !== 'default') {
          continue;
        }
      }

      const directive = this.convertMethodCallToDirective(decorator, data);
      if (directive) {
        directiveContainer.methodCalls.push(directive);
      }
    }

    // Convert @GQLImport decorators to import directives
    for (const { decorator, data } of decoratorContainer.imports) {
      // Schema filtering logic
      if (schemaId) {
        // If a specific schema is requested, only include decorators for that schema
        if (data.schema !== schemaId) {
          continue;
        }
      } else {
        // If no schema specified, include decorators without schema or with 'default' schema
        if (data.schema && data.schema !== 'default') {
          continue;
        }
      }

      const directive = this.convertImportToDirective(decorator, data);
      if (directive) {
        directiveContainer.imports.push(directive);
      }
    }

    // Convert @GQLField decorators to field directives
    for (const { decorator, data } of decoratorContainer.fields) {
      // Skip if schema doesn't match
      if (schemaId && data.schema && data.schema !== schemaId) {
        continue;
      }

      const fieldDirective = this.convertFieldToDirective(decorator, data);
      if (fieldDirective) {
        directiveContainer.fieldFields.push(fieldDirective);
      }
    }

    // Convert @GQLContext decorators to context directives
    for (const { decorator, data } of decoratorContainer.contexts) {
      // Skip if schema doesn't match
      if (schemaId && data.schema && data.schema !== schemaId) {
        continue;
      }

      const directive = this.convertContextToDirective(decorator, data);
      if (directive) {
        if (!directiveContainer.others.context) {
          directiveContainer.others.context = [];
        }
        directiveContainer.others.context.push(directive);
      }
    }

    return directiveContainer;
  }

  /**
   * Convert @GQLMethodCall decorator to methodCall directive
   * @param decorator The parsed decorator
   * @param data The method call data
   * @returns Converted directive or null if invalid
   */
  private convertMethodCallToDirective(
    decorator: ParsedDecorator,
    data: GQLMethodCallData
  ): ParsedDirective | null {
    // Validate the decorator data
    const validation = DecoratorValidator.validate(decorator, data);
    if (!validation.valid) {
      console.warn(`Invalid @GQLMethodCall decorator at ${decorator.filePath}:${decorator.lineNumber}:`, validation.errors);
      return null;
    }

    // Build the method call content - if no call is provided, use a special marker for smart default
    let content: string;
    if (!data.call) {
      // Use a special marker to indicate this is a smart default
      content = `__SMART_DEFAULT__${data.type}.${data.field}`;
    } else {
      content = data.call;
    }

    // Enhanced type casting support
    if (data.enableTypeCasting) {
      content = this.enhanceTypeCasting(content);
    }

    // Add async wrapper if specified
    if (data.async && !content.includes('await')) {
      content = `await ${content}`;
    }

    // Create the directive with schema identifier support
    const directive: ParsedDirective = {
      name: 'methodCall',
      content,
      raw: this.generateMethodCallComment(data, content),
    };

    return directive;
  }

  /**
   * Generate method call comment with schema identifier support
   * @param data The method call data
   * @param content The method call content
   * @returns Generated comment string
   */
  private generateMethodCallComment(data: GQLMethodCallData, content: string): string {
    const baseComment = `# @methodCall(${content})`;

    if (data.schema && data.schema !== 'default') {
      // Include schema identifier in comment for multi-schema support
      return `${baseComment} [Schema: ${data.schema}]`;
    }

    return baseComment;
  }

  /**
   * Convert @GQLImport decorator to import directive
   * @param decorator The parsed decorator
   * @param data The import data
   * @returns Converted directive or null if invalid
   */
  private convertImportToDirective(
    decorator: ParsedDecorator,
    data: GQLImportData
  ): ParsedDirective | null {
    // Validate the decorator data
    const validation = DecoratorValidator.validate(decorator, data);
    if (!validation.valid) {
      console.warn(`Invalid @GQLImport decorator at ${decorator.filePath}:${decorator.lineNumber}:`, validation.errors);
      return null;
    }

    // Resolve import path if it's relative
    let importStatement = data.importStatement;
    if (this.config.codebaseDir && importStatement.includes('./')) {
      importStatement = this.resolveImportPath(decorator.filePath, importStatement);
    }

    // Handle conditional imports
    if (data.conditional && data.condition) {
      importStatement = `/* Conditional: ${data.condition} */ ${importStatement}`;
    }

    // Create the directive
    const directive: ParsedDirective = {
      name: 'import',
      content: importStatement,
      raw: `# @import(${importStatement})`,
    };

    return directive;
  }

  /**
   * Deduplicate and validate import statements
   * @param imports Array of import statements
   * @returns Deduplicated and validated imports
   */
  public static deduplicateImports(imports: string[]): string[] {
    const seen = new Set<string>();
    const deduplicated: string[] = [];
    const importMap = new Map<string, Set<string>>();

    for (const importStatement of imports) {
      // Skip empty or invalid imports
      if (!importStatement || !importStatement.trim()) {
        continue;
      }

      // Parse import statement to extract module and imports
      const parsed = this.parseImportStatement(importStatement);
      if (!parsed) {
        // If we can't parse it, just add it as-is if not seen
        if (!seen.has(importStatement)) {
          seen.add(importStatement);
          deduplicated.push(importStatement);
        }
        continue;
      }

      const { module, imports: importedNames } = parsed;

      // Merge imports from the same module
      if (!importMap.has(module)) {
        importMap.set(module, new Set());
      }

      const existingImports = importMap.get(module)!;
      importedNames.forEach(name => existingImports.add(name));
    }

    // Convert back to import statements
    for (const [module, importedNames] of importMap) {
      if (importedNames.size > 0) {
        const sortedImports = Array.from(importedNames).sort();
        const importStatement = `import { ${sortedImports.join(', ')} } from '${module}';`;
        deduplicated.push(importStatement);
      }
    }

    return deduplicated;
  }

  /**
   * Parse an import statement to extract module and imported names
   * @param importStatement The import statement to parse
   * @returns Parsed import data or null if invalid
   */
  private static parseImportStatement(importStatement: string): { module: string; imports: string[] } | null {
    // Handle conditional imports
    const cleanStatement = importStatement.replace(/\/\*.*?\*\/\s*/, '').trim();

    // Match: import { name1, name2 } from 'module';
    const match = cleanStatement.match(/^import\s*\{\s*([^}]+)\s*\}\s*from\s*['"]([^'"]+)['"];?$/);
    if (!match) {
      return null;
    }

    const [, importList, module] = match;
    const imports = importList.split(',').map(name => name.trim()).filter(name => name);

    return { module, imports };
  }

  /**
   * Convert @GQLField decorator to field directive
   * @param decorator The parsed decorator
   * @param data The field data
   * @returns Converted field directive or null if invalid
   */
  private convertFieldToDirective(
    decorator: ParsedDecorator,
    data: GQLFieldData
  ): FieldDirectiveField | null {
    // Validate the decorator data
    const validation = DecoratorValidator.validate(decorator, data);
    if (!validation.valid) {
      console.warn(`Invalid @GQLField decorator at ${decorator.filePath}:${decorator.lineNumber}:`, validation.errors);
      return null;
    }

    // Smart import detection: if no importPath is specified, check if the type is defined in the same file
    let importPath = data.importPath;
    if (!importPath && this.isTypeDefinedInSameFile(decorator.filePath, data.type)) {
      // Generate relative import path to the decorator file
      importPath = this.generateSmartImportPath(decorator.filePath);
    }

    // Create the field directive
    const fieldDirective: FieldDirectiveField = {
      name: data.name,
      type: data.type,
      raw: `${data.name}${data.optional ? '?' : ''}: ${data.type}`,
      optional: data.optional,
      importPath: importPath,
      hidden: data.hidden,
    };

    return fieldDirective;
  }

  /**
   * Convert @GQLContext decorator to context directive
   * @param decorator The parsed decorator
   * @param data The context data
   * @returns Converted directive or null if invalid
   */
  private convertContextToDirective(
    decorator: ParsedDecorator,
    data: GQLContextData
  ): ParsedDirective | null {
    // Validate the decorator data
    const validation = DecoratorValidator.validate(decorator, data);
    if (!validation.valid) {
      console.warn(`Invalid @GQLContext decorator at ${decorator.filePath}:${decorator.lineNumber}:`, validation.errors);
      return null;
    }

    // Build context directive content
    const content = `{path: "${data.path}", name: "${data.name}"}`;

    // Create the directive
    const directive: ParsedDirective = {
      name: 'context',
      content,
      raw: `# @context(${content})`,
    };

    return directive;
  }

  /**
   * Merge decorator-sourced directives with comment-based directives
   * @param decoratorDirectives Directives from decorators
   * @param commentDirectives Directives from comments
   * @returns Merged directive container
   */
  public mergeDirectives(
    decoratorDirectives: DirectiveContainer,
    commentDirectives: DirectiveContainer
  ): DirectiveContainer {
    const merged: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {},
    };

    // Apply precedence rules
    if (this.precedenceRules.decoratorsOverrideComments) {
      // Decorators take precedence - add comment directives first, then decorators
      merged.imports = [...commentDirectives.imports, ...decoratorDirectives.imports];
      merged.methodCalls = [...commentDirectives.methodCalls, ...decoratorDirectives.methodCalls];
      merged.fieldFields = [...commentDirectives.fieldFields, ...decoratorDirectives.fieldFields];

      // Merge other directives
      const allOtherKeys = new Set([
        ...Object.keys(commentDirectives.others),
        ...Object.keys(decoratorDirectives.others),
      ]);

      for (const key of allOtherKeys) {
        merged.others[key] = [
          ...(commentDirectives.others[key] || []),
          ...(decoratorDirectives.others[key] || []),
        ];
      }
    } else {
      // Comments take precedence - add decorator directives first, then comments
      merged.imports = [...decoratorDirectives.imports, ...commentDirectives.imports];
      merged.methodCalls = [...decoratorDirectives.methodCalls, ...commentDirectives.methodCalls];
      merged.fieldFields = [...decoratorDirectives.fieldFields, ...commentDirectives.fieldFields];

      // Merge other directives
      const allOtherKeys = new Set([
        ...Object.keys(decoratorDirectives.others),
        ...Object.keys(commentDirectives.others),
      ]);

      for (const key of allOtherKeys) {
        merged.others[key] = [
          ...(decoratorDirectives.others[key] || []),
          ...(commentDirectives.others[key] || []),
        ];
      }
    }

    return merged;
  }

  /**
   * Split decorators by schema for multi-schema support
   * @param decoratorContainer The decorator container to split
   * @returns Multi-schema decorator container
   */
  public splitBySchema(decoratorContainer: DecoratorContainer): MultiSchemaDecoratorContainer {
    const result: MultiSchemaDecoratorContainer = {
      default: {
        methodCalls: [],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      },
      schemas: {},
    };

    // Process method calls
    for (const item of decoratorContainer.methodCalls) {
      const schemaId = item.data.schema ?? 'default';
      if (schemaId === 'default') {
        result.default.methodCalls.push(item);
      } else {
        if (!result.schemas[schemaId]) {
          result.schemas[schemaId] = {
            methodCalls: [],
            imports: [],
            fields: [],
            contexts: [],
            others: {},
          };
        }
        result.schemas[schemaId].methodCalls.push(item);
      }
    }

    // Process imports
    for (const item of decoratorContainer.imports) {
      const schemaId = item.data.schema ?? 'default';
      if (schemaId === 'default') {
        result.default.imports.push(item);
      } else {
        if (!result.schemas[schemaId]) {
          result.schemas[schemaId] = {
            methodCalls: [],
            imports: [],
            fields: [],
            contexts: [],
            others: {},
          };
        }
        result.schemas[schemaId].imports.push(item);
      }
    }

    // Process fields
    for (const item of decoratorContainer.fields) {
      const schemaId = item.data.schema ?? 'default';
      if (schemaId === 'default') {
        result.default.fields.push(item);
      } else {
        if (!result.schemas[schemaId]) {
          result.schemas[schemaId] = {
            methodCalls: [],
            imports: [],
            fields: [],
            contexts: [],
            others: {},
          };
        }
        result.schemas[schemaId].fields.push(item);
      }
    }

    // Process contexts
    for (const item of decoratorContainer.contexts) {
      const schemaId = item.data.schema ?? 'default';
      if (schemaId === 'default') {
        result.default.contexts.push(item);
      } else {
        if (!result.schemas[schemaId]) {
          result.schemas[schemaId] = {
            methodCalls: [],
            imports: [],
            fields: [],
            contexts: [],
            others: {},
          };
        }
        result.schemas[schemaId].contexts.push(item);
      }
    }

    return result;
  }

  /**
   * Validate schema identifiers and detect potential collisions
   * @param decoratorContainer The decorator container to validate
   * @returns Validation result with warnings and errors
   */
  public validateSchemaIdentifiers(decoratorContainer: DecoratorContainer): {
    valid: boolean;
    warnings: string[];
    errors: string[];
    collisions: Array<{
      type: string;
      field?: string;
      schemas: string[];
      location: string;
    }>;
  } {
    const result = {
      valid: true,
      warnings: [] as string[],
      errors: [] as string[],
      collisions: [] as Array<{
        type: string;
        field?: string;
        schemas: string[];
        location: string;
      }>
    };

    // Track method calls by type/field to detect collisions
    const methodCallMap = new Map<string, Array<{ schema: string; location: string }>>();

    // Check method call decorators for collisions
    for (const { decorator, data } of decoratorContainer.methodCalls) {
      const key = `${data.type}.${data.field ?? 'undefined'}`;
      const schema = data.schema ?? 'default';
      const location = `${decorator.filePath}:${decorator.lineNumber}`;

      if (!methodCallMap.has(key)) {
        methodCallMap.set(key, []);
      }
      methodCallMap.get(key)!.push({ schema, location });
    }

    // Detect collisions where same type/field has multiple schemas
    for (const [key, entries] of methodCallMap.entries()) {
      const schemas = [...new Set(entries.map(e => e.schema))];
      if (schemas.length > 1) {
        const [type, field] = key.split('.');
        result.collisions.push({
          type,
          field: field !== 'undefined' ? field : undefined,
          schemas,
          location: entries.map(e => e.location).join(', ')
        });
        result.warnings.push(
          `Schema collision detected for ${type}${field !== 'undefined' ? `.${field}` : ''}: ` +
          `multiple schemas (${schemas.join(', ')}) define method calls for the same type/field`
        );
      }
    }

    // Validate schema identifier format
    const allSchemas = new Set<string>();
    for (const { data } of [...decoratorContainer.methodCalls, ...decoratorContainer.imports, ...decoratorContainer.fields, ...decoratorContainer.contexts]) {
      if (data.schema) {
        allSchemas.add(data.schema);
      }
    }

    for (const schema of allSchemas) {
      if (!this.isValidSchemaIdentifier(schema)) {
        result.errors.push(`Invalid schema identifier format: "${schema}". Schema identifiers must be alphanumeric with optional hyphens and underscores.`);
        result.valid = false;
      }
    }

    return result;
  }

  /**
   * Validate schema identifier format
   * @param schemaId The schema identifier to validate
   * @returns True if valid, false otherwise
   */
  private isValidSchemaIdentifier(schemaId: string): boolean {
    // Schema identifiers should be alphanumeric with optional hyphens and underscores
    // Must not start with a number or special character
    const pattern = /^[a-zA-Z][a-zA-Z0-9_-]*$/;
    return pattern.test(schemaId) && schemaId.length <= 50; // Reasonable length limit
  }

  /**
   * Resolve import path relative to decorator file location
   * @param decoratorFilePath The file containing the decorator
   * @param importPath The import path to resolve
   * @returns Resolved import path
   */
  private resolveImportPath(decoratorFilePath: string, importPath: string): string {
    if (!this.config.codebaseDir) {
      return importPath;
    }

    // Get decorator directory for path resolution
    const decoratorDir = path.dirname(decoratorFilePath);

    // If import path is relative, resolve it
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      const resolvedPath = path.resolve(decoratorDir, importPath);
      const relativeToProject = path.relative(process.cwd(), resolvedPath);
      return relativeToProject.startsWith('.') ? relativeToProject : `./${relativeToProject}`;
    }

    return importPath;
  }

  /**
   * Enhance method call content with type casting support
   * @param content The original method call content
   * @returns Enhanced content with type casting patterns
   */
  private enhanceTypeCasting(content: string): string {
    // Support common type casting patterns
    const typeCastingPatterns = [
      // Pattern: return someMethod() as SomeType
      {
        regex: /^return\s+(.+?)(\s+as\s+\w+)?$/,
        replacement: (match: string, call: string, existingCast: string) => {
          if (existingCast) {
            return match; // Already has type casting
          }
          return `return ${call.trim()}`;
        }
      },
      // Pattern: return await someMethod() as SomeType
      {
        regex: /^return\s+await\s+(.+?)(\s+as\s+\w+)?$/,
        replacement: (match: string, call: string, existingCast: string) => {
          if (existingCast) {
            return match; // Already has type casting
          }
          return `return await ${call.trim()}`;
        }
      },
      // Pattern: obj.property as SomeType
      {
        regex: /^(.+?)(\s+as\s+\w+)?$/,
        replacement: (match: string, expression: string, existingCast: string) => {
          if (existingCast || expression.includes('(')) {
            return match; // Already has type casting or is a function call
          }
          return expression.trim();
        }
      }
    ];

    // Apply type casting patterns
    for (const pattern of typeCastingPatterns) {
      const match = content.match(pattern.regex);
      if (match) {
        const enhanced = pattern.replacement(match[0], match[1], match[2]);
        if (enhanced !== content) {
          console.log(`Enhanced type casting for: ${content} → ${enhanced}`);
          return enhanced;
        }
      }
    }

    return content;
  }

  /**
   * Generate smart import path for a decorator function
   * @param decoratorFilePath The file containing the decorator
   * @param targetOutputPath The target resolver output path
   * @param functionName The function name to import
   * @returns Generated import statement
   */
  public generateSmartImport(
    decoratorFilePath: string,
    targetOutputPath: string,
    functionName: string
  ): string {
    let importPath: string;

    // Try to use alias-based import if alias is configured and file is within codebase
    if (this.aliasConfig) {
      const aliasImport = AliasConfigUtils.convertToAliasImport(decoratorFilePath, this.aliasConfig);
      if (aliasImport) {
        importPath = aliasImport;
      } else {
        // Fall back to relative path if file is outside codebase
        const outputDir = path.dirname(targetOutputPath);
        const relativePath = path.relative(outputDir, decoratorFilePath);
        const pathWithoutExtension = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
        // Normalize path separators for cross-platform compatibility
        const normalizedPath = pathWithoutExtension.replace(/\\/g, '/');
        importPath = normalizedPath.startsWith('.') ? normalizedPath : `./${normalizedPath}`;
      }
    } else {
      // Use relative path when no alias is configured
      const outputDir = path.dirname(targetOutputPath);
      const relativePath = path.relative(outputDir, decoratorFilePath);
      const pathWithoutExtension = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
      // Normalize path separators for cross-platform compatibility
      const normalizedPath = pathWithoutExtension.replace(/\\/g, '/');
      importPath = normalizedPath.startsWith('.') ? normalizedPath : `./${normalizedPath}`;
    }

    return `import { ${functionName} } from '${importPath}';`;
  }

  /**
   * Generate smart imports for all decorator method calls for a specific type/field
   * @param typeName The GraphQL type name
   * @param fieldName The GraphQL field name (required for precise import matching)
   * @param targetOutputPath The target resolver output path
   * @param schemaId Optional schema identifier
   * @returns Array of import statements
   */
  public generateSmartImportsForTypeField(
    typeName: string,
    fieldName: string | undefined,
    targetOutputPath: string,
    schemaId?: string
  ): string[] {
    if (!this.decoratorMetadata) {
      return [];
    }

    // Require fieldName to prevent unused imports across all fields of the same type
    if (!fieldName) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`⚠️ generateSmartImportsForTypeField called without fieldName for type ${typeName}. Skipping to prevent unused imports.`);
      }
      return [];
    }

    const imports: string[] = [];
    const importedFunctions = new Set<string>();

    // Find matching method call decorators
    for (const { decorator, data } of this.decoratorMetadata.methodCalls) {
      // Schema filtering logic (consistent with convertToDirectiveContainer)
      if (schemaId) {
        // If a specific schema is requested, only include decorators for that schema
        if (data.schema !== schemaId) {
          continue;
        }
      } else {
        // If no schema specified, include decorators without schema or with 'default' schema
        if (data.schema && data.schema !== 'default') {
          continue;
        }
      }

      // Check if this decorator matches the exact type AND field (both required)
      const typeMatches = data.type === typeName;
      const fieldMatches = data.field === fieldName;

      if (typeMatches && fieldMatches && data.call) {
        // Extract function name from the call expression
        const functionName = this.extractFunctionNameFromCall(data.call);

        if (functionName && !importedFunctions.has(functionName)) {
          // Check if there's an explicit @GQLImport for this function
          const hasExplicitImport = this.hasExplicitImportForFunction(functionName, schemaId);

          if (!hasExplicitImport) {
            // Auto-generate import using smart estimation
            const autoImportStatement = this.generateAutoImport(
              decorator.filePath,
              targetOutputPath,
              functionName
            );
            if (autoImportStatement) {
              imports.push(autoImportStatement);
              importedFunctions.add(functionName);
            }
          } else {
            // Use the existing smart import logic for explicit imports
            const importStatement = this.generateSmartImport(
              decorator.filePath,
              targetOutputPath,
              functionName
            );
            imports.push(importStatement);
            importedFunctions.add(functionName);
          }
        }
      }
    }

    return imports;
  }

  /**
   * Check if there's an explicit @GQLImport decorator for a function
   * @param functionName The function name to check
   * @param schemaId Optional schema identifier
   * @returns True if explicit import exists, false otherwise
   */
  private hasExplicitImportForFunction(functionName: string, schemaId?: string): boolean {
    if (!this.decoratorMetadata) {
      return false;
    }

    return this.decoratorMetadata.imports.some(({ data }) => {
      // Schema filtering logic (consistent with convertToDirectiveContainer)
      if (schemaId) {
        // If a specific schema is requested, only include decorators for that schema
        if (data.schema !== schemaId) {
          return false;
        }
      } else {
        // If no schema specified, include decorators without schema or with 'default' schema
        if (data.schema && data.schema !== 'default') {
          return false;
        }
      }

      // Check if the import statement includes this function name
      return data.importStatement.includes(functionName);
    });
  }

  /**
   * Generate auto-import statement using smart estimation
   * @param decoratorFilePath The file containing the decorator
   * @param targetOutputPath The target resolver output path
   * @param functionName The function name to import
   * @returns Generated import statement or null if estimation fails
   */
  private generateAutoImport(
    decoratorFilePath: string,
    targetOutputPath: string,
    functionName: string
  ): string | null {
    try {
      // Strategy 1: Try alias-based import if configured
      if (this.aliasConfig) {
        const aliasImport = this.generateAliasBasedAutoImport(decoratorFilePath, functionName);
        if (aliasImport) {
          return aliasImport;
        }
      }

      // Strategy 2: Try index-based import detection
      const indexImport = this.generateIndexBasedAutoImport(decoratorFilePath, functionName);
      if (indexImport) {
        return indexImport;
      }

      // Strategy 3: Fall back to relative path import
      const relativeImport = this.generateRelativePathAutoImport(
        decoratorFilePath,
        targetOutputPath,
        functionName
      );
      return relativeImport;

    } catch (error) {
      console.warn(`Failed to generate auto-import for ${functionName}:`, error);
      return null;
    }
  }

  /**
   * Extract function name from a method call expression
   * @param callExpression The method call expression
   * @returns The function name or null if not extractable
   */
  private extractFunctionNameFromCall(callExpression: string): string | null {
    // Handle various call patterns:
    // - functionName(args) -> functionName
    // - obj.method(args) -> method
    // - await functionName(args) -> functionName
    // - return functionName(args) -> functionName

    const patterns = [
      /^(?:return\s+)?(?:await\s+)?([A-Za-z_$][A-Za-z0-9_$]*)\s*\(/,  // Simple function call
      /^(?:return\s+)?(?:await\s+)?[A-Za-z_$][A-Za-z0-9_$]*\.([A-Za-z_$][A-Za-z0-9_$]*)\s*\(/,  // Method call
    ];

    for (const pattern of patterns) {
      const match = callExpression.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Generate alias-based auto-import using configured alias
   * @param decoratorFilePath The file containing the decorator
   * @param functionName The function name to import
   * @returns Generated import statement or null if not applicable
   */
  private generateAliasBasedAutoImport(
    decoratorFilePath: string,
    functionName: string
  ): string | null {
    if (!this.aliasConfig) {
      return null;
    }

    const aliasImport = AliasConfigUtils.convertToAliasImport(decoratorFilePath, this.aliasConfig);
    if (aliasImport) {
      return `import { ${functionName} } from '${aliasImport}';`;
    }

    return null;
  }

  /**
   * Generate index-based auto-import by looking for index files
   * @param decoratorFilePath The file containing the decorator
   * @param functionName The function name to import
   * @returns Generated import statement or null if no index found
   */
  private generateIndexBasedAutoImport(
    decoratorFilePath: string,
    functionName: string
  ): string | null {
    try {
      const dirPath = path.dirname(decoratorFilePath);
      const indexFiles = ['index.ts', 'index.tsx', 'index.js', 'index.jsx'];

      for (const indexFile of indexFiles) {
        const indexPath = path.join(dirPath, indexFile);
        if (fs.existsSync(indexPath)) {
          // Check if the index file exports the function
          const indexContent = fs.readFileSync(indexPath, 'utf8');
          if (this.checkIfFunctionExportedInFile(indexContent, functionName)) {
            // Generate import path to the directory (index file)
            if (this.aliasConfig) {
              const aliasImport = AliasConfigUtils.convertToAliasImport(dirPath, this.aliasConfig);
              if (aliasImport) {
                return `import { ${functionName} } from '${aliasImport}';`;
              }
            }

            // Fall back to relative path to directory
            const relativePath = path.relative(process.cwd(), dirPath);
            const normalizedPath = relativePath.replace(/\\/g, '/');
            const importPath = normalizedPath.startsWith('.') ? normalizedPath : `./${normalizedPath}`;
            return `import { ${functionName} } from '${importPath}';`;
          }
        }
      }

      return null;
    } catch (_error) {
      return null;
    }
  }

  /**
   * Generate relative path auto-import as fallback
   * @param decoratorFilePath The file containing the decorator
   * @param targetOutputPath The target resolver output path
   * @param functionName The function name to import
   * @returns Generated import statement
   */
  private generateRelativePathAutoImport(
    decoratorFilePath: string,
    targetOutputPath: string,
    functionName: string
  ): string {
    const outputDir = path.dirname(targetOutputPath);
    const relativePath = path.relative(outputDir, decoratorFilePath);
    const pathWithoutExtension = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    const normalizedPath = pathWithoutExtension.replace(/\\/g, '/');
    const importPath = normalizedPath.startsWith('.') ? normalizedPath : `./${normalizedPath}`;

    return `import { ${functionName} } from '${importPath}';`;
  }

  /**
   * Check if a function is exported in a file content
   * @param fileContent The file content to check
   * @param functionName The function name to look for
   * @returns True if function is exported
   */
  private checkIfFunctionExportedInFile(fileContent: string, functionName: string): boolean {
    const exportPatterns = [
      new RegExp(`export\\s+(?:const|let|var|function)\\s+${functionName}\\b`),
      new RegExp(`export\\s*\\{[^}]*\\b${functionName}\\b[^}]*\\}`),
      new RegExp(`export\\s+default\\s+${functionName}\\b`),
    ];

    return exportPatterns.some(pattern => pattern.test(fileContent));
  }

  /**
   * Check if a type is defined in the same file as the decorator
   * @param filePath The file path containing the decorator
   * @param typeName The type name to check
   * @returns True if the type is defined in the same file
   */
  private isTypeDefinedInSameFile(filePath: string, typeName: string): boolean {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }

      const fileContent = fs.readFileSync(filePath, 'utf8');

      // Check for interface, type, class, or enum definitions
      const typeDefinitionPatterns = [
        new RegExp(`export\\s+interface\\s+${typeName}\\s*{`, 'i'),
        new RegExp(`export\\s+type\\s+${typeName}\\s*=`, 'i'),
        new RegExp(`export\\s+class\\s+${typeName}\\s*{`, 'i'),
        new RegExp(`export\\s+enum\\s+${typeName}\\s*{`, 'i'),
        new RegExp(`interface\\s+${typeName}\\s*{`, 'i'),
        new RegExp(`type\\s+${typeName}\\s*=`, 'i'),
        new RegExp(`class\\s+${typeName}\\s*{`, 'i'),
        new RegExp(`enum\\s+${typeName}\\s*{`, 'i'),
      ];

      return typeDefinitionPatterns.some(pattern => pattern.test(fileContent));
    } catch (error) {
      console.warn(`Error checking type definition in file ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Generate a smart import path for a decorator file
   * @param decoratorFilePath The file path containing the decorator
   * @returns An import path that can be used from generated files (alias-based or relative)
   */
  private generateSmartImportPath(decoratorFilePath: string): string {
    // Try to use alias-based import if alias is configured and file is within codebase
    if (this.aliasConfig) {
      const aliasImport = AliasConfigUtils.convertToAliasImport(decoratorFilePath, this.aliasConfig);
      if (aliasImport) {
        return aliasImport;
      }
    }

    // Fall back to relative path
    const relativePath = path.relative(process.cwd(), decoratorFilePath);
    const pathWithoutExtension = relativePath.replace(/\.(ts|js)$/, '');
    // Normalize path separators for cross-platform compatibility (Windows uses backslashes)
    const importPath = pathWithoutExtension.replace(/\\/g, '/');

    // Ensure it starts with ./ for relative imports
    return importPath.startsWith('./') ? importPath : `./${importPath}`;
  }
}
