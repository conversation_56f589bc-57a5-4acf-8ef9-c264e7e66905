/**
 * Type map data structures for two-phase processing architecture
 * 
 * This module defines the core data structures used to store parsed TypeScript
 * information during the parsing phase, which is then used during the generation
 * phase without re-parsing files.
 */

import type { ParsedDecorator, DecoratorContainer } from './decorator-parser';
import type { ParsedDirective } from './directive-parser';
import type { FieldDirectiveField } from './directive-parser';

/**
 * Information about a parsed TypeScript file
 */
export interface ParsedFileInfo {
  /** Absolute path to the file */
  filePath: string;
  /** File size in bytes */
  size: number;
  /** Last modified timestamp */
  lastModified: number;
  /** File content hash for change detection */
  contentHash: string;
  /** Parsing timestamp */
  parsedAt: number;
  /** Parser used (oxc, swc, typescript) */
  parser: string;
  /** Parse duration in milliseconds */
  parseDuration: number;
}

/**
 * Method call information extracted from decorators or directives
 */
export interface MethodCallInfo {
  /** Type name this method call belongs to */
  typeName: string;
  /** Field name this method call is for */
  fieldName: string;
  /** Method call content */
  call: string;
  /** Import path for the method */
  importPath?: string;
  /** Schema identifier */
  schemaId?: string;
  /** Source file information */
  sourceFile: ParsedFileInfo;
  /** Line number in source file */
  lineNumber: number;
  /** Whether this is async */
  async?: boolean;
  /** Type casting enabled */
  enableTypeCasting?: boolean;
}

/**
 * Import information extracted from files
 */
export interface ImportInfo {
  /** Import statement */
  statement: string;
  /** Module being imported from */
  module: string;
  /** Named imports */
  namedImports: string[];
  /** Default import */
  defaultImport?: string;
  /** Namespace import */
  namespaceImport?: string;
  /** Source file information */
  sourceFile: ParsedFileInfo;
  /** Line number in source file */
  lineNumber: number;
}

/**
 * Field information extracted from decorators or directives
 */
export interface FieldInfo {
  /** Type name this field belongs to */
  typeName: string;
  /** Field name */
  fieldName: string;
  /** Field type */
  fieldType: string;
  /** Import path for the field type */
  importPath?: string;
  /** Whether field is optional */
  optional?: boolean;
  /** Schema identifier */
  schemaId?: string;
  /** Source file information */
  sourceFile: ParsedFileInfo;
  /** Line number in source file */
  lineNumber: number;
}

/**
 * Context information extracted from decorators
 */
export interface ContextInfo {
  /** Context type */
  contextType: string;
  /** Import path for the context */
  importPath?: string;
  /** Source file information */
  sourceFile: ParsedFileInfo;
  /** Line number in source file */
  lineNumber: number;
}

/**
 * Complete type map containing all parsed information
 */
export interface TypeMap {
  /** Map version for compatibility checking */
  version: string;
  /** Generation timestamp */
  generatedAt: number;
  /** Schema identifier this map is for */
  schemaId?: string;
  /** Base directory that was scanned */
  baseDirectory: string;
  /** All parsed files */
  files: Map<string, ParsedFileInfo>;
  /** Method calls by type and field */
  methodCalls: Map<string, Map<string, MethodCallInfo[]>>;
  /** Imports by file */
  imports: Map<string, ImportInfo[]>;
  /** Fields by type */
  fields: Map<string, FieldInfo[]>;
  /** Context information */
  contexts: ContextInfo[];
  /** Performance metrics */
  metrics: TypeMapMetrics;
}

/**
 * Performance metrics for type map building
 */
export interface TypeMapMetrics {
  /** Total files scanned */
  totalFiles: number;
  /** Files successfully parsed */
  successfullyParsed: number;
  /** Files that failed to parse */
  failedToParse: number;
  /** Total parsing time in milliseconds */
  totalParseTime: number;
  /** Average parse time per file */
  averageParseTime: number;
  /** Peak memory usage during parsing */
  peakMemoryUsage: number;
  /** Parser usage statistics */
  parserStats: {
    oxc: { count: number; totalTime: number };
    swc: { count: number; totalTime: number };
    typescript: { count: number; totalTime: number };
  };
}

/**
 * Configuration for type map building
 */
export interface TypeMapConfig {
  /** Base directory to scan */
  baseDirectory: string;
  /** File patterns to include */
  includePatterns: string[];
  /** File patterns to exclude */
  excludePatterns: string[];
  /** Maximum number of parallel workers */
  maxWorkers?: number;
  /** Enable caching */
  enableCaching?: boolean;
  /** Cache directory */
  cacheDirectory?: string;
  /** Schema identifier */
  schemaId?: string;
  /** Enable debug logging */
  debug?: boolean;
  /** Memory pressure threshold (0-1) */
  memoryPressureThreshold?: number;
  /** Enable decorator scanning */
  enableDecorators?: boolean;
}

/**
 * Result of type map building operation
 */
export interface TypeMapBuildResult {
  /** The built type map */
  typeMap: TypeMap;
  /** Whether the map was loaded from cache */
  fromCache: boolean;
  /** Build duration in milliseconds */
  buildDuration: number;
  /** Any warnings encountered */
  warnings: string[];
  /** Any errors encountered (non-fatal) */
  errors: string[];
}
