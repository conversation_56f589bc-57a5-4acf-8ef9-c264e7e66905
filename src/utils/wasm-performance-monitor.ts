import { EventEmitter } from 'events';
import { getGlobalWASMBridge, type WASMPerformanceMetrics } from './wasm-bridge';

/**
 * Phase 3: WASM Performance Monitor
 * Comprehensive monitoring and optimization for WASM operations
 */

export interface WASMPerformanceConfig {
  enableMonitoring: boolean;
  enableOptimization: boolean;
  enableReporting: boolean;
  reportingInterval: number; // milliseconds
  performanceThreshold: number; // milliseconds - threshold for slow operations
  enableAdaptiveOptimization: boolean;
  debug: boolean;
}

export interface WASMOperationMetrics {
  operationName: string;
  wasmTime: number;
  fallbackTime: number;
  wasmSuccess: boolean;
  fallbackSuccess: boolean;
  speedupRatio: number; // wasm vs fallback performance
  timestamp: number;
}

export interface WASMPerformanceReport {
  totalOperations: number;
  wasmOperations: number;
  fallbackOperations: number;
  averageWasmSpeedup: number;
  slowOperations: WASMOperationMetrics[];
  recommendations: string[];
  systemInfo: {
    wasmSupported: boolean;
    platformOptimal: boolean;
    memoryPressure: number;
  };
}

/**
 * WASM Performance Monitor for optimization and reporting
 */
export class WASMPerformanceMonitor extends EventEmitter {
  private config: Required<WASMPerformanceConfig>;
  private wasmBridge = getGlobalWASMBridge();
  private operationHistory: WASMOperationMetrics[] = [];
  private maxHistorySize = 1000;
  private reportingTimer?: NodeJS.Timeout;
  private adaptiveSettings = {
    wasmThreshold: 1000, // Use WASM for operations > 1000ms expected
    fallbackThreshold: 100, // Use fallback for operations < 100ms expected
  };

  constructor(config: Partial<WASMPerformanceConfig> = {}) {
    super();
    
    this.config = {
      enableMonitoring: true,
      enableOptimization: true,
      enableReporting: false,
      reportingInterval: 60000, // 1 minute
      performanceThreshold: 1000, // 1 second
      enableAdaptiveOptimization: true,
      debug: false,
      ...config
    };

    if (this.config.enableReporting) {
      this.startPeriodicReporting();
    }

    this.setupEventHandlers();
  }

  /**
   * Setup event handlers for WASM bridge
   */
  private setupEventHandlers(): void {
    this.wasmBridge.on('wasmInitialized', (event) => {
      if (this.config.debug) {
        console.log('🔧 WASM Performance Monitor: WASM initialized', event);
      }
    });
  }

  /**
   * Start periodic performance reporting
   */
  private startPeriodicReporting(): void {
    this.reportingTimer = setInterval(() => {
      const report = this.generatePerformanceReport();
      this.emit('performanceReport', report);
      
      if (this.config.debug) {
        console.log('📊 WASM Performance Report:', report);
      }
    }, this.config.reportingInterval);
  }

  /**
   * Monitor a WASM operation
   */
  async monitorOperation<T>(
    operationName: string,
    wasmOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    expectedComplexity: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<T> {
    if (!this.config.enableMonitoring) {
      // Just execute the operation without monitoring
      if (this.wasmBridge.isWASMAvailable()) {
        return wasmOperation();
      } else {
        return fallbackOperation();
      }
    }

    const startTime = Date.now();
    let wasmTime = 0;
    let fallbackTime = 0;
    let wasmSuccess = false;
    let fallbackSuccess = false;
    let result: T;

    // Determine which operation to try first based on adaptive settings
    const shouldTryWasmFirst = this.shouldUseWasm(operationName, expectedComplexity);

    if (shouldTryWasmFirst && this.wasmBridge.isWASMAvailable()) {
      // Try WASM first
      const wasmStartTime = Date.now();
      try {
        result = await wasmOperation();
        wasmTime = Date.now() - wasmStartTime;
        wasmSuccess = true;
      } catch (wasmError) {
        wasmTime = Date.now() - wasmStartTime;
        wasmSuccess = false;
        
        if (this.config.debug) {
          console.warn(`⚠️ WASM operation ${operationName} failed:`, wasmError);
        }

        // Fallback to JavaScript
        const fallbackStartTime = Date.now();
        result = await fallbackOperation();
        fallbackTime = Date.now() - fallbackStartTime;
        fallbackSuccess = true;
      }
    } else {
      // Use fallback
      const fallbackStartTime = Date.now();
      result = await fallbackOperation();
      fallbackTime = Date.now() - fallbackStartTime;
      fallbackSuccess = true;
    }

    // Record metrics
    const totalTime = Date.now() - startTime;
    const speedupRatio = fallbackTime > 0 && wasmTime > 0 ? fallbackTime / wasmTime : 1;

    const metrics: WASMOperationMetrics = {
      operationName,
      wasmTime,
      fallbackTime,
      wasmSuccess,
      fallbackSuccess,
      speedupRatio,
      timestamp: Date.now()
    };

    this.recordOperation(metrics);

    // Adaptive optimization
    if (this.config.enableAdaptiveOptimization) {
      this.updateAdaptiveSettings(metrics);
    }

    // Check for slow operations
    if (totalTime > this.config.performanceThreshold) {
      this.emit('slowOperation', { operationName, totalTime, metrics });
    }

    return result;
  }

  /**
   * Determine if WASM should be used for an operation
   */
  private shouldUseWasm(operationName: string, expectedComplexity: 'low' | 'medium' | 'high'): boolean {
    if (!this.wasmBridge.isWASMAvailable()) {
      return false;
    }

    // Check historical performance for this operation
    const historicalData = this.operationHistory
      .filter(op => op.operationName === operationName)
      .slice(-10); // Last 10 operations

    if (historicalData.length > 3) {
      const avgSpeedup = historicalData.reduce((sum, op) => sum + op.speedupRatio, 0) / historicalData.length;
      return avgSpeedup > 1.2; // Use WASM if it's at least 20% faster
    }

    // Default based on complexity
    switch (expectedComplexity) {
      case 'low':
        return false; // Overhead not worth it for simple operations
      case 'medium':
        return true; // Good candidate for WASM
      case 'high':
        return true; // Definitely use WASM for complex operations
      default:
        return true;
    }
  }

  /**
   * Record operation metrics
   */
  private recordOperation(metrics: WASMOperationMetrics): void {
    this.operationHistory.push(metrics);

    // Keep history size manageable
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift();
    }

    if (this.config.debug) {
      console.log(`📈 Recorded operation: ${metrics.operationName} (WASM: ${metrics.wasmTime}ms, Fallback: ${metrics.fallbackTime}ms, Speedup: ${metrics.speedupRatio.toFixed(2)}x)`);
    }
  }

  /**
   * Update adaptive settings based on performance data
   */
  private updateAdaptiveSettings(metrics: WASMOperationMetrics): void {
    // Adjust thresholds based on performance
    if (metrics.wasmSuccess && metrics.speedupRatio > 2) {
      // WASM is performing very well, lower the threshold
      this.adaptiveSettings.wasmThreshold = Math.max(500, this.adaptiveSettings.wasmThreshold - 100);
    } else if (metrics.wasmSuccess && metrics.speedupRatio < 1.1) {
      // WASM is barely faster, raise the threshold
      this.adaptiveSettings.wasmThreshold = Math.min(2000, this.adaptiveSettings.wasmThreshold + 100);
    }
  }

  /**
   * Generate comprehensive performance report
   */
  generatePerformanceReport(): WASMPerformanceReport {
    const wasmMetrics = this.wasmBridge.getMetrics();
    const totalOperations = this.operationHistory.length;
    const wasmOperations = this.operationHistory.filter(op => op.wasmSuccess).length;
    const fallbackOperations = totalOperations - wasmOperations;

    // Calculate average speedup
    const speedups = this.operationHistory
      .filter(op => op.wasmSuccess && op.fallbackTime > 0)
      .map(op => op.speedupRatio);
    const averageWasmSpeedup = speedups.length > 0 
      ? speedups.reduce((sum, ratio) => sum + ratio, 0) / speedups.length 
      : 1;

    // Identify slow operations
    const slowOperations = this.operationHistory
      .filter(op => (op.wasmTime + op.fallbackTime) > this.config.performanceThreshold)
      .slice(-10); // Last 10 slow operations

    // Generate recommendations
    const recommendations = this.generateRecommendations(wasmMetrics, averageWasmSpeedup);

    return {
      totalOperations,
      wasmOperations,
      fallbackOperations,
      averageWasmSpeedup,
      slowOperations,
      recommendations,
      systemInfo: {
        wasmSupported: this.wasmBridge.isWASMAvailable(),
        platformOptimal: averageWasmSpeedup > 1.5,
        memoryPressure: this.estimateMemoryPressure()
      }
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(wasmMetrics: WASMPerformanceMetrics, averageSpeedup: number): string[] {
    const recommendations: string[] = [];

    if (!wasmMetrics.platformSupported) {
      recommendations.push('WASM is not supported on this platform. Consider upgrading Node.js or using a different environment.');
    } else if (averageSpeedup < 1.2) {
      recommendations.push('WASM performance is marginal. Consider disabling WASM for better consistency.');
    } else if (averageSpeedup > 3) {
      recommendations.push('WASM is performing excellently. Consider migrating more operations to WASM.');
    }

    if (wasmMetrics.wasmSuccessRate < 0.9) {
      recommendations.push('WASM operations are failing frequently. Check for compatibility issues.');
    }

    if (wasmMetrics.fallbackCalls > wasmMetrics.wasmCalls * 2) {
      recommendations.push('Fallback is being used more than WASM. Review operation complexity thresholds.');
    }

    return recommendations;
  }

  /**
   * Estimate memory pressure (simplified)
   */
  private estimateMemoryPressure(): number {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed / memUsage.heapTotal;
  }

  /**
   * Get current adaptive settings
   */
  getAdaptiveSettings() {
    return { ...this.adaptiveSettings };
  }

  /**
   * Get operation history
   */
  getOperationHistory(): WASMOperationMetrics[] {
    return [...this.operationHistory];
  }

  /**
   * Clear operation history
   */
  clearHistory(): void {
    this.operationHistory = [];
  }

  /**
   * Stop monitoring and clean up
   */
  stop(): void {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
      this.reportingTimer = undefined;
    }
  }
}

/**
 * Global WASM performance monitor instance
 */
let globalWASMPerformanceMonitor: WASMPerformanceMonitor | null = null;

/**
 * Get or create global WASM performance monitor
 */
export function getGlobalWASMPerformanceMonitor(config?: Partial<WASMPerformanceConfig>): WASMPerformanceMonitor {
  if (!globalWASMPerformanceMonitor) {
    globalWASMPerformanceMonitor = new WASMPerformanceMonitor(config);
  }
  return globalWASMPerformanceMonitor;
}
