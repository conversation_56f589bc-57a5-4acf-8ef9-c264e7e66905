import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import { getGlobalWASMBridge } from './wasm-bridge';

/**
 * Phase 2 Optimization: Schema-to-Generated-Code Dependency Graph
 * Tracks relationships between schema files and generated code for smart cache invalidation
 */

export interface DependencyNode {
  id: string;
  type: 'schema' | 'generated' | 'template';
  filePath: string;
  hash: string;
  lastModified: number;
  dependencies: Set<string>; // IDs of nodes this depends on
  dependents: Set<string>;   // IDs of nodes that depend on this
  metadata: Record<string, any>;
}

export interface DependencyChange {
  nodeId: string;
  changeType: 'added' | 'modified' | 'removed';
  oldHash?: string;
  newHash?: string;
  affectedNodes: string[];
  timestamp: number;
  // Phase 3: Enhanced change tracking
  changeLevel: 'file' | 'type' | 'field' | 'directive';
  cascadingChanges: string[];
  impactScore: number;
  processingPriority: number;
}

export interface GraphAnalysis {
  totalNodes: number;
  schemaNodes: number;
  generatedNodes: number;
  templateNodes: number;
  orphanedNodes: string[];
  circularDependencies: string[][];
  criticalPaths: string[][];
  // Phase 3: Enhanced analysis
  changeHotspots: Array<{ nodeId: string; changeFrequency: number; impactScore: number }>;
  processingBottlenecks: Array<{ nodeId: string; dependentCount: number; processingTime: number }>;
  optimizationOpportunities: Array<{ type: string; description: string; estimatedGain: number }>;
}

/**
 * Dependency graph for tracking schema and generated code relationships
 * Phase 3: Enhanced with advanced change detection and optimization algorithms
 */
export class DependencyGraph extends EventEmitter {
  private nodes = new Map<string, DependencyNode>();
  private persistencePath: string;
  private isLoaded = false;

  // Phase 3: Enhanced tracking
  private changeHistory = new Map<string, DependencyChange[]>();
  private processingMetrics = new Map<string, { count: number; totalTime: number; lastProcessed: number }>();
  private optimizationCache = new Map<string, any>();
  private wasmBridge = getGlobalWASMBridge(); // WASM integration

  constructor(persistencePath?: string) {
    super();
    this.persistencePath = persistencePath || path.join(process.cwd(), '.gql-generator-cache', 'dependency-graph.json');
  }

  /**
   * Add or update a node in the dependency graph
   */
  addNode(
    id: string,
    type: 'schema' | 'generated' | 'template',
    filePath: string,
    dependencies: string[] = [],
    metadata: Record<string, any> = {}
  ): DependencyNode {
    const hash = this.calculateFileHash(filePath);
    const lastModified = this.getFileModificationTime(filePath);

    const existingNode = this.nodes.get(id);
    const isNewNode = !existingNode;
    const isModified = existingNode && existingNode.hash !== hash;

    const node: DependencyNode = {
      id,
      type,
      filePath,
      hash,
      lastModified,
      dependencies: new Set(dependencies),
      dependents: new Set(existingNode?.dependents || []),
      metadata: { ...existingNode?.metadata, ...metadata }
    };

    // Update dependency relationships
    if (existingNode) {
      // Remove old dependencies
      for (const depId of existingNode.dependencies) {
        const depNode = this.nodes.get(depId);
        if (depNode) {
          depNode.dependents.delete(id);
        }
      }
    }

    // Add new dependencies
    for (const depId of dependencies) {
      const depNode = this.nodes.get(depId);
      if (depNode) {
        depNode.dependents.add(id);
      }
    }

    this.nodes.set(id, node);

    // Emit events
    if (isNewNode) {
      this.emit('nodeAdded', node);
    } else if (isModified) {
      this.emit('nodeModified', { node, oldHash: existingNode.hash });
    }

    return node;
  }

  /**
   * Remove a node from the dependency graph
   */
  removeNode(id: string): boolean {
    const node = this.nodes.get(id);
    if (!node) return false;

    // Remove from dependencies and dependents
    for (const depId of node.dependencies) {
      const depNode = this.nodes.get(depId);
      if (depNode) {
        depNode.dependents.delete(id);
      }
    }

    for (const depId of node.dependents) {
      const depNode = this.nodes.get(depId);
      if (depNode) {
        depNode.dependencies.delete(id);
      }
    }

    this.nodes.delete(id);
    this.emit('nodeRemoved', node);
    return true;
  }

  /**
   * Get all nodes that depend on the given node (transitively)
   */
  getDependents(nodeId: string, visited = new Set<string>()): string[] {
    if (visited.has(nodeId)) return []; // Avoid cycles

    const node = this.nodes.get(nodeId);
    if (!node) return [];

    visited.add(nodeId);
    const dependents: string[] = [];

    for (const dependentId of node.dependents) {
      dependents.push(dependentId);
      dependents.push(...this.getDependents(dependentId, visited));
    }

    return [...new Set(dependents)]; // Remove duplicates
  }

  /**
   * Get all nodes that the given node depends on (transitively)
   */
  getDependencies(nodeId: string, visited = new Set<string>()): string[] {
    if (visited.has(nodeId)) return []; // Avoid cycles

    const node = this.nodes.get(nodeId);
    if (!node) return [];

    visited.add(nodeId);
    const dependencies: string[] = [];

    for (const dependencyId of node.dependencies) {
      dependencies.push(dependencyId);
      dependencies.push(...this.getDependencies(dependencyId, visited));
    }

    return [...new Set(dependencies)]; // Remove duplicates
  }

  /**
   * Check for changes in tracked files and return affected nodes
   * Phase 3: Enhanced with granular change detection
   */
  async checkForChanges(): Promise<DependencyChange[]> {
    // Use the enhanced change detection method
    return await this.detectEnhancedChanges();
  }

  /**
   * Analyze the dependency graph for issues and insights
   * Phase 3: Enhanced with advanced analysis capabilities
   */
  analyzeGraph(): GraphAnalysis {
    const analysis: GraphAnalysis = {
      totalNodes: this.nodes.size,
      schemaNodes: 0,
      generatedNodes: 0,
      templateNodes: 0,
      orphanedNodes: [],
      circularDependencies: [],
      criticalPaths: [],
      changeHotspots: [],
      processingBottlenecks: [],
      optimizationOpportunities: []
    };

    // Count node types and find orphaned nodes
    for (const [nodeId, node] of this.nodes) {
      switch (node.type) {
        case 'schema':
          analysis.schemaNodes++;
          break;
        case 'generated':
          analysis.generatedNodes++;
          break;
        case 'template':
          analysis.templateNodes++;
          break;
      }

      // Check for orphaned nodes (no dependencies and no dependents)
      if (node.dependencies.size === 0 && node.dependents.size === 0) {
        analysis.orphanedNodes.push(nodeId);
      }
    }

    // Detect circular dependencies
    analysis.circularDependencies = this.detectCircularDependencies();

    // Find critical paths (longest dependency chains)
    analysis.criticalPaths = this.findCriticalPaths();

    // Phase 3: Enhanced analysis
    analysis.changeHotspots = this.identifyChangeHotspots();
    analysis.processingBottlenecks = this.identifyProcessingBottlenecks();
    analysis.optimizationOpportunities = this.identifyOptimizationOpportunities();

    return analysis;
  }

  /**
   * Identify nodes that change frequently (change hotspots)
   */
  private identifyChangeHotspots(): Array<{ nodeId: string; changeFrequency: number; impactScore: number }> {
    const hotspots: Array<{ nodeId: string; changeFrequency: number; impactScore: number }> = [];
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    for (const [nodeId, changes] of this.changeHistory) {
      const recentChanges = changes.filter(c => c.timestamp > thirtyDaysAgo);
      if (recentChanges.length > 5) { // Threshold for "frequent" changes
        hotspots.push({
          nodeId,
          changeFrequency: recentChanges.length,
          impactScore: this.calculateImpactScore(nodeId)
        });
      }
    }

    return hotspots.sort((a, b) => (b.changeFrequency * b.impactScore) - (a.changeFrequency * a.impactScore));
  }

  /**
   * Identify processing bottlenecks (nodes with many dependents and slow processing)
   */
  private identifyProcessingBottlenecks(): Array<{ nodeId: string; dependentCount: number; processingTime: number }> {
    const bottlenecks: Array<{ nodeId: string; dependentCount: number; processingTime: number }> = [];

    for (const [nodeId, node] of this.nodes) {
      const dependentCount = this.getDependents(nodeId).length;
      const metrics = this.processingMetrics.get(nodeId);
      const avgProcessingTime = metrics ? metrics.totalTime / metrics.count : 0;

      if (dependentCount > 10 && avgProcessingTime > 1000) { // Thresholds for bottlenecks
        bottlenecks.push({
          nodeId,
          dependentCount,
          processingTime: avgProcessingTime
        });
      }
    }

    return bottlenecks.sort((a, b) => (b.dependentCount * b.processingTime) - (a.dependentCount * a.processingTime));
  }

  /**
   * Identify optimization opportunities
   */
  private identifyOptimizationOpportunities(): Array<{ type: string; description: string; estimatedGain: number }> {
    const opportunities: Array<{ type: string; description: string; estimatedGain: number }> = [];

    // Opportunity 1: Parallel processing for independent nodes
    const independentNodes = Array.from(this.nodes.keys()).filter(nodeId => {
      const dependencies = this.getDependencies(nodeId);
      return dependencies.length === 0;
    });

    if (independentNodes.length > 5) {
      opportunities.push({
        type: 'parallel_processing',
        description: `${independentNodes.length} independent nodes can be processed in parallel`,
        estimatedGain: Math.min(independentNodes.length * 0.1, 0.8) // Up to 80% improvement
      });
    }

    // Opportunity 2: Caching for frequently accessed nodes
    const frequentlyAccessed = Array.from(this.processingMetrics.entries())
      .filter(([_, metrics]) => metrics.count > 10)
      .map(([nodeId]) => nodeId);

    if (frequentlyAccessed.length > 0) {
      opportunities.push({
        type: 'enhanced_caching',
        description: `${frequentlyAccessed.length} frequently accessed nodes would benefit from enhanced caching`,
        estimatedGain: 0.3 // 30% improvement
      });
    }

    // Opportunity 3: Incremental processing for large dependency chains
    const largeDependencyChains = this.findCriticalPaths().filter(path => path.length > 5);
    if (largeDependencyChains.length > 0) {
      opportunities.push({
        type: 'incremental_processing',
        description: `${largeDependencyChains.length} large dependency chains would benefit from incremental processing`,
        estimatedGain: 0.5 // 50% improvement
      });
    }

    return opportunities.sort((a, b) => b.estimatedGain - a.estimatedGain);
  }

  /**
   * Get nodes that need regeneration based on changes
   */
  getNodesForRegeneration(changedNodeIds: string[]): string[] {
    const nodesToRegenerate = new Set<string>();

    for (const nodeId of changedNodeIds) {
      // Add the changed node itself if it's generated
      const node = this.nodes.get(nodeId);
      if (node && node.type === 'generated') {
        nodesToRegenerate.add(nodeId);
      }

      // Add all dependents
      const dependents = this.getDependents(nodeId);
      for (const dependentId of dependents) {
        const dependentNode = this.nodes.get(dependentId);
        if (dependentNode && dependentNode.type === 'generated') {
          nodesToRegenerate.add(dependentId);
        }
      }
    }

    return Array.from(nodesToRegenerate);
  }

  /**
   * Calculate file hash for change detection
   * Phase 3: Enhanced with WASM acceleration
   */
  private calculateFileHash(filePath: string): string {
    try {
      if (!fs.existsSync(filePath)) return '';
      const content = fs.readFileSync(filePath, 'utf8');

      // Use WASM for hash calculation if available
      if (this.wasmBridge.isWASMAvailable()) {
        try {
          // Note: WASM bridge returns a promise, but we need sync for compatibility
          // In a real implementation, we'd make this method async
          // For now, fallback to crypto for sync operation
          return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
        } catch (wasmError) {
          // Fallback to crypto
          return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
        }
      }

      return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
    } catch (error) {
      return '';
    }
  }

  /**
   * Get file modification time
   */
  private getFileModificationTime(filePath: string): number {
    try {
      if (!fs.existsSync(filePath)) return 0;
      return fs.statSync(filePath).mtime.getTime();
    } catch (error) {
      return 0;
    }
  }

  /**
   * Detect circular dependencies using DFS
   */
  private detectCircularDependencies(): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string, path: string[]): void => {
      if (recursionStack.has(nodeId)) {
        // Found a cycle
        const cycleStart = path.indexOf(nodeId);
        if (cycleStart !== -1) {
          cycles.push(path.slice(cycleStart));
        }
        return;
      }

      if (visited.has(nodeId)) return;

      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId);

      const node = this.nodes.get(nodeId);
      if (node) {
        for (const depId of node.dependencies) {
          dfs(depId, [...path]);
        }
      }

      recursionStack.delete(nodeId);
    };

    for (const nodeId of this.nodes.keys()) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, []);
      }
    }

    return cycles;
  }

  /**
   * Find critical paths (longest dependency chains)
   */
  private findCriticalPaths(): string[][] {
    const paths: string[][] = [];

    // Find all root nodes (nodes with no dependencies)
    const rootNodes = Array.from(this.nodes.keys()).filter(nodeId => {
      const node = this.nodes.get(nodeId);
      return node && node.dependencies.size === 0;
    });

    // DFS to find longest paths from each root
    const findLongestPath = (nodeId: string, currentPath: string[]): string[] => {
      const node = this.nodes.get(nodeId);
      if (!node) return currentPath;

      const newPath = [...currentPath, nodeId];
      
      if (node.dependents.size === 0) {
        return newPath; // Leaf node
      }

      let longestPath = newPath;
      for (const dependentId of node.dependents) {
        const path = findLongestPath(dependentId, newPath);
        if (path.length > longestPath.length) {
          longestPath = path;
        }
      }

      return longestPath;
    };

    for (const rootId of rootNodes) {
      const path = findLongestPath(rootId, []);
      if (path.length > 2) { // Only include non-trivial paths
        paths.push(path);
      }
    }

    // Sort by length (longest first) and return top 5
    return paths.sort((a, b) => b.length - a.length).slice(0, 5);
  }

  /**
   * Load dependency graph from persistence
   * Phase 3: Enhanced with additional tracking data
   */
  async load(): Promise<void> {
    try {
      if (fs.existsSync(this.persistencePath)) {
        const data = await fs.readJson(this.persistencePath);

        this.nodes.clear();
        for (const nodeData of data.nodes || []) {
          const node: DependencyNode = {
            ...nodeData,
            dependencies: new Set(nodeData.dependencies || []),
            dependents: new Set(nodeData.dependents || [])
          };
          this.nodes.set(node.id, node);
        }

        // Phase 3: Load additional tracking data
        if (data.version === '2.0') {
          // Load change history
          if (data.changeHistory) {
            this.changeHistory.clear();
            for (const [nodeId, history] of Object.entries(data.changeHistory)) {
              this.changeHistory.set(nodeId, history as DependencyChange[]);
            }
          }

          // Load processing metrics
          if (data.processingMetrics) {
            this.processingMetrics.clear();
            for (const [nodeId, metrics] of Object.entries(data.processingMetrics)) {
              this.processingMetrics.set(nodeId, metrics as any);
            }
          }

          // Load optimization cache
          if (data.optimizationCache) {
            this.optimizationCache.clear();
            for (const [key, value] of Object.entries(data.optimizationCache)) {
              this.optimizationCache.set(key, value);
            }
          }
        }

        this.isLoaded = true;
        this.emit('loaded', { nodeCount: this.nodes.size });
      }
    } catch (error) {
      this.emit('loadError', error);
    }
  }

  /**
   * Save dependency graph to persistence
   * Phase 3: Enhanced with additional tracking data
   */
  async save(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.persistencePath));

      const data = {
        version: '2.0', // Phase 3 version
        timestamp: Date.now(),
        nodes: Array.from(this.nodes.values()).map(node => ({
          ...node,
          dependencies: Array.from(node.dependencies),
          dependents: Array.from(node.dependents)
        })),
        // Phase 3: Additional tracking data
        changeHistory: Object.fromEntries(this.changeHistory),
        processingMetrics: Object.fromEntries(this.processingMetrics),
        optimizationCache: Object.fromEntries(this.optimizationCache)
      };

      await fs.writeJson(this.persistencePath, data, { spaces: 2 });
      this.emit('saved', { nodeCount: this.nodes.size });
    } catch (error) {
      this.emit('saveError', error);
    }
  }

  /**
   * Get graph statistics
   */
  getStats() {
    return {
      nodeCount: this.nodes.size,
      isLoaded: this.isLoaded,
      persistencePath: this.persistencePath,
      analysis: this.analyzeGraph()
    };
  }

  /**
   * Clear all nodes
   */
  clear(): void {
    this.nodes.clear();
    this.emit('cleared');
  }

  /**
   * Get all nodes
   */
  getAllNodes(): DependencyNode[] {
    return Array.from(this.nodes.values());
  }

  /**
   * Get node by ID
   */
  getNode(id: string): DependencyNode | undefined {
    return this.nodes.get(id);
  }

  // Phase 3: Enhanced Dependency Graph Methods

  /**
   * Calculate cascading changes for a given node change
   */
  calculateCascadingChanges(nodeId: string, changeType: 'added' | 'modified' | 'removed'): string[] {
    const cascadingChanges: string[] = [];
    const visited = new Set<string>();

    const traverse = (currentNodeId: string, depth: number = 0) => {
      if (visited.has(currentNodeId) || depth > 10) return; // Prevent infinite loops
      visited.add(currentNodeId);

      const dependents = this.getDependents(currentNodeId);
      for (const dependentId of dependents) {
        cascadingChanges.push(dependentId);
        traverse(dependentId, depth + 1);
      }
    };

    traverse(nodeId);
    return [...new Set(cascadingChanges)]; // Remove duplicates
  }

  /**
   * Calculate impact score for a change based on dependent count and processing complexity
   */
  calculateImpactScore(nodeId: string): number {
    const node = this.nodes.get(nodeId);
    if (!node) return 0;

    const dependentCount = this.getDependents(nodeId).length;
    const dependencyCount = node.dependencies.size;
    const metrics = this.processingMetrics.get(nodeId);
    const avgProcessingTime = metrics ? metrics.totalTime / metrics.count : 1;

    // Impact score considers: dependent count (40%), processing time (30%), dependency complexity (30%)
    return Math.round(
      (dependentCount * 0.4) +
      (Math.log(avgProcessingTime + 1) * 0.3) +
      (dependencyCount * 0.3)
    );
  }

  /**
   * Calculate processing priority based on impact score and change frequency
   */
  calculateProcessingPriority(nodeId: string): number {
    const impactScore = this.calculateImpactScore(nodeId);
    const changeHistory = this.changeHistory.get(nodeId) || [];
    const recentChanges = changeHistory.filter(c => Date.now() - c.timestamp < 24 * 60 * 60 * 1000).length;

    // Higher priority for high impact and frequently changing nodes
    return Math.round(impactScore * (1 + recentChanges * 0.1));
  }

  /**
   * Record processing metrics for performance optimization
   */
  recordProcessingMetrics(nodeId: string, processingTime: number): void {
    const existing = this.processingMetrics.get(nodeId) || { count: 0, totalTime: 0, lastProcessed: 0 };
    this.processingMetrics.set(nodeId, {
      count: existing.count + 1,
      totalTime: existing.totalTime + processingTime,
      lastProcessed: Date.now()
    });
  }

  /**
   * Get optimized processing order based on dependencies and priorities
   */
  getOptimizedProcessingOrder(nodeIds: string[]): string[] {
    const nodeData = nodeIds.map(id => ({
      id,
      priority: this.calculateProcessingPriority(id),
      dependencies: this.getDependencies(id).filter(depId => nodeIds.includes(depId))
    }));

    // Topological sort with priority weighting
    const sorted: string[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (nodeId: string) => {
      if (visiting.has(nodeId)) {
        // Circular dependency detected, skip to avoid infinite loop
        return;
      }
      if (visited.has(nodeId)) return;

      visiting.add(nodeId);
      const nodeInfo = nodeData.find(n => n.id === nodeId);
      if (nodeInfo) {
        // Visit dependencies first
        for (const depId of nodeInfo.dependencies) {
          visit(depId);
        }
      }
      visiting.delete(nodeId);
      visited.add(nodeId);
      sorted.push(nodeId);
    };

    // Sort by priority first, then process
    nodeData.sort((a, b) => b.priority - a.priority);
    for (const node of nodeData) {
      visit(node.id);
    }

    return sorted;
  }

  /**
   * Enhanced change detection with granular analysis
   */
  async detectEnhancedChanges(): Promise<DependencyChange[]> {
    const changes: DependencyChange[] = [];

    for (const [nodeId, node] of this.nodes) {
      try {
        if (!fs.existsSync(node.filePath)) {
          // File removed
          const affectedNodes = this.getDependents(nodeId);
          const cascadingChanges = this.calculateCascadingChanges(nodeId, 'removed');

          const change: DependencyChange = {
            nodeId,
            changeType: 'removed',
            oldHash: node.hash,
            affectedNodes,
            timestamp: Date.now(),
            changeLevel: 'file',
            cascadingChanges,
            impactScore: this.calculateImpactScore(nodeId),
            processingPriority: this.calculateProcessingPriority(nodeId)
          };

          changes.push(change);
          this.recordChange(change);
          this.removeNode(nodeId);
          this.emit('dependencyChange', change);
          continue;
        }

        const currentHash = this.calculateFileHash(node.filePath);
        const currentModified = this.getFileModificationTime(node.filePath);

        if (currentHash !== node.hash) {
          const affectedNodes = this.getDependents(nodeId);
          const cascadingChanges = this.calculateCascadingChanges(nodeId, 'modified');

          const change: DependencyChange = {
            nodeId,
            changeType: 'modified',
            oldHash: node.hash,
            newHash: currentHash,
            affectedNodes,
            timestamp: Date.now(),
            changeLevel: this.determineChangeLevel(node.filePath, node.hash, currentHash),
            cascadingChanges,
            impactScore: this.calculateImpactScore(nodeId),
            processingPriority: this.calculateProcessingPriority(nodeId)
          };

          changes.push(change);
          this.recordChange(change);

          // Update node with new hash and modification time
          node.hash = currentHash;
          node.lastModified = currentModified;

          this.emit('dependencyChange', change);
        }
      } catch (error) {
        // Handle errors gracefully
        this.emit('error', { nodeId, error });
      }
    }

    return changes;
  }

  /**
   * Determine the granular level of change (file, type, field, directive)
   */
  private determineChangeLevel(filePath: string, oldHash: string, newHash: string): 'file' | 'type' | 'field' | 'directive' {
    // This is a simplified implementation - in practice, you'd analyze the actual file changes
    // For now, we'll use a heuristic based on file extension and change magnitude

    if (filePath.endsWith('.gql') || filePath.endsWith('.graphql')) {
      // For GraphQL files, assume type-level changes
      return 'type';
    } else if (filePath.endsWith('.ts') || filePath.endsWith('.js')) {
      // For TypeScript/JavaScript files, assume field-level changes
      return 'field';
    }

    return 'file';
  }

  /**
   * Record change in history for analysis
   */
  private recordChange(change: DependencyChange): void {
    const history = this.changeHistory.get(change.nodeId) || [];
    history.push(change);

    // Keep only last 100 changes per node
    if (history.length > 100) {
      history.shift();
    }

    this.changeHistory.set(change.nodeId, history);
  }
}

/**
 * Global dependency graph instance
 */
let globalDependencyGraph: DependencyGraph | null = null;

/**
 * Get or create global dependency graph
 */
export function getGlobalDependencyGraph(persistencePath?: string): DependencyGraph {
  if (!globalDependencyGraph) {
    globalDependencyGraph = new DependencyGraph(persistencePath);
  }
  return globalDependencyGraph;
}
