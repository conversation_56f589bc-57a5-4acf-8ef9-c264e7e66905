import * as fs from 'fs-extra';
import * as path from 'path';
import type { GraphQLSchema } from 'graphql';
import * as glob from 'glob';
import _ from 'lodash';
import { SchemaMapper } from '@utils/schema-mapper';
import { parseTypeScriptFile, generatePreservedImplementation } from '@utils/ts-parser';
import { isJavaScriptKeyword } from '@generators/utils/keyword-utils';
import { WatchedFileWriter } from './watched-file-writer';

/**
 * Common options for all generators
 */
export interface GeneratorOptions {
  /** Path to the schema directory or file */
  schema?: string;
  /** Path to the output directory */
  output?: string;
  /** Whether to force overwrite existing implementations */
  force?: boolean;
  /** Path to custom context module */
  context?: string;
  /** Name of the context type */
  contextName?: string;
  /** Enable debug mode for verbose logging */
  debug?: boolean;
  /** Schema identifier for multi-schema support */
  schemaId?: string;
  /** Import alias for codebase directory */
  aliasCodebase?: string;
}

/**
 * Base class for all code generators with common functionality
 */
export abstract class BaseGenerator {
  protected schema: GraphQLSchema;
  protected schemaMapper: SchemaMapper;
  protected outputRoot: string;
  protected schemaRoot: string;
  protected options: GeneratorOptions;

  /**
   * Create a new generator
   * @param schema The GraphQL schema
   * @param options Generator options
   */
  constructor(schema: GraphQLSchema, options: GeneratorOptions) {
    this.schema = schema;
    this.options = options;

    this.outputRoot = options.output ?
      (path.isAbsolute(options.output) ? path.normalize(options.output) : path.resolve(process.cwd(), options.output)) :
      path.join(process.cwd(), 'src', 'generated');

    this.schemaRoot = options.schema ?
      (path.isAbsolute(options.schema) ? path.normalize(options.schema) : path.resolve(process.cwd(), options.schema)) :
      path.join(process.cwd(), 'schema');

    this.schemaMapper = new SchemaMapper(this.schemaRoot, this.outputRoot);

    // Ensure the output directory exists
    fs.ensureDirSync(this.outputRoot);
  }

  /**
   * Initialize the generator
   */
  protected async init(): Promise<void> {
    // Analyze the schema to build type-to-file mapping
    this.schemaMapper.analyzeSchema(this.schema);
  }

  /**
   * Generate implementation files for all types
   */
  public abstract generate(): Promise<void>;

  /**
   * Generate an index file for a directory
   * @param directoryPath Path to the directory
   * @param files List of files in the directory (without extension)
   */
  protected generateIndexFile(directoryPath: string, files: string[]): void {
    if (!fs.existsSync(directoryPath)) {
      return;
    }

    const indexPath = path.join(directoryPath, 'index.ts');
    let indexContent = '// Generated index file\n\n';

    // First, add imports for all resolvers we'll reference in the exports object
    const validFiles = files.filter(file => file !== 'index' && !file.startsWith('.'));
    for (const file of validFiles) {
      const camelCaseName = _.camelCase(file);
      indexContent += `import { default as ${camelCaseName} } from './${file}';\n`;
    }

    // Add a blank line after imports if there are any
    if (validFiles.length > 0) {
      indexContent += '\n';
    }

    // Add exports for each file
    for (const file of validFiles) {
      const camelCaseName = _.camelCase(file);
      indexContent += `export { ${camelCaseName} };\n`;
    }

    // Add exports object
    if (validFiles.length > 0) {
      indexContent += '\n// Consolidated exports object\n';
      indexContent += 'export const resolvers = {\n';

      for (const file of validFiles) {
        const camelCaseName = _.camelCase(file);
        indexContent += `  ${camelCaseName},\n`;
      }

      indexContent += '};\n';
    }

    // Write the index file
    WatchedFileWriter.writeFileSync(indexPath, indexContent);
    if (process.env.DEBUG_PARSER) {
      console.log(`Generated index file at ${indexPath}`);
    }
  }

  /**
   * Generate a recursive index file that includes exports from subdirectories
   * @param directoryPath Path to the directory
   */
  protected generateRecursiveIndexFile(directoryPath: string): void {
    if (!fs.existsSync(directoryPath)) {
      return;
    }

    // Get all subdirectories
    const subdirectories = fs.readdirSync(directoryPath)
      .filter(item => {
        const itemPath = path.join(directoryPath, item);
        return fs.statSync(itemPath).isDirectory() && !item.startsWith('.');
      });

    // Get all TypeScript files in this directory (excluding index.ts)
    const tsFiles = fs.readdirSync(directoryPath)
      .filter(item => {
        const itemPath = path.join(directoryPath, item);
        return fs.statSync(itemPath).isFile() &&
          item.endsWith('.ts') &&
          item !== 'index.ts';
      })
      .map(filename => filename.replace(/\.ts$/, ''));

    // Start building index content
    let indexContent = '// Generated recursive index file\n\n';

    // Import from subdirectories
    for (const subdir of subdirectories) {
      const importPath = `./${subdir}`;
      indexContent += `import * as ${_.camelCase(subdir)} from '${importPath}';\n`;
    }

    // Import resolvers from this directory
    for (const file of tsFiles) {
      const camelCaseName = _.camelCase(file);
      indexContent += `import { default as ${camelCaseName} } from './${file}';\n`;
    }

    // Add a blank line after imports if there are any
    if (subdirectories.length > 0 || tsFiles.length > 0) {
      indexContent += '\n';
    }

    // Add exports for local resolvers
    if (tsFiles.length > 0) {
      indexContent += '// Export local resolvers\n';
      for (const file of tsFiles) {
        const camelCaseName = _.camelCase(file);
        indexContent += `export { ${camelCaseName} };\n`;
      }
      indexContent += '\n';
    }

    // Create consolidated exports object
    indexContent += '// Consolidated exports\n';
    indexContent += 'export const resolvers = {\n';

    // Add local resolvers to the exports object
    for (const file of tsFiles) {
      const camelCaseName = _.camelCase(file);
      indexContent += `  ${camelCaseName},\n`;
    }

    // Add subdirectory resolvers to the exports object
    if (subdirectories.length > 0) {
      indexContent += '\n  // Resolvers from subdirectories\n';
      for (const subdir of subdirectories) {
        const camelCaseName = _.camelCase(subdir);
        indexContent += `  ...${camelCaseName}.resolvers,\n`;
      }
    }

    indexContent += '};\n';

    // Write the index file
    const indexPath = path.join(directoryPath, 'index.ts');
    WatchedFileWriter.writeFileSync(indexPath, indexContent);
    if (process.env.DEBUG_PARSER) {
      console.log(`Generated recursive index file at ${indexPath}`);
    }
  }

  /**
   * Find all TypeScript files in a directory and its subdirectories
   * @param directoryPath Path to the directory
   * @returns Map of file paths to file names (without extension)
   */
  protected findAllFiles(directoryPath: string): Map<string, string> {
    if (!fs.existsSync(directoryPath)) {
      return new Map();
    }

    const files = glob.sync('**/*.ts', {
      cwd: directoryPath,
      ignore: ['**/*.d.ts', '**/node_modules/**'],
      absolute: true,
      windowsPathsNoEscape: true
    });

    const fileMap = new Map<string, string>();
    for (const file of files) {
      const fileName = path.basename(file, '.ts');
      fileMap.set(file, fileName);
    }

    return fileMap;
  }

  /**
   * Clean up empty directories
   * @param directoryPath Path to the directory
   */
  protected cleanupEmptyDirectories(directoryPath: string): void {
    if (!fs.existsSync(directoryPath)) {
      return;
    }

    const items = fs.readdirSync(directoryPath);

    // Recursively process subdirectories
    for (const item of items) {
      const itemPath = path.join(directoryPath, item);

      if (fs.statSync(itemPath).isDirectory()) {
        this.cleanupEmptyDirectories(itemPath);
      }
    }

    // Check if directory is empty after processing subdirectories
    const remainingItems = fs.readdirSync(directoryPath);
    if (remainingItems.length === 0) {
      // Remove empty directory
      fs.rmdirSync(directoryPath);
      console.log(`Removed empty directory: ${directoryPath}`);
    }
  }

  /**
   * Check if a file has custom implementation code
   * @param filePath Path to the file
   * @returns Whether the file has custom implementation
   */
  protected async hasCustomImplementation(filePath: string): Promise<boolean> {
    if (!fs.existsSync(filePath)) {
      return false;
    }

    try {
      const parsedFile = await parseTypeScriptFile(filePath);

      // Has custom implementation if any TODOs are removed or implementation is added
      return parsedFile ? parsedFile.hasCustomImplementation : false;
    } catch (error) {
      console.error(`Error checking for custom implementation in ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Preserve custom implementation when updating a file
   * @param filePath Path to the existing file
   * @param newContent New content to replace the file with
   * @returns The merged content with preserved custom implementation
   */
  protected async preserveCustomImplementation(filePath: string, newContent: string): Promise<string> {
    if (!fs.existsSync(filePath)) {
      return newContent;
    }

    try {
      const existingCode = await parseTypeScriptFile(filePath);
      if (!existingCode) {
        return newContent;
      }

      return generatePreservedImplementation(newContent, existingCode);
    } catch (error) {
      console.error(`Error preserving implementation in ${filePath}:`, error);
      return newContent;
    }
  }

  /**
   * Create the full path for an implementation file
   * @param typeName The GraphQL type name
   * @param fieldName The field name
   * @returns The file path or null if the type is not found
   */
  protected getImplementationFilePath(typeName: string, fieldName: string): string | null {
    const outputPath = this.schemaMapper.getOutputPathForType(typeName);

    if (!outputPath) {
      return null;
    }

    // Convert field name to file name (camelCase -> kebab-case)
    const fileName = this.convertToFileName(fieldName);
    return path.join(outputPath, `${fileName}.ts`);
  }

  /**
   * Convert a field name to a file name
   * @param fieldName The GraphQL field name
   * @returns The file name
   */
  protected convertToFileName(fieldName: string): string {
    // Convert camelCase to kebab-case
    const kebabCase = _.kebabCase(fieldName);

    // Check if the original name is a JavaScript keyword
    // If it is, append "-handler" to the kebab-case name
    return isJavaScriptKeyword(fieldName) ? `${kebabCase}-handler` : kebabCase;
  }
}