import { getGlobalOptimizedDirectiveParser, DirectiveParsingMetrics } from './optimized-directive-parser';
import { getGlobalCommentDirectiveCache, CommentDirectiveCacheStatistics } from './comment-directive-cache';
import { getGlobalCacheInvalidator, CacheInvalidationStats } from './comment-directive-cache-invalidator';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Performance monitoring configuration
 */
export interface PerformanceMonitorConfig {
  enableRealTimeMonitoring?: boolean;
  enableLogging?: boolean;
  reportingIntervalMs?: number;
  logFilePath?: string;
  enableMetricsCollection?: boolean;
}

/**
 * Comprehensive performance metrics
 */
export interface CommentDirectivePerformanceMetrics {
  parsing: DirectiveParsingMetrics;
  cache: CommentDirectiveCacheStatistics;
  invalidation: CacheInvalidationStats;
  system: {
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
    timestamp: number;
  };
  performance: {
    totalTimeSaved: number;
    performanceGain: number;
    cacheEfficiency: number;
    averageResponseTime: number;
  };
}

/**
 * Performance report for analysis
 */
export interface PerformanceReport {
  summary: {
    totalOperations: number;
    cacheHitRate: number;
    averageParseTime: number;
    totalTimeSaved: number;
    performanceImprovement: string;
  };
  details: CommentDirectivePerformanceMetrics;
  recommendations: string[];
  timestamp: number;
}

/**
 * Performance monitoring dashboard for comment directive parsing
 */
export class CommentDirectivePerformanceMonitor {
  private parser = getGlobalOptimizedDirectiveParser();
  private cache = getGlobalCommentDirectiveCache();
  private invalidator = getGlobalCacheInvalidator();
  private config: Required<PerformanceMonitorConfig>;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private metricsHistory: CommentDirectivePerformanceMetrics[] = [];
  private startTime = Date.now();

  constructor(config: PerformanceMonitorConfig = {}) {
    this.config = {
      enableRealTimeMonitoring: false,
      enableLogging: false,
      reportingIntervalMs: 30000, // 30 seconds
      logFilePath: './performance-logs/comment-directive-performance.log',
      enableMetricsCollection: true,
      ...config
    };
  }

  /**
   * Start performance monitoring
   */
  async startMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    if (this.config.enableRealTimeMonitoring) {
      this.monitoringInterval = setInterval(() => {
        this.collectAndLogMetrics();
      }, this.config.reportingIntervalMs);

      if (this.config.enableLogging) {
        console.log(`📊 Comment directive performance monitoring started (interval: ${this.config.reportingIntervalMs}ms)`);
      }
    }

    // Ensure log directory exists
    if (this.config.logFilePath) {
      const logDir = path.dirname(this.config.logFilePath);
      await fs.ensureDir(logDir);
    }
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;

      if (this.config.enableLogging) {
        console.log('🛑 Comment directive performance monitoring stopped');
      }
    }
  }

  /**
   * Collect current performance metrics
   */
  collectMetrics(): CommentDirectivePerformanceMetrics {
    const parsingMetrics = this.parser.getMetrics();
    const cacheStats = this.cache.getStatistics();
    const invalidationStats = this.invalidator.getStatistics();
    const memoryUsage = process.memoryUsage();
    const uptime = Date.now() - this.startTime;

    // Calculate performance metrics
    const totalOperations = parsingMetrics.totalCalls;
    const cacheHitRate = totalOperations > 0 
      ? ((parsingMetrics.cacheHits / totalOperations) * 100) 
      : 0;
    
    const totalTimeSaved = cacheStats.totalTimeSaved;
    const performanceGain = parsingMetrics.averageParseTime > 0 
      ? (totalTimeSaved / (parsingMetrics.totalParseTime + totalTimeSaved)) * 100 
      : 0;
    
    const cacheEfficiency = (cacheStats.fileContentCache.hitRate + cacheStats.directiveCache.hitRate) / 2;
    const averageResponseTime = parsingMetrics.averageParseTime;

    const metrics: CommentDirectivePerformanceMetrics = {
      parsing: parsingMetrics,
      cache: cacheStats,
      invalidation: invalidationStats,
      system: {
        memoryUsage,
        uptime,
        timestamp: Date.now()
      },
      performance: {
        totalTimeSaved,
        performanceGain,
        cacheEfficiency,
        averageResponseTime
      }
    };

    if (this.config.enableMetricsCollection) {
      this.metricsHistory.push(metrics);
      
      // Keep only last 100 metrics to prevent memory bloat
      if (this.metricsHistory.length > 100) {
        this.metricsHistory.shift();
      }
    }

    return metrics;
  }

  /**
   * Generate comprehensive performance report
   */
  generateReport(): PerformanceReport {
    const metrics = this.collectMetrics();
    const parsing = metrics.parsing;
    const cache = metrics.cache;

    // Calculate summary statistics
    const totalOperations = parsing.totalCalls;
    const cacheHitRate = totalOperations > 0 
      ? Math.round(((parsing.cacheHits / totalOperations) * 100) * 100) / 100 
      : 0;
    
    const averageParseTime = Math.round(parsing.averageParseTime * 100) / 100;
    const totalTimeSaved = Math.round(cache.totalTimeSaved);
    
    // Calculate performance improvement
    const baselineTime = parsing.totalParseTime + cache.totalTimeSaved;
    const actualTime = parsing.totalParseTime;
    const improvementPercent = baselineTime > 0 
      ? Math.round(((baselineTime - actualTime) / baselineTime) * 100 * 100) / 100 
      : 0;

    // Generate recommendations
    const recommendations = this.generateRecommendations(metrics);

    return {
      summary: {
        totalOperations,
        cacheHitRate,
        averageParseTime,
        totalTimeSaved,
        performanceImprovement: `${improvementPercent}% faster`
      },
      details: metrics,
      recommendations,
      timestamp: Date.now()
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(metrics: CommentDirectivePerformanceMetrics): string[] {
    const recommendations: string[] = [];
    const cache = metrics.cache;
    const parsing = metrics.parsing;

    // Cache hit rate recommendations
    if (cache.fileContentCache.hitRate < 70) {
      recommendations.push('Consider increasing file content cache size or TTL for better hit rates');
    }

    if (cache.directiveCache.hitRate < 80) {
      recommendations.push('Directive cache hit rate is low - consider preloading frequently used schemas');
    }

    // Memory usage recommendations
    const memoryMB = metrics.system.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryMB > 100) {
      recommendations.push('High memory usage detected - consider reducing cache sizes');
    }

    // Performance recommendations
    if (parsing.averageParseTime > 50) {
      recommendations.push('Average parse time is high - consider enabling batch processing');
    }

    if (parsing.batchParseCount < parsing.totalCalls * 0.5) {
      recommendations.push('Low batch parse usage - consider using batch extraction for multiple fields');
    }

    // Cache efficiency recommendations
    if (metrics.performance.cacheEfficiency < 60) {
      recommendations.push('Overall cache efficiency is low - review cache configuration');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is optimal - no recommendations at this time');
    }

    return recommendations;
  }

  /**
   * Collect metrics and log them
   */
  private async collectAndLogMetrics(): Promise<void> {
    try {
      const metrics = this.collectMetrics();
      
      if (this.config.enableLogging) {
        this.logMetrics(metrics);
      }

      if (this.config.logFilePath) {
        await this.writeMetricsToFile(metrics);
      }
    } catch (error) {
      console.error('❌ Error collecting performance metrics:', error);
    }
  }

  /**
   * Log metrics to console
   */
  private logMetrics(metrics: CommentDirectivePerformanceMetrics): void {
    const { parsing, cache, performance } = metrics;
    
    console.log('\n📊 Comment Directive Performance Metrics:');
    console.log(`   Total Operations: ${parsing.totalCalls}`);
    console.log(`   Cache Hit Rate: ${Math.round(((parsing.cacheHits / Math.max(parsing.totalCalls, 1)) * 100) * 100) / 100}%`);
    console.log(`   Average Parse Time: ${Math.round(parsing.averageParseTime * 100) / 100}ms`);
    console.log(`   Total Time Saved: ${Math.round(cache.totalTimeSaved)}ms`);
    console.log(`   Performance Gain: ${Math.round(performance.performanceGain * 100) / 100}%`);
    console.log(`   Memory Usage: ${Math.round(metrics.system.memoryUsage.heapUsed / 1024 / 1024)}MB`);
  }

  /**
   * Write metrics to log file
   */
  private async writeMetricsToFile(metrics: CommentDirectivePerformanceMetrics): Promise<void> {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        metrics
      };
      
      const logLine = JSON.stringify(logEntry) + '\n';
      await fs.appendFile(this.config.logFilePath, logLine);
    } catch (error) {
      console.error('❌ Error writing metrics to file:', error);
    }
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(): CommentDirectivePerformanceMetrics[] {
    return [...this.metricsHistory];
  }

  /**
   * Clear metrics history
   */
  clearHistory(): void {
    this.metricsHistory = [];
    
    if (this.config.enableLogging) {
      console.log('🧹 Performance metrics history cleared');
    }
  }

  /**
   * Export performance report to file
   */
  async exportReport(filePath?: string): Promise<string> {
    const report = this.generateReport();
    const exportPath = filePath || `./performance-reports/comment-directive-report-${Date.now()}.json`;
    
    await fs.ensureDir(path.dirname(exportPath));
    await fs.writeFile(exportPath, JSON.stringify(report, null, 2));
    
    if (this.config.enableLogging) {
      console.log(`📄 Performance report exported to: ${exportPath}`);
    }
    
    return exportPath;
  }

  /**
   * Reset all performance counters
   */
  resetCounters(): void {
    this.parser.resetMetrics();
    this.cache.clear();
    this.clearHistory();
    this.startTime = Date.now();
    
    if (this.config.enableLogging) {
      console.log('🔄 Performance counters reset');
    }
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<PerformanceMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart monitoring if interval changed
    if (newConfig.reportingIntervalMs && this.monitoringInterval) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }
}

// Global performance monitor instance
let globalPerformanceMonitor: CommentDirectivePerformanceMonitor | null = null;

/**
 * Get the global performance monitor instance
 */
export function getGlobalPerformanceMonitor(config?: PerformanceMonitorConfig): CommentDirectivePerformanceMonitor {
  if (!globalPerformanceMonitor) {
    globalPerformanceMonitor = new CommentDirectivePerformanceMonitor(config);
  }
  return globalPerformanceMonitor;
}

/**
 * Reset the global monitor (useful for testing)
 */
export function resetGlobalPerformanceMonitor(): void {
  if (globalPerformanceMonitor) {
    globalPerformanceMonitor.stopMonitoring();
  }
  globalPerformanceMonitor = null;
}
