import * as fs from 'fs-extra';
import * as path from 'path';
import { WatchedFileWriter } from './watched-file-writer';
import { UI } from './ui';
import { BackupNamingUtils, type BackupNamingOptions } from './backup-naming';

/**
 * Options for safe file modification
 */
export interface SafeFileModificationOptions {
  /** Create backup before modification */
  createBackup?: boolean;
  /** Backup directory (defaults to .backup-{timestamp}) */
  backupDir?: string;
  /** Enable verbose logging */
  verbose?: boolean;
  /** Validate file after modification */
  validateAfterModification?: boolean;
  /** Backup naming options */
  backupOptions?: BackupNamingOptions;
  /** Context information for better backup naming */
  context?: {
    codebasePath?: string;
    schemaPath?: string;
    operation?: string;
  };
}

/**
 * Result of a file modification operation
 */
export interface FileModificationResult {
  /** Whether the modification was successful */
  success: boolean;
  /** Original file path */
  filePath: string;
  /** Backup file path (if backup was created) */
  backupPath?: string;
  /** Error message if modification failed */
  error?: string;
  /** Number of changes made */
  changesCount: number;
}

/**
 * Information about a backup
 */
export interface BackupInfo {
  /** Original file path */
  originalPath: string;
  /** Backup file path */
  backupPath: string;
  /** Timestamp when backup was created */
  timestamp: Date;
  /** File size in bytes */
  size: number;
}

/**
 * Safe file modifier with backup and validation capabilities
 */
export class SafeFileModifier {
  private options: SafeFileModificationOptions;
  private backups: Map<string, BackupInfo> = new Map();

  constructor(options: SafeFileModificationOptions = {}) {
    this.options = {
      createBackup: true,
      verbose: false,
      validateAfterModification: true,
      ...options
    };

    // Set default backup directory if not provided
    if (!this.options.backupDir) {
      if (this.options.context?.codebasePath && this.options.context?.schemaPath) {
        // Use new backup naming system if context is available
        this.initializeBackupDirectoryWithContext();
      } else {
        // Fallback to legacy naming for backward compatibility
        const timestamp = BackupNamingUtils.generateTimestamp();
        const baseDir = this.options.backupOptions?.baseBackupDir || '.backup';
        this.options.backupDir = path.join(process.cwd(), baseDir, `safe-file-modifier-${timestamp}`);
      }
    }
  }

  /**
   * Initialize backup directory using context information
   */
  private async initializeBackupDirectoryWithContext(): Promise<void> {
    if (!this.options.context?.codebasePath || !this.options.context?.schemaPath) {
      return;
    }

    try {
      const backupInfo = await BackupNamingUtils.createBackupDirectory(
        this.options.context.codebasePath,
        this.options.context.schemaPath,
        {
          ...this.options.backupOptions,
          customPrefix: this.options.context.operation || 'file-modification'
        }
      );

      this.options.backupDir = backupInfo.backupDir;

      if (this.options.verbose) {
        UI.info(`📁 Using backup directory: ${backupInfo.backupDir}`);
      }
    } catch (error) {
      if (this.options.verbose) {
        UI.warning(`Failed to create context-based backup directory: ${error}`);
      }
      // Fallback to simple naming
      const timestamp = BackupNamingUtils.generateTimestamp();
      const baseDir = this.options.backupOptions?.baseBackupDir || '.backup';
      this.options.backupDir = path.join(process.cwd(), baseDir, `safe-file-modifier-${timestamp}`);
    }
  }

  /**
   * Safely modify a file with backup and validation
   * @param filePath Path to the file to modify
   * @param modifier Function that takes file content and returns modified content
   * @returns Result of the modification operation
   */
  public async modifyFile(
    filePath: string,
    modifier: (content: string) => string | Promise<string>
  ): Promise<FileModificationResult> {
    try {
      // Validate input
      if (!await fs.pathExists(filePath)) {
        return {
          success: false,
          filePath,
          error: `File does not exist: ${filePath}`,
          changesCount: 0
        };
      }

      // Read original content
      const originalContent = await fs.readFile(filePath, 'utf8');
      
      // Create backup if enabled
      let backupPath: string | undefined;
      if (this.options.createBackup) {
        backupPath = await this.createBackup(filePath, originalContent);
      }

      // Apply modifications
      const modifiedContent = await modifier(originalContent);
      
      // Check if content actually changed
      if (originalContent === modifiedContent) {
        if (this.options.verbose) {
          UI.info(`📄 No changes needed for ${path.relative(process.cwd(), filePath)}`);
        }
        return {
          success: true,
          filePath,
          backupPath,
          changesCount: 0
        };
      }

      // Write modified content
      await WatchedFileWriter.writeFile(filePath, modifiedContent);

      // Validate after modification if enabled
      if (this.options.validateAfterModification) {
        const isValid = await this.validateFile(filePath, modifiedContent);
        if (!isValid) {
          // Restore from backup if validation fails
          if (backupPath) {
            await this.restoreFromBackup(filePath, backupPath);
          }
          return {
            success: false,
            filePath,
            backupPath,
            error: 'File validation failed after modification',
            changesCount: 0
          };
        }
      }

      // Calculate changes count (simple line diff)
      const changesCount = this.calculateChangesCount(originalContent, modifiedContent);

      if (this.options.verbose) {
        UI.success(`✅ Modified ${path.relative(process.cwd(), filePath)} (${changesCount} changes)`);
      }

      return {
        success: true,
        filePath,
        backupPath,
        changesCount
      };

    } catch (error) {
      return {
        success: false,
        filePath,
        error: `Failed to modify file: ${error}`,
        changesCount: 0
      };
    }
  }

  /**
   * Add content to a file at a specific location
   * @param filePath Path to the file
   * @param content Content to add
   * @param location Where to add the content ('start', 'end', or line number)
   * @returns Result of the modification
   */
  public async addContent(
    filePath: string,
    content: string,
    location: 'start' | 'end' | number = 'end'
  ): Promise<FileModificationResult> {
    return this.modifyFile(filePath, (originalContent) => {
      const lines = originalContent.split('\n');
      
      if (location === 'start') {
        return content + '\n' + originalContent;
      } else if (location === 'end') {
        return originalContent + '\n' + content;
      } else if (typeof location === 'number') {
        // Insert at specific line number (1-based)
        const insertIndex = Math.max(0, Math.min(location - 1, lines.length));
        lines.splice(insertIndex, 0, content);
        return lines.join('\n');
      }
      
      return originalContent;
    });
  }

  /**
   * Remove content from a file using a pattern or line numbers
   * @param filePath Path to the file
   * @param pattern Regex pattern or string to remove
   * @param options Removal options
   * @returns Result of the modification
   */
  public async removeContent(
    filePath: string,
    pattern: string | RegExp,
    options: { 
      removeEmptyLines?: boolean;
      global?: boolean;
    } = {}
  ): Promise<FileModificationResult> {
    return this.modifyFile(filePath, (originalContent) => {
      let modifiedContent = originalContent;
      
      if (typeof pattern === 'string') {
        if (options.global) {
          modifiedContent = modifiedContent.split(pattern).join('');
        } else {
          const index = modifiedContent.indexOf(pattern);
          if (index !== -1) {
            modifiedContent = modifiedContent.substring(0, index) + 
                            modifiedContent.substring(index + pattern.length);
          }
        }
      } else {
        // Regex pattern
        const flags = options.global ? 'gm' : 'm';
        const regex = new RegExp(pattern.source, flags);
        modifiedContent = modifiedContent.replace(regex, '');
      }
      
      // Remove empty lines if requested
      if (options.removeEmptyLines) {
        modifiedContent = modifiedContent.replace(/^\s*\n/gm, '');
      }
      
      return modifiedContent;
    });
  }

  /**
   * Replace content in a file
   * @param filePath Path to the file
   * @param searchPattern Pattern to search for
   * @param replacement Replacement content
   * @param options Replacement options
   * @returns Result of the modification
   */
  public async replaceContent(
    filePath: string,
    searchPattern: string | RegExp,
    replacement: string,
    options: { global?: boolean } = {}
  ): Promise<FileModificationResult> {
    return this.modifyFile(filePath, (originalContent) => {
      if (typeof searchPattern === 'string') {
        if (options.global) {
          return originalContent.split(searchPattern).join(replacement);
        } else {
          return originalContent.replace(searchPattern, replacement);
        }
      } else {
        // Regex pattern
        const flags = options.global ? 'gm' : 'm';
        const regex = new RegExp(searchPattern.source, flags);
        return originalContent.replace(regex, replacement);
      }
    });
  }

  /**
   * Create a backup of a file
   */
  private async createBackup(filePath: string, content: string): Promise<string> {
    await fs.ensureDir(this.options.backupDir!);

    let backupPath: string;

    // Use new backup naming system if backup options are provided
    if (this.options.backupOptions) {
      backupPath = BackupNamingUtils.generateStructuredBackupPath(
        filePath,
        this.options.backupDir!,
        {
          ...this.options.backupOptions,
          organizeByType: true,
          preserveStructure: true
        }
      );
    } else {
      // Fallback to legacy naming for backward compatibility
      const fileName = path.basename(filePath);
      const relativePath = path.relative(process.cwd(), filePath);
      const safeRelativePath = relativePath.replace(/[/\\]/g, '_');
      const timestamp = BackupNamingUtils.generateTimestamp();
      const backupFileName = `${safeRelativePath}.${timestamp}.backup`;
      backupPath = path.join(this.options.backupDir!, backupFileName);
    }

    // Ensure the backup directory exists
    await fs.ensureDir(path.dirname(backupPath));

    // Handle naming collisions
    if (await fs.pathExists(backupPath)) {
      backupPath = await BackupNamingUtils.resolveNamingCollision(backupPath);
    }

    await fs.writeFile(backupPath, content, 'utf8');

    // Store backup info
    const backupInfo: BackupInfo = {
      originalPath: filePath,
      backupPath,
      timestamp: new Date(),
      size: Buffer.byteLength(content, 'utf8')
    };

    this.backups.set(filePath, backupInfo);

    if (this.options.verbose) {
      UI.info(`💾 Created backup: ${path.relative(process.cwd(), backupPath)}`);
    }

    // Create backup metadata if enabled
    if (this.options.backupOptions?.createMetadata !== false) {
      await this.createBackupMetadata(backupPath, filePath, content);
    }

    return backupPath;
  }

  /**
   * Create metadata for individual file backup
   */
  private async createBackupMetadata(backupPath: string, originalPath: string, content: string): Promise<void> {
    try {
      const metadataPath = `${backupPath}.metadata.json`;
      const metadata = {
        originalPath,
        backupPath,
        createdAt: new Date().toISOString(),
        operation: this.options.context?.operation || 'file-modification',
        originalSize: Buffer.byteLength(content, 'utf8'),
        checksum: this.calculateChecksum(content),
        context: this.options.context || {}
      };

      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      if (this.options.verbose) {
        UI.warning(`Failed to create backup metadata: ${error}`);
      }
    }
  }

  /**
   * Calculate a simple checksum for content verification
   */
  private calculateChecksum(content: string): string {
    // Simple hash for verification - in production, consider using crypto
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  /**
   * Restore a file from backup
   */
  public async restoreFromBackup(filePath: string, backupPath?: string): Promise<boolean> {
    try {
      const actualBackupPath = backupPath || this.backups.get(filePath)?.backupPath;

      if (!actualBackupPath || !await fs.pathExists(actualBackupPath)) {
        throw new Error(`Backup not found for ${filePath}`);
      }

      // Verify backup integrity if metadata exists
      const metadataPath = `${actualBackupPath}.metadata.json`;
      if (await fs.pathExists(metadataPath)) {
        const isValid = await this.verifyBackupIntegrity(actualBackupPath, metadataPath);
        if (!isValid) {
          UI.warning(`⚠️ Backup integrity check failed for ${actualBackupPath}`);
        }
      }

      const backupContent = await fs.readFile(actualBackupPath, 'utf8');
      await WatchedFileWriter.writeFile(filePath, backupContent);

      if (this.options.verbose) {
        UI.success(`🔄 Restored ${path.relative(process.cwd(), filePath)} from backup`);
      }

      return true;
    } catch (error) {
      if (this.options.verbose) {
        UI.error(`❌ Failed to restore ${filePath} from backup: ${error}`);
      }
      return false;
    }
  }

  /**
   * Verify backup integrity using metadata
   */
  private async verifyBackupIntegrity(backupPath: string, metadataPath: string): Promise<boolean> {
    try {
      const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
      const backupContent = await fs.readFile(backupPath, 'utf8');

      // Verify checksum
      const currentChecksum = this.calculateChecksum(backupContent);
      if (metadata.checksum && metadata.checksum !== currentChecksum) {
        return false;
      }

      // Verify size
      const currentSize = Buffer.byteLength(backupContent, 'utf8');
      if (metadata.originalSize && metadata.originalSize !== currentSize) {
        return false;
      }

      return true;
    } catch (error) {
      if (this.options.verbose) {
        UI.warning(`Failed to verify backup integrity: ${error}`);
      }
      return false;
    }
  }

  /**
   * Find and restore from the most recent backup of a file
   */
  public async restoreFromMostRecentBackup(filePath: string): Promise<boolean> {
    try {
      // First check if we have a tracked backup
      const trackedBackup = this.backups.get(filePath);
      if (trackedBackup && await fs.pathExists(trackedBackup.backupPath)) {
        return await this.restoreFromBackup(filePath, trackedBackup.backupPath);
      }

      // Search for backups in the backup directory
      if (!this.options.backupDir || !await fs.pathExists(this.options.backupDir)) {
        throw new Error('No backup directory found');
      }

      const backupCandidates = await this.findBackupCandidates(filePath);
      if (backupCandidates.length === 0) {
        throw new Error('No backup candidates found');
      }

      // Sort by creation time (most recent first)
      backupCandidates.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Try to restore from the most recent backup
      return await this.restoreFromBackup(filePath, backupCandidates[0].backupPath);

    } catch (error) {
      if (this.options.verbose) {
        UI.error(`❌ Failed to restore from most recent backup: ${error}`);
      }
      return false;
    }
  }

  /**
   * Find backup candidates for a given file
   */
  private async findBackupCandidates(filePath: string): Promise<BackupInfo[]> {
    const candidates: BackupInfo[] = [];

    if (!this.options.backupDir || !await fs.pathExists(this.options.backupDir)) {
      return candidates;
    }

    const relativePath = path.relative(process.cwd(), filePath);
    const fileName = path.basename(filePath);

    // Search recursively in backup directory
    const searchDir = async (dir: string): Promise<void> => {
      const entries = await fs.readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          await searchDir(fullPath);
        } else if (entry.isFile() && entry.name.endsWith('.backup')) {
          // Check if this backup is for our file
          if (entry.name.includes(fileName) || entry.name.includes(relativePath.replace(/[/\\]/g, '_'))) {
            try {
              const stats = await fs.stat(fullPath);
              candidates.push({
                originalPath: filePath,
                backupPath: fullPath,
                timestamp: stats.mtime,
                size: stats.size
              });
            } catch (error) {
              // Ignore files that can't be read
            }
          }
        }
      }
    };

    await searchDir(this.options.backupDir);
    return candidates;
  }

  /**
   * Validate a file after modification
   */
  private async validateFile(filePath: string, content: string): Promise<boolean> {
    try {
      // Basic validation - check if file is readable and has valid encoding
      const readContent = await fs.readFile(filePath, 'utf8');
      
      // Check if content matches what we wrote
      if (readContent !== content) {
        return false;
      }
      
      // Additional validation based on file type
      const ext = path.extname(filePath).toLowerCase();
      
      if (ext === '.ts' || ext === '.tsx') {
        // Basic TypeScript syntax validation
        return this.validateTypeScriptSyntax(content);
      } else if (ext === '.gql' || ext === '.graphql') {
        // Basic GraphQL syntax validation
        return this.validateGraphQLSyntax(content);
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Basic TypeScript syntax validation
   */
  private validateTypeScriptSyntax(content: string): boolean {
    try {
      // Check for basic syntax issues
      const lines = content.split('\n');
      let braceCount = 0;
      let parenCount = 0;
      
      for (const line of lines) {
        // Count braces and parentheses
        braceCount += (line.match(/\{/g) || []).length;
        braceCount -= (line.match(/\}/g) || []).length;
        parenCount += (line.match(/\(/g) || []).length;
        parenCount -= (line.match(/\)/g) || []).length;
      }
      
      // Basic balance check
      return braceCount === 0 && parenCount === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Basic GraphQL syntax validation
   */
  private validateGraphQLSyntax(content: string): boolean {
    try {
      // Check for basic GraphQL syntax
      const lines = content.split('\n');
      let braceCount = 0;
      
      for (const line of lines) {
        braceCount += (line.match(/\{/g) || []).length;
        braceCount -= (line.match(/\}/g) || []).length;
      }
      
      return braceCount === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate the number of changes between two content strings
   */
  private calculateChangesCount(original: string, modified: string): number {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    let changes = 0;
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i] || '';
      const modifiedLine = modifiedLines[i] || '';
      
      if (originalLine !== modifiedLine) {
        changes++;
      }
    }
    
    return changes;
  }

  /**
   * Get backup information for a file
   */
  public getBackupInfo(filePath: string): BackupInfo | undefined {
    return this.backups.get(filePath);
  }

  /**
   * Get all backup information
   */
  public getAllBackups(): BackupInfo[] {
    return Array.from(this.backups.values());
  }

  /**
   * Clean up old backups
   */
  public async cleanupBackups(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    let cleanedCount = 0;
    
    for (const [filePath, backupInfo] of this.backups.entries()) {
      if (backupInfo.timestamp < cutoffDate) {
        try {
          await fs.remove(backupInfo.backupPath);
          this.backups.delete(filePath);
          cleanedCount++;
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    }
    
    return cleanedCount;
  }
}
