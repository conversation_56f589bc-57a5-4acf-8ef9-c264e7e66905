/**
 * TypeMapCache - Persistent storage and retrieval for type maps
 * 
 * This service provides caching functionality for type maps to avoid
 * re-parsing files when they haven't changed. It includes cache validation,
 * invalidation, and cleanup mechanisms.
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { promisify } from 'util';
import * as glob from 'glob';

import type { TypeMap, ParsedFileInfo } from './type-map';

const globAsync = promisify(glob.glob);

/**
 * Cache entry metadata
 */
export interface CacheEntry {
  /** Type map data */
  typeMap: TypeMap;
  /** Cache creation timestamp */
  createdAt: number;
  /** Cache last accessed timestamp */
  lastAccessed: number;
  /** Cache entry version */
  version: string;
  /** File checksums for validation */
  fileChecksums: Map<string, string>;
}

/**
 * Cache configuration
 */
export interface TypeMapCacheConfig {
  /** Cache directory */
  cacheDirectory: string;
  /** Maximum cache age in milliseconds */
  maxAge?: number;
  /** Maximum cache size in bytes */
  maxSize?: number;
  /** Enable debug logging */
  debug?: boolean;
  /** Compression level (0-9) */
  compressionLevel?: number;
}

/**
 * Cache validation result
 */
export interface CacheValidationResult {
  /** Whether cache is valid */
  isValid: boolean;
  /** Reason for invalidity */
  reason?: string;
  /** Files that have changed */
  changedFiles?: string[];
  /** Files that are missing */
  missingFiles?: string[];
}

/**
 * TypeMapCache - Handles persistent storage of type maps
 */
export class TypeMapCache {
  private config: Required<TypeMapCacheConfig>;

  constructor(config: TypeMapCacheConfig) {
    this.config = {
      maxAge: config.maxAge || 24 * 60 * 60 * 1000, // 24 hours
      maxSize: config.maxSize || 100 * 1024 * 1024, // 100MB
      debug: config.debug || false,
      compressionLevel: config.compressionLevel || 6,
      ...config
    };
  }

  /**
   * Get type map from cache
   */
  async get(schemaId: string = 'default'): Promise<TypeMap | null> {
    try {
      const cacheFile = this.getCacheFilePath(schemaId);
      
      if (!(await fs.pathExists(cacheFile))) {
        if (this.config.debug) {
          console.log(`Cache miss: ${cacheFile} does not exist`);
        }
        return null;
      }

      const cacheEntry = await this.loadCacheEntry(cacheFile);
      if (!cacheEntry) {
        return null;
      }

      // Validate cache
      const validation = await this.validateCache(cacheEntry);
      if (!validation.isValid) {
        if (this.config.debug) {
          console.log(`Cache invalid: ${validation.reason}`);
          if (validation.changedFiles?.length) {
            console.log(`Changed files: ${validation.changedFiles.join(', ')}`);
          }
        }
        await this.delete(schemaId);
        return null;
      }

      // Update last accessed time
      cacheEntry.lastAccessed = Date.now();
      await this.saveCacheEntry(cacheFile, cacheEntry);

      if (this.config.debug) {
        console.log(`Cache hit: Loaded type map for schema ${schemaId}`);
      }

      return cacheEntry.typeMap;
    } catch (error) {
      if (this.config.debug) {
        console.warn(`Failed to load cache for schema ${schemaId}:`, error);
      }
      return null;
    }
  }

  /**
   * Store type map in cache
   */
  async set(typeMap: TypeMap, schemaId: string = 'default'): Promise<void> {
    try {
      await fs.ensureDir(this.config.cacheDirectory);

      // Calculate file checksums for validation
      const fileChecksums = await this.calculateFileChecksums(typeMap);

      const cacheEntry: CacheEntry = {
        typeMap,
        createdAt: Date.now(),
        lastAccessed: Date.now(),
        version: '1.0.0',
        fileChecksums
      };

      const cacheFile = this.getCacheFilePath(schemaId);
      await this.saveCacheEntry(cacheFile, cacheEntry);

      if (this.config.debug) {
        console.log(`Cached type map for schema ${schemaId}`);
      }

      // Cleanup old cache entries
      await this.cleanup();
    } catch (error) {
      if (this.config.debug) {
        console.warn(`Failed to cache type map for schema ${schemaId}:`, error);
      }
    }
  }

  /**
   * Delete cache entry
   */
  async delete(schemaId: string = 'default'): Promise<void> {
    try {
      const cacheFile = this.getCacheFilePath(schemaId);
      if (await fs.pathExists(cacheFile)) {
        await fs.remove(cacheFile);
        if (this.config.debug) {
          console.log(`Deleted cache for schema ${schemaId}`);
        }
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn(`Failed to delete cache for schema ${schemaId}:`, error);
      }
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      if (await fs.pathExists(this.config.cacheDirectory)) {
        const files = await globAsync(path.join(this.config.cacheDirectory, 'type-map-*.json'), {}) as string[];
        await Promise.all(files.map((file: string) => fs.remove(file)));
        if (this.config.debug) {
          console.log(`Cleared ${files.length} cache entries`);
        }
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to clear cache:', error);
      }
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    totalEntries: number;
    totalSize: number;
    oldestEntry: number;
    newestEntry: number;
  }> {
    try {
      const files = await globAsync(path.join(this.config.cacheDirectory, 'type-map-*.json'), {}) as string[];
      let totalSize = 0;
      let oldestEntry = Date.now();
      let newestEntry = 0;

      for (const file of files) {
        const stats = await fs.stat(file);
        totalSize += stats.size;
        
        const createdAt = stats.birthtime.getTime();
        if (createdAt < oldestEntry) oldestEntry = createdAt;
        if (createdAt > newestEntry) newestEntry = createdAt;
      }

      return {
        totalEntries: files.length,
        totalSize,
        oldestEntry: files.length > 0 ? oldestEntry : 0,
        newestEntry: files.length > 0 ? newestEntry : 0
      };
    } catch (error) {
      return {
        totalEntries: 0,
        totalSize: 0,
        oldestEntry: 0,
        newestEntry: 0
      };
    }
  }

  /**
   * Validate cache entry
   */
  private async validateCache(cacheEntry: CacheEntry): Promise<CacheValidationResult> {
    // Check age
    const age = Date.now() - cacheEntry.createdAt;
    if (age > this.config.maxAge) {
      return {
        isValid: false,
        reason: `Cache expired (age: ${Math.round(age / 1000 / 60)} minutes)`
      };
    }

    // Check version compatibility
    if (cacheEntry.version !== '1.0.0') {
      return {
        isValid: false,
        reason: `Version mismatch (cached: ${cacheEntry.version}, expected: 1.0.0)`
      };
    }

    // Check file changes
    const changedFiles: string[] = [];
    const missingFiles: string[] = [];

    for (const [filePath, expectedChecksum] of cacheEntry.fileChecksums.entries()) {
      try {
        if (!(await fs.pathExists(filePath))) {
          missingFiles.push(filePath);
          continue;
        }

        const currentChecksum = await this.calculateFileChecksum(filePath);
        if (currentChecksum !== expectedChecksum) {
          changedFiles.push(filePath);
        }
      } catch (error) {
        // Treat errors as file changes
        changedFiles.push(filePath);
      }
    }

    if (changedFiles.length > 0 || missingFiles.length > 0) {
      return {
        isValid: false,
        reason: `Files changed or missing`,
        changedFiles,
        missingFiles
      };
    }

    return { isValid: true };
  }

  /**
   * Calculate checksums for all files in type map
   */
  private async calculateFileChecksums(typeMap: TypeMap): Promise<Map<string, string>> {
    const checksums = new Map<string, string>();
    
    for (const [filePath] of typeMap.files) {
      try {
        const checksum = await this.calculateFileChecksum(filePath);
        checksums.set(filePath, checksum);
      } catch (error) {
        // Skip files that can't be read
        if (this.config.debug) {
          console.warn(`Failed to calculate checksum for ${filePath}:`, error);
        }
      }
    }

    return checksums;
  }

  /**
   * Calculate checksum for a single file
   */
  private async calculateFileChecksum(filePath: string): Promise<string> {
    const content = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Load cache entry from file
   */
  private async loadCacheEntry(cacheFile: string): Promise<CacheEntry | null> {
    try {
      const data = await fs.readJson(cacheFile);
      
      // Convert Map objects from JSON
      if (data.typeMap) {
        data.typeMap.files = new Map(data.typeMap.files as any);
        data.typeMap.methodCalls = new Map(
          (data.typeMap.methodCalls as any[]).map(([key, value]: [string, any[]]) => [
            key,
            new Map(value)
          ])
        );
        data.typeMap.imports = new Map(data.typeMap.imports as any);
        data.typeMap.fields = new Map(data.typeMap.fields as any);
      }
      
      data.fileChecksums = new Map(data.fileChecksums);
      
      return data;
    } catch (error) {
      if (this.config.debug) {
        console.warn(`Failed to load cache entry from ${cacheFile}:`, error);
      }
      return null;
    }
  }

  /**
   * Save cache entry to file
   */
  private async saveCacheEntry(cacheFile: string, cacheEntry: CacheEntry): Promise<void> {
    // Convert Map objects to JSON-serializable format
    const serializable = {
      ...cacheEntry,
      typeMap: {
        ...cacheEntry.typeMap,
        files: Array.from(cacheEntry.typeMap.files.entries()),
        methodCalls: Array.from(cacheEntry.typeMap.methodCalls.entries()).map(([key, value]) => [
          key,
          Array.from(value.entries())
        ]),
        imports: Array.from(cacheEntry.typeMap.imports.entries()),
        fields: Array.from(cacheEntry.typeMap.fields.entries())
      },
      fileChecksums: Array.from(cacheEntry.fileChecksums.entries())
    };

    await fs.writeJson(cacheFile, serializable, { spaces: 2 });
  }

  /**
   * Get cache file path for schema
   */
  private getCacheFilePath(schemaId: string): string {
    return path.join(this.config.cacheDirectory, `type-map-${schemaId}.json`);
  }

  /**
   * Cleanup old cache entries
   */
  private async cleanup(): Promise<void> {
    try {
      const stats = await this.getStats();
      
      // Check size limit
      if (stats.totalSize > this.config.maxSize) {
        if (this.config.debug) {
          console.log(`Cache size (${Math.round(stats.totalSize / 1024 / 1024)}MB) exceeds limit, cleaning up...`);
        }
        await this.cleanupBySize();
      }

      // Check age limit
      const oldestAllowed = Date.now() - this.config.maxAge;
      if (stats.oldestEntry < oldestAllowed) {
        if (this.config.debug) {
          console.log('Cleaning up expired cache entries...');
        }
        await this.cleanupByAge();
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to cleanup cache:', error);
      }
    }
  }

  /**
   * Cleanup cache entries by size (remove oldest first)
   */
  private async cleanupBySize(): Promise<void> {
    const files = await globAsync(path.join(this.config.cacheDirectory, 'type-map-*.json'), {}) as string[];
    const fileStats = await Promise.all(
      files.map(async (file: string) => ({
        file,
        stats: await fs.stat(file)
      }))
    );

    // Sort by creation time (oldest first)
    fileStats.sort((a: any, b: any) => a.stats.birthtime.getTime() - b.stats.birthtime.getTime());

    let currentSize = fileStats.reduce((sum: number, { stats }: any) => sum + stats.size, 0);
    
    for (const { file, stats } of fileStats) {
      if (currentSize <= this.config.maxSize) {
        break;
      }
      
      await fs.remove(file);
      currentSize -= stats.size;
      
      if (this.config.debug) {
        console.log(`Removed cache file: ${file}`);
      }
    }
  }

  /**
   * Cleanup cache entries by age
   */
  private async cleanupByAge(): Promise<void> {
    const files = await globAsync(path.join(this.config.cacheDirectory, 'type-map-*.json'), {}) as string[];
    const oldestAllowed = Date.now() - this.config.maxAge;

    for (const file of files) {
      try {
        const stats = await fs.stat(file);
        if (stats.birthtime.getTime() < oldestAllowed) {
          await fs.remove(file);
          if (this.config.debug) {
            console.log(`Removed expired cache file: ${file}`);
          }
        }
      } catch (error) {
        // File might have been deleted already
      }
    }
  }
}

/**
 * Global type map cache instance
 */
let globalTypeMapCache: TypeMapCache | null = null;

/**
 * Get or create global type map cache
 */
export function getGlobalTypeMapCache(config?: TypeMapCacheConfig): TypeMapCache {
  if (!globalTypeMapCache && config) {
    globalTypeMapCache = new TypeMapCache(config);
  }
  if (!globalTypeMapCache) {
    throw new Error('TypeMapCache not initialized. Provide config on first call.');
  }
  return globalTypeMapCache;
}

/**
 * Reset global type map cache (for testing)
 */
export function resetGlobalTypeMapCache(): void {
  globalTypeMapCache = null;
}
