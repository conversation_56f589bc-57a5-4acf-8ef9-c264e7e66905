import { EventEmitter } from 'events';

/**
 * Phase 3: Enhanced Memory Pressure Monitor
 * Advanced memory monitoring with memory-mapped operations support and automatic management
 */

export interface MemoryPressureConfig {
  warningThreshold: number; // 0-1 scale (default: 0.7)
  criticalThreshold: number; // 0-1 scale (default: 0.9)
  monitoringInterval: number; // milliseconds (default: 1000)
  enableGCTrigger: boolean; // trigger garbage collection (default: true)
  maxMemoryUsage?: number; // bytes (default: auto-detect)
  enableLogging: boolean; // log memory events (default: false)
  // Phase 3: Memory-mapped operations support
  enableMemoryMappedTracking: boolean; // track memory-mapped file usage (default: true)
  memoryMappedThreshold: number; // 0-1 scale for memory-mapped operations (default: 0.8)
  enableAutomaticFallback: boolean; // automatic fallback to standard I/O (default: true)
  enablePredictiveManagement: boolean; // predictive memory management (default: true)
  adaptiveThresholds: boolean; // adjust thresholds based on system behavior (default: true)
}

export interface MemoryMetrics {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  arrayBuffers: number;
  memoryPressure: number; // 0-1 scale
  timestamp: number;
  // Phase 3: Enhanced metrics
  memoryMappedUsage?: number; // memory-mapped file usage
  predictedPressure?: number; // predicted memory pressure
  adaptiveThreshold?: number; // current adaptive threshold
  systemAvailableMemory?: number; // available system memory
}

export interface MemoryPressureEvent {
  level: 'normal' | 'warning' | 'critical';
  metrics: MemoryMetrics;
  recommendation: string;
}

/**
 * Advanced memory pressure monitoring system
 * Phase 3: Enhanced with memory-mapped operations support and predictive management
 */
export class MemoryPressureMonitor extends EventEmitter {
  private config: Required<MemoryPressureConfig>;
  private monitorInterval?: NodeJS.Timeout;
  private isMonitoring = false;
  private lastMetrics?: MemoryMetrics;
  private pressureHistory: number[] = [];
  private maxHistorySize = 60; // Keep 1 minute of history at 1s intervals

  // Phase 3: Enhanced monitoring
  private memoryMappedTracking = new Map<string, number>(); // file -> memory usage
  private adaptiveThresholds = { warning: 0.7, critical: 0.9 };
  private predictiveModel: Array<{ pressure: number; timestamp: number }> = [];
  private systemMemoryInfo: { total: number; available: number } = { total: 0, available: 0 };

  constructor(config: Partial<MemoryPressureConfig> = {}) {
    super();

    this.config = {
      warningThreshold: 0.7,
      criticalThreshold: 0.9,
      monitoringInterval: 1000,
      enableGCTrigger: true,
      maxMemoryUsage: this.detectMaxMemoryUsage(),
      enableLogging: false,
      // Phase 3: Enhanced defaults
      enableMemoryMappedTracking: true,
      memoryMappedThreshold: 0.8,
      enableAutomaticFallback: true,
      enablePredictiveManagement: true,
      adaptiveThresholds: true,
      ...config
    };

    // Initialize adaptive thresholds
    this.adaptiveThresholds.warning = this.config.warningThreshold;
    this.adaptiveThresholds.critical = this.config.criticalThreshold;

    // Initialize system memory info
    this.initializeSystemMemoryInfo();
  }

  /**
   * Start memory pressure monitoring
   */
  start(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.monitorInterval = setInterval(() => {
      this.checkMemoryPressure();
    }, this.config.monitoringInterval);

    if (this.config.enableLogging) {
      console.log('🔍 Memory pressure monitoring started');
    }

    this.emit('started');
  }

  /**
   * Stop memory pressure monitoring
   */
  stop(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = undefined;
    }

    if (this.config.enableLogging) {
      console.log('🔍 Memory pressure monitoring stopped');
    }

    this.emit('stopped');
  }

  /**
   * Get current memory metrics
   */
  getCurrentMetrics(): MemoryMetrics {
    const memUsage = process.memoryUsage();
    const memoryPressure = Math.min(1, memUsage.heapUsed / this.config.maxMemoryUsage);
    
    return {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      arrayBuffers: memUsage.arrayBuffers || 0,
      memoryPressure,
      timestamp: Date.now()
    };
  }

  /**
   * Check memory pressure and emit events
   */
  private checkMemoryPressure(): void {
    const metrics = this.getCurrentMetrics();
    this.lastMetrics = metrics;
    
    // Update pressure history
    this.pressureHistory.push(metrics.memoryPressure);
    if (this.pressureHistory.length > this.maxHistorySize) {
      this.pressureHistory.shift();
    }

    // Determine pressure level
    let level: 'normal' | 'warning' | 'critical' = 'normal';
    let recommendation = 'Memory usage is normal';

    if (metrics.memoryPressure >= this.config.criticalThreshold) {
      level = 'critical';
      recommendation = 'Critical memory pressure detected. Consider reducing batch sizes or enabling streaming.';
      
      if (this.config.enableGCTrigger && global.gc) {
        global.gc();
        if (this.config.enableLogging) {
          console.log('🗑️  Triggered garbage collection due to critical memory pressure');
        }
      }
    } else if (metrics.memoryPressure >= this.config.warningThreshold) {
      level = 'warning';
      recommendation = 'High memory usage detected. Monitor for potential issues.';
    }

    // Emit events
    this.emit('metrics', metrics);
    
    if (level !== 'normal') {
      const event: MemoryPressureEvent = { level, metrics, recommendation };
      this.emit('pressure', event);
      
      if (this.config.enableLogging) {
        console.warn(`⚠️  Memory pressure ${level}: ${Math.round(metrics.memoryPressure * 100)}% - ${recommendation}`);
      }
    }
  }

  /**
   * Wait for memory pressure to decrease below threshold
   */
  async waitForMemoryRecovery(targetPressure = 0.6, timeout = 30000): Promise<boolean> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const checkRecovery = () => {
        const metrics = this.getCurrentMetrics();
        
        if (metrics.memoryPressure <= targetPressure) {
          resolve(true);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          resolve(false); // Timeout
          return;
        }
        
        setTimeout(checkRecovery, 100);
      };
      
      // Trigger garbage collection if available
      if (this.config.enableGCTrigger && global.gc) {
        global.gc();
      }
      
      checkRecovery();
    });
  }

  /**
   * Get memory pressure trend analysis
   */
  getPressureTrend(): {
    current: number;
    average: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    volatility: number;
  } {
    if (this.pressureHistory.length < 2) {
      return {
        current: this.lastMetrics?.memoryPressure || 0,
        average: this.lastMetrics?.memoryPressure || 0,
        trend: 'stable',
        volatility: 0
      };
    }

    const current = this.pressureHistory[this.pressureHistory.length - 1];
    const average = this.pressureHistory.reduce((sum, val) => sum + val, 0) / this.pressureHistory.length;
    
    // Calculate trend over last 10 samples
    const recentSamples = this.pressureHistory.slice(-10);
    const firstHalf = recentSamples.slice(0, Math.floor(recentSamples.length / 2));
    const secondHalf = recentSamples.slice(Math.floor(recentSamples.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    const trendThreshold = 0.05; // 5% change threshold
    
    if (secondAvg - firstAvg > trendThreshold) {
      trend = 'increasing';
    } else if (firstAvg - secondAvg > trendThreshold) {
      trend = 'decreasing';
    }
    
    // Calculate volatility (standard deviation)
    const variance = this.pressureHistory.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / this.pressureHistory.length;
    const volatility = Math.sqrt(variance);

    return { current, average, trend, volatility };
  }

  /**
   * Get recommendations based on current memory state
   */
  getRecommendations(): string[] {
    const metrics = this.getCurrentMetrics();
    const trend = this.getPressureTrend();
    const recommendations: string[] = [];

    if (metrics.memoryPressure > this.config.criticalThreshold) {
      recommendations.push('Reduce batch sizes immediately');
      recommendations.push('Enable streaming processing');
      recommendations.push('Consider splitting large operations');
    } else if (metrics.memoryPressure > this.config.warningThreshold) {
      recommendations.push('Monitor memory usage closely');
      recommendations.push('Consider reducing concurrent operations');
    }

    if (trend.trend === 'increasing' && trend.volatility > 0.1) {
      recommendations.push('Memory usage is increasing rapidly - investigate memory leaks');
    }

    if (metrics.external > metrics.heapUsed * 2) {
      recommendations.push('High external memory usage detected - check for large buffers');
    }

    return recommendations;
  }

  /**
   * Auto-detect maximum memory usage based on system
   */
  private detectMaxMemoryUsage(): number {
    const os = require('os');
    const totalMemory = os.totalmem();
    
    // Use 70% of total system memory as default limit
    return Math.floor(totalMemory * 0.7);
  }

  /**
   * Get monitoring statistics
   */
  getStats() {
    return {
      isMonitoring: this.isMonitoring,
      config: this.config,
      lastMetrics: this.lastMetrics,
      pressureHistory: [...this.pressureHistory],
      trend: this.getPressureTrend(),
      recommendations: this.getRecommendations()
    };
  }

  /**
   * Cleanup and stop monitoring
   */
  destroy(): void {
    this.stop();
    this.removeAllListeners();
    this.pressureHistory = [];
  }

  // Phase 3: Enhanced Memory Management Methods

  /**
   * Initialize system memory information
   */
  private initializeSystemMemoryInfo(): void {
    try {
      // Get system memory info (Node.js doesn't provide this directly, so we estimate)
      const memUsage = process.memoryUsage();
      this.systemMemoryInfo.total = memUsage.rss * 4; // Rough estimate
      this.systemMemoryInfo.available = this.systemMemoryInfo.total - memUsage.rss;
    } catch (error) {
      // Fallback values
      this.systemMemoryInfo.total = 8 * 1024 * 1024 * 1024; // 8GB default
      this.systemMemoryInfo.available = 4 * 1024 * 1024 * 1024; // 4GB default
    }
  }

  /**
   * Track memory-mapped file usage
   */
  public trackMemoryMappedFile(filePath: string, memoryUsage: number): void {
    if (!this.config.enableMemoryMappedTracking) return;

    this.memoryMappedTracking.set(filePath, memoryUsage);

    // Check if memory-mapped usage exceeds threshold
    const totalMappedMemory = Array.from(this.memoryMappedTracking.values())
      .reduce((sum, usage) => sum + usage, 0);

    const mappedPressure = totalMappedMemory / this.systemMemoryInfo.total;

    if (mappedPressure > this.config.memoryMappedThreshold) {
      this.emit('memoryMappedPressure', {
        totalMappedMemory,
        mappedPressure,
        threshold: this.config.memoryMappedThreshold,
        recommendation: 'Consider reducing memory-mapped file usage or clearing cache'
      });
    }
  }

  /**
   * Remove memory-mapped file tracking
   */
  public untrackMemoryMappedFile(filePath: string): void {
    this.memoryMappedTracking.delete(filePath);
  }

  /**
   * Get current memory-mapped usage
   */
  public getMemoryMappedUsage(): { totalUsage: number; fileCount: number; pressure: number } {
    const totalUsage = Array.from(this.memoryMappedTracking.values())
      .reduce((sum, usage) => sum + usage, 0);

    return {
      totalUsage,
      fileCount: this.memoryMappedTracking.size,
      pressure: totalUsage / this.systemMemoryInfo.total
    };
  }

  /**
   * Predict future memory pressure based on historical data
   */
  private predictMemoryPressure(): number {
    if (!this.config.enablePredictiveManagement || this.predictiveModel.length < 5) {
      return this.lastMetrics?.memoryPressure || 0;
    }

    // Simple linear regression for trend prediction
    const recentData = this.predictiveModel.slice(-10); // Last 10 data points
    const n = recentData.length;

    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    recentData.forEach((point, index) => {
      const x = index;
      const y = point.pressure;
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    });

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Predict next value
    const predictedPressure = slope * n + intercept;

    return Math.max(0, Math.min(1, predictedPressure));
  }

  /**
   * Update adaptive thresholds based on system behavior
   */
  private updateAdaptiveThresholds(): void {
    if (!this.config.adaptiveThresholds || this.pressureHistory.length < 30) return;

    // Calculate average pressure over last 30 measurements
    const recentPressure = this.pressureHistory.slice(-30);
    const avgPressure = recentPressure.reduce((sum, p) => sum + p, 0) / recentPressure.length;
    const maxPressure = Math.max(...recentPressure);

    // Adjust thresholds based on system behavior
    if (avgPressure < 0.3 && maxPressure < 0.5) {
      // System is running comfortably, can be more aggressive
      this.adaptiveThresholds.warning = Math.max(0.6, this.config.warningThreshold - 0.1);
      this.adaptiveThresholds.critical = Math.max(0.8, this.config.criticalThreshold - 0.1);
    } else if (avgPressure > 0.6) {
      // System is under pressure, be more conservative
      this.adaptiveThresholds.warning = Math.min(0.8, this.config.warningThreshold + 0.1);
      this.adaptiveThresholds.critical = Math.min(0.95, this.config.criticalThreshold + 0.05);
    }
  }

  /**
   * Enhanced memory metrics collection
   */
  private collectEnhancedMetrics(): MemoryMetrics {
    const memUsage = process.memoryUsage();
    const maxMemory = this.config.maxMemoryUsage || this.systemMemoryInfo.total;
    const memoryPressure = memUsage.heapUsed / maxMemory;

    // Update system memory info
    this.systemMemoryInfo.available = this.systemMemoryInfo.total - memUsage.rss;

    // Get memory-mapped usage
    const mappedUsage = this.getMemoryMappedUsage();

    // Predict future pressure
    const predictedPressure = this.predictMemoryPressure();

    const metrics: MemoryMetrics = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      arrayBuffers: memUsage.arrayBuffers,
      memoryPressure,
      timestamp: Date.now(),
      // Phase 3: Enhanced metrics
      memoryMappedUsage: mappedUsage.totalUsage,
      predictedPressure,
      adaptiveThreshold: this.adaptiveThresholds.warning,
      systemAvailableMemory: this.systemMemoryInfo.available
    };

    // Update predictive model
    this.predictiveModel.push({
      pressure: memoryPressure,
      timestamp: metrics.timestamp
    });

    // Keep only recent data for prediction
    if (this.predictiveModel.length > 100) {
      this.predictiveModel.shift();
    }

    return metrics;
  }

  /**
   * Check for automatic fallback conditions
   */
  public shouldFallbackToStandardIO(): boolean {
    if (!this.config.enableAutomaticFallback) return false;

    const mappedUsage = this.getMemoryMappedUsage();
    const currentPressure = this.lastMetrics?.memoryPressure || 0;
    const predictedPressure = this.lastMetrics?.predictedPressure || 0;

    // Fallback conditions
    return (
      mappedUsage.pressure > this.config.memoryMappedThreshold ||
      currentPressure > this.adaptiveThresholds.critical ||
      predictedPressure > this.adaptiveThresholds.warning
    );
  }

  /**
   * Get enhanced statistics including Phase 3 metrics
   */
  public getEnhancedStats() {
    const baseStats = this.getStats();
    const mappedUsage = this.getMemoryMappedUsage();

    return {
      ...baseStats,
      // Phase 3 metrics
      memoryMappedTracking: this.config.enableMemoryMappedTracking,
      memoryMappedUsage: mappedUsage,
      adaptiveThresholds: this.adaptiveThresholds,
      predictiveManagement: this.config.enablePredictiveManagement,
      automaticFallback: this.config.enableAutomaticFallback,
      systemMemoryInfo: this.systemMemoryInfo,
      shouldFallback: this.shouldFallbackToStandardIO()
    };
  }
}

/**
 * Global memory pressure monitor instance
 */
let globalMemoryMonitor: MemoryPressureMonitor | null = null;

/**
 * Get or create global memory pressure monitor
 */
export function getGlobalMemoryMonitor(config?: Partial<MemoryPressureConfig>): MemoryPressureMonitor {
  if (!globalMemoryMonitor) {
    globalMemoryMonitor = new MemoryPressureMonitor(config);
  }
  return globalMemoryMonitor;
}

/**
 * Cleanup global memory monitor
 */
export function cleanupGlobalMemoryMonitor(): void {
  if (globalMemoryMonitor) {
    globalMemoryMonitor.destroy();
    globalMemoryMonitor = null;
  }
}
