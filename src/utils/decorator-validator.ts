import type {
  DecoratorValidationResult} from './decorator-types';
import {
  GQLMethodCallData,
  GQLImportData,
  GQLFieldData,
  GQLContextData,
  isGQLMethodCallData,
  isGQLImportData,
  isGQLFieldData,
  isGQLContextData,
} from './decorator-types';
import type { ParsedDecorator, DecoratorContainer } from './decorator-parser';
import type { DirectiveContainer } from './directive-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import { InputSanitizer } from './input-sanitizer';

/**
 * Interface for inheritance validation context
 */
export interface InheritanceValidationContext {
  typeName: string;
  fieldName: string;
  interfaces: string[];
  decoratorDirectives: DirectiveContainer;
  commentDirectives: DirectiveContainer;
}

/**
 * Interface for inheritance conflict information
 */
export interface InheritanceConflict {
  type: 'method_call' | 'import' | 'field' | 'other';
  fieldName: string;
  decoratorSource: string;
  commentSource: string;
  severity: 'error' | 'warning';
  message: string;
}

/**
 * Comprehensive validator for TypeScript decorators
 */
export class DecoratorValidator {
  /**
   * Validate a decorator and its data
   * @param decorator The parsed decorator
   * @param data The decorator data
   * @returns Validation result
   */
  public static validate(decorator: ParsedDecorator, data: any): DecoratorValidationResult {
    const result: DecoratorValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    // Validate based on decorator type
    switch (decorator.name) {
      case 'GQLMethodCall':
        this.validateGQLMethodCall(data, result);
        break;
      case 'GQLImport':
        this.validateGQLImport(data, result);
        break;
      case 'GQLField':
        this.validateGQLField(data, result);
        break;
      case 'GQLContext':
        this.validateGQLContext(data, result);
        break;
      default:
        result.warnings.push(`Unknown decorator type: ${decorator.name}`);
        break;
    }

    // Set overall validity
    result.valid = result.errors.length === 0;

    return result;
  }

  /**
   * Validate @GQLMethodCall decorator data
   * @param data The decorator data
   * @param result The validation result to populate
   */
  private static validateGQLMethodCall(data: any, result: DecoratorValidationResult): void {
    if (!isGQLMethodCallData(data)) {
      result.errors.push('Invalid @GQLMethodCall data structure');
      return;
    }

    // Required fields
    if (!data.type || data.type.trim() === '') {
      result.errors.push('Missing or empty "type" parameter');
    }

    // field parameter is required (call parameter is optional for smart defaults)
    if (!data.field || data.field.trim() === '') {
      result.errors.push('Missing or empty "field" parameter');
    }

    // call parameter is optional for smart defaults
    // if (!data.call || data.call.trim() === '') {
    //   result.errors.push('Missing or empty "call" parameter');
    // }

    // Validate type name format with enhanced security checks
    if (data.type) {
      if (!this.isValidGraphQLTypeName(data.type)) {
        result.errors.push(`Invalid GraphQL type name: "${data.type}"`);
      }

      // Additional security validation for type names
      const typeSanitizationResult = InputSanitizer.sanitizeInput(data.type, {
        maxLength: 255,
        allowSpecialChars: false,
      });

      if (!typeSanitizationResult.isSafe) {
        result.errors.push(`Unsafe type name detected: ${typeSanitizationResult.error}`);
      }

      // Ensure it's a valid identifier
      if (!InputSanitizer.isValidIdentifier(data.type)) {
        result.errors.push(`Type name contains invalid characters: "${data.type}"`);
      }
    }

    // Validate field name format with enhanced security checks
    if (data.field) {
      if (!this.isValidGraphQLFieldName(data.field)) {
        result.errors.push(`Invalid GraphQL field name: "${data.field}"`);
      }

      // Additional security validation for field names
      const fieldSanitizationResult = InputSanitizer.sanitizeInput(data.field, {
        maxLength: 255,
        allowSpecialChars: false,
      });

      if (!fieldSanitizationResult.isSafe) {
        result.errors.push(`Unsafe field name detected: ${fieldSanitizationResult.error}`);
      }

      // Ensure it's a valid identifier
      if (!InputSanitizer.isValidIdentifier(data.field)) {
        result.errors.push(`Field name contains invalid characters: "${data.field}"`);
      }
    }

    // Validate method call expression with enhanced security checks
    if (data.call) {
      // First check basic syntax
      if (!this.isValidMethodCallExpression(data.call)) {
        result.warnings.push(`Method call expression may be invalid: "${data.call}"`);
      }

      // Enhanced security validation
      const sanitizationResult = InputSanitizer.sanitizeMethodCall(data.call);
      if (!sanitizationResult.isSafe) {
        result.errors.push(`Unsafe method call detected: ${sanitizationResult.error}`);
        if (sanitizationResult.threats) {
          result.errors.push(`Security threats: ${sanitizationResult.threats.join(', ')}`);
        }
      } else if (sanitizationResult.warnings) {
        result.warnings.push(...sanitizationResult.warnings);
      }
    }

    // Validate schema identifier if provided
    if (data.schema && !this.isValidSchemaIdentifier(data.schema)) {
      result.errors.push(`Invalid schema identifier: "${data.schema}"`);
    }

    // Check for type casting patterns
    if (data.call && this.hasTypeCastingPattern(data.call) && !data.enableTypeCasting) {
      result.warnings.push('Method call contains type casting but enableTypeCasting is not set to true');
    }
  }

  /**
   * Validate @GQLImport decorator data
   * @param data The decorator data
   * @param result The validation result to populate
   */
  private static validateGQLImport(data: any, result: DecoratorValidationResult): void {
    if (!isGQLImportData(data)) {
      result.errors.push('Invalid @GQLImport data structure');
      return;
    }

    // Required fields
    if (!data.importStatement || data.importStatement.trim() === '') {
      result.errors.push('Missing or empty import statement');
      return;
    }

    // Validate import statement syntax with enhanced security checks
    if (!this.isValidImportStatement(data.importStatement)) {
      result.errors.push(`Invalid import statement syntax: "${data.importStatement}"`);
    }

    // Enhanced security validation for import statements
    const sanitizationResult = InputSanitizer.sanitizeImportStatement(data.importStatement);
    if (!sanitizationResult.isSafe) {
      result.errors.push(`Unsafe import statement detected: ${sanitizationResult.error}`);
      if (sanitizationResult.threats) {
        result.errors.push(`Security threats: ${sanitizationResult.threats.join(', ')}`);
      }
    } else if (sanitizationResult.warnings) {
      result.warnings.push(...sanitizationResult.warnings);
    }

    // Validate schema identifier if provided
    if (data.schema && !this.isValidSchemaIdentifier(data.schema)) {
      result.errors.push(`Invalid schema identifier: "${data.schema}"`);
    }

    // Check for conditional import consistency
    if (data.conditional && !data.condition) {
      result.warnings.push('Conditional import specified but no condition provided');
    }

    if (!data.conditional && data.condition) {
      result.warnings.push('Condition provided but conditional is not set to true');
    }
  }

  /**
   * Validate @GQLField decorator data
   * @param data The decorator data
   * @param result The validation result to populate
   */
  private static validateGQLField(data: any, result: DecoratorValidationResult): void {
    if (!isGQLFieldData(data)) {
      result.errors.push('Invalid @GQLField data structure');
      return;
    }

    // Required fields
    if (!data.ref || data.ref.trim() === '') {
      result.errors.push('Missing or empty "ref" parameter');
    }

    if (!data.name || data.name.trim() === '') {
      result.errors.push('Missing or empty "name" parameter');
    }

    if (!data.type || data.type.trim() === '') {
      result.errors.push('Missing or empty "type" parameter');
    }

    // Validate reference type name
    if (data.ref && !this.isValidGraphQLTypeName(data.ref)) {
      result.errors.push(`Invalid GraphQL reference type name: "${data.ref}"`);
    }

    // Validate field name
    if (data.name && !this.isValidGraphQLFieldName(data.name)) {
      result.errors.push(`Invalid GraphQL field name: "${data.name}"`);
    }

    // Validate field type
    if (data.type && !this.isValidGraphQLTypeName(data.type)) {
      result.warnings.push(`Field type may be invalid: "${data.type}"`);
    }

    // Validate import path if provided
    if (data.importPath && !this.isValidImportPath(data.importPath)) {
      result.warnings.push(`Import path may be invalid: "${data.importPath}"`);
    }

    // Validate schema identifier if provided
    if (data.schema && !this.isValidSchemaIdentifier(data.schema)) {
      result.errors.push(`Invalid schema identifier: "${data.schema}"`);
    }
  }

  /**
   * Validate @GQLContext decorator data
   * @param data The decorator data
   * @param result The validation result to populate
   */
  private static validateGQLContext(data: any, result: DecoratorValidationResult): void {
    if (!isGQLContextData(data)) {
      result.errors.push('Invalid @GQLContext data structure');
      return;
    }

    // Required fields
    if (!data.path || data.path.trim() === '') {
      result.errors.push('Missing or empty "path" parameter');
    }

    if (!data.name || data.name.trim() === '') {
      result.errors.push('Missing or empty "name" parameter');
    }

    // Validate context path
    if (data.path && !this.isValidImportPath(data.path)) {
      result.warnings.push(`Context path may be invalid: "${data.path}"`);
    }

    // Validate context name
    if (data.name && !this.isValidTypeScriptIdentifier(data.name)) {
      result.errors.push(`Invalid TypeScript identifier for context name: "${data.name}"`);
    }

    // Validate schema identifier if provided
    if (data.schema && !this.isValidSchemaIdentifier(data.schema)) {
      result.errors.push(`Invalid schema identifier: "${data.schema}"`);
    }
  }

  /**
   * Check if a string is a valid GraphQL type name
   * @param name The type name to validate
   * @returns Whether the name is valid
   */
  private static isValidGraphQLTypeName(name: string): boolean {
    // GraphQL type names must start with a letter and contain only letters, numbers, and underscores
    return /^[A-Za-z][A-Za-z0-9_]*$/.test(name);
  }

  /**
   * Check if a string is a valid GraphQL field name
   * @param name The field name to validate
   * @returns Whether the name is valid
   */
  private static isValidGraphQLFieldName(name: string): boolean {
    // GraphQL field names must start with a letter or underscore and contain only letters, numbers, and underscores
    return /^[A-Za-z_][A-Za-z0-9_]*$/.test(name);
  }

  /**
   * Check if a string is a valid TypeScript identifier
   * @param name The identifier to validate
   * @returns Whether the identifier is valid
   */
  private static isValidTypeScriptIdentifier(name: string): boolean {
    // TypeScript identifiers must start with a letter, underscore, or dollar sign
    return /^[A-Za-z_$][A-Za-z0-9_$]*$/.test(name);
  }

  /**
   * Check if a string is a valid schema identifier
   * @param schema The schema identifier to validate
   * @returns Whether the identifier is valid
   */
  private static isValidSchemaIdentifier(schema: string): boolean {
    // Schema identifiers should be valid identifiers or simple strings
    return /^[A-Za-z][A-Za-z0-9_-]*$/.test(schema);
  }

  /**
   * Check if a method call expression is valid (basic syntax check)
   * @param call The method call expression
   * @returns Whether the expression appears valid
   */
  private static isValidMethodCallExpression(call: string): boolean {
    // Basic check for method call patterns
    // This is a simple heuristic and may not catch all cases
    const patterns = [
      /^[A-Za-z_$][A-Za-z0-9_$]*\s*\(/,  // Simple function call
      /^[A-Za-z_$][A-Za-z0-9_$.]*\s*\(/,  // Property access with call
      /^await\s+[A-Za-z_$][A-Za-z0-9_$.]*\s*\(/,  // Await expression
      /^return\s+[A-Za-z_$][A-Za-z0-9_$.]*\s*\(/,  // Return with call
    ];

    return patterns.some(pattern => pattern.test(call.trim()));
  }

  /**
   * Check if a method call contains type casting patterns
   * @param call The method call expression
   * @returns Whether type casting is detected
   */
  private static hasTypeCastingPattern(call: string): boolean {
    // Check for 'as Type' patterns
    return /\s+as\s+[A-Za-z][A-Za-z0-9_<>[\]|&\s]*/.test(call);
  }

  /**
   * Check if an import statement is valid (basic syntax check)
   * @param importStatement The import statement
   * @returns Whether the statement appears valid
   */
  private static isValidImportStatement(importStatement: string): boolean {
    // Clean the statement (remove conditional comments)
    const cleanStatement = importStatement.replace(/\/\*.*?\*\/\s*/, '').trim();

    // Enhanced patterns for import statements (more permissive)
    const patterns = [
      /^import\s+\{\s*[A-Za-z_$][A-Za-z0-9_$,\s]*\s*\}\s+from\s+['"][^'"]+['"];?$/,  // Named imports
      /^import\s+[A-Za-z_$][A-Za-z0-9_$]*\s+from\s+['"][^'"]+['"];?$/,  // Default import
      /^import\s+\*\s+as\s+[A-Za-z_$][A-Za-z0-9_$]*\s+from\s+['"][^'"]+['"];?$/,  // Namespace import
      /^import\s+[A-Za-z_$][A-Za-z0-9_$]*\s*,\s*\{\s*[A-Za-z_$][A-Za-z0-9_$,\s]*\s*\}\s+from\s+['"][^'"]+['"];?$/,  // Mixed import
      /^import\s+['"][^'"]+['"];?$/,  // Side-effect imports
      /^import\s*\(\s*['"][^'"]+['"]\s*\)$/,  // Dynamic imports
      // More permissive patterns for common variations
      /^import\s+\{\s*[A-Za-z_$][A-Za-z0-9_$,\s]*\s*\}\s+from\s+['"][^'"]+['"]$/,  // Named imports without semicolon
      /^import\s+[A-Za-z_$][A-Za-z0-9_$]*\s+from\s+['"][^'"]+['"]$/,  // Default import without semicolon
    ];

    const isValid = patterns.some(pattern => pattern.test(cleanStatement));

    if (!isValid) {
      // For now, be more permissive and just check if it starts with 'import'
      const isBasicImport = cleanStatement.trim().startsWith('import');
      if (!isBasicImport) {
        console.warn(`Invalid import statement pattern: "${cleanStatement}"`);
      }
      return isBasicImport;
    }

    return isValid;
  }

  /**
   * Check if an import path is valid (basic format check)
   * @param importPath The import path
   * @returns Whether the path appears valid
   */
  private static isValidImportPath(importPath: string): boolean {
    // Allow relative paths, absolute paths, and module names
    return (
      importPath.startsWith('./') ||
      importPath.startsWith('../') ||
      importPath.startsWith('/') ||
      importPath.startsWith('@/') ||
      /^[a-zA-Z][a-zA-Z0-9_-]*/.test(importPath)  // Module names
    );
  }

  /**
   * Validate file existence for import paths
   * @param importPath The import path to validate
   * @param basePath The base path to resolve relative imports
   * @returns Whether the file exists
   */
  public static async validateFileExists(importPath: string, basePath: string): Promise<boolean> {
    try {
      // Skip validation for module names (not file paths)
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        return true; // Assume module names are valid
      }

      const resolvedPath = path.resolve(basePath, importPath);
      
      // Check for TypeScript files
      const extensions = ['.ts', '.tsx', '.js', '.jsx'];
      for (const ext of extensions) {
        if (await fs.pathExists(resolvedPath + ext)) {
          return true;
        }
      }

      // Check if it's a directory with index file
      for (const ext of extensions) {
        if (await fs.pathExists(path.join(resolvedPath, 'index' + ext))) {
          return true;
        }
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * Batch validate multiple decorators
   * @param decorators Array of decorator-data pairs
   * @returns Array of validation results
   */
  public static validateBatch(
    decorators: Array<{ decorator: ParsedDecorator; data: any }>
  ): Array<{ decorator: ParsedDecorator; result: DecoratorValidationResult }> {
    return decorators.map(({ decorator, data }) => ({
      decorator,
      result: this.validate(decorator, data),
    }));
  }

  /**
   * Get validation summary from multiple results
   * @param results Array of validation results
   * @returns Summary statistics
   */
  public static getValidationSummary(
    results: Array<{ decorator: ParsedDecorator; result: DecoratorValidationResult }>
  ): {
    total: number;
    valid: number;
    invalid: number;
    totalErrors: number;
    totalWarnings: number;
    errorsByType: Record<string, number>;
  } {
    const summary = {
      total: results.length,
      valid: 0,
      invalid: 0,
      totalErrors: 0,
      totalWarnings: 0,
      errorsByType: {} as Record<string, number>,
    };

    for (const { decorator, result } of results) {
      if (result.valid) {
        summary.valid++;
      } else {
        summary.invalid++;
      }

      summary.totalErrors += result.errors.length;
      summary.totalWarnings += result.warnings.length;

      if (result.errors.length > 0) {
        summary.errorsByType[decorator.name] = (summary.errorsByType[decorator.name] || 0) + 1;
      }
    }

    return summary;
  }

  /**
   * Validate decorator usage in inheritance context
   * @param context The inheritance validation context
   * @returns Validation result with inheritance-specific errors and warnings
   */
  public static validateInheritanceContext(context: InheritanceValidationContext): DecoratorValidationResult {
    const result: DecoratorValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    // Check for conflicts between decorator and comment-based directives
    const conflicts = this.detectInheritanceConflicts(context);

    for (const conflict of conflicts) {
      if (conflict.severity === 'error') {
        result.errors.push(conflict.message);
        result.valid = false;
      } else {
        result.warnings.push(conflict.message);
      }
    }

    // Validate decorator usage in interface inheritance chains
    this.validateDecoratorInheritanceChain(context, result);

    // Validate precedence rules
    this.validatePrecedenceRules(context, result);

    return result;
  }

  /**
   * Detect conflicts between decorator and comment-based directives
   * @param context The inheritance validation context
   * @returns Array of detected conflicts
   */
  public static detectInheritanceConflicts(context: InheritanceValidationContext): InheritanceConflict[] {
    const conflicts: InheritanceConflict[] = [];

    // Check method call conflicts
    for (const decoratorMethodCall of context.decoratorDirectives.methodCalls) {
      for (const commentMethodCall of context.commentDirectives.methodCalls) {
        if (this.isConflictingMethodCall(decoratorMethodCall, commentMethodCall, context)) {
          conflicts.push({
            type: 'method_call',
            fieldName: context.fieldName,
            decoratorSource: decoratorMethodCall.content || 'decorator',
            commentSource: commentMethodCall.content || 'comment',
            severity: 'warning',
            message: `Conflicting method calls for ${context.typeName}.${context.fieldName}: decorator defines "${decoratorMethodCall.content}" while comment defines "${commentMethodCall.content}". Decorator takes precedence.`,
          });
        }
      }
    }

    // Check import conflicts
    for (const decoratorImport of context.decoratorDirectives.imports) {
      for (const commentImport of context.commentDirectives.imports) {
        if (this.isConflictingImport(decoratorImport, commentImport)) {
          conflicts.push({
            type: 'import',
            fieldName: context.fieldName,
            decoratorSource: decoratorImport.content || 'decorator',
            commentSource: commentImport.content || 'comment',
            severity: 'warning',
            message: `Conflicting imports: decorator defines "${decoratorImport.content}" while comment defines "${commentImport.content}". Decorator takes precedence.`,
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Validate decorator usage in inheritance chains
   * @param context The inheritance validation context
   * @param result The validation result to populate
   */
  private static validateDecoratorInheritanceChain(
    context: InheritanceValidationContext,
    result: DecoratorValidationResult
  ): void {
    // Check if decorators are properly scoped to interfaces
    for (const methodCall of context.decoratorDirectives.methodCalls) {
      const content = methodCall.content || '';

      // Extract type information from method call (simplified approach)
      const typeMatch = content.match(/type:\s*["']([^"']+)["']/);
      if (typeMatch) {
        const decoratorType = typeMatch[1];

        // Check if the decorator type is in the inheritance chain
        if (!context.interfaces.includes(decoratorType) && decoratorType !== context.typeName) {
          result.warnings.push(
            `Method call decorator references type "${decoratorType}" which is not in the inheritance chain for ${context.typeName}`
          );
        }
      }
    }

    // Validate field decorators in inheritance context
    for (const field of context.decoratorDirectives.fieldFields) {
      // Check if field decorators make sense in the inheritance context
      if (field.name && !this.isValidFieldForType(field.name, context.typeName)) {
        result.warnings.push(
          `Field decorator defines field "${field.name}" which may not be appropriate for type ${context.typeName}`
        );
      }
    }
  }

  /**
   * Validate precedence rules in inheritance scenarios
   * @param context The inheritance validation context
   * @param result The validation result to populate
   */
  private static validatePrecedenceRules(
    context: InheritanceValidationContext,
    result: DecoratorValidationResult
  ): void {
    // Check if there are both decorator and comment directives for the same field
    const hasDecoratorDirectives =
      context.decoratorDirectives.methodCalls.length > 0 ||
      context.decoratorDirectives.imports.length > 0 ||
      context.decoratorDirectives.fieldFields.length > 0;

    const hasCommentDirectives =
      context.commentDirectives.methodCalls.length > 0 ||
      context.commentDirectives.imports.length > 0 ||
      context.commentDirectives.fieldFields.length > 0;

    if (hasDecoratorDirectives && hasCommentDirectives) {
      result.warnings.push(
        `Both decorator and comment-based directives found for ${context.typeName}.${context.fieldName}. ` +
        `Decorator directives will take precedence according to the configured precedence rules.`
      );
    }
  }

  /**
   * Check if two method calls are conflicting
   * @param decoratorMethodCall Method call from decorator
   * @param commentMethodCall Method call from comment
   * @param context The inheritance context
   * @returns True if they conflict
   */
  private static isConflictingMethodCall(
    decoratorMethodCall: any,
    commentMethodCall: any,
    context: InheritanceValidationContext
  ): boolean {
    // Simplified conflict detection - in practice, this would be more sophisticated
    const decoratorContent = decoratorMethodCall.content || '';
    const commentContent = commentMethodCall.content || '';

    // Check if they have different content (indicating a conflict)
    // For the same field, different method calls are considered conflicts
    return decoratorContent !== commentContent &&
           decoratorContent.trim() !== '' &&
           commentContent.trim() !== '';
  }

  /**
   * Check if two imports are conflicting
   * @param decoratorImport Import from decorator
   * @param commentImport Import from comment
   * @returns True if they conflict
   */
  private static isConflictingImport(decoratorImport: any, commentImport: any): boolean {
    const decoratorContent = decoratorImport.content || '';
    const commentContent = commentImport.content || '';

    // Check if they import different things from the same module or same things from different modules
    return decoratorContent !== commentContent &&
           (this.extractModuleName(decoratorContent) === this.extractModuleName(commentContent) ||
            this.extractImportNames(decoratorContent).some(name =>
              this.extractImportNames(commentContent).includes(name)
            ));
  }

  /**
   * Extract module name from import statement
   * @param importStatement The import statement
   * @returns The module name
   */
  private static extractModuleName(importStatement: string): string {
    const match = importStatement.match(/from\s+['"]([^'"]+)['"]/);
    return match ? match[1] : '';
  }

  /**
   * Extract imported names from import statement
   * @param importStatement The import statement
   * @returns Array of imported names
   */
  private static extractImportNames(importStatement: string): string[] {
    const namedMatch = importStatement.match(/\{\s*([^}]+)\s*\}/);
    if (namedMatch) {
      return namedMatch[1].split(',').map(name => name.trim());
    }

    const defaultMatch = importStatement.match(/import\s+([A-Za-z_$][A-Za-z0-9_$]*)/);
    if (defaultMatch) {
      return [defaultMatch[1]];
    }

    return [];
  }

  /**
   * Check if a field is valid for a given type (simplified validation)
   * @param fieldName The field name
   * @param typeName The type name
   * @returns True if the field is valid for the type
   */
  private static isValidFieldForType(fieldName: string, typeName: string): boolean {
    // This is a simplified check - in practice, this would validate against the GraphQL schema
    // For now, we'll assume all fields are valid
    return true;
  }

  /**
   * Validate decorator container for inheritance scenarios
   * @param decoratorContainer The decorator container
   * @param interfaces The interfaces in the inheritance chain
   * @returns Validation result
   */
  public static validateDecoratorContainer(
    decoratorContainer: DecoratorContainer,
    interfaces: string[] = []
  ): DecoratorValidationResult {
    const result: DecoratorValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
    };

    // Validate all method calls
    for (const methodCall of decoratorContainer.methodCalls) {
      const methodCallResult = this.validate(methodCall.decorator, methodCall.data);
      result.errors.push(...methodCallResult.errors);
      result.warnings.push(...methodCallResult.warnings);
      if (!methodCallResult.valid) {
        result.valid = false;
      }
    }

    // Validate all imports
    for (const importDirective of decoratorContainer.imports) {
      const importResult = this.validate(importDirective.decorator, importDirective.data);
      result.errors.push(...importResult.errors);
      result.warnings.push(...importResult.warnings);
      if (!importResult.valid) {
        result.valid = false;
      }
    }

    // Validate all fields
    for (const field of decoratorContainer.fields) {
      const fieldResult = this.validate(field.decorator, field.data);
      result.errors.push(...fieldResult.errors);
      result.warnings.push(...fieldResult.warnings);
      if (!fieldResult.valid) {
        result.valid = false;
      }
    }

    // Validate all contexts
    for (const context of decoratorContainer.contexts) {
      const contextResult = this.validate(context.decorator, context.data);
      result.errors.push(...contextResult.errors);
      result.warnings.push(...contextResult.warnings);
      if (!contextResult.valid) {
        result.valid = false;
      }
    }

    return result;
  }
}
