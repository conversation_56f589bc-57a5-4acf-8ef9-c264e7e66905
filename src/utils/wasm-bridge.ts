import path from 'path';
import { EventEmitter } from 'events';

/**
 * Phase 3: JavaScript-WASM Bridge Implementation
 * Provides seamless integration between TypeScript and WASM modules
 * with automatic fallback to JavaScript implementations
 */

export interface WASMConfig {
  enableWASM: boolean;
  enableFallback: boolean;
  enablePerformanceMonitoring: boolean;
  wasmModulePath?: string;
  debug: boolean;
}

export interface WASMPerformanceMetrics {
  wasmCalls: number;
  fallbackCalls: number;
  totalWasmTime: number;
  totalFallbackTime: number;
  averageWasmTime: number;
  averageFallbackTime: number;
  wasmSuccessRate: number;
  platformSupported: boolean;
}

export interface SchemaParseResult {
  types: string[];
  directives: string[];
  fields: string[];
  lineCount: number;
}

export interface DependencyGraphResult {
  sortedOrder: string[];
  hasCycles: boolean;
  totalNodes: number;
  totalEdges: number;
  maxDepth: number;
}

/**
 * WASM Bridge for high-performance operations
 */
export class WASMBridge extends EventEmitter {
  private config: Required<WASMConfig>;
  private wasmModule: any = null;
  private platformSupported = false;
  private metrics: WASMPerformanceMetrics;
  private fallbackImplementations: Map<string, Function> = new Map();

  constructor(config: Partial<WASMConfig> = {}) {
    super();
    
    this.config = {
      enableWASM: true,
      enableFallback: true,
      enablePerformanceMonitoring: true,
      wasmModulePath: path.join(__dirname, '../../gql-wasm-core'),
      debug: false,
      ...config
    };

    this.metrics = {
      wasmCalls: 0,
      fallbackCalls: 0,
      totalWasmTime: 0,
      totalFallbackTime: 0,
      averageWasmTime: 0,
      averageFallbackTime: 0,
      wasmSuccessRate: 0,
      platformSupported: false
    };

    this.initializeFallbackImplementations();
    this.initializeWASM();
  }

  /**
   * Initialize WASM module with platform detection
   */
  private async initializeWASM(): Promise<void> {
    if (!this.config.enableWASM) {
      if (this.config.debug) {
        console.log('🚫 WASM disabled by configuration');
      }
      return;
    }

    try {
      // Try to load the WASM module
      const wasmModule = require(this.config.wasmModulePath);
      this.wasmModule = wasmModule;
      this.platformSupported = true;
      this.metrics.platformSupported = true;

      if (this.config.debug) {
        console.log('✅ WASM module loaded successfully');
      }

      this.emit('wasmInitialized', { success: true });
    } catch (error) {
      this.platformSupported = false;
      this.metrics.platformSupported = false;

      if (this.config.debug) {
        console.warn('⚠️ WASM module failed to load:', error);
        if (this.config.enableFallback) {
          console.log('🔄 Falling back to JavaScript implementations');
        }
      }

      this.emit('wasmInitialized', { success: false, error });
    }
  }

  /**
   * Initialize fallback JavaScript implementations
   */
  private initializeFallbackImplementations(): void {
    // Fallback for file hash calculation
    this.fallbackImplementations.set('calculateFileHash', (content: string): string => {
      const crypto = require('crypto');
      return crypto.createHash('sha256').update(content).digest('hex');
    });

    // Fallback for schema parsing
    this.fallbackImplementations.set('parseSchemaFast', (schemaContent: string): SchemaParseResult => {
      const lines = schemaContent.split('\n');
      const types: string[] = [];
      const directives: string[] = [];
      const fields: string[] = [];

      for (const line of lines) {
        const trimmed = line.trim();
        
        if (trimmed.startsWith('type ') || trimmed.startsWith('interface ') || 
            trimmed.startsWith('union ') || trimmed.startsWith('enum ') ||
            trimmed.startsWith('input ')) {
          const parts = trimmed.split(/\s+/);
          if (parts.length >= 2) {
            types.push(`${parts[0]}:${parts[1]}`);
          }
        }

        if (trimmed.includes('@')) {
          const directiveMatch = trimmed.match(/@\w+/g);
          if (directiveMatch) {
            directives.push(...directiveMatch);
          }
        }

        if (trimmed.includes(':') && !trimmed.startsWith('type')) {
          fields.push(trimmed);
        }
      }

      return {
        types,
        directives,
        fields,
        lineCount: lines.length
      };
    });

    // Fallback for template compilation
    this.fallbackImplementations.set('compileTemplateFast', (templateContent: string, templateName: string): string => {
      // Simple template compilation
      let result = templateContent;
      let replacements = 0;

      result = result.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
        replacements++;
        return `ctx.${variable.trim()}`;
      });

      result += `\n// Compiled by JS fallback: ${replacements} replacements`;
      return result;
    });

    // Fallback for dependency graph calculation
    this.fallbackImplementations.set('calculateDependencyGraph', (nodes: string[], edges: string[]): DependencyGraphResult => {
      const graph = new Map<string, string[]>();
      const inDegree = new Map<string, number>();

      // Initialize
      for (const node of nodes) {
        graph.set(node, []);
        inDegree.set(node, 0);
      }

      // Parse edges
      for (const edge of edges) {
        const [source, target] = edge.split('->').map(s => s.trim());
        if (graph.has(source) && inDegree.has(target)) {
          graph.get(source)!.push(target);
          inDegree.set(target, inDegree.get(target)! + 1);
        }
      }

      // Topological sort
      const queue: string[] = [];
      const sortedOrder: string[] = [];

      for (const [node, degree] of inDegree) {
        if (degree === 0) {
          queue.push(node);
        }
      }

      while (queue.length > 0) {
        const node = queue.shift()!;
        sortedOrder.push(node);

        for (const dep of graph.get(node) || []) {
          const newDegree = inDegree.get(dep)! - 1;
          inDegree.set(dep, newDegree);
          if (newDegree === 0) {
            queue.push(dep);
          }
        }
      }

      return {
        sortedOrder,
        hasCycles: sortedOrder.length !== nodes.length,
        totalNodes: nodes.length,
        totalEdges: edges.length,
        maxDepth: this.calculateMaxDepthFallback(graph, nodes)
      };
    });
  }

  /**
   * Calculate max depth fallback implementation
   */
  private calculateMaxDepthFallback(graph: Map<string, string[]>, nodes: string[]): number {
    let maxDepth = 0;
    const memo = new Map<string, number>();

    const calculateDepth = (node: string): number => {
      if (memo.has(node)) {
        return memo.get(node)!;
      }

      let maxChildDepth = 0;
      for (const dep of graph.get(node) || []) {
        maxChildDepth = Math.max(maxChildDepth, calculateDepth(dep));
      }

      const depth = maxChildDepth + 1;
      memo.set(node, depth);
      return depth;
    };

    for (const node of nodes) {
      maxDepth = Math.max(maxDepth, calculateDepth(node));
    }

    return maxDepth;
  }

  /**
   * Execute function with WASM or fallback
   */
  private async executeWithFallback<T>(
    functionName: string,
    wasmFunction: () => T,
    fallbackFunction: () => T
  ): Promise<T> {
    const startTime = Date.now();

    if (this.platformSupported && this.wasmModule && this.config.enableWASM) {
      try {
        const result = wasmFunction();
        const executionTime = Date.now() - startTime;
        
        this.updateMetrics('wasm', executionTime, true);
        
        if (this.config.debug) {
          console.log(`⚡ WASM ${functionName} executed in ${executionTime}ms`);
        }
        
        return result;
      } catch (error) {
        if (this.config.debug) {
          console.warn(`❌ WASM ${functionName} failed:`, error);
        }
        
        this.updateMetrics('wasm', Date.now() - startTime, false);
        
        if (!this.config.enableFallback) {
          throw error;
        }
      }
    }

    // Fallback to JavaScript implementation
    const fallbackStartTime = Date.now();
    const result = fallbackFunction();
    const fallbackTime = Date.now() - fallbackStartTime;
    
    this.updateMetrics('fallback', fallbackTime, true);
    
    if (this.config.debug) {
      console.log(`🔄 JS fallback ${functionName} executed in ${fallbackTime}ms`);
    }
    
    return result;
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(type: 'wasm' | 'fallback', executionTime: number, success: boolean): void {
    if (!this.config.enablePerformanceMonitoring) return;

    if (type === 'wasm') {
      this.metrics.wasmCalls++;
      if (success) {
        this.metrics.totalWasmTime += executionTime;
      }
    } else {
      this.metrics.fallbackCalls++;
      this.metrics.totalFallbackTime += executionTime;
    }

    // Update averages
    if (this.metrics.wasmCalls > 0) {
      this.metrics.averageWasmTime = this.metrics.totalWasmTime / this.metrics.wasmCalls;
      this.metrics.wasmSuccessRate = (this.metrics.wasmCalls - this.metrics.fallbackCalls) / this.metrics.wasmCalls;
    }
    
    if (this.metrics.fallbackCalls > 0) {
      this.metrics.averageFallbackTime = this.metrics.totalFallbackTime / this.metrics.fallbackCalls;
    }
  }

  // Public API methods

  /**
   * Calculate file hash using WASM or fallback
   */
  async calculateFileHash(content: string): Promise<string> {
    return this.executeWithFallback(
      'calculateFileHash',
      () => this.wasmModule.calculateFileHash(content),
      () => this.fallbackImplementations.get('calculateFileHash')!(content)
    );
  }

  /**
   * Parse schema using WASM or fallback
   */
  async parseSchemaFast(schemaContent: string): Promise<SchemaParseResult> {
    return this.executeWithFallback(
      'parseSchemaFast',
      () => this.wasmModule.parseSchemaFast(schemaContent),
      () => this.fallbackImplementations.get('parseSchemaFast')!(schemaContent)
    );
  }

  /**
   * Compile template using WASM or fallback
   */
  async compileTemplateFast(templateContent: string, templateName: string): Promise<string> {
    return this.executeWithFallback(
      'compileTemplateFast',
      () => this.wasmModule.compileTemplateFast(templateContent, templateName),
      () => this.fallbackImplementations.get('compileTemplateFast')!(templateContent, templateName)
    );
  }

  /**
   * Calculate dependency graph using WASM or fallback
   */
  async calculateDependencyGraph(nodes: string[], edges: string[]): Promise<DependencyGraphResult> {
    return this.executeWithFallback(
      'calculateDependencyGraph',
      () => this.wasmModule.calculateDependencyGraph(nodes, edges),
      () => this.fallbackImplementations.get('calculateDependencyGraph')!(nodes, edges)
    );
  }

  /**
   * Get performance metrics
   */
  getMetrics(): WASMPerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Check if WASM is available
   */
  isWASMAvailable(): boolean {
    return this.platformSupported && this.wasmModule !== null;
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      wasmCalls: 0,
      fallbackCalls: 0,
      totalWasmTime: 0,
      totalFallbackTime: 0,
      averageWasmTime: 0,
      averageFallbackTime: 0,
      wasmSuccessRate: 0,
      platformSupported: this.platformSupported
    };
  }
}

/**
 * Global WASM bridge instance
 */
let globalWASMBridge: WASMBridge | null = null;

/**
 * Get or create global WASM bridge
 */
export function getGlobalWASMBridge(config?: Partial<WASMConfig>): WASMBridge {
  if (!globalWASMBridge) {
    globalWASMBridge = new WASMBridge(config);
  }
  return globalWASMBridge;
}
