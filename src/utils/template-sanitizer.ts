import { InputSanitizer } from './input-sanitizer';

/**
 * Template sanitization utilities to prevent injection attacks in code generation
 */

/**
 * Configuration for template sanitization
 */
export interface TemplateSanitizationConfig {
  /** Whether to allow JavaScript expressions */
  allowJavaScript?: boolean;
  /** Whether to allow import statements */
  allowImports?: boolean;
  /** Whether to allow method calls */
  allowMethodCalls?: boolean;
  /** Maximum length for template values */
  maxLength?: number;
}

/**
 * Result of template sanitization
 */
export interface TemplateSanitizationResult {
  /** Whether the template value is safe */
  isSafe: boolean;
  /** The sanitized value */
  sanitizedValue?: string;
  /** Error message if unsafe */
  error?: string;
  /** Warning messages */
  warnings?: string[];
}

/**
 * Template sanitizer for code generation
 */
export class TemplateSanitizer {
  private static readonly DEFAULT_CONFIG: Required<TemplateSanitizationConfig> = {
    allowJavaScript: false,
    allowImports: true,
    allowMethodCalls: true,
    maxLength: 5000,
  };

  /**
   * Sanitize a template value for safe interpolation
   * @param value The value to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeTemplateValue(
    value: string,
    config: TemplateSanitizationConfig = {}
  ): TemplateSanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };
    
    if (typeof value !== 'string') {
      return {
        isSafe: false,
        error: 'Template value must be a string',
      };
    }

    // Basic input sanitization first
    const inputResult = InputSanitizer.sanitizeInput(value, {
      maxLength: cfg.maxLength,
      allowCodeInjection: cfg.allowJavaScript,
    });

    if (!inputResult.isSafe) {
      return {
        isSafe: false,
        error: inputResult.error,
      };
    }

    return {
      isSafe: true,
      sanitizedValue: inputResult.sanitizedInput,
      warnings: inputResult.warnings,
    };
  }

  /**
   * Sanitize a method call for template interpolation
   * @param methodCall The method call to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeMethodCall(
    methodCall: string,
    config: TemplateSanitizationConfig = {}
  ): TemplateSanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };

    if (!cfg.allowMethodCalls) {
      return {
        isSafe: false,
        error: 'Method calls are not allowed in this context',
      };
    }

    // Use the input sanitizer's method call validation
    const sanitizationResult = InputSanitizer.sanitizeMethodCall(methodCall);
    
    if (!sanitizationResult.isSafe) {
      return {
        isSafe: false,
        error: sanitizationResult.error,
      };
    }

    // Additional template-specific validation
    const warnings: string[] = sanitizationResult.warnings || [];

    // Check for template injection patterns
    if (methodCall.includes('{{') || methodCall.includes('}}')) {
      return {
        isSafe: false,
        error: 'Method call contains template injection patterns',
      };
    }

    // Check for script tag injection
    if (/<script/i.test(methodCall)) {
      return {
        isSafe: false,
        error: 'Method call contains script tag injection',
      };
    }

    return {
      isSafe: true,
      sanitizedValue: sanitizationResult.sanitizedInput,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Sanitize an import statement for template interpolation
   * @param importStatement The import statement to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeImportStatement(
    importStatement: string,
    config: TemplateSanitizationConfig = {}
  ): TemplateSanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };

    if (!cfg.allowImports) {
      return {
        isSafe: false,
        error: 'Import statements are not allowed in this context',
      };
    }

    // Use the input sanitizer's import validation
    const sanitizationResult = InputSanitizer.sanitizeImportStatement(importStatement);
    
    if (!sanitizationResult.isSafe) {
      return {
        isSafe: false,
        error: sanitizationResult.error,
      };
    }

    // Additional template-specific validation
    const warnings: string[] = sanitizationResult.warnings || [];

    // Check for template injection patterns
    if (importStatement.includes('{{') || importStatement.includes('}}')) {
      return {
        isSafe: false,
        error: 'Import statement contains template injection patterns',
      };
    }

    return {
      isSafe: true,
      sanitizedValue: sanitizationResult.sanitizedInput,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Sanitize a file path for template interpolation
   * @param filePath The file path to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeFilePath(
    filePath: string,
    config: TemplateSanitizationConfig = {}
  ): TemplateSanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };

    // Basic validation
    if (!InputSanitizer.isValidFilePath(filePath)) {
      return {
        isSafe: false,
        error: 'File path contains invalid characters',
      };
    }

    // Check for template injection patterns
    if (filePath.includes('{{') || filePath.includes('}}')) {
      return {
        isSafe: false,
        error: 'File path contains template injection patterns',
      };
    }

    // Check length
    if (filePath.length > cfg.maxLength) {
      return {
        isSafe: false,
        error: `File path exceeds maximum length of ${cfg.maxLength} characters`,
      };
    }

    return {
      isSafe: true,
      sanitizedValue: filePath,
    };
  }

  /**
   * Sanitize an identifier for template interpolation
   * @param identifier The identifier to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeIdentifier(
    identifier: string,
    config: TemplateSanitizationConfig = {}
  ): TemplateSanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };

    // Basic validation
    if (!InputSanitizer.isValidIdentifier(identifier)) {
      return {
        isSafe: false,
        error: 'Identifier contains invalid characters',
      };
    }

    // Check for template injection patterns
    if (identifier.includes('{{') || identifier.includes('}}')) {
      return {
        isSafe: false,
        error: 'Identifier contains template injection patterns',
      };
    }

    // Check length
    if (identifier.length > cfg.maxLength) {
      return {
        isSafe: false,
        error: `Identifier exceeds maximum length of ${cfg.maxLength} characters`,
      };
    }

    return {
      isSafe: true,
      sanitizedValue: identifier,
    };
  }

  /**
   * Escape a string for safe interpolation in JavaScript code
   * @param value The value to escape
   * @returns Escaped value
   */
  public static escapeForJavaScript(value: string): string {
    return value
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/"/g, '\\"')
      .replace(/`/g, '\\`')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/\0/g, '\\0');
  }

  /**
   * Escape a string for safe interpolation in template literals
   * @param value The value to escape
   * @returns Escaped value
   */
  public static escapeForTemplateLiteral(value: string): string {
    return value
      .replace(/\\/g, '\\\\')
      .replace(/`/g, '\\`')
      .replace(/\$\{/g, '\\${')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/\0/g, '\\0');
  }

  /**
   * Create a safe template data object with sanitized values
   * @param data The template data to sanitize
   * @param config Sanitization configuration
   * @returns Sanitized template data
   */
  public static sanitizeTemplateData(
    data: Record<string, any>,
    config: TemplateSanitizationConfig = {}
  ): Record<string, any> {
    const sanitizedData: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        const result = this.sanitizeTemplateValue(value, config);
        if (result.isSafe) {
          sanitizedData[key] = result.sanitizedValue;
        } else {
          console.warn(`Template data key '${key}' failed sanitization: ${result.error}`);
          sanitizedData[key] = ''; // Use empty string as fallback
        }
      } else if (Array.isArray(value)) {
        sanitizedData[key] = value.map(item => 
          typeof item === 'string' 
            ? this.sanitizeTemplateValue(item, config).sanitizedValue || ''
            : item
        );
      } else {
        sanitizedData[key] = value;
      }
    }

    return sanitizedData;
  }
}
