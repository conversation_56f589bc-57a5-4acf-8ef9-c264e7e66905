import * as fs from 'fs-extra';
import * as glob from 'glob';
import { WorkerThreadPool, WorkerTask, WorkerResult } from './worker-thread-pool';
import type { DecoratorContainer } from './decorator-parser';

/**
 * Configuration for parallel decorator parsing
 */
export interface ParallelDecoratorParserConfig {
  /** Maximum number of worker threads (default: CPU cores - 1) */
  maxWorkers?: number;
  /** Timeout for individual file processing in milliseconds (default: 30000) */
  taskTimeout?: number;
  /** Batch size for processing files (default: 10) */
  batchSize?: number;
  /** Whether to enable performance monitoring (default: true) */
  enableMonitoring?: boolean;
  /** Whether to use worker threads (default: true, falls back to single-threaded if false) */
  useWorkerThreads?: boolean;
}

/**
 * Performance metrics for parallel decorator parsing
 */
export interface ParallelDecoratorParsingMetrics {
  totalDuration: number;
  filesProcessed: number;
  decoratorsFound: number;
  averageTimePerFile: number;
  workerUtilization: number;
  parallelEfficiency: number; // Speedup compared to estimated single-threaded time
  batchesProcessed: number;
  failedFiles: number;
}

/**
 * Parallel decorator parser using worker threads for improved performance
 */
export class ParallelDecoratorParser {
  private workerPool: WorkerThreadPool | null = null;
  private config: Required<ParallelDecoratorParserConfig>;

  constructor(config: ParallelDecoratorParserConfig = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || Math.max(1, require('os').cpus().length - 1),
      taskTimeout: config.taskTimeout || 30000,
      batchSize: config.batchSize || 10,
      enableMonitoring: config.enableMonitoring ?? true,
      useWorkerThreads: config.useWorkerThreads ?? true
    };

    if (this.config.useWorkerThreads) {
      try {
        this.workerPool = new WorkerThreadPool({
          maxWorkers: this.config.maxWorkers,
          taskTimeout: this.config.taskTimeout,
          enableMonitoring: this.config.enableMonitoring
        });
      } catch (error) {
        console.warn('Failed to initialize worker thread pool, falling back to single-threaded processing:', error);
        this.config.useWorkerThreads = false;
      }
    }
  }

  /**
   * Scan a codebase directory for decorators using parallel processing
   */
  public async scanCodebase(
    codebaseDir: string,
    patterns: string[] = ['**/*.ts', '**/*.tsx']
  ): Promise<{ container: DecoratorContainer; metrics: ParallelDecoratorParsingMetrics }> {
    const startTime = Date.now();

    // Verify codebase directory exists
    try {
      await fs.access(codebaseDir);
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Codebase directory not found: ${codebaseDir}`);
      }
      return {
        container: { methodCalls: [], imports: [], fields: [], contexts: [], others: {} },
        metrics: this.createEmptyMetrics()
      };
    }

    // Find all TypeScript files
    const allPatterns = patterns.map(pattern => `${codebaseDir}/${pattern}`);
    const files: string[] = [];
    
    for (const pattern of allPatterns) {
      const matchedFiles = glob.sync(pattern, { 
        ignore: ['**/node_modules/**', '**/dist/**', '**/*.d.ts'],
        absolute: true 
      });
      files.push(...matchedFiles);
    }

    // Remove duplicates
    const uniqueFiles = [...new Set(files)];

    if (process.env.DEBUG_PARSER) {
      console.log(`Found ${uniqueFiles.length} TypeScript files to scan for decorators`);
    }

    // Process files
    const container: DecoratorContainer = {
      methodCalls: [],
      imports: [],
      fields: [],
      contexts: [],
      others: {}
    };

    let failedFiles = 0;
    let batchesProcessed = 0;

    if (this.config.useWorkerThreads && this.workerPool && uniqueFiles.length > this.config.batchSize) {
      // Use parallel processing for large file sets
      const batches = this.createBatches(uniqueFiles, this.config.batchSize);
      batchesProcessed = batches.length;

      if (process.env.DEBUG_PARSER) {
        console.log(`Processing ${uniqueFiles.length} files in ${batches.length} batches using ${this.config.maxWorkers} worker threads`);
      }

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const batchStartTime = Date.now();

        try {
          const tasks: WorkerTask[] = batch.map((filePath, index) => ({
            id: `batch-${i}-file-${index}`,
            filePath,
            type: 'decorator-scan' as const
          }));

          const results = await this.workerPool.submitTasks(tasks);

          // Process results
          for (const result of results) {
            if (result.success && result.result) {
              const decorators = result.result.decorators;
              container.methodCalls.push(...decorators.methodCalls);
              container.imports.push(...decorators.imports);
              container.fields.push(...decorators.fields);
              container.contexts.push(...decorators.contexts);
            } else {
              failedFiles++;
              if (process.env.DEBUG_PARSER) {
                console.warn(`Failed to process file in worker thread: ${result.error}`);
              }
            }
          }

          const batchDuration = Date.now() - batchStartTime;
          if (process.env.DEBUG_PARSER) {
            const progress = Math.round(((i + 1) / batches.length) * 100);
            console.log(`Batch ${i + 1}/${batches.length} completed in ${batchDuration}ms (${progress}% done)`);
          }
        } catch (error) {
          failedFiles += batch.length;
          if (process.env.DEBUG_PARSER) {
            console.error(`Error processing batch ${i + 1}:`, error);
          }
        }
      }
    } else {
      // Fall back to single-threaded processing
      if (process.env.DEBUG_PARSER) {
        console.log(`Processing ${uniqueFiles.length} files using single-threaded processing`);
      }

      const { DecoratorParser } = await import('./decorator-parser.js');
      const parser = new DecoratorParser();
      
      try {
        const result = await parser.scanCodebase(codebaseDir, patterns);
        container.methodCalls.push(...result.methodCalls);
        container.imports.push(...result.imports);
        container.fields.push(...result.fields);
        container.contexts.push(...result.contexts);
      } catch (error) {
        if (process.env.DEBUG_PARSER) {
          console.error('Error in single-threaded processing:', error);
        }
        failedFiles = uniqueFiles.length;
      }
    }

    const totalDuration = Date.now() - startTime;
    const totalDecorators = container.methodCalls.length + container.imports.length + 
                           container.fields.length + container.contexts.length;

    // Calculate metrics
    const workerMetrics = this.workerPool?.getMetrics();
    const metrics: ParallelDecoratorParsingMetrics = {
      totalDuration,
      filesProcessed: uniqueFiles.length - failedFiles,
      decoratorsFound: totalDecorators,
      averageTimePerFile: uniqueFiles.length > 0 ? totalDuration / uniqueFiles.length : 0,
      workerUtilization: workerMetrics?.workerUtilization || 0,
      parallelEfficiency: this.calculateParallelEfficiency(totalDuration, uniqueFiles.length),
      batchesProcessed,
      failedFiles
    };

    // Log completion info
    if (process.env.DEBUG_PARSER || totalDecorators > 0) {
      console.log(`Parallel decorator scan complete in ${totalDuration}ms. Found ${totalDecorators} decorators in ${metrics.filesProcessed} files.`);
      
      if (this.config.useWorkerThreads && workerMetrics) {
        console.log(`Worker utilization: ${workerMetrics.workerUtilization.toFixed(1)}%, Parallel efficiency: ${metrics.parallelEfficiency.toFixed(1)}x`);
      }
    }

    return { container, metrics };
  }

  /**
   * Create batches of files for parallel processing
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Calculate parallel processing efficiency
   */
  private calculateParallelEfficiency(actualDuration: number, fileCount: number): number {
    // Estimate single-threaded time based on average file processing time
    const estimatedSingleThreadTime = fileCount * 50; // Assume 50ms per file average
    return estimatedSingleThreadTime / actualDuration;
  }

  /**
   * Create empty metrics object
   */
  private createEmptyMetrics(): ParallelDecoratorParsingMetrics {
    return {
      totalDuration: 0,
      filesProcessed: 0,
      decoratorsFound: 0,
      averageTimePerFile: 0,
      workerUtilization: 0,
      parallelEfficiency: 0,
      batchesProcessed: 0,
      failedFiles: 0
    };
  }

  /**
   * Get current configuration
   */
  public getConfig(): Required<ParallelDecoratorParserConfig> {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ParallelDecoratorParserConfig>): void {
    Object.assign(this.config, newConfig);
  }

  /**
   * Shutdown the parallel parser and clean up resources
   */
  public async shutdown(): Promise<void> {
    if (this.workerPool) {
      await this.workerPool.shutdown();
      this.workerPool = null;
    }
  }
}
