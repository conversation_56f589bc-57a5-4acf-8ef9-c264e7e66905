import { DirectiveContainer } from './directive-parser';
import { getGlobalBatchDirectiveParser, BatchDirectiveResult } from './batch-directive-parser';
import { getGlobalCommentDirectiveCache } from './comment-directive-cache';
import { getGlobalCacheInvalidator } from './comment-directive-cache-invalidator';
import * as path from 'path';

/**
 * Performance metrics for directive parsing
 */
export interface DirectiveParsingMetrics {
  totalCalls: number;
  cacheHits: number;
  cacheMisses: number;
  totalParseTime: number;
  averageParseTime: number;
  batchParseCount: number;
  filesCached: number;
}

/**
 * Optimized directive parser that uses caching and batch processing
 * Drop-in replacement for DirectiveParser.extractDirectivesFromSchema()
 */
export class OptimizedDirectiveParser {
  private batchParser = getGlobalBatchDirectiveParser();
  private cache = getGlobalCommentDirectiveCache();
  private invalidator = getGlobalCacheInvalidator();
  private enableLogging: boolean;
  private enablePerformanceTracking: boolean;
  
  // Performance tracking
  private metrics: DirectiveParsingMetrics = {
    totalCalls: 0,
    cacheHits: 0,
    cacheMisses: 0,
    totalParseTime: 0,
    averageParseTime: 0,
    batchParseCount: 0,
    filesCached: 0
  };

  // File structure cache to avoid re-parsing
  private fileStructureCache = new Map<string, BatchDirectiveResult>();

  constructor(enableLogging: boolean = false, enablePerformanceTracking: boolean = true) {
    this.enableLogging = enableLogging;
    this.enablePerformanceTracking = enablePerformanceTracking;
  }

  /**
   * Optimized version of DirectiveParser.extractDirectivesFromSchema()
   * Uses caching and batch processing for maximum performance
   */
  async extractDirectivesFromSchema(
    schemaFilePath: string,
    typeName: string,
    fieldName?: string,
    debug: boolean = false
  ): Promise<DirectiveContainer> {
    const startTime = Date.now();
    this.metrics.totalCalls++;



    try {
      // Normalize file path
      const normalizedPath = path.resolve(schemaFilePath);
      
      // Check if we have a cached structure for this file
      let batchResult = this.fileStructureCache.get(normalizedPath);
      
      if (!batchResult || !batchResult.success) {
        // Parse the entire file structure
        batchResult = await this.batchParser.parseSchemaFile(normalizedPath);
        
        if (batchResult.success) {
          this.fileStructureCache.set(normalizedPath, batchResult);
          this.metrics.batchParseCount++;
          
          if (this.enableLogging) {
            console.log(`📊 Batch parsed structure for: ${normalizedPath}`);
          }
        }
      }

      // Extract directives from the parsed structure
      let directives: DirectiveContainer;
      
      if (batchResult.success) {
        directives = this.batchParser.getDirectivesFromStructure(
          batchResult.structure,
          typeName,
          fieldName
        );
        
        if (batchResult.cacheHit) {
          this.metrics.cacheHits++;
        } else {
          this.metrics.cacheMisses++;
        }
      } else {
        // Fallback to empty directives if parsing failed
        directives = this.createEmptyDirectiveContainer();
        this.metrics.cacheMisses++;
      }

      // Update performance metrics
      const parseTime = Date.now() - startTime;
      this.updateMetrics(parseTime);

      if (debug || this.enableLogging) {
        console.log(`🔍 Extracted directives for ${typeName}${fieldName ? `.${fieldName}` : ''} from ${normalizedPath} (${parseTime}ms)`);
      }

      return directives;

    } catch (error) {
      const parseTime = Date.now() - startTime;
      this.updateMetrics(parseTime);
      
      if (this.enableLogging) {
        console.error(`❌ Error extracting directives from ${schemaFilePath}:`, error);
      }
      
      return this.createEmptyDirectiveContainer();
    }
  }

  /**
   * Batch extract directives for multiple types/fields from the same file
   * More efficient when extracting many directives from the same schema file
   */
  async batchExtractDirectives(
    schemaFilePath: string,
    requests: Array<{ typeName: string; fieldName?: string }>
  ): Promise<Map<string, DirectiveContainer>> {
    const startTime = Date.now();
    const results = new Map<string, DirectiveContainer>();
    
    try {
      const normalizedPath = path.resolve(schemaFilePath);
      
      // Parse the file structure once
      const batchResult = await this.batchParser.parseSchemaFile(normalizedPath);
      
      if (batchResult.success) {
        // Extract directives for all requests
        for (const request of requests) {
          const key = `${request.typeName}${request.fieldName ? `.${request.fieldName}` : ''}`;
          const directives = this.batchParser.getDirectivesFromStructure(
            batchResult.structure,
            request.typeName,
            request.fieldName
          );
          results.set(key, directives);
        }
      } else {
        // If batch parsing failed, return empty results for all requests
        for (const request of requests) {
          const key = `${request.typeName}${request.fieldName ? `.${request.fieldName}` : ''}`;
          results.set(key, this.createEmptyDirectiveContainer());
        }
      }

      this.metrics.batchParseCount++;
      if (batchResult.success && batchResult.cacheHit) {
        this.metrics.cacheHits++;
      } else {
        this.metrics.cacheMisses++;
      }

      const parseTime = Date.now() - startTime;
      this.updateMetrics(parseTime);

      if (this.enableLogging) {
        console.log(`📦 Batch extracted ${requests.length} directive sets from ${normalizedPath} (${parseTime}ms)`);
      }

    } catch (error) {
      if (this.enableLogging) {
        console.error(`❌ Error in batch extraction from ${schemaFilePath}:`, error);
      }
    }

    return results;
  }

  /**
   * Preload and cache schema files for better performance
   */
  async preloadSchemaFiles(filePaths: string[]): Promise<void> {
    const startTime = Date.now();
    
    if (this.enableLogging) {
      console.log(`🚀 Preloading ${filePaths.length} schema files...`);
    }

    const preloadPromises = filePaths.map(async (filePath) => {
      try {
        const normalizedPath = path.resolve(filePath);
        const batchResult = await this.batchParser.parseSchemaFile(normalizedPath);
        
        if (batchResult.success) {
          this.fileStructureCache.set(normalizedPath, batchResult);
          this.metrics.filesCached++;
        }
      } catch (error) {
        if (this.enableLogging) {
          console.error(`❌ Error preloading ${filePath}:`, error);
        }
      }
    });

    await Promise.all(preloadPromises);
    
    const preloadTime = Date.now() - startTime;
    
    if (this.enableLogging) {
      console.log(`✅ Preloaded ${this.metrics.filesCached} schema files in ${preloadTime}ms`);
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics(): DirectiveParsingMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalCalls: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalParseTime: 0,
      averageParseTime: 0,
      batchParseCount: 0,
      filesCached: 0
    };
  }

  /**
   * Get cache statistics
   */
  getCacheStatistics() {
    return {
      directive: this.cache.getStatistics(),
      batch: this.batchParser.getCacheStatistics(),
      invalidator: this.invalidator.getStatistics()
    };
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    this.cache.clear();
    this.batchParser.clearCache();
    this.fileStructureCache.clear();
    this.invalidator.invalidateAll();
    
    if (this.enableLogging) {
      console.log('🧹 All directive parser caches cleared');
    }
  }

  /**
   * Invalidate cache for specific files
   */
  invalidateFiles(filePaths: string[]): void {
    for (const filePath of filePaths) {
      const normalizedPath = path.resolve(filePath);
      this.fileStructureCache.delete(normalizedPath);
    }
    
    this.invalidator.invalidateFiles(filePaths);
    
    if (this.enableLogging) {
      console.log(`🗑️ Invalidated cache for ${filePaths.length} files`);
    }
  }

  /**
   * Start automatic cache invalidation
   */
  async startCacheInvalidation(schemaPaths: string[] = ['./schema']): Promise<void> {
    await this.invalidator.startWatching(schemaPaths);
    
    if (this.enableLogging) {
      console.log(`👁️ Started cache invalidation for paths: ${schemaPaths.join(', ')}`);
    }
  }

  /**
   * Stop automatic cache invalidation
   */
  async stopCacheInvalidation(): Promise<void> {
    await this.invalidator.stopWatching();
    
    if (this.enableLogging) {
      console.log('🛑 Stopped cache invalidation');
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(parseTime: number): void {
    if (!this.enablePerformanceTracking) return;

    this.metrics.totalParseTime += parseTime;
    this.metrics.averageParseTime = this.metrics.totalCalls > 0
      ? this.metrics.totalParseTime / this.metrics.totalCalls
      : 0;
  }

  /**
   * Create empty directive container
   */
  private createEmptyDirectiveContainer(): DirectiveContainer {
    return {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
  }

  /**
   * Enable or disable logging
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * Enable or disable performance tracking
   */
  setPerformanceTracking(enabled: boolean): void {
    this.enablePerformanceTracking = enabled;
  }
}

// Global optimized parser instance
let globalOptimizedDirectiveParser: OptimizedDirectiveParser | null = null;

/**
 * Get the global optimized directive parser instance
 */
export function getGlobalOptimizedDirectiveParser(
  enableLogging: boolean = false,
  enablePerformanceTracking: boolean = true
): OptimizedDirectiveParser {
  if (!globalOptimizedDirectiveParser) {
    globalOptimizedDirectiveParser = new OptimizedDirectiveParser(enableLogging, enablePerformanceTracking);
  }
  return globalOptimizedDirectiveParser;
}

/**
 * Reset the global parser (useful for testing)
 */
export function resetGlobalOptimizedDirectiveParser(): void {
  if (globalOptimizedDirectiveParser) {
    globalOptimizedDirectiveParser.stopCacheInvalidation().catch(console.error);
  }
  globalOptimizedDirectiveParser = null;
}
