import { DirectiveContainer, ParsedDirective } from './directive-parser';
import { getGlobalCommentDirectiveCache } from './comment-directive-cache';

/**
 * Parsed schema structure for efficient directive extraction
 */
interface ParsedSchemaStructure {
  types: Map<string, TypeDefinition>;
  interfaces: Map<string, TypeDefinition>;
  unions: Map<string, TypeDefinition>;
  schema?: SchemaDefinition;
}

/**
 * Type definition with fields and directives
 */
interface TypeDefinition {
  name: string;
  kind: 'type' | 'interface' | 'union';
  startLine: number;
  endLine: number;
  directives: DirectiveContainer;
  fields: Map<string, FieldDefinition>;
}

/**
 * Field definition with directives
 */
interface FieldDefinition {
  name: string;
  line: number;
  directives: DirectiveContainer;
}

/**
 * Schema definition with directives
 */
interface SchemaDefinition {
  startLine: number;
  directives: DirectiveContainer;
}

/**
 * Batch result for directive extraction
 */
export interface BatchDirectiveResult {
  success: boolean;
  structure: ParsedSchemaStructure;
  parseTime: number;
  cacheHit: boolean;
}

/**
 * High-performance batch parser for comment directives
 * Parses entire schema files in one pass for maximum efficiency
 */
export class BatchDirectiveParser {
  private static readonly DIRECTIVE_REGEX = /^\s*#\s*@([a-zA-Z][a-zA-Z0-9_]*)\s*(?:\((.*)\))?\s*$/;
  private static readonly FIELD_DIRECTIVE_REGEX = 
    /\s*([a-zA-Z][a-zA-Z0-9_]*)\s*(\??)?\s*:\s*((?:[^,<\[\{]|\||<[^>]*>|\[[^\]]*\]|\{[^}]*\})+)(?:,|$)/g;

  private cache = getGlobalCommentDirectiveCache();
  private enableLogging: boolean;

  constructor(enableLogging: boolean = false) {
    this.enableLogging = enableLogging;
  }

  /**
   * Parse entire schema file and extract all directives in one pass
   */
  async parseSchemaFile(filePath: string): Promise<BatchDirectiveResult> {
    const startTime = Date.now();
    
    try {
      // Try to get from cache first
      const cacheKey = this.cache.generateDirectiveCacheKey(filePath, '__BATCH_PARSE__');
      
      // Get file content (cached or fresh)
      const content = await this.cache.getFileContent(filePath);
      if (!content) {
        return {
          success: false,
          structure: { types: new Map(), interfaces: new Map(), unions: new Map() },
          parseTime: Date.now() - startTime,
          cacheHit: false
        };
      }

      const fileHash = this.cache.generateFileHash(content);
      
      // Check if we have cached parsed structure
      const cachedStructure = this.cache.getDirectiveResults(cacheKey, fileHash);
      if (cachedStructure) {
        return {
          success: true,
          structure: this.deserializeStructure(cachedStructure),
          parseTime: Date.now() - startTime,
          cacheHit: true
        };
      }

      // Parse the schema structure
      const structure = this.parseSchemaStructure(content);
      
      // Cache the serialized structure
      const serializedStructure = this.serializeStructure(structure);
      this.cache.cacheDirectiveResults(cacheKey, serializedStructure, fileHash);

      const parseTime = Date.now() - startTime;
      
      if (this.enableLogging) {
        console.log(`📊 Batch parsed schema: ${filePath} (${parseTime}ms)`);
      }

      return {
        success: true,
        structure,
        parseTime,
        cacheHit: false
      };
    } catch (error) {
      if (this.enableLogging) {
        console.error(`❌ Error batch parsing schema ${filePath}:`, error);
      }
      
      return {
        success: false,
        structure: { types: new Map(), interfaces: new Map(), unions: new Map() },
        parseTime: Date.now() - startTime,
        cacheHit: false
      };
    }
  }

  /**
   * Get directives for a specific type or field from parsed structure
   */
  getDirectivesFromStructure(
    structure: ParsedSchemaStructure,
    typeName: string,
    fieldName?: string
  ): DirectiveContainer {


    // Check all type collections
    const typeCollections = [structure.types, structure.interfaces, structure.unions];

    for (const collection of typeCollections) {
      const typeDef = collection.get(typeName);
      if (typeDef) {
        if (fieldName) {
          // Return field directives
          const fieldDef = typeDef.fields.get(fieldName);



          return fieldDef?.directives || this.createEmptyDirectiveContainer();
        } else {
          // Return type directives
          return typeDef.directives;
        }
      }
    }



    return this.createEmptyDirectiveContainer();
  }

  /**
   * Parse schema content into structured format
   */
  private parseSchemaStructure(content: string): ParsedSchemaStructure {
    const lines = content.split('\n');
    const structure: ParsedSchemaStructure = {
      types: new Map(),
      interfaces: new Map(),
      unions: new Map()
    };



    let currentType: TypeDefinition | null = null;
    let pendingDirectives: DirectiveContainer = this.createEmptyDirectiveContainer();
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) {
        i++;
        continue;
      }

      // Parse directive comments
      if (line.startsWith('#')) {
        this.parseDirectiveLine(line, pendingDirectives);
        i++;
        continue;
      }

      // Parse schema definition
      if (line.startsWith('schema {')) {
        structure.schema = {
          startLine: i,
          directives: { ...pendingDirectives }
        };
        pendingDirectives = this.createEmptyDirectiveContainer();
        i++;
        continue;
      }

      // Parse type definitions
      const typeMatch = line.match(/^(type|interface|union)\s+([a-zA-Z][a-zA-Z0-9_]*)/);
      if (typeMatch) {
        const [, kind, typeName] = typeMatch;
        
        currentType = {
          name: typeName,
          kind: kind as 'type' | 'interface' | 'union',
          startLine: i,
          endLine: -1,
          directives: { ...pendingDirectives },
          fields: new Map()
        };

        // Add to appropriate collection
        if (kind === 'type') {
          structure.types.set(typeName, currentType);
        } else if (kind === 'interface') {
          structure.interfaces.set(typeName, currentType);
        } else if (kind === 'union') {
          structure.unions.set(typeName, currentType);
        }



        pendingDirectives = this.createEmptyDirectiveContainer();
        i++;
        continue;
      }



      // Parse field definitions within types
      if (currentType && line.includes(':') && !line.startsWith('}')) {
        const fieldMatch = line.match(/^([a-zA-Z][a-zA-Z0-9_]*)\s*(\([^)]*\))?\s*:\s*/);
        if (fieldMatch) {
          const fieldName = fieldMatch[1];



          currentType.fields.set(fieldName, {
            name: fieldName,
            line: i,
            directives: { ...pendingDirectives }
          });



          pendingDirectives = this.createEmptyDirectiveContainer();
        }
      }

      // End of type definition
      if (line === '}' && currentType) {
        currentType.endLine = i;
        currentType = null;
      }

      i++;
    }

    return structure;
  }

  /**
   * Parse a single directive line
   */
  private parseDirectiveLine(line: string, directives: DirectiveContainer): void {
    const match = line.match(BatchDirectiveParser.DIRECTIVE_REGEX);



    if (!match) return;

    const [raw, name, content = ''] = match;
    const directive: ParsedDirective = { name, content, raw };



    // Sort directive by type
    if (name === 'import') {
      directives.imports.push(directive);
    } else if (name === 'methodCall') {
      directives.methodCalls.push(directive);
    } else if (name === 'field') {
      this.parseFieldDirectiveFields(content, directives);
    } else {
      directives.others[name] ??= [];
      directives.others[name].push(directive);
    }
  }

  /**
   * Parse field directive fields
   */
  private parseFieldDirectiveFields(content: string, directives: DirectiveContainer): void {
    let match;
    BatchDirectiveParser.FIELD_DIRECTIVE_REGEX.lastIndex = 0;
    
    while ((match = BatchDirectiveParser.FIELD_DIRECTIVE_REGEX.exec(content)) !== null) {
      const [, fieldName, optional, fieldType] = match;
      
      directives.fieldFields.push({
        name: fieldName,
        type: fieldType.trim(),
        raw: `@field(${fieldName}${optional || ''}: ${fieldType.trim()})`
      });
    }
  }

  /**
   * Create empty directive container
   */
  private createEmptyDirectiveContainer(): DirectiveContainer {
    return {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
  }

  /**
   * Serialize structure for caching
   */
  private serializeStructure(structure: ParsedSchemaStructure): DirectiveContainer {
    // Convert structure to a serializable format
    const serialized: any = {
      types: Array.from(structure.types.entries()),
      interfaces: Array.from(structure.interfaces.entries()),
      unions: Array.from(structure.unions.entries()),
      schema: structure.schema
    };

    // Convert Maps to arrays for each type
    for (const [typeName, typeDef] of serialized.types) {
      typeDef.fields = Array.from(typeDef.fields.entries());
    }
    for (const [typeName, typeDef] of serialized.interfaces) {
      typeDef.fields = Array.from(typeDef.fields.entries());
    }
    for (const [typeName, typeDef] of serialized.unions) {
      typeDef.fields = Array.from(typeDef.fields.entries());
    }

    // Store in directive container format for caching
    return {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: { __BATCH_STRUCTURE__: [{ name: '__BATCH_STRUCTURE__', content: JSON.stringify(serialized), raw: '' }] }
    };
  }

  /**
   * Deserialize structure from cache
   */
  private deserializeStructure(cached: DirectiveContainer): ParsedSchemaStructure {
    const serializedData = cached.others.__BATCH_STRUCTURE__?.[0]?.content;
    if (!serializedData) {
      return { types: new Map(), interfaces: new Map(), unions: new Map() };
    }

    const serialized = JSON.parse(serializedData);
    
    const structure: ParsedSchemaStructure = {
      types: new Map(),
      interfaces: new Map(),
      unions: new Map(),
      schema: serialized.schema
    };

    // Restore Maps from arrays
    for (const [typeName, typeDef] of serialized.types) {
      typeDef.fields = new Map(typeDef.fields);
      structure.types.set(typeName, typeDef);
    }
    for (const [typeName, typeDef] of serialized.interfaces) {
      typeDef.fields = new Map(typeDef.fields);
      structure.interfaces.set(typeName, typeDef);
    }
    for (const [typeName, typeDef] of serialized.unions) {
      typeDef.fields = new Map(typeDef.fields);
      structure.unions.set(typeName, typeDef);
    }

    return structure;
  }

  /**
   * Get cache statistics
   */
  getCacheStatistics() {
    return this.cache.getStatistics();
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Global batch parser instance
let globalBatchDirectiveParser: BatchDirectiveParser | null = null;

/**
 * Get the global batch directive parser instance
 */
export function getGlobalBatchDirectiveParser(enableLogging: boolean = false): BatchDirectiveParser {
  if (!globalBatchDirectiveParser) {
    globalBatchDirectiveParser = new BatchDirectiveParser(enableLogging);
  }
  return globalBatchDirectiveParser;
}

/**
 * Reset the global parser (useful for testing)
 */
export function resetGlobalBatchDirectiveParser(): void {
  globalBatchDirectiveParser = null;
}
