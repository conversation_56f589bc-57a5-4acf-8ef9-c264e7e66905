import * as path from 'path';
import * as fs from 'fs-extra';
import type { GraphQLSchema, DocumentNode } from 'graphql';
import { parse } from 'graphql';
import * as glob from 'glob';
import _ from 'lodash';

/**
 * Normalize a path to use forward slashes for cross-platform compatibility
 */
function normalizePath(inputPath: string): string {
  return inputPath.replace(/\\/g, '/');
}

/**
 * Stores information about a GraphQL type and its location in the schema
 */
export interface TypeLocation {
  /** The name of the type */
  typeName: string;
  /** The source schema file this type was defined in */
  sourceFile: string;
  /** The directory containing the source file (relative to schema root) */
  sourceDir: string;
  /** The category of the type (query, mutation, interface, etc.) */
  category: 'query' | 'mutation' | 'interface' | 'union' | 'object' | 'input' | 'scalar' | 'enum' | 'subscription';
}

/**
 * Maps schema structure to output structure
 */
export class SchemaMapper {
  /** Map from type name to its location info */
  private typeLocationMap = new Map<string, TypeLocation>();
  /** The root schema directory */
  private _schemaRoot: string;
  /** The root output directory */
  private outputRoot: string;

  /**
   * Creates a new SchemaMapper
   * @param schemaPath Path to the schema directory or file
   * @param outputPath Path to the output directory
   */
  constructor(schemaPath: string, outputPath: string) {
    this._schemaRoot = this.resolveSchemaRoot(schemaPath);
    this.outputRoot = outputPath;
  }

  /**
   * Get the root schema directory
   */
  get schemaRoot(): string {
    return this._schemaRoot;
  }

  /**
   * Resolves the schema root directory
   */
  private resolveSchemaRoot(schemaPath: string): string {
    // Handle absolute paths first
    if (path.isAbsolute(schemaPath)) {
      // If schemaPath is a file, use its directory
      if (fs.existsSync(schemaPath) && fs.statSync(schemaPath).isFile()) {
        return path.dirname(schemaPath);
      }
      // If schemaPath is a directory, use it directly
      else if (fs.existsSync(schemaPath) && fs.statSync(schemaPath).isDirectory()) {
        return schemaPath;
      }
      // If it's a glob pattern, extract the base directory
      else if (schemaPath.includes('*')) {
        // Remove wildcard part to get base directory
        const baseDir = schemaPath.split('*')[0];
        return baseDir.endsWith('/') ? baseDir.slice(0, -1) : baseDir;
      }
      // Return the absolute path as-is if it doesn't exist (might be created later)
      return schemaPath;
    }

    const resolvedPath = path.resolve(process.cwd(), schemaPath);

    // If resolved path is a file, use its directory
    if (fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isFile()) {
      return path.dirname(resolvedPath);
    }
    // If resolved path is a directory, use it directly
    else if (fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory()) {
      return resolvedPath;
    }
    // If it's a glob pattern, extract the base directory
    else if (schemaPath.includes('*')) {
      // Remove wildcard part to get base directory
      const baseDir = resolvedPath.split('*')[0];
      return baseDir.endsWith('/') ? baseDir.slice(0, -1) : baseDir;
    }
    // Return the resolved path if it doesn't exist (might be created later)
    return resolvedPath;
  }

  /**
   * Analyzes schema files and builds a map of types to their source locations
   * @param schema The GraphQL schema
   * @returns Map of types to their locations
   */
  public analyzeSchema(schema: GraphQLSchema): Map<string, TypeLocation> {
    if (process.env.DEBUG_PARSER) {
      console.log(`Analyzing schema from root: ${this.schemaRoot}`);
    }

    // Find all GraphQL files in schema directory
    const schemaFiles = glob.sync(path.join(this.schemaRoot, '**', '*.{gql,graphql}'), { windowsPathsNoEscape: true });
    if (process.env.DEBUG_PARSER) {
      console.log(`Found ${schemaFiles.length} schema files`);
    }

    // Process each schema file
    for (const schemaFile of schemaFiles) {
      this.processSchemaFile(schemaFile, schema);
    }

    if (process.env.DEBUG_PARSER) {
      console.log(`Analyzed ${this.typeLocationMap.size} types from schema`);
    }
    return this.typeLocationMap;
  }

  /**
   * Process a single schema file to extract and map types
   * @param schemaFile Path to the schema file
   * @param _schema The GraphQL schema
   */
  private processSchemaFile(schemaFile: string, _schema: GraphQLSchema): void {
    try {
      // Read the schema file
      const schemaContent = fs.readFileSync(schemaFile, 'utf8');

      // Parse the file to a DocumentNode
      const document = parse(schemaContent);

      // Get relative path from schema root for cleaner reference
      // Use normalized paths for cross-platform compatibility
      const relativeSchemaPath = normalizePath(path.relative(this.schemaRoot, schemaFile));
      const sourceDir = normalizePath(path.dirname(relativeSchemaPath));

      // Extract types from the document
      this.extractTypesFromDocument(document, relativeSchemaPath, sourceDir);
    } catch (error: any) {
      console.error(`Error processing schema file ${schemaFile}: ${error.message}`);
    }
  }

  /**
   * Extract types from a GraphQL document
   * @param document The parsed GraphQL document
   * @param sourceFile The source file path
   * @param sourceDir The directory containing the source file
   */
  private extractTypesFromDocument(document: DocumentNode, sourceFile: string, sourceDir: string): void {
    // Process each definition in the document
    for (const definition of document.definitions) {
      if (definition.kind === 'ObjectTypeDefinition') {
        const typeName = definition.name.value;

        // Determine category based on type name pattern
        let category: TypeLocation['category'] = 'object';
        if (typeName.endsWith('Query')) category = 'query';
        else if (typeName.endsWith('Mutation')) category = 'mutation';
        else if (typeName.endsWith('Subscription')) category = 'subscription';

        // Store type location
        this.typeLocationMap.set(typeName, {
          typeName,
          sourceFile,
          sourceDir,
          category
        });

      } else if (definition.kind === 'InterfaceTypeDefinition') {
        const typeName = definition.name.value;

        // Store interface location
        this.typeLocationMap.set(typeName, {
          typeName,
          sourceFile,
          sourceDir,
          category: 'interface'
        });

      } else if (definition.kind === 'UnionTypeDefinition') {
        const typeName = definition.name.value;

        // Store union location
        this.typeLocationMap.set(typeName, {
          typeName,
          sourceFile,
          sourceDir,
          category: 'union'
        });

      } else if (definition.kind === 'InputObjectTypeDefinition') {
        const typeName = definition.name.value;

        // Store input type location
        this.typeLocationMap.set(typeName, {
          typeName,
          sourceFile,
          sourceDir,
          category: 'input'
        });
      }
      // Add other definition kinds as needed (enum, scalar, etc.)
    }
  }

  /**
   * Gets the output directory path for a type
   * @param typeName Name of the type
   * @returns Path to the output directory for this type
   */
  public getOutputPathForType(typeName: string): string | null {
    // Get the type location information
    const typeLocation = this.getTypeLocation(typeName);

    if (!typeLocation) {
      return null;
    }

    // Get the relative path from schema root to preserve directory structure
    const relativePath = normalizePath(path.relative(this.schemaRoot, path.dirname(path.join(this.schemaRoot, typeLocation.sourceFile))));

    // Get the filename without extension to add to the path
    const fileBaseName = path.basename(typeLocation.sourceFile, path.extname(typeLocation.sourceFile));

    // Based on the type category, determine the output path
    // Use normalized paths for Windows compatibility
    switch (typeLocation.category) {
      case 'query':
        // Preserve directory structure for query types
        return normalizePath(path.join(this.outputRoot, relativePath, this.convertToDirectoryName(typeName)));

      case 'mutation':
        // Preserve directory structure for mutation types
        return normalizePath(path.join(this.outputRoot, relativePath, this.convertToDirectoryName(typeName)));

      case 'subscription':
        // Preserve directory structure for subscription types
        return normalizePath(path.join(this.outputRoot, relativePath, this.convertToDirectoryName(typeName)));

      case 'interface':
        // Fully mirror schema structure by including file name in the path
        return normalizePath(path.join(this.outputRoot, relativePath, _.kebabCase(fileBaseName), this.convertToDirectoryName(typeName)));

      case 'union':
        // Fully mirror schema structure by including file name in the path
        return normalizePath(path.join(this.outputRoot, relativePath, _.kebabCase(fileBaseName), this.convertToDirectoryName(typeName)));

      case 'object':
        // For object types, preserve the original directory structure
        return normalizePath(path.join(this.outputRoot, relativePath));

      default:
        // For other types (scalar, enum, input), preserve directory structure
        return normalizePath(path.join(this.outputRoot, relativePath));
    }
  }

  /**
   * Convert a type name to a directory name (PascalCase -> kebab-case)
   */
  private convertToDirectoryName(typeName: string): string {
    return typeName
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
      .toLowerCase();
  }

  /**
   * Gets all type names that match a specific category
   * @param category The category to filter by
   * @returns Array of type names matching the category
   */
  public getTypesByCategory(category: TypeLocation['category']): string[] {
    return Array.from(this.typeLocationMap.entries())
      .filter(([_, location]) => location.category === category)
      .map(([typeName]) => typeName);
  }

  /**
   * Gets the type location for a specific type
   * @param typeName The name of the GraphQL type
   * @returns The type location or null if not found
   */
  public getTypeLocation(typeName: string): TypeLocation | null {
    return this.typeLocationMap.get(typeName) ?? null;
  }
}