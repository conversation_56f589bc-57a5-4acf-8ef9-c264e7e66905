import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import { DirectiveContainer } from './directive-parser';

/**
 * Cache entry for file content
 */
interface FileContentCacheEntry {
  content: string;
  mtime: number;
  size: number;
  lastAccessedAt: number;
  accessCount: number;
}

/**
 * Cache entry for directive results
 */
interface DirectiveCacheEntry {
  directives: DirectiveContainer;
  fileHash: string;
  lastAccessedAt: number;
  accessCount: number;
}

/**
 * Cache statistics for monitoring
 */
export interface CommentDirectiveCacheStatistics {
  fileContentCache: {
    hits: number;
    misses: number;
    hitRate: number;
    entryCount: number;
    totalMemoryUsage: number;
  };
  directiveCache: {
    hits: number;
    misses: number;
    hitRate: number;
    entryCount: number;
  };
  totalTimeSaved: number;
  averageParseTime: number;
}

/**
 * Configuration options for the comment directive cache
 */
export interface CommentDirectiveCacheOptions {
  maxFileContentEntries?: number;
  maxDirectiveEntries?: number;
  maxMemoryUsage?: number; // in bytes
  ttl?: number; // time to live in milliseconds
  enablePerformanceTracking?: boolean;
  enableLogging?: boolean;
}

/**
 * High-performance cache system for comment directive parsing
 * Implements LRU eviction with file modification time tracking
 */
export class CommentDirectiveCache {
  private fileContentCache = new Map<string, FileContentCacheEntry>();
  private directiveCache = new Map<string, DirectiveCacheEntry>();
  private fileContentAccessOrder: string[] = [];
  private directiveAccessOrder: string[] = [];
  
  private statistics = {
    fileContent: { hits: 0, misses: 0 },
    directive: { hits: 0, misses: 0 },
    totalTimeSaved: 0,
    parseTimes: [] as number[]
  };

  private parseTimes: number[] = [];

  private readonly options: Required<CommentDirectiveCacheOptions>;

  constructor(options: CommentDirectiveCacheOptions = {}) {
    this.options = {
      maxFileContentEntries: 100,
      maxDirectiveEntries: 500,
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      ttl: 30 * 60 * 1000, // 30 minutes
      enablePerformanceTracking: true,
      enableLogging: false,
      ...options,
    };

    if (this.options.enableLogging) {
      console.log('🚀 CommentDirectiveCache initialized with options:', this.options);
    }
  }

  /**
   * Get file content from cache or read from disk
   */
  async getFileContent(filePath: string): Promise<string | null> {
    const now = Date.now();
    
    try {
      // Check if file exists
      const stats = await fs.stat(filePath);
      const mtime = stats.mtimeMs;
      
      // Check cache first
      const cachedEntry = this.fileContentCache.get(filePath);
      if (cachedEntry && cachedEntry.mtime === mtime && !this.isExpired(cachedEntry.lastAccessedAt)) {
        // Cache hit
        cachedEntry.lastAccessedAt = now;
        cachedEntry.accessCount++;
        this.updateFileContentAccessOrder(filePath);
        this.statistics.fileContent.hits++;
        
        if (this.options.enableLogging) {
          console.log(`✅ File content cache hit: ${filePath}`);
        }
        
        return cachedEntry.content;
      }

      // Cache miss - read from disk
      this.statistics.fileContent.misses++;
      const readStart = Date.now();
      const content = await fs.readFile(filePath, 'utf8');
      const readTime = Date.now() - readStart;
      
      if (this.options.enablePerformanceTracking) {
        this.parseTimes.push(readTime);
        if (this.parseTimes.length > 1000) {
          this.parseTimes.shift();
        }
      }

      // Create cache entry
      const entry: FileContentCacheEntry = {
        content,
        mtime,
        size: Buffer.byteLength(content, 'utf8'),
        lastAccessedAt: now,
        accessCount: 1,
      };

      // Add to cache
      this.fileContentCache.set(filePath, entry);
      this.updateFileContentAccessOrder(filePath);
      
      // Perform eviction if needed
      this.evictFileContentIfNeeded();
      
      if (this.options.enableLogging) {
        console.log(`🔄 File content cached: ${filePath} (${readTime}ms)`);
      }
      
      return content;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`❌ Error reading file ${filePath}:`, error);
      }
      return null;
    }
  }

  /**
   * Get directive results from cache
   */
  getDirectiveResults(cacheKey: string, fileHash: string): DirectiveContainer | null {
    const cachedEntry = this.directiveCache.get(cacheKey);
    
    if (cachedEntry && cachedEntry.fileHash === fileHash && !this.isExpired(cachedEntry.lastAccessedAt)) {
      // Cache hit
      cachedEntry.lastAccessedAt = Date.now();
      cachedEntry.accessCount++;
      this.updateDirectiveAccessOrder(cacheKey);
      this.statistics.directive.hits++;
      
      if (this.options.enableLogging) {
        console.log(`✅ Directive cache hit: ${cacheKey}`);
      }
      
      return cachedEntry.directives;
    }
    
    this.statistics.directive.misses++;
    return null;
  }

  /**
   * Cache directive results
   */
  cacheDirectiveResults(cacheKey: string, directives: DirectiveContainer, fileHash: string): void {
    const entry: DirectiveCacheEntry = {
      directives,
      fileHash,
      lastAccessedAt: Date.now(),
      accessCount: 1,
    };

    this.directiveCache.set(cacheKey, entry);
    this.updateDirectiveAccessOrder(cacheKey);
    
    // Perform eviction if needed
    this.evictDirectiveIfNeeded();
    
    if (this.options.enableLogging) {
      console.log(`🔄 Directive results cached: ${cacheKey}`);
    }
  }

  /**
   * Generate cache key for directive results
   */
  generateDirectiveCacheKey(filePath: string, typeName: string, fieldName?: string): string {
    const key = `${filePath}:${typeName}${fieldName ? `:${fieldName}` : ''}`;
    return crypto.createHash('sha256').update(key).digest('hex').substring(0, 16);
  }

  /**
   * Generate hash for file content
   */
  generateFileHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(lastAccessedAt: number): boolean {
    return Date.now() - lastAccessedAt > this.options.ttl;
  }

  /**
   * Update file content access order for LRU
   */
  private updateFileContentAccessOrder(filePath: string): void {
    const index = this.fileContentAccessOrder.indexOf(filePath);
    if (index > -1) {
      this.fileContentAccessOrder.splice(index, 1);
    }
    this.fileContentAccessOrder.push(filePath);
  }

  /**
   * Update directive access order for LRU
   */
  private updateDirectiveAccessOrder(cacheKey: string): void {
    const index = this.directiveAccessOrder.indexOf(cacheKey);
    if (index > -1) {
      this.directiveAccessOrder.splice(index, 1);
    }
    this.directiveAccessOrder.push(cacheKey);
  }

  /**
   * Evict file content entries if needed
   */
  private evictFileContentIfNeeded(): void {
    // Check entry count limit
    while (this.fileContentCache.size > this.options.maxFileContentEntries) {
      const oldestKey = this.fileContentAccessOrder.shift();
      if (oldestKey) {
        this.fileContentCache.delete(oldestKey);
      }
    }

    // Check memory usage limit
    let totalMemory = 0;
    for (const entry of this.fileContentCache.values()) {
      totalMemory += entry.size;
    }

    while (totalMemory > this.options.maxMemoryUsage && this.fileContentAccessOrder.length > 0) {
      const oldestKey = this.fileContentAccessOrder.shift();
      if (oldestKey) {
        const entry = this.fileContentCache.get(oldestKey);
        if (entry) {
          totalMemory -= entry.size;
          this.fileContentCache.delete(oldestKey);
        }
      }
    }
  }

  /**
   * Evict directive entries if needed
   */
  private evictDirectiveIfNeeded(): void {
    while (this.directiveCache.size > this.options.maxDirectiveEntries) {
      const oldestKey = this.directiveAccessOrder.shift();
      if (oldestKey) {
        this.directiveCache.delete(oldestKey);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStatistics(): CommentDirectiveCacheStatistics {
    const fileContentHits = this.statistics.fileContent.hits;
    const fileContentMisses = this.statistics.fileContent.misses;
    const fileContentTotal = fileContentHits + fileContentMisses;
    
    const directiveHits = this.statistics.directive.hits;
    const directiveMisses = this.statistics.directive.misses;
    const directiveTotal = directiveHits + directiveMisses;

    let totalMemoryUsage = 0;
    for (const entry of this.fileContentCache.values()) {
      totalMemoryUsage += entry.size;
    }

    const averageParseTime = this.parseTimes.length > 0 
      ? this.parseTimes.reduce((sum, time) => sum + time, 0) / this.parseTimes.length 
      : 0;

    return {
      fileContentCache: {
        hits: fileContentHits,
        misses: fileContentMisses,
        hitRate: fileContentTotal > 0 ? Math.round((fileContentHits / fileContentTotal) * 100) : 0,
        entryCount: this.fileContentCache.size,
        totalMemoryUsage,
      },
      directiveCache: {
        hits: directiveHits,
        misses: directiveMisses,
        hitRate: directiveTotal > 0 ? Math.round((directiveHits / directiveTotal) * 100) : 0,
        entryCount: this.directiveCache.size,
      },
      totalTimeSaved: this.statistics.totalTimeSaved,
      averageParseTime,
    };
  }

  /**
   * Clear all caches
   */
  clear(): void {
    this.fileContentCache.clear();
    this.directiveCache.clear();
    this.fileContentAccessOrder = [];
    this.directiveAccessOrder = [];
    this.statistics = {
      fileContent: { hits: 0, misses: 0 },
      directive: { hits: 0, misses: 0 },
      totalTimeSaved: 0,
      parseTimes: []
    };
    this.parseTimes = [];
    
    if (this.options.enableLogging) {
      console.log('🧹 CommentDirectiveCache cleared');
    }
  }

  /**
   * Invalidate cache entries for a specific file
   */
  invalidateFile(filePath: string): void {
    // Remove from file content cache
    this.fileContentCache.delete(filePath);
    const fileIndex = this.fileContentAccessOrder.indexOf(filePath);
    if (fileIndex > -1) {
      this.fileContentAccessOrder.splice(fileIndex, 1);
    }

    // Remove related directive cache entries
    const keysToRemove: string[] = [];
    for (const [key] of this.directiveCache) {
      if (key.startsWith(crypto.createHash('sha256').update(filePath).digest('hex').substring(0, 8))) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      this.directiveCache.delete(key);
      const directiveIndex = this.directiveAccessOrder.indexOf(key);
      if (directiveIndex > -1) {
        this.directiveAccessOrder.splice(directiveIndex, 1);
      }
    }

    if (this.options.enableLogging) {
      console.log(`🗑️ Cache invalidated for file: ${filePath}`);
    }
  }
}

// Global cache instance
let globalCommentDirectiveCache: CommentDirectiveCache | null = null;

/**
 * Get the global comment directive cache instance
 */
export function getGlobalCommentDirectiveCache(options?: CommentDirectiveCacheOptions): CommentDirectiveCache {
  if (!globalCommentDirectiveCache) {
    globalCommentDirectiveCache = new CommentDirectiveCache(options);
  }
  return globalCommentDirectiveCache;
}

/**
 * Reset the global cache (useful for testing)
 */
export function resetGlobalCommentDirectiveCache(): void {
  globalCommentDirectiveCache = null;
}
