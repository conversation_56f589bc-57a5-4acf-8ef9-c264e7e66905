import type { ParsedDirective } from '@utils/directive-parser';
import { DirectiveParser } from '@utils/directive-parser';
import { DirectiveRegistry } from '@utils/directive-registry';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Validation result for a single directive
 */
export interface DirectiveValidationResult {
    directive: ParsedDirective;
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Validation result for a schema file
 */
export interface SchemaValidationResult {
    schemaFile: string;
    directives: DirectiveValidationResult[];
    isValid: boolean;
}

/**
 * Options for directive validation
 */
export interface ValidationOptions {
    schemaPath: string;
    directiveNames?: string[];
    strict?: boolean;
    includeWarnings?: boolean;
}

/**
 * Validates directives in GraphQL schema files
 */
export class DirectiveValidator {
    /**
     * Validate all directives in schema files matching a pattern
     * @param options Validation options
     * @returns Validation results for all schema files
     */
    public static async validateDirectives(
        options: ValidationOptions
    ): Promise<SchemaValidationResult[]> {
        const {
            schemaPath,
            directiveNames,
            strict = false,
            includeWarnings = true
        } = options;

        try {
            // Find all schema files
            const schemaFiles = await this.findSchemaFiles(schemaPath);

            if (schemaFiles.length === 0) {
                console.warn(`No schema files found matching pattern: ${schemaPath}`);
                return [];
            }

            // Validate each schema file
            const results: SchemaValidationResult[] = [];

            for (const schemaFile of schemaFiles) {
                const result = await this.validateSchemaFile(schemaFile, {
                    directiveNames,
                    strict,
                    includeWarnings
                });

                results.push(result);
            }

            return results;
        } catch (error) {
            console.error(`Error validating directives: ${error}`);
            return [];
        }
    }

    /**
     * Validate directives in a single schema file
     * @param schemaFile Path to the schema file
     * @param options Validation options
     * @returns Validation result for the schema file
     */
    public static async validateSchemaFile(
        schemaFile: string,
        options: Omit<ValidationOptions, 'schemaPath'>
    ): Promise<SchemaValidationResult> {
        const { directiveNames, strict = false, includeWarnings = true } = options;

        try {
            // Read the schema file
            const schemaContent = await fs.promises.readFile(schemaFile, 'utf8');
            const lines = schemaContent.split('\n');

            // Find all directive comments
            const directiveLines = lines.filter(line => {
                const trimmed = line.trim();
                return trimmed.startsWith('#') && trimmed.includes('@');
            });

            // Parse and validate each directive
            const directiveResults: DirectiveValidationResult[] = [];

            for (const line of directiveLines) {
                // Extract directive using regex
                const match = line.match(DirectiveParser['DIRECTIVE_REGEX']);

                if (!match) {
                    if (strict) {
                        directiveResults.push({
                            directive: { name: 'unknown', content: line, raw: line },
                            isValid: false,
                            errors: ['Invalid directive syntax'],
                            warnings: []
                        });
                    }
                    continue;
                }

                const [raw, name, content = ''] = match;
                const directive: ParsedDirective = { name, content, raw };

                // Skip if we're only validating specific directives
                if (directiveNames && !directiveNames.includes(name)) {
                    continue;
                }

                // Validate the directive
                const validationResult = this.validateDirective(directive);

                // Only include warnings if requested
                if (!includeWarnings) {
                    validationResult.warnings = [];
                }

                directiveResults.push(validationResult);
            }

            // Determine if the schema file is valid overall
            const isValid = directiveResults.every(result => result.isValid);

            return {
                schemaFile,
                directives: directiveResults,
                isValid
            };
        } catch (error) {
            console.error(`Error validating schema file ${schemaFile}: ${error}`);

            return {
                schemaFile,
                directives: [],
                isValid: false
            };
        }
    }

    /**
     * Validate a single directive
     * @param directive The directive to validate
     * @returns Validation result
     */
    public static validateDirective(directive: ParsedDirective): DirectiveValidationResult {
        const result: DirectiveValidationResult = {
            directive,
            isValid: true,
            errors: [],
            warnings: []
        };

        // Check if we have a handler for this directive type
        const handlers = DirectiveRegistry.getHandlers(directive.name);

        if (handlers.length === 0) {
            result.warnings.push(`No registered handler for directive type '@${directive.name}'`);
        } else {
            // Validate with each handler
            let validatedByAny = false;

            for (const handler of handlers) {
                if (handler.validate) {
                    try {
                        const isValid = handler.validate(directive);

                        if (isValid) {
                            validatedByAny = true;
                        }
                    } catch (error) {
                        result.errors.push(`Validation error in handler '${handler.directiveName}': ${error}`);
                        result.isValid = false;
                    }
                }
            }

            if (!validatedByAny && handlers.some(h => h.validate)) {
                result.errors.push(`Directive '@${directive.name}' failed validation with all handlers`);
                result.isValid = false;
            }
        }

        // Directive-specific validation
        switch (directive.name) {
            case 'import':
                this.validateImportDirective(directive, result);
                break;

            case 'methodCall':
                this.validateMethodCallDirective(directive, result);
                break;

            case 'resolver':
                this.validateResolverDirective(directive, result);
                break;

            case 'context':
                this.validateContextDirective(directive, result);
                break;

            case 'field':
                this.validateFieldDirective(directive, result);
                break;

            case 'alias':
                this.validateAliasDirective(directive, result);
                break;

            default:
                // No specific validation for other directives
                break;
        }

        return result;
    }

    /**
     * Validate an import directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateImportDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // Check for valid import statement syntax
        if (!content.startsWith('import ')) {
            result.errors.push(`Import directive must start with 'import': ${content}`);
            result.isValid = false;
        }

        // Check for import from syntax
        if (!content.includes(' from ')) {
            result.errors.push(`Import directive must include ' from ': ${content}`);
            result.isValid = false;
        }

        // Check for quoted path
        const pathMatch = content.match(/from\s+["']([^"']+)["']/);
        if (!pathMatch) {
            result.errors.push(`Import directive path must be quoted: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Validate a methodCall directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateMethodCallDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // For methodCall directives, we accept any valid JavaScript expression
        // that contains function calls, including:
        // - Simple calls: methodName(args)
        // - Property access: obj.method(args)
        // - Async calls: await obj.method(args)
        // - Complex expressions: await context.dataSources.userAPI.getById(obj.authorId)

        // Check if the content contains at least one function call (has parentheses)
        if (!content.includes('(') || !content.includes(')')) {
            result.errors.push(`MethodCall directive must contain a function call with parentheses: ${content}`);
            result.isValid = false;
        }

        // Check for balanced parentheses
        const openCount = (content.match(/\(/g) || []).length;
        const closeCount = (content.match(/\)/g) || []).length;

        if (openCount !== closeCount) {
            result.errors.push(`MethodCall directive has unbalanced parentheses: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Validate a resolver directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateResolverDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // Check that it contains a valid function-like expression
        if (!content.includes('(') || !content.includes(')')) {
            result.errors.push(`Resolver directive must include a function call or expression: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Validate a context directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateContextDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // Check for path property
        if (!content.includes('path:')) {
            result.errors.push(`Context directive must include 'path' property: ${content}`);
            result.isValid = false;
        }

        // Check for name property
        if (!content.includes('name:')) {
            result.errors.push(`Context directive must include 'name' property: ${content}`);
            result.isValid = false;
        }

        // Check for quoted values
        const pathMatch = content.match(/path:\s*["']([^"']+)["']/);
        const nameMatch = content.match(/name:\s*["']([^"']+)["']/);

        if (!pathMatch) {
            result.errors.push(`Context directive path must be quoted: ${content}`);
            result.isValid = false;
        }

        if (!nameMatch) {
            result.errors.push(`Context directive name must be quoted: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Validate a field directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateFieldDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // Check for field definition format: field: Type
        const fieldDirectiveRegex = /\s*([a-zA-Z][a-zA-Z0-9_]*)\s*:\s*([^,]+)(?:,|$)/g;
        let match;
        let foundValid = false;

        while ((match = fieldDirectiveRegex.exec(content)) !== null) {
            foundValid = true;
            const [, fieldName, fieldType] = match;

            // Check for valid field name
            if (!fieldName || !fieldName.match(/^[a-zA-Z][a-zA-Z0-9_]*$/)) {
                result.errors.push(`Field directive has invalid field name: ${fieldName}`);
                result.isValid = false;
            }

            // Check for valid field type
            if (!fieldType || fieldType.trim() === '') {
                result.errors.push(`Field directive field '${fieldName}' has empty type`);
                result.isValid = false;
            }

            // Check for import path format if present
            if (fieldType.includes('"')) {
                const importMatch = fieldType.match(/"([^"]+)"/);
                if (!importMatch) {
                    result.errors.push(`Field directive field '${fieldName}' has invalid import path format: ${fieldType}`);
                    result.isValid = false;
                }
            }
        }

        if (!foundValid) {
            result.errors.push(`Field directive must contain at least one valid field definition: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Validate an alias directive
     * @param directive The directive to validate
     * @param result The validation result to update
     */
    private static validateAliasDirective(
        directive: ParsedDirective,
        result: DirectiveValidationResult
    ): void {
        const content = directive.content.trim();

        // Check for path property
        if (!content.includes('path:')) {
            result.errors.push(`Alias directive must include 'path' property: ${content}`);
            result.isValid = false;
        }

        // Check for quoted path
        const pathMatch = content.match(/path:\s*["']([^"']+)["']/);
        if (!pathMatch) {
            result.errors.push(`Alias directive path must be quoted: ${content}`);
            result.isValid = false;
        }
    }

    /**
     * Find all schema files matching a pattern
     * @param pattern Glob pattern for schema files
     * @returns List of matching schema file paths
     */
    private static async findSchemaFiles(pattern: string): Promise<string[]> {
        // Simple implementation that looks for .gql files in the specified directory
        // For advanced glob pattern matching, proper glob library integration would be needed
        try {
            // Extract directory from pattern by removing glob characters
            const baseDir = pattern.replace(/\*\*\/|\*\.gql$|\*/, '');

            if (!fs.existsSync(baseDir)) {
                return [];
            }

            // Get all files in directory recursively
            const files = await this.getAllFiles(baseDir);

            // Filter for .gql files
            return files.filter(file => file.endsWith('.gql') || file.endsWith('.graphql'));
        } catch (error) {
            console.error(`Error finding schema files: ${error}`);
            return [];
        }
    }

    /**
     * Get all files in a directory recursively
     * @param dir Directory to search
     * @returns List of file paths
     */
    private static async getAllFiles(dir: string): Promise<string[]> {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        // Process all entries in parallel
        const filePromises = entries.map(async entry => {
            const res = path.resolve(dir, entry.name);
            return entry.isDirectory() ? await this.getAllFiles(res) : res;
        });

        // Wait for all promises and flatten the results
        const files = await Promise.all(filePromises);
        return files.flat();
    }

    /**
     * Format validation results for display
     * @param results Validation results
     * @param includeWarnings Whether to include warnings in the output
     * @returns Formatted string of validation results
     */
    public static formatValidationResults(
        results: SchemaValidationResult[],
        includeWarnings: boolean = true
    ): string {
        let output = '';
        let totalErrors = 0;
        let totalWarnings = 0;

        for (const result of results) {
            const fileErrors = result.directives.flatMap(d => d.errors);
            const fileWarnings = includeWarnings ? result.directives.flatMap(d => d.warnings) : [];

            totalErrors += fileErrors.length;
            totalWarnings += fileWarnings.length;

            if (fileErrors.length > 0 || fileWarnings.length > 0) {
                output += `\nFile: ${result.schemaFile}\n`;

                if (fileErrors.length > 0) {
                    output += `  Errors (${fileErrors.length}):\n`;
                    fileErrors.forEach(error => {
                        output += `    - ${error}\n`;
                    });
                }

                if (fileWarnings.length > 0) {
                    output += `  Warnings (${fileWarnings.length}):\n`;
                    fileWarnings.forEach(warning => {
                        output += `    - ${warning}\n`;
                    });
                }
            }
        }

        // Add summary
        output += '\n==== Summary ====\n';
        output += `Files analyzed: ${results.length}\n`;
        output += `Total errors: ${totalErrors}\n`;

        if (includeWarnings) {
            output += `Total warnings: ${totalWarnings}\n`;
        }

        return output;
    }
} 