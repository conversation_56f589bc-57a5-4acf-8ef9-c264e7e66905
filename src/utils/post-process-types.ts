import * as fs from 'fs-extra';
import * as glob from 'glob';
import * as path from 'path';
import type { FieldDirectiveField } from './directive-parser';
import { FieldDirectiveProcessor } from './field-directive-processor';
import { processAllTypesWithFieldDirectives } from './type-processors';
import { WatchedFileWriter } from './watched-file-writer';
import type { DecoratorContainer } from './decorator-parser';
import { DecoratorProcessor } from './decorator-processor';
import { DirectiveProcessor } from './directive-processor';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from './decorator-types';
import { AliasConfig, AliasConfigUtils } from './alias-config';
import type { TypeMap, FieldInfo, MethodCallInfo } from './type-map';

/**
 * Normalize a path to use forward slashes consistently across platforms
 * @param inputPath Path to normalize
 * @returns Normalized path with forward slashes
 */
function normalizePath(inputPath: string): string {
  return inputPath.replace(/\\/g, '/');
}

/**
 * Post-processes TypeScript types generated from GraphQL schema
 * to add fields from @field directives and @GQLField decorators
 * @param typesDtsPath Path to generated types.d.ts file
 * @param schemaPath Path to schema directory
 * @param debug Enable debug logging
 * @param decoratorMetadata Optional decorator metadata for @GQLField processing
 * @param codebaseDir Optional codebase directory for alias configuration
 * @param aliasCodebase Optional alias for codebase imports
 * @param typeMap Optional pre-built type map for two-phase processing
 */
export async function postProcessTypes(
  typesDtsPath: string,
  schemaPath: string,
  debug: boolean = false,
  decoratorMetadata?: DecoratorContainer,
  codebaseDir?: string,
  aliasCodebase?: string,
  typeMap?: TypeMap
): Promise<void> {
  console.log(`Post-processing types: ${typesDtsPath}`);
  console.log(`Schema path: ${schemaPath}`);

  try {
    // Check if the types file exists
    if (!fs.existsSync(typesDtsPath)) {
      console.error(`Types file not found: ${typesDtsPath}`);
      return;
    }

    // Read the generated types file
    const typesContent = fs.readFileSync(typesDtsPath, 'utf8');
    const lines = typesContent.split('\n');

    // Use type map if available (two-phase processing)
    if (typeMap) {
      console.log('Using pre-built type map for post-processing');
      await postProcessWithTypeMap(lines, typeMap, typesDtsPath, debug);
      return;
    }

    // Find all schema files
    let schemaFiles: string[] = [];

    // Try multiple patterns to find schema files
    const normalizedSchemaPath = normalizePath(schemaPath);
    console.log(`Normalized schema path: ${normalizedSchemaPath}`);

    if (fs.existsSync(normalizedSchemaPath)) {
      if (fs.statSync(normalizedSchemaPath).isDirectory()) {
        console.log(`Schema path is a directory: ${normalizedSchemaPath}`);
        const globPattern = normalizePath(path.join(normalizedSchemaPath, '**/*.gql'));
        console.log(`Using glob pattern: ${globPattern}`);
        schemaFiles = glob.sync(globPattern, { windowsPathsNoEscape: true });
      } else {
        console.log(`Schema path is a file: ${normalizedSchemaPath}`);
        schemaFiles = [normalizedSchemaPath];
      }
    } else {
      // Try to resolve schema paths
      console.log(`Schema path doesn't exist directly: ${normalizedSchemaPath}`);

      // Try common patterns
      const possiblePatterns = [
        normalizedSchemaPath,
        `${normalizedSchemaPath}.gql`,
        normalizePath(path.join(process.cwd(), normalizedSchemaPath)),
        normalizePath(path.join(process.cwd(), `${normalizedSchemaPath}.gql`)),
        normalizePath(path.join(process.cwd(), 'schema', '**/*.gql')),
      ];

      for (const pattern of possiblePatterns) {
        console.log(`Trying pattern: ${pattern}`);
        const files = glob.sync(pattern, { windowsPathsNoEscape: true });
        if (files.length > 0) {
          console.log(`Found schema files with pattern: ${pattern}`);
          schemaFiles = files;
          break;
        }
      }
    }

    console.log(`Found ${schemaFiles.length} schema files`);

    // Always log schema files in debug mode or if none were found (to help diagnose issues)
    if (debug || schemaFiles.length === 0) {
      console.log('Current working directory:', process.cwd());
      console.log('Platform:', process.platform);

      if (schemaFiles.length === 0) {
        console.log('WARNING: No schema files found initially. Trying additional patterns...');

        // Try additional patterns specifically for Windows compatibility
        const additionalPatterns = [
          // Try with different slashes
          normalizedSchemaPath.replace(/\//g, '\\'),
          // Try with both forward and backward slashes for glob patterns
          normalizedSchemaPath.replace(/\//g, '/') + '/**/*.gql',
          normalizedSchemaPath.replace(/\//g, '\\') + '\\**\\*.gql',
          // Try absolute paths with different slash styles
          path.resolve(process.cwd(), normalizedSchemaPath),
          path.resolve(process.cwd(), 'schema'),
          // Try common schema locations
          path.resolve(process.cwd(), 'src/schema/**/*.gql'),
          path.resolve(process.cwd(), 'schema/**/*.gql'),
          './schema/**/*.gql',
          './src/schema/**/*.gql',
          '**/*.gql', // Last resort: try to find any .gql files
        ];

        console.log('Trying additional patterns for Windows compatibility:');
        for (const pattern of additionalPatterns) {
          const normalizedPattern = normalizePath(pattern);
          console.log(`  - ${normalizedPattern}`);
          const files = glob.sync(normalizedPattern, {
            windowsPathsNoEscape: true,
            absolute: true, // Use absolute paths to avoid relative path issues
          });

          if (files.length > 0) {
            console.log(`Found ${files.length} schema files with pattern: ${normalizedPattern}`);
            schemaFiles = files;
            break;
          }
        }

        if (schemaFiles.length === 0) {
          console.log(
            'WARNING: Still no schema files found. This will prevent @field directives from working.'
          );
          console.log('Original tried patterns:');
          console.log(`  - ${normalizedSchemaPath}`);
          console.log(`  - ${normalizedSchemaPath}.gql`);
          console.log(`  - ${normalizePath(path.join(process.cwd(), normalizedSchemaPath))}`);
          console.log(
            `  - ${normalizePath(path.join(process.cwd(), `${normalizedSchemaPath}.gql`))}`
          );
          console.log(`  - ${normalizePath(path.join(process.cwd(), 'schema', '**/*.gql'))}`);
        }
      }

      // Log found schema files
      if (schemaFiles.length > 0) {
        console.log(`Found ${schemaFiles.length} schema files:`);
        schemaFiles.forEach((file, index) => {
          console.log(`  ${index + 1}. ${file}`);
        });
      }
    }

    // Map to store field directive fields for each type
    const typeToFieldFields = new Map<string, FieldDirectiveField[]>();

    // Process all types with @field directives from schema files
    if (schemaFiles.length > 0) {
      console.log(`Processing ${schemaFiles.length} schema files for @field directives...`);
      await processAllTypesWithFieldDirectives(schemaFiles, typeToFieldFields, true); // Always use debug=true here
    } else {
      console.warn('No schema files to process. @field directives will not be applied.');
    }

    // Process @GQLField decorators if decorator metadata is provided
    if (decoratorMetadata) {
      console.log(`Processing ${decoratorMetadata.fields.length} @GQLField decorators...`);

      // Create a decorator processor to convert decorators to field directive fields
      const decoratorProcessor = new DecoratorProcessor(
        { ...DEFAULT_DECORATOR_CONFIG, enabled: true },
        DEFAULT_PRECEDENCE_RULES
      );

      // Set alias configuration if provided
      const aliasConfig = AliasConfigUtils.createAliasConfig(codebaseDir, aliasCodebase);
      decoratorProcessor.setAliasConfig(aliasConfig || undefined);

      // Convert decorator metadata to directive container
      const decoratorDirectives = decoratorProcessor.convertToDirectiveContainer(decoratorMetadata);

      // Add decorator-based field directive fields to the map
      for (const fieldField of decoratorDirectives.fieldFields) {
        // Find the original @GQLField decorator to get the ref (type name)
        const matchingFieldDecorator = decoratorMetadata.fields.find(fd =>
          fd.data.name === fieldField.name && fd.data.type === fieldField.type
        );

        if (matchingFieldDecorator) {
          const typeName = matchingFieldDecorator.data.ref;

          // Get existing fields for this type or create new array
          const existingFields = typeToFieldFields.get(typeName) || [];

          // Add the field if it doesn't already exist
          const fieldExists = existingFields.some(existing => existing.name === fieldField.name);
          if (!fieldExists) {
            existingFields.push(fieldField);
            typeToFieldFields.set(typeName, existingFields);

            if (debug) {
              console.log(`Added @GQLField decorator field: ${typeName}.${fieldField.name}: ${fieldField.type}`);
            }
          }
        }
      }
    }

    console.log(`Found ${typeToFieldFields.size} types with field directive fields`);
    for (const [typeName, fieldFields] of typeToFieldFields.entries()) {
      console.log(`Type ${typeName} has ${fieldFields.length} field directive fields:`);
      fieldFields.forEach((field, index) => {
        console.log(
          `  ${index + 1}. ${field.name}: ${field.type}${field.importPath ? ` (from ${field.importPath})` : ''}`
        );
      });
    }

    // Process @field directives
    if (typeToFieldFields.size > 0) {
      console.log(`Processing @field directives for ${typeToFieldFields.size} types...`);
      await processFieldDirectives(lines, typeToFieldFields, debug);
    }

    // If no fields found, we're done
    if (typeToFieldFields.size === 0) {
      console.log('No @field directives found, skipping post-processing');
      return;
    }

    // Write the modified content back to the file
    const modifiedContent = lines.join('\n');
    WatchedFileWriter.writeFileSync(typesDtsPath, modifiedContent);

    if (debug) {
      console.log(`Post-processing completed. File written with ${lines.length} lines.`);

      // Verify the changes were applied by checking for duplicates
      const verificationLines = modifiedContent.split('\n');
      for (const [typeName] of typeToFieldFields.entries()) {
        const typeDefIndex = findMainTypeDefinition(verificationLines, typeName);
        if (typeDefIndex !== -1) {
          const typeEndIndex = findTypeDefEndIndex(verificationLines, typeDefIndex);
          const typeContent = verificationLines.slice(typeDefIndex, typeEndIndex + 1).join('\n');
          console.log(`Verification - ${typeName} type definition:\n${typeContent}`);
        }
      }
    }

    console.log('Post-processing completed successfully.');
  } catch (error) {
    console.error('Error during post-processing:', error);
    throw error;
  }
}

/**
 * Process @field directives by adding fields to main type definitions and ResolversParentTypes
 * For interfaces: @field directive fields are added directly to the interface type definition
 */
async function processFieldDirectives(
  lines: string[],
  typeToFieldFields: Map<string, FieldDirectiveField[]>,
  debug: boolean = false
): Promise<void> {
  console.log(`[DEBUG] Processing @field directives for ${typeToFieldFields.size} types...`);
  for (const [typeName, fieldFields] of typeToFieldFields.entries()) {
    console.log(`[DEBUG] Type: ${typeName} has ${fieldFields.length} field directive fields`);
  }

  // Collect all import statements first to deduplicate them
  const allImportStatements: string[] = [];
  const allFieldFields: FieldDirectiveField[] = [];

  // Collect all field directive fields from all types
  for (const [typeName, fieldFields] of typeToFieldFields.entries()) {
    allFieldFields.push(...fieldFields);
  }

  // Generate deduplicated import statements for all field directive fields
  if (allFieldFields.length > 0) {
    const importStatements = FieldDirectiveProcessor.generateImports(allFieldFields);
    allImportStatements.push(...importStatements);

    // Add all import statements at once to the import section BEFORE processing any types
    // This ensures that line numbers remain consistent throughout the processing
    if (allImportStatements.length > 0) {
      const importSectionEndIndex = findImportSectionEndIndex(lines);
      lines.splice(importSectionEndIndex, 0, ...allImportStatements);

      if (debug) {
        console.log(`Added ${allImportStatements.length} import statements at line ${importSectionEndIndex + 1}. All subsequent line numbers shifted down by ${allImportStatements.length}.`);
      }
    }
  }

  // Track which types were originally union types with @field directives
  const originalUnionTypesWithFieldDirectives = new Set<string>();

  for (const [typeName, fieldFields] of typeToFieldFields.entries()) {
    if (debug) {
      console.log(`Processing @field directives for type: ${typeName} (${fieldFields.length} fields)`);
      fieldFields.forEach((field, index) => {
        console.log(`  Field ${index + 1}: ${field.name}: ${field.type}`);
      });
    }

    // Detect and resolve duplicate field conflicts before processing
    await resolveDuplicateFieldConflicts(lines, typeName, fieldFields, debug);

    // Find the type definition to determine its kind
    const typeDefIndex = findMainTypeDefinition(lines, typeName);
    // Check if this is a union type
    const isUnion = isUnionType(lines, typeDefIndex);
    if (isUnion) {
      // Remember that this type was originally a union type with @field directives
      originalUnionTypesWithFieldDirectives.add(typeName);
      if (debug) {
        console.log(`Type ${typeName} is a union type, applying union-specific @field directive processing`);
      }
      // Process union types with @field directives
      await processFieldDirectivesForUnionType(lines, typeName, fieldFields, typeDefIndex, debug);
    } else if (isObjectOrInterfaceType(lines, typeDefIndex)) {
      if (debug) {
        console.log(`Type ${typeName} is an object/interface type, applying standard @field directive processing`);
      }
      // Process field-level overrides and additions for main type definitions (without adding imports)
      await processFieldDirectivesForType(lines, typeName, fieldFields, debug, false);
    } else {
      if (debug) {
        console.log(`Type ${typeName} definition not found or unsupported type structure, skipping @field directive processing`);
      }
      continue;
    }

    // Process ResolversParentTypes for object/interface types and union types with @field directives
    const isObjectOrInterface = isObjectOrInterfaceType(lines, typeDefIndex);
    const wasOriginallyUnion = originalUnionTypesWithFieldDirectives.has(typeName);

    if (wasOriginallyUnion) {
      // Types that were originally union types with @field directives need their ResolversParentTypes entry
      // to point directly to the intersection type instead of ResolversUnionTypes, so that resolver functions
      // can access the additional fields (e.g., obj.id in __resolveType)
      if (debug) {
        console.log(`Type ${typeName} was originally a union type with @field directive, updating ResolversParentTypes to use intersection type`);
      }

      // Find ResolversParentTypes section and replace the union type reference
      const resolversParentTypesIndex = findResolversParentTypesIndex(lines);
      if (resolversParentTypesIndex !== -1) {
        const endIndex = findTypeDefEndIndex(lines, resolversParentTypesIndex);
        await replaceUnionTypeReferenceInResolversParentTypes(
          lines,
          typeName,
          resolversParentTypesIndex,
          endIndex,
          fieldFields,
          debug
        );
      } else if (debug) {
        console.log(`Could not find ResolversParentTypes section for ${typeName}`);
      }
    } else if (isObjectOrInterface) {
      // Determine if this is an interface type by checking if it's in ResolversInterfaceTypes
      const isInterfaceType = isTypeInResolversInterfaceTypes(lines, typeName);

      if (isInterfaceType) {
        if (debug) {
          console.log(`Type ${typeName} is an interface, @field directive fields already added to main type definition`);
        }
        // For interfaces, @field directive fields are already added to the main type definition above
        // No need to add them to ResolversInterfaceTypes
      } else {
        if (debug) {
          console.log(`Type ${typeName} is not an interface, adding @field directive fields to ResolversParentTypes`);
        }

        // Find ResolversParentTypes section and add hidden fields
        const resolversParentTypesIndex = findResolversParentTypesIndex(lines);
        if (resolversParentTypesIndex !== -1) {
          const endIndex = findTypeDefEndIndex(lines, resolversParentTypesIndex);
          await processFieldDirectiveFieldsForType(
            lines,
            typeName,
            fieldFields,
            resolversParentTypesIndex,
            endIndex,
            debug
          );
        }
      }
    } else if (debug) {
      console.log(`Type ${typeName} definition not found or unsupported type structure, skipping ResolversParentTypes processing`);
    }
  }

  // Fix standalone interfaces in ResolversInterfaceTypes (change "never" to actual interface type)
  await fixStandaloneInterfacesInResolversInterfaceTypes(lines, debug);
}

/**
 * Process @field directive fields for a specific type in main type definitions
 */
async function processFieldDirectivesForType(
  lines: string[],
  typeName: string,
  fieldFields: FieldDirectiveField[],
  debug: boolean = false,
  addImports: boolean = true
): Promise<void> {
  // Find the main type definition
  const typeDefIndex = findMainTypeDefinition(lines, typeName);
  if (typeDefIndex === -1) {
    if (debug) {
      console.log(`Type definition not found for: ${typeName}`);
    }
    return;
  }

  // Separate field-level overrides from new field additions
  const fieldOverrides: FieldDirectiveField[] = [];
  const newFields: FieldDirectiveField[] = [];

  for (const field of fieldFields) {
    if (isFieldLevelOverride(field, lines, typeName, typeDefIndex)) {
      fieldOverrides.push(field);
    } else {
      newFields.push(field);
    }
  }

  // Add new fields to the type
  if (newFields.length > 0) {
    await addFieldDirectiveFieldsToType(
      lines,
      typeName,
      newFields,
      typeDefIndex,
      debug,
      addImports
    );
  }

  // Override existing field types
  if (fieldOverrides.length > 0) {
    await overrideFieldTypes(lines, typeName, fieldOverrides, typeDefIndex, debug, addImports);
  }
}

/**
 * Process @field directive fields for union types
 * Keep union types pure and add @field directive fields only to ResolversParentTypes
 */
async function processFieldDirectivesForUnionType(
  lines: string[],
  typeName: string,
  fieldFields: FieldDirectiveField[],
  typeDefIndex: number,
  debug: boolean = false
): Promise<void> {
  if (debug) {
    console.log(`Union type ${typeName} will remain pure, @field directive fields will be added only to ResolversParentTypes`);
  }

  // For union types, we don't modify the main type definition
  // The @field directive fields will be added to ResolversParentTypes as an intersection type
  // This keeps the union type pure while allowing resolvers to access the additional fields
}

/**
 * Replace union type reference in ResolversParentTypes to create an intersection type
 * with @field directive fields for union types with @field directives
 */
async function replaceUnionTypeReferenceInResolversParentTypes(
  lines: string[],
  typeName: string,
  resolversParentTypesIndex: number,
  endIndex: number,
  fieldFields: FieldDirectiveField[],
  debug: boolean = false
): Promise<void> {
  if (debug) {
    console.log(`Creating intersection type for union ${typeName} in ResolversParentTypes with ${fieldFields.length} additional fields`);
  }

  // Find the type definition within ResolversParentTypes
  for (let i = resolversParentTypesIndex; i < endIndex; i++) {
    const line = lines[i].trim();

    // Look for the pattern: TypeName: ResolversUnionTypes<ResolversParentTypes>['TypeName'];
    const unionTypePattern = new RegExp(`^${typeName}:\\s*ResolversUnionTypes<ResolversParentTypes>\\['${typeName}'\\];?$`);

    if (unionTypePattern.test(line)) {
      if (debug) {
        console.log(`Found union type reference for ${typeName}, creating intersection type with @field directive fields`);
      }

      // Generate field definitions for the @field directive fields
      const fieldDefinitions: string[] = [];
      for (const field of fieldFields) {
        const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
        fieldDefinitions.push(`    ${field.name}: ${tsType};`);
      }

      // Create intersection type: OriginalUnionType & { field1: Type1; field2: Type2; }
      const indentation = lines[i].match(/^\s*/)?.[0] || '  ';
      const newTypeDefinition = [
        `${indentation}${typeName}: ${typeName} & {`,
        ...fieldDefinitions,
        `${indentation}};`
      ];

      // Replace the current line with the new intersection type definition
      lines.splice(i, 1, ...newTypeDefinition);

      if (debug) {
        console.log(`Created intersection type for ${typeName} in ResolversParentTypes`);
      }

      return;
    }
  }

  if (debug) {
    console.log(`Union type reference for ${typeName} not found in ResolversParentTypes (may already be replaced)`);
  }
}

/**
 * Process field directive fields for ResolversParentTypes
 */
async function processFieldDirectiveFieldsForType(
  lines: string[],
  typeName: string,
  fieldDirectiveFields: FieldDirectiveField[],
  resolversParentTypesIndex: number,
  endIndex: number,
  debug: boolean = false
): Promise<void> {
  if (debug) {
    console.log(`Processing field directive fields for type: ${typeName} in ResolversParentTypes`);
  }

  // First, check if the main type definition already contains these @field directive fields
  const mainTypeDefIndex = findMainTypeDefinition(lines, typeName);
  if (mainTypeDefIndex !== -1) {
    const mainTypeEndIndex = findTypeDefEndIndex(lines, mainTypeDefIndex);

    // Check if all @field directive fields are already present in the main type
    const fieldsAlreadyInMainType = fieldDirectiveFields.every(field => {
      for (let i = mainTypeDefIndex; i < mainTypeEndIndex; i++) {
        const line = lines[i].trim();
        if (line.includes(`${field.name}:`)) {
          return true;
        }
      }
      return false;
    });

    if (fieldsAlreadyInMainType) {
      if (debug) {
        console.log(`All @field directive fields for ${typeName} are already present in the main type definition, skipping ResolversParentTypes processing`);
      }
      return;
    }
  }

  // Find the type definition within ResolversParentTypes
  let typeStartIndex = -1;
  let typeEndIndex = -1;

  for (let i = resolversParentTypesIndex; i < endIndex; i++) {
    const line = lines[i].trim();

    // More precise type matching - look for exact type definition patterns
    // Match patterns like "  TypeName: SomeType;" or "  TypeName: {"
    const typeDefPattern = new RegExp(`^\\s*${typeName}:\\s*(.+)$`);
    const match = line.match(typeDefPattern);

    if (match) {
      const typeValue = match[1].trim();

      // Skip scalar type definitions (they don't have braces and are single-line)
      // Examples to skip: "Scalars['AddressLabel']['output'];"
      if (typeValue.includes('Scalars[') && typeValue.endsWith(';')) {
        if (debug) {
          console.log(`Skipping scalar type definition for ${typeName}: ${line}`);
        }
        continue;
      }

      // Skip union type references (they reference other types, not object definitions)
      // Examples to skip: "ResolversUnionTypes<ResolversParentTypes>['SomeUnion'];"
      if (typeValue.includes('ResolversUnionTypes') || typeValue.includes('ResolversInterfaceTypes')) {
        if (debug) {
          console.log(`Skipping union/interface type reference for ${typeName}: ${line}`);
        }
        continue;
      }

      typeStartIndex = i;

      // Check if this is an object type definition (has opening brace)
      if (typeValue.includes('{') || typeValue === '{') {
        // Find the end of this type definition using brace counting
        let braceCount = 0;
        let foundOpenBrace = false;

        for (let j = i; j < endIndex; j++) {
          const currentLine = lines[j];
          for (const char of currentLine) {
            if (char === '{') {
              braceCount++;
              foundOpenBrace = true;
            } else if (char === '}') {
              braceCount--;
              if (foundOpenBrace && braceCount === 0) {
                typeEndIndex = j;
                break;
              }
            }
          }
          if (typeEndIndex !== -1) break;
        }

        if (debug) {
          console.log(`Found object type definition for ${typeName} from line ${typeStartIndex + 1} to ${typeEndIndex + 1}`);
        }
        break;
      } else {
        // Single-line type definition - convert it to an intersection type with @field directive fields
        if (debug) {
          console.log(`Converting single-line type definition for ${typeName} to intersection type: ${line}`);
        }

        // Create intersection type: OriginalType & { field1: Type1; field2: Type2; }
        const fieldsToAdd: string[] = [];
        for (const field of fieldDirectiveFields) {
          const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
          fieldsToAdd.push(`    ${field.name}: ${tsType};`);
        }

        // Remove the semicolon from typeValue if it exists
        const cleanTypeValue = typeValue.endsWith(';') ? typeValue.slice(0, -1) : typeValue;

        // Replace the single-line definition with an intersection type
        const newTypeDefinition = [
          `  ${typeName}: ${cleanTypeValue} & {`,
          ...fieldsToAdd,
          '  };'
        ];

        // Replace the current line with the new intersection type definition
        lines.splice(i, 1, ...newTypeDefinition);

        if (debug) {
          console.log(`Converted ${typeName} to intersection type with ${fieldDirectiveFields.length} fields`);
        }

        // We've handled this type, so return early
        return;
      }
    }
  }

  if (typeStartIndex === -1 || typeEndIndex === -1) {
    if (debug) {
      console.log(`Type ${typeName} not found or not suitable for field addition in ResolversParentTypes, skipping`);
    }
    // Don't add new type definitions - this prevents duplicates
    // The type should already exist in ResolversParentTypes from the base GraphQL generation
    return;
  } else {
    if (debug) {
      console.log(
        `Adding field directive fields to existing type ${typeName} in ResolversParentTypes (lines ${typeStartIndex + 1}-${typeEndIndex + 1})`
      );
    }

    // Validate that we found a proper object type definition
    if (typeEndIndex <= typeStartIndex) {
      console.warn(`Invalid type boundaries for ${typeName}, skipping field addition`);
      return;
    }

    // Add fields to existing type
    const fieldsToAdd: string[] = [];
    for (const field of fieldDirectiveFields) {
      const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
      fieldsToAdd.push(`    ${field.name}: ${tsType};`);
    }

    // Insert before the closing brace of the type
    lines.splice(typeEndIndex, 0, ...fieldsToAdd);

    if (debug) {
      console.log(`Added ${fieldDirectiveFields.length} fields to existing type ${typeName}`);
    }
  }
}

/**
 * Resolve duplicate field conflicts between GraphQL schema fields and @field directive fields
 * This function implements case-insensitive duplicate detection and removes conflicting GraphQL fields
 * to prevent duplicate identifier compilation errors.
 */
async function resolveDuplicateFieldConflicts(
  lines: string[],
  typeName: string,
  fieldDirectiveFields: FieldDirectiveField[],
  debug: boolean = false
): Promise<void> {
  if (fieldDirectiveFields.length === 0) {
    if (debug) {
      console.log(`Skipping duplicate field conflict resolution for ${typeName}: no @field directive fields`);
    }
    return;
  }

  if (debug) {
    console.log(`Checking for duplicate field conflicts in type: ${typeName}`);
  }

  // Find the main type definition
  const typeDefIndex = findMainTypeDefinition(lines, typeName);
  if (typeDefIndex === -1) {
    if (debug) {
      console.log(`Type definition for ${typeName} not found, skipping duplicate resolution`);
    }
    return;
  }

  // Skip conflict resolution for union types since they don't have individual fields
  const isUnion = isUnionType(lines, typeDefIndex);
  if (debug) {
    console.log(`Type ${typeName} at line ${typeDefIndex + 1}: "${lines[typeDefIndex]?.trim()}" - isUnion: ${isUnion}`);
  }
  if (isUnion) {
    if (debug) {
      console.log(`Type ${typeName} is a union type, skipping duplicate field conflict resolution (union types don't have individual fields)`);
    }
    return;
  }

  const typeEndIndex = findTypeDefEndIndex(lines, typeDefIndex);

  // Create a case-insensitive map of @field directive field names
  const fieldDirectiveFieldsMap = new Map<string, FieldDirectiveField>();
  for (const field of fieldDirectiveFields) {
    fieldDirectiveFieldsMap.set(field.name.toLowerCase(), field);
  }

  // Track fields to remove (GraphQL schema fields that conflict with @field directive fields)
  const fieldsToRemove: Array<{ lineIndex: number; fieldName: string }> = [];

  // Scan through the type definition to find conflicting fields
  for (let i = typeDefIndex + 1; i < typeEndIndex; i++) {
    const line = lines[i].trim();

    // Match field definitions (e.g., "fieldName: Type;" or "fieldName?: Maybe<Type>;")
    const fieldMatch = line.match(/^\s*(\w+)(\?)?:\s*(.+);?\s*$/);
    if (fieldMatch) {
      const [, fieldName] = fieldMatch;
      const fieldNameLower = fieldName.toLowerCase();

      // Check if this GraphQL field conflicts with a @field directive field (case-insensitive)
      if (fieldDirectiveFieldsMap.has(fieldNameLower)) {
        const directiveField = fieldDirectiveFieldsMap.get(fieldNameLower)!;

        // Only remove if there's an actual conflict (same name, case-insensitive)
        // This prevents duplicate identifier compilation errors
        if (fieldNameLower === directiveField.name.toLowerCase()) {
          fieldsToRemove.push({ lineIndex: i, fieldName });

          if (debug) {
            console.log(`Found duplicate field conflict: GraphQL field "${fieldName}" conflicts with @field directive field "${directiveField.name}"`);
          }
        }
      }
    }
  }

  // Remove conflicting fields in reverse order to maintain line indices
  for (let i = fieldsToRemove.length - 1; i >= 0; i--) {
    const { lineIndex, fieldName } = fieldsToRemove[i];

    // Double-check that we're removing the correct line within the correct type
    if (lineIndex < typeDefIndex || lineIndex >= typeEndIndex) {
      if (debug) {
        console.log(`ERROR: Attempted to remove line ${lineIndex + 1} for field "${fieldName}" but it's outside the type definition bounds (${typeDefIndex + 1}-${typeEndIndex + 1}). Skipping removal.`);
      }
      continue;
    }

    if (debug) {
      console.log(`Removing conflicting GraphQL field "${fieldName}" at line ${lineIndex + 1} to prevent duplicate identifier`);
      console.log(`Line content before removal: "${lines[lineIndex]}"`);
    }

    lines.splice(lineIndex, 1);

    if (debug) {
      console.log(`Line removed successfully. Next line is now: "${lines[lineIndex] || 'END_OF_FILE'}"`);
    }
  }

  if (fieldsToRemove.length > 0 && debug) {
    console.log(`Resolved ${fieldsToRemove.length} duplicate field conflicts for type ${typeName}`);
  }
}

/**
 * Process field directive fields for ResolversInterfaceTypes
 */
async function processFieldDirectiveFieldsForInterfaceType(
  lines: string[],
  typeName: string,
  fieldDirectiveFields: FieldDirectiveField[],
  resolversInterfaceTypesIndex: number,
  endIndex: number,
  debug: boolean = false
): Promise<void> {
  if (debug) {
    console.log(`Processing field directive fields for interface type: ${typeName} in ResolversInterfaceTypes`);
  }

  // Find the type definition within ResolversInterfaceTypes
  let typeStartIndex = -1;
  let typeEndIndex = -1;

  for (let i = resolversInterfaceTypesIndex; i < endIndex; i++) {
    const line = lines[i].trim();
    if (line.includes(`${typeName}:`)) {
      typeStartIndex = i;

      // For interface types, we need to find the semicolon that ends the type definition
      // Interface types in ResolversInterfaceTypes are typically single-line like: "TypeName: never;"
      // or "TypeName: ( ImplementingType );"

      // Check if this is a single-line definition
      if (line.includes(';')) {
        typeEndIndex = i;
      } else {
        // Multi-line definition, find the end
        for (let j = i + 1; j < endIndex; j++) {
          if (lines[j].includes(';')) {
            typeEndIndex = j;
            break;
          }
        }
      }
      break;
    }
  }

  if (typeStartIndex === -1) {
    if (debug) {
      console.log(`Interface type ${typeName} not found in ResolversInterfaceTypes, cannot add @field directive fields`);
    }
    return;
  }

  if (debug) {
    console.log(`Modifying existing interface type ${typeName} in ResolversInterfaceTypes to include @field directive fields`);
  }

  // For interface types, we need to replace the "never" or existing type definition
  // with an object type that includes the @field directive fields
  const currentLine = lines[typeStartIndex];
  const typeNameMatch = currentLine.match(new RegExp(`(\\s*${typeName}:\\s*)`));

  if (typeNameMatch) {
    const prefix = typeNameMatch[1];

    // Create the new type definition with @field directive fields
    const fieldDefinitions: string[] = [];
    for (const field of fieldDirectiveFields) {
      const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
      fieldDefinitions.push(`    ${field.name}: ${tsType};`);
    }

    // Replace the current type definition
    const newTypeDefinition = `${prefix}{
${fieldDefinitions.join('\n')}
  };`;

    // Replace the lines from typeStartIndex to typeEndIndex
    lines.splice(typeStartIndex, typeEndIndex - typeStartIndex + 1, newTypeDefinition);
  }
}

/**
 * Find the main type definition in the generated types
 */
function findMainTypeDefinition(lines: string[], typeName: string): number {
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (
      line.startsWith(`export type ${typeName} =`) ||
      line.startsWith(`export interface ${typeName}`)
    ) {
      return i;
    }
  }
  return -1;
}

/**
 * Check if a type definition is a union type
 */
function isUnionType(lines: string[], typeDefIndex: number): boolean {
  if (typeDefIndex === -1) return false;

  const line = lines[typeDefIndex].trim();
  // Union types are defined as: export type UnionName = Type1 | Type2 | Type3
  // They don't have braces and contain the | operator
  return line.includes(' = ') && line.includes(' | ') && !line.includes('{');
}

/**
 * Check if a type definition is an object type or interface (has braces)
 */
function isObjectOrInterfaceType(lines: string[], typeDefIndex: number): boolean {
  if (typeDefIndex === -1) return false;

  // Look for opening brace on the same line or subsequent lines
  for (let i = typeDefIndex; i < Math.min(typeDefIndex + 3, lines.length); i++) {
    if (lines[i].includes('{')) {
      return true;
    }
  }
  return false;
}

/**
 * Check if a field is a field-level override (field already exists in the type)
 */
function isFieldLevelOverride(
  field: FieldDirectiveField,
  lines: string[],
  typeName: string,
  typeDefIndex: number
): boolean {
  const endIndex = findTypeDefEndIndex(lines, typeDefIndex);

  for (let i = typeDefIndex; i < endIndex; i++) {
    const line = lines[i].trim();
    if (line.includes(`${field.name}:`)) {
      return true;
    }
  }

  return false;
}

/**
 * Add new @field directive fields to a type definition
 */
async function addFieldDirectiveFieldsToType(
  lines: string[],
  typeName: string,
  fieldFields: FieldDirectiveField[],
  typeDefIndex: number,
  debug: boolean = false,
  addImports: boolean = true
): Promise<void> {
  if (debug) {
    console.log(`Adding ${fieldFields.length} new fields to type: ${typeName}`);
  }

  // Import statements are now added upfront to avoid line number shifting issues
  // No need to add imports here since they're already added at the beginning of post-processing

  // Find the end of the type definition
  const typeEndIndex = findTypeDefEndIndex(lines, typeDefIndex);

  // Add the new fields before the closing brace
  const fieldsToAdd: string[] = [];
  for (const field of fieldFields) {
    const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
    fieldsToAdd.push(`  ${field.name}: ${tsType};`);
  }

  // Insert the fields before the closing brace
  lines.splice(typeEndIndex, 0, ...fieldsToAdd);
}

/**
 * Override existing field types with @field directive specifications
 */
async function overrideFieldTypes(
  lines: string[],
  typeName: string,
  fieldOverrides: FieldDirectiveField[],
  typeDefIndex: number,
  debug: boolean = false,
  addImports: boolean = true
): Promise<void> {
  if (debug) {
    console.log(`Overriding ${fieldOverrides.length} field types for type: ${typeName}`);
  }

  // Import statements are now added upfront to avoid line number shifting issues
  // No need to add imports here since they're already added at the beginning of post-processing

  const typeEndIndex = findTypeDefEndIndex(lines, typeDefIndex);

  // Override field types
  for (const field of fieldOverrides) {
    for (let i = typeDefIndex; i < typeEndIndex; i++) {
      const line = lines[i];
      if (line.includes(`${field.name}:`)) {
        const tsType = convertGraphQLTypeToTypeScript(field.type, field.optional);
        // Replace the field type
        const fieldRegex = new RegExp(`(\\s*${field.name}\\s*:\\s*)[^;]+`);
        lines[i] = line.replace(fieldRegex, `$1${tsType}`);
        break;
      }
    }
  }
}

/**
 * Convert GraphQL type to TypeScript type
 */
function convertGraphQLTypeToTypeScript(graphqlType: string, isOptional: boolean = false): string {
  let tsType = graphqlType;

  // Handle array types
  if (tsType.startsWith('[') && tsType.endsWith(']')) {
    const innerType = tsType.slice(1, -1);
    tsType = `Array<${convertGraphQLTypeToTypeScript(innerType)}>`;
  }

  // Handle non-null types
  if (tsType.endsWith('!')) {
    tsType = tsType.slice(0, -1);
    // Non-null types are required unless explicitly marked optional
    if (!isOptional) {
      return convertGraphQLTypeToTypeScript(tsType, false);
    }
  }

  // Note: All scalar types are now mapped to 'unknown' by the generator
  // This function should primarily handle non-scalar types

  // Add optional marker if needed
  return isOptional ? `${tsType} | null | undefined` : tsType;
}

/**
 * Find the end index of the import section
 */
function findImportSectionEndIndex(lines: string[]): number {
  let lastImportIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('import ') || (line.startsWith('export ') && line.includes('from '))) {
      lastImportIndex = i;
    } else if (line && !line.startsWith('//') && !line.startsWith('/*') && lastImportIndex !== -1) {
      break;
    }
  }

  return lastImportIndex + 1;
}

/**
 * Find the ResolversParentTypes index
 */
function findResolversParentTypesIndex(lines: string[]): number {
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('ResolversParentTypes = ResolversObject<{')) {
      return i;
    }
  }
  return -1;
}

/**
 * Find the ResolversInterfaceTypes index
 */
function findResolversInterfaceTypesIndex(lines: string[]): number {
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('ResolversInterfaceTypes<_RefType extends Record<string, unknown>> = ResolversObject<{')) {
      return i;
    }
  }
  return -1;
}

/**
 * Check if a type is defined in ResolversInterfaceTypes
 */
function isTypeInResolversInterfaceTypes(lines: string[], typeName: string): boolean {
  const resolversInterfaceTypesIndex = findResolversInterfaceTypesIndex(lines);
  if (resolversInterfaceTypesIndex === -1) {
    return false;
  }

  const endIndex = findTypeDefEndIndex(lines, resolversInterfaceTypesIndex);

  for (let i = resolversInterfaceTypesIndex; i < endIndex; i++) {
    const line = lines[i].trim();
    if (line.includes(`${typeName}:`)) {
      return true;
    }
  }

  return false;
}

/**
 * Fix standalone interfaces in ResolversInterfaceTypes by replacing "never" with actual interface type
 */
async function fixStandaloneInterfacesInResolversInterfaceTypes(
  lines: string[],
  debug: boolean = false
): Promise<void> {
  const resolversInterfaceTypesIndex = findResolversInterfaceTypesIndex(lines);
  if (resolversInterfaceTypesIndex === -1) {
    if (debug) {
      console.log('ResolversInterfaceTypes not found, skipping standalone interface fix');
    }
    return;
  }

  const endIndex = findTypeDefEndIndex(lines, resolversInterfaceTypesIndex);

  for (let i = resolversInterfaceTypesIndex; i < endIndex; i++) {
    const line = lines[i].trim();

    // Look for interface types set to "never"
    const neverMatch = line.match(/(\s*)(\w+):\s*never;/);
    if (neverMatch) {
      const [, indent, typeName] = neverMatch;

      // Check if this is actually an interface type by looking for its definition
      const interfaceDefIndex = findMainTypeDefinition(lines, typeName);
      if (interfaceDefIndex !== -1) {
        const interfaceDefLine = lines[interfaceDefIndex].trim();
        if (interfaceDefLine.startsWith(`export type ${typeName} =`) ||
          interfaceDefLine.startsWith(`export interface ${typeName}`)) {

          if (debug) {
            console.log(`Fixing standalone interface ${typeName}: changing "never" to actual interface type`);
          }

          // Replace "never" with the actual interface type
          lines[i] = lines[i].replace(/:\s*never;/, `: ${typeName};`);
        }
      }
    }
  }
}

/**
 * Find the end index of a type definition
 */
function findTypeDefEndIndex(lines: string[], startIndex: number): number {
  let braceCount = 0;
  let foundOpenBrace = false;

  for (let i = startIndex; i < lines.length; i++) {
    const line = lines[i];
    for (const char of line) {
      if (char === '{') {
        braceCount++;
        foundOpenBrace = true;
      } else if (char === '}') {
        braceCount--;
        if (foundOpenBrace && braceCount === 0) {
          return i;
        }
      }
    }
  }

  return lines.length;
}

/**
 * Post-process types using pre-built type map (two-phase processing)
 */
async function postProcessWithTypeMap(
  lines: string[],
  typeMap: TypeMap,
  typesDtsPath: string,
  debug: boolean
): Promise<void> {
  console.log('Processing types with pre-built type map');

  // Process field directives from type map
  await processFieldsFromTypeMap(lines, typeMap, debug);

  // Process method calls from type map
  await processMethodCallsFromTypeMap(lines, typeMap, debug);

  // Write the modified content back
  fs.writeFileSync(typesDtsPath, lines.join('\n'));

  console.log('Type map post-processing completed');
}

/**
 * Process field information from type map
 */
async function processFieldsFromTypeMap(
  lines: string[],
  typeMap: TypeMap,
  debug: boolean
): Promise<void> {
  const fieldsToAdd: FieldDirectiveField[] = [];

  // Convert type map fields to FieldDirectiveField format
  for (const [typeName, fields] of typeMap.fields) {
    for (const field of fields) {
      fieldsToAdd.push({
        name: field.fieldName,
        type: field.fieldType,
        raw: `${field.fieldName}: ${field.fieldType}`,
        importPath: field.importPath,
        optional: field.optional || false
      });
    }
  }

  if (fieldsToAdd.length > 0) {
    console.log(`Adding ${fieldsToAdd.length} fields from type map`);

    // Create a map for processing
    const typeToFieldFields = new Map<string, FieldDirectiveField[]>();

    // Group fields by type name
    for (const [typeName, fields] of typeMap.fields) {
      const convertedFields = fields.map(field => ({
        name: field.fieldName,
        type: field.fieldType,
        raw: `${field.fieldName}: ${field.fieldType}`,
        importPath: field.importPath,
        optional: field.optional || false
      }));
      typeToFieldFields.set(typeName, convertedFields);
    }

    // Use existing field directive processing function
    await processFieldDirectives(lines, typeToFieldFields, debug);
  }
}

/**
 * Process method call information from type map
 */
async function processMethodCallsFromTypeMap(
  lines: string[],
  typeMap: TypeMap,
  debug: boolean
): Promise<void> {
  // Convert type map method calls to decorator container format
  const decoratorContainer: DecoratorContainer = {
    methodCalls: [],
    imports: [],
    fields: [],
    contexts: [],
    others: {}
  };

  // Convert method calls
  for (const [typeName, typeMethodCalls] of typeMap.methodCalls) {
    for (const [fieldName, methodCalls] of typeMethodCalls) {
      for (const methodCall of methodCalls) {
        decoratorContainer.methodCalls.push({
          decorator: {
            name: 'GQLMethodCall',
            target: `${typeName}.${fieldName}`,
            filePath: methodCall.sourceFile.filePath,
            lineNumber: methodCall.lineNumber,
            arguments: `"${methodCall.call}"`,
            raw: `@GQLMethodCall("${methodCall.call}")`
          },
          data: {
            call: methodCall.call,
            type: 'method',
            schema: methodCall.schemaId,
            async: methodCall.async,
            enableTypeCasting: methodCall.enableTypeCasting
          }
        });
      }
    }
  }

  // Convert imports
  for (const [filePath, imports] of typeMap.imports) {
    for (const importInfo of imports) {
      decoratorContainer.imports.push({
        decorator: {
          name: 'GQLImport',
          target: importInfo.statement,
          filePath: importInfo.sourceFile.filePath,
          lineNumber: importInfo.lineNumber,
          arguments: `"${importInfo.module}"`,
          raw: importInfo.statement
        },
        data: {
          importStatement: importInfo.statement,
          schema: importInfo.sourceFile.filePath
        }
      });
    }
  }

  // Convert contexts
  for (const context of typeMap.contexts) {
    decoratorContainer.contexts.push({
      decorator: {
        name: 'GQLContext',
        target: context.contextType,
        filePath: context.sourceFile.filePath,
        lineNumber: context.lineNumber,
        arguments: `"${context.contextType}"`,
        raw: `@GQLContext("${context.contextType}")`
      },
      data: {
        name: context.contextType,
        path: context.importPath || ''
      }
    });
  }

  if (decoratorContainer.methodCalls.length > 0 ||
      decoratorContainer.imports.length > 0 ||
      decoratorContainer.contexts.length > 0) {
    console.log(`Processing ${decoratorContainer.methodCalls.length} method calls from type map`);

    // Use existing decorator processor to convert to directive container
    const decoratorProcessor = new DecoratorProcessor(
      DEFAULT_DECORATOR_CONFIG,
      DEFAULT_PRECEDENCE_RULES
    );

    // Convert decorator container to directive container
    const directiveContainer = decoratorProcessor.convertToDirectiveContainer(decoratorContainer);

    // For now, we'll skip processing the directives since the main functionality
    // is handled by the field processing above. This can be enhanced later.
    console.log(`Converted ${decoratorContainer.methodCalls.length} method calls to directives`);
  }
}
