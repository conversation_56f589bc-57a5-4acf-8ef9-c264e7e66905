import type { Ora } from 'ora';
import { chalkLoader, type ChalkInstance } from './chalk-loader';

// Dynamic ora loader to handle ESM compatibility
let oraModule: any = null;
let oraLoadPromise: Promise<any> | null = null;

async function loadOra(): Promise<any> {
  if (oraModule) return oraModule;
  if (oraLoadPromise) return oraLoadPromise;

  oraLoadPromise = (async () => {
    try {
      const module = await import('ora');
      oraModule = module.default || module;
      return oraModule;
    } catch (error) {
      console.warn('Failed to load ora module, spinner functionality disabled:', error instanceof Error ? error.message : String(error));
      // Return a mock ora function that does nothing
      oraModule = () => ({
        start: () => ({ stop: () => {}, succeed: () => {}, fail: () => {}, text: '' }),
        stop: () => {},
        succeed: () => {},
        fail: () => {},
        text: ''
      });
      return oraModule;
    }
  })();

  return oraLoadPromise;
}

/**
 * UI utility functions for enhanced CLI experience
 */
export class UI {
  private static activeSpinner: Ora | null = null;
  private static stats = {
    regenerations: 0,
    totalDuration: 0,
    filesProcessed: 0,
    startTime: Date.now(),
  };

  /**
   * Get chalk instance synchronously with fallback
   */
  private static getChalk(): ChalkInstance {
    return chalkLoader.getChalkSync();
  }

  /**
   * Color-coded success message
   */
  static success(message: string): void {
    const chalk = this.getChalk();
    if (this.activeSpinner) {
      this.activeSpinner.succeed(message);
      this.activeSpinner = null;
    } else {
      console.log(chalk.green(`✅ ${message}`));
    }
  }

  /**
   * Color-coded error message
   */
  static error(message: string): void {
    const chalk = this.getChalk();
    if (this.activeSpinner) {
      this.activeSpinner.fail(message);
      this.activeSpinner = null;
    } else {
      console.log(chalk.red(`❌ ${message}`));
    }
  }

  /**
   * Color-coded info message
   */
  static info(message: string): void {
    const chalk = this.getChalk();
    console.log(chalk.blue(`ℹ️  ${message}`));
  }

  /**
   * Color-coded warning message
   */
  static warning(message: string): void {
    const chalk = this.getChalk();
    console.log(chalk.yellow(`⚠️  ${message}`));
  }

  /**
   * Start a spinner with message
   */
  static spinner(message: string): void {
    if (this.activeSpinner) {
      this.activeSpinner.stop();
    }

    // Try to load ora asynchronously and start spinner
    loadOra().then(ora => {
      if (!this.activeSpinner) { // Only start if no other spinner was started in the meantime
        this.activeSpinner = ora(message).start();
      }
    }).catch(() => {
      // Fallback to simple console.log if ora fails
      console.log(`🔄 ${message}`);
    });
  }

  /**
   * Update spinner text
   */
  static updateSpinner(message: string): void {
    if (this.activeSpinner) {
      this.activeSpinner.text = message;
    }
  }

  /**
   * Stop active spinner without message
   */
  static stopSpinner(): void {
    if (this.activeSpinner) {
      this.activeSpinner.stop();
      this.activeSpinner = null;
    }
  }

  /**
   * Compact startup message
   */
  static startup(schemaPaths: string[], outputPaths: string[], bidirectional: boolean): void {
    const chalk = this.getChalk();
    const pairs = schemaPaths.length;
    const bidirStatus = bidirectional ? chalk.green('ON') : chalk.gray('OFF');
    console.log(chalk.cyan(`🔍 Watching ${pairs} schema pair${pairs > 1 ? 's' : ''} | 🔄 Bidirectional: ${bidirStatus} | ⚡ Ready`));
  }

  /**
   * Batch file change notification
   */
  static fileChanges(changes: Array<{ type: 'add' | 'change' | 'unlink', path: string }>): void {
    if (changes.length === 0) return;

    const grouped = changes.reduce((acc, change) => {
      acc[change.type] = acc[change.type] || [];
      acc[change.type].push(change.path);
      return acc;
    }, {} as Record<string, string[]>);

    const parts: string[] = [];
    
    if (grouped.change?.length) {
      const count = grouped.change.length;
      const files = count <= 2 ? grouped.change.join(', ') : `${grouped.change.slice(0, 2).join(', ')} (+${count - 2} more)`;
      parts.push(`modified: ${files}`);
    }
    
    if (grouped.add?.length) {
      const count = grouped.add.length;
      const files = count <= 2 ? grouped.add.join(', ') : `${grouped.add.slice(0, 2).join(', ')} (+${count - 2} more)`;
      parts.push(`added: ${files}`);
    }
    
    if (grouped.unlink?.length) {
      const count = grouped.unlink.length;
      const files = count <= 2 ? grouped.unlink.join(', ') : `${grouped.unlink.slice(0, 2).join(', ')} (+${count - 2} more)`;
      parts.push(`removed: ${files}`);
    }

    const chalk = this.getChalk();
    console.log(chalk.blue(`📝 ${parts.join(', ')}`));
  }

  /**
   * Status line with live stats
   */
  static statusLine(): void {
    const now = Date.now();
    const uptime = Math.floor((now - this.stats.startTime) / 1000);
    const avgTime = this.stats.regenerations > 0 ? Math.round(this.stats.totalDuration / this.stats.regenerations) : 0;
    
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = uptime % 60;
    
    let uptimeStr = '';
    if (hours > 0) uptimeStr += `${hours}h `;
    if (minutes > 0) uptimeStr += `${minutes}m `;
    uptimeStr += `${seconds}s`;

    const chalk = this.getChalk();
    const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false });
    const status = chalk.gray(`[${timestamp}]`) +
                  chalk.green(` 👀 Active`) +
                  chalk.blue(` | 🔄 ${this.stats.regenerations} regens`) +
                  (avgTime > 0 ? chalk.yellow(` | ⚡ ${avgTime}ms avg`) : '') +
                  chalk.gray(` | ⏱️  ${uptimeStr}`);
    
    // Clear line and write status
    process.stdout.write('\r\x1b[K' + status);
  }

  /**
   * Update regeneration stats
   */
  static updateStats(duration: number, filesProcessed: number = 0): void {
    this.stats.regenerations++;
    this.stats.totalDuration += duration;
    this.stats.filesProcessed += filesProcessed;
  }

  /**
   * Shutdown summary
   */
  static shutdown(): void {
    const chalk = this.getChalk();
    const totalTime = Date.now() - this.stats.startTime;
    const hours = Math.floor(totalTime / (1000 * 60 * 60));
    const minutes = Math.floor((totalTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((totalTime % (1000 * 60)) / 1000);

    let durationStr = '';
    if (hours > 0) durationStr += `${hours}h `;
    if (minutes > 0) durationStr += `${minutes}m `;
    durationStr += `${seconds}s`;

    console.log('\n' + chalk.gray('─'.repeat(50)));
    console.log(chalk.cyan('📊 Watch Session Summary'));
    console.log(chalk.gray(`   Duration: ${durationStr}`));
    console.log(chalk.gray(`   Regenerations: ${this.stats.regenerations}`));
    console.log(chalk.gray(`   Files processed: ${this.stats.filesProcessed}`));
    if (this.stats.regenerations > 0) {
      const avgTime = Math.round(this.stats.totalDuration / this.stats.regenerations);
      console.log(chalk.gray(`   Average regen time: ${avgTime}ms`));
    }
    console.log(chalk.gray('─'.repeat(50)));
    console.log(chalk.yellow('🛑 Shutting down gracefully...'));
  }

  /**
   * Clear current line (useful for status updates)
   */
  static clearLine(): void {
    process.stdout.write('\r\x1b[K');
  }

  /**
   * Print newline (useful after status line)
   */
  static newLine(): void {
    console.log('');
  }
}

/**
 * File change tracker for batching notifications
 */
export class FileChangeTracker {
  private changes: Array<{ type: 'add' | 'change' | 'unlink', path: string }> = [];
  private timer: NodeJS.Timeout | null = null;
  private readonly debounceMs: number;

  constructor(debounceMs: number = 100) {
    this.debounceMs = debounceMs;
  }

  /**
   * Add a file change to the batch
   */
  addChange(type: 'add' | 'change' | 'unlink', filePath: string): void {
    // Remove any existing change for this file
    this.changes = this.changes.filter(change => change.path !== filePath);
    
    // Add new change
    this.changes.push({ type, path: filePath });

    // Reset timer
    if (this.timer) {
      clearTimeout(this.timer);
    }

    this.timer = setTimeout(() => {
      this.flush();
    }, this.debounceMs);
  }

  /**
   * Flush all pending changes
   */
  flush(): void {
    if (this.changes.length > 0) {
      UI.fileChanges([...this.changes]);
      this.changes = [];
    }
    
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * Clear all pending changes without flushing
   */
  clear(): void {
    this.changes = [];
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}
