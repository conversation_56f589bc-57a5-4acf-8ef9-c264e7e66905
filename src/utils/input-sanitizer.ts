/**
 * Input sanitization utilities for decorator content and user-provided data
 */

/**
 * Configuration for input sanitization
 */
export interface SanitizationConfig {
  /** Maximum length for input strings */
  maxLength?: number;
  /** Whether to allow special characters */
  allowSpecialChars?: boolean;
  /** Whether to allow code injection patterns */
  allowCodeInjection?: boolean;
  /** Custom forbidden patterns */
  forbiddenPatterns?: RegExp[];
  /** Whether to sanitize HTML/XML content */
  sanitizeMarkup?: boolean;
}

/**
 * Result of input sanitization
 */
export interface SanitizationResult {
  /** Whether the input is safe */
  isSafe: boolean;
  /** The sanitized input (if safe) */
  sanitizedInput?: string;
  /** Error message if unsafe */
  error?: string;
  /** Warning messages */
  warnings?: string[];
  /** Detected threats */
  threats?: string[];
}

/**
 * Input sanitizer for decorator content and user data
 */
export class InputSanitizer {
  private static readonly DEFAULT_CONFIG: Required<SanitizationConfig> = {
    maxLength: 10000,
    allowSpecialChars: true,
    allowCodeInjection: false,
    forbiddenPatterns: [],
    sanitizeMarkup: true,
  };

  /**
   * Dangerous patterns that could indicate code injection attempts
   */
  private static readonly DANGEROUS_PATTERNS = [
    // JavaScript injection patterns
    /eval\s*\(/i,
    /Function\s*\(/i,
    /setTimeout\s*\(/i,
    /setInterval\s*\(/i,
    /new\s+Function\s*\(/i,
    
    // Process/system access
    /process\s*\./i,
    /require\s*\(/i,
    /\bimport\s*\(/i, // Only dynamic imports, not static import statements
    /global\s*\./i,
    /window\s*\./i,
    
    // File system access
    /fs\s*\./i,
    /readFile/i,
    /writeFile/i,
    /exec\s*\(/i,
    /spawn\s*\(/i,
    
    // Network access
    /fetch\s*\(/i,
    /XMLHttpRequest/i,
    /WebSocket/i,
    
    // Prototype pollution
    /__proto__/i,
    /constructor\s*\./i,
    /prototype\s*\./i,
    
    // SQL injection patterns
    /union\s+select/i,
    /drop\s+table/i,
    /delete\s+from/i,
    /insert\s+into/i,
    
    // Command injection
    /\|\s*[a-z]/i,
    /&&\s*[a-z]/i,
    /;\s*[a-z]/i,
    /`[^`]*`/,
    /\$\([^)]*\)/,
    
    // Path traversal (only flag excessive traversal, not legitimate relative imports)
    /\.\.\/\.\.\/\.\.\//, // 3+ levels of path traversal
    /\.\.\\\\\.\.\\\\\.\.\\\\/,  // Windows path traversal (3+ levels)
    /~\//,
    
    // Script tags and HTML injection
    /<script/i,
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /javascript:/i,
    /data:text\/html/i,
    /vbscript:/i,
    
    // Template injection
    /\{\{.*\}\}/,
    /\$\{.*\}/,
    /<\%.*\%>/,
  ];

  /**
   * Patterns specific to method call validation
   */
  private static readonly METHOD_CALL_DANGEROUS_PATTERNS = [
    // Direct dangerous function calls
    /\beval\b/i,
    /\bFunction\b/i,
    /\brequire\b/i,
    /\bprocess\b/i,
    /\b__dirname\b/i,
    /\b__filename\b/i,
    
    // Suspicious method chains
    /\.constructor\s*\(/i,
    /\.call\s*\(/i,
    /\.apply\s*\(/i,
    /\.bind\s*\(/i,
    
    // File system operations
    /\bfs\./i,
    /\bpath\./i,
    /readFile|writeFile|unlink|rmdir/i,
    
    // Network operations
    /\bhttp\./i,
    /\bhttps\./i,
    /\bnet\./i,
    /\burl\./i,
  ];

  /**
   * Sanitize general input
   * @param input The input to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeInput(
    input: string,
    config: SanitizationConfig = {}
  ): SanitizationResult {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };
    const warnings: string[] = [];
    const threats: string[] = [];

    // Basic validation
    if (typeof input !== 'string') {
      return {
        isSafe: false,
        error: 'Input must be a string',
      };
    }

    // Check length
    if (input.length > cfg.maxLength) {
      return {
        isSafe: false,
        error: `Input exceeds maximum length of ${cfg.maxLength} characters`,
      };
    }

    // Check for null bytes
    if (input.includes('\0') || input.includes('\x00')) {
      threats.push('null bytes');
      return {
        isSafe: false,
        error: 'Input contains null bytes',
        threats,
      };
    }

    // Check for dangerous patterns
    if (!cfg.allowCodeInjection) {
      for (const pattern of this.DANGEROUS_PATTERNS) {
        if (pattern.test(input)) {
          threats.push(`dangerous pattern: ${pattern.source}`);
        }
      }
    }

    // Check custom forbidden patterns
    for (const pattern of cfg.forbiddenPatterns) {
      if (pattern.test(input)) {
        threats.push(`forbidden pattern: ${pattern.source}`);
      }
    }

    // If threats were detected, reject the input
    if (threats.length > 0) {
      return {
        isSafe: false,
        error: 'Input contains potentially dangerous content',
        threats,
      };
    }

    // Sanitize markup if requested
    let sanitizedInput = input;
    if (cfg.sanitizeMarkup) {
      sanitizedInput = this.sanitizeMarkup(sanitizedInput);
      if (sanitizedInput !== input) {
        warnings.push('HTML/XML content was sanitized');
      }
    }

    return {
      isSafe: true,
      sanitizedInput,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Sanitize method call expressions
   * @param methodCall The method call to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeMethodCall(
    methodCall: string,
    config: SanitizationConfig = {}
  ): SanitizationResult {
    const baseResult = this.sanitizeInput(methodCall, config);
    
    if (!baseResult.isSafe) {
      return baseResult;
    }

    const threats: string[] = baseResult.threats || [];
    const warnings: string[] = baseResult.warnings || [];

    // Additional checks specific to method calls
    for (const pattern of this.METHOD_CALL_DANGEROUS_PATTERNS) {
      if (pattern.test(methodCall)) {
        threats.push(`dangerous method call pattern: ${pattern.source}`);
      }
    }

    // Check for suspicious string concatenation that could lead to injection
    if (/\+\s*["'`]/.test(methodCall) || /["'`]\s*\+/.test(methodCall)) {
      warnings.push('Method call contains string concatenation which could be risky');
    }

    // Check for template literals with expressions
    if (/`[^`]*\$\{[^}]*\}[^`]*`/.test(methodCall)) {
      warnings.push('Method call contains template literals with expressions');
    }

    if (threats.length > 0) {
      return {
        isSafe: false,
        error: 'Method call contains potentially dangerous patterns',
        threats,
      };
    }

    return {
      isSafe: true,
      sanitizedInput: baseResult.sanitizedInput,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Sanitize import statements
   * @param importStatement The import statement to sanitize
   * @param config Sanitization configuration
   * @returns Sanitization result
   */
  public static sanitizeImportStatement(
    importStatement: string,
    config: SanitizationConfig = {}
  ): SanitizationResult {
    const baseResult = this.sanitizeInput(importStatement, config);
    
    if (!baseResult.isSafe) {
      return baseResult;
    }

    const threats: string[] = baseResult.threats || [];
    const warnings: string[] = baseResult.warnings || [];

    // Check for suspicious import paths
    const suspiciousImportPatterns = [
      /['"]\.\.\/\.\.\/\.\.\/.*['"]/, // Excessive path traversal (3+ levels up)
      /['"]\/etc\/.*['"]/, // System directories
      /['"]\/proc\/.*['"]/, // Process directories
      /['"]\/sys\/.*['"]/, // System directories
      /['"]~\/.*['"]/, // Home directory access
      /['"]file:\/\/.*['"]/, // File protocol
      /['"]data:.*['"]/, // Data URLs
      /['"]javascript:.*['"]/, // JavaScript URLs
      /['"]vbscript:.*['"]/, // VBScript URLs
    ];

    for (const pattern of suspiciousImportPatterns) {
      if (pattern.test(importStatement)) {
        threats.push(`suspicious import path: ${pattern.source}`);
      }
    }

    // Check for dynamic imports
    if (/import\s*\(/.test(importStatement)) {
      warnings.push('Dynamic import detected - ensure path is trusted');
    }

    if (threats.length > 0) {
      return {
        isSafe: false,
        error: 'Import statement contains potentially dangerous paths',
        threats,
      };
    }

    return {
      isSafe: true,
      sanitizedInput: baseResult.sanitizedInput,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  /**
   * Sanitize HTML/XML markup
   * @param input The input containing markup
   * @returns Sanitized input
   */
  private static sanitizeMarkup(input: string): string {
    return input
      .replace(/<script[^>]*>.*?<\/script>/gis, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gis, '')
      .replace(/<object[^>]*>.*?<\/object>/gis, '')
      .replace(/<embed[^>]*>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/data:text\/html/gi, '');
  }

  /**
   * Validate that a string contains only safe characters for identifiers
   * @param identifier The identifier to validate
   * @returns Whether the identifier is safe
   */
  public static isValidIdentifier(identifier: string): boolean {
    // Allow letters, numbers, underscore, and dollar sign
    return /^[A-Za-z_$][A-Za-z0-9_$]*$/.test(identifier);
  }

  /**
   * Validate that a string is a safe file path
   * @param filePath The file path to validate
   * @returns Whether the path is safe
   */
  public static isValidFilePath(filePath: string): boolean {
    // Basic path validation - no null bytes, no path traversal
    return !filePath.includes('\0') && 
           !filePath.includes('..') && 
           !/[<>:"|?*]/.test(filePath);
  }
}
