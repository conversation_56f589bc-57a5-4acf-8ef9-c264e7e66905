/**
 * Parallel Type Map Scanner - High-performance parallel scanning for type map building
 * 
 * This service implements parallel scanning of TypeScript files to build complete
 * type maps without any code generation. It's designed for maximum performance
 * and memory efficiency.
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import { Worker } from 'worker_threads';
import * as os from 'os';
import { promisify } from 'util';

import type {
  TypeMap,
  TypeMapConfig,
  ParsedFileInfo,
  MethodCallInfo,
  ImportInfo,
  FieldInfo,
  ContextInfo
} from './type-map';
import { MemoryPressureMonitor } from './memory-pressure-monitor';

const globAsync = promisify(glob.glob);

/**
 * Worker task for parallel processing
 */
export interface ScannerWorkerTask {
  id: string;
  filePath: string;
  type: 'scan-file';
}

/**
 * Worker result from parallel processing
 */
export interface ScannerWorkerResult {
  taskId: string;
  success: boolean;
  filePath: string;
  result?: {
    methodCalls: MethodCallInfo[];
    imports: ImportInfo[];
    fields: FieldInfo[];
    contexts: ContextInfo[];
    fileInfo: ParsedFileInfo;
  };
  error?: string;
  duration: number;
}

/**
 * Parallel scanning metrics
 */
export interface ParallelScanMetrics {
  totalFiles: number;
  successfullyScanned: number;
  failedToScan: number;
  totalScanTime: number;
  averageScanTime: number;
  peakMemoryUsage: number;
  workerCount: number;
  batchesProcessed: number;
}

/**
 * Parallel Type Map Scanner
 */
export class ParallelTypeMapScanner {
  private config: TypeMapConfig;
  private workers: Worker[] = [];
  private memoryMonitor: MemoryPressureMonitor;
  private isShuttingDown = false;

  constructor(config: TypeMapConfig) {
    this.config = config;
    this.memoryMonitor = new MemoryPressureMonitor({
      warningThreshold: (config.memoryPressureThreshold || 0.8) * 0.8,
      criticalThreshold: config.memoryPressureThreshold || 0.8,
      monitoringInterval: 1000,
      enableGCTrigger: true,
      enableLogging: config.debug || false,
      enableMemoryMappedTracking: false,
      memoryMappedThreshold: 0.8,
      enableAutomaticFallback: true,
      enablePredictiveManagement: false,
      adaptiveThresholds: false
    });
  }

  /**
   * Scan files in parallel to build type map
   */
  async scanFiles(files: string[]): Promise<{
    typeMap: Partial<TypeMap>;
    metrics: ParallelScanMetrics;
  }> {
    const startTime = Date.now();
    
    if (this.config.debug) {
      console.log(`🚀 Starting parallel scan of ${files.length} files with ${this.config.maxWorkers} workers`);
    }

    // Start memory monitoring
    await this.memoryMonitor.start();

    // Initialize workers
    await this.initializeWorkers();

    // Process files in batches
    const batchSize = Math.max(1, Math.floor(files.length / (this.config.maxWorkers! * 2)));
    const batches = this.createBatches(files, batchSize);
    
    const typeMap: Partial<TypeMap> = {
      files: new Map(),
      methodCalls: new Map(),
      imports: new Map(),
      fields: new Map(),
      contexts: []
    };

    let successfullyScanned = 0;
    let failedToScan = 0;
    let peakMemoryUsage = 0;

    // Process batches
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      if (this.config.debug) {
        console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} files)`);
      }

      // Create tasks for this batch
      const tasks: ScannerWorkerTask[] = batch.map((filePath, index) => ({
        id: `batch-${i}-file-${index}`,
        filePath,
        type: 'scan-file'
      }));

      // Process batch in parallel
      const results = await this.processBatch(tasks);

      // Aggregate results
      for (const result of results) {
        if (result.success && result.result) {
          successfullyScanned++;
          
          // Add file info
          typeMap.files!.set(result.filePath, result.result.fileInfo);

          // Add method calls
          for (const methodCall of result.result.methodCalls) {
            if (!typeMap.methodCalls!.has(methodCall.typeName)) {
              typeMap.methodCalls!.set(methodCall.typeName, new Map());
            }
            const typeMethodCalls = typeMap.methodCalls!.get(methodCall.typeName)!;
            if (!typeMethodCalls.has(methodCall.fieldName)) {
              typeMethodCalls.set(methodCall.fieldName, []);
            }
            typeMethodCalls.get(methodCall.fieldName)!.push(methodCall);
          }

          // Add imports
          if (result.result.imports.length > 0) {
            typeMap.imports!.set(result.filePath, result.result.imports);
          }

          // Add fields
          for (const field of result.result.fields) {
            if (!typeMap.fields!.has(field.typeName)) {
              typeMap.fields!.set(field.typeName, []);
            }
            typeMap.fields!.get(field.typeName)!.push(field);
          }

          // Add contexts
          typeMap.contexts!.push(...result.result.contexts);
        } else {
          failedToScan++;
          if (this.config.debug) {
            console.warn(`Failed to scan ${result.filePath}: ${result.error}`);
          }
        }
      }

      // Check memory pressure
      const currentMemory = process.memoryUsage().heapUsed;
      if (currentMemory > peakMemoryUsage) {
        peakMemoryUsage = currentMemory;
      }

      // Check if we need to pause due to memory pressure
      const metrics = this.memoryMonitor.getCurrentMetrics();
      if (metrics.memoryPressure > 0.8) {
        if (this.config.debug) {
          console.log('⚠️  Memory pressure detected, pausing briefly...');
        }
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const totalScanTime = Date.now() - startTime;
    const metrics: ParallelScanMetrics = {
      totalFiles: files.length,
      successfullyScanned,
      failedToScan,
      totalScanTime,
      averageScanTime: files.length > 0 ? totalScanTime / files.length : 0,
      peakMemoryUsage,
      workerCount: this.workers.length,
      batchesProcessed: batches.length
    };

    await this.cleanup();

    if (this.config.debug) {
      console.log(`✅ Parallel scan completed in ${totalScanTime}ms`);
      console.log(`📊 Scanned ${successfullyScanned}/${files.length} files successfully`);
    }

    return { typeMap, metrics };
  }

  /**
   * Initialize worker threads
   */
  private async initializeWorkers(): Promise<void> {
    // Use the compiled JavaScript version of the worker
    const workerScript = path.join(__dirname, '../../dist/src/utils/type-map-scanner-worker.js');
    
    for (let i = 0; i < this.config.maxWorkers!; i++) {
      try {
        const worker = new Worker(workerScript);
        
        worker.on('error', (error) => {
          if (this.config.debug) {
            console.warn(`Worker ${i} error:`, error);
          }
        });

        worker.on('exit', (code) => {
          if (code !== 0 && !this.isShuttingDown) {
            if (this.config.debug) {
              console.warn(`Worker ${i} exited with code ${code}`);
            }
          }
        });

        this.workers.push(worker);
      } catch (error) {
        if (this.config.debug) {
          console.warn(`Failed to create worker ${i}:`, error);
        }
      }
    }

    if (this.workers.length === 0) {
      throw new Error('Failed to create any worker threads');
    }
  }

  /**
   * Process a batch of tasks
   */
  private async processBatch(tasks: ScannerWorkerTask[]): Promise<ScannerWorkerResult[]> {
    return new Promise((resolve, reject) => {
      const results: ScannerWorkerResult[] = [];
      let completedTasks = 0;

      // Distribute tasks among workers
      const tasksPerWorker = Math.ceil(tasks.length / this.workers.length);
      
      for (let i = 0; i < this.workers.length; i++) {
        const worker = this.workers[i];
        const workerTasks = tasks.slice(i * tasksPerWorker, (i + 1) * tasksPerWorker);
        
        if (workerTasks.length === 0) continue;

        const messageHandler = (result: ScannerWorkerResult) => {
          results.push(result);
          completedTasks++;
          
          if (completedTasks === tasks.length) {
            // Remove listeners
            worker.off('message', messageHandler);
            resolve(results);
          }
        };

        worker.on('message', messageHandler);
        
        // Send tasks to worker
        for (const task of workerTasks) {
          worker.postMessage(task);
        }
      }

      // Set timeout
      setTimeout(() => {
        reject(new Error('Batch processing timeout'));
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Create batches from file list
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop memory monitoring
    await this.memoryMonitor.stop();
    
    // Terminate workers
    await Promise.all(this.workers.map(worker => worker.terminate()));
    this.workers = [];
  }
}

/**
 * Global parallel scanner instance
 */
let globalParallelScanner: ParallelTypeMapScanner | null = null;

/**
 * Get or create global parallel scanner
 */
export function getGlobalParallelScanner(config?: TypeMapConfig): ParallelTypeMapScanner {
  if (!globalParallelScanner && config) {
    globalParallelScanner = new ParallelTypeMapScanner(config);
  }
  if (!globalParallelScanner) {
    throw new Error('ParallelTypeMapScanner not initialized. Provide config on first call.');
  }
  return globalParallelScanner;
}

/**
 * Reset global parallel scanner (for testing)
 */
export function resetGlobalParallelScanner(): void {
  globalParallelScanner = null;
}
