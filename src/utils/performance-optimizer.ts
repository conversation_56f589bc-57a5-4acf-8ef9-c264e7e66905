import * as os from 'os';
import { DecoratorParser } from './decorator-parser';
import { ParallelDecoratorParser } from './parallel-decorator-parser';

/**
 * Performance optimization configuration
 */
export interface PerformanceOptimizerConfig {
  /** Enable parallel processing for large codebases (default: auto-detect) */
  enableParallelProcessing?: boolean;
  /** Minimum file count to trigger parallel processing (default: 50) */
  parallelProcessingThreshold?: number;
  /** Maximum number of worker threads (default: CPU cores - 1) */
  maxWorkerThreads?: number;
  /** Enable enhanced caching (default: true) */
  enableEnhancedCaching?: boolean;
  /** Enable memory optimization (default: true) */
  enableMemoryOptimization?: boolean;
  /** Enable performance monitoring (default: true) */
  enablePerformanceMonitoring?: boolean;
}

/**
 * Performance optimization recommendations
 */
export interface PerformanceRecommendations {
  useParallelProcessing: boolean;
  recommendedWorkerCount: number;
  enableCaching: boolean;
  memoryOptimizations: string[];
  estimatedSpeedup: number;
  reasoning: string[];
}

/**
 * System performance metrics
 */
export interface SystemMetrics {
  cpuCores: number;
  totalMemoryGB: number;
  freeMemoryGB: number;
  memoryPressure: number; // 0-1 scale
  nodeVersion: string;
  platform: string;
}

/**
 * Performance optimizer for gql-generator
 */
export class PerformanceOptimizer {
  private config: Required<PerformanceOptimizerConfig>;
  private systemMetrics: SystemMetrics;

  constructor(config: PerformanceOptimizerConfig = {}) {
    this.systemMetrics = this.collectSystemMetrics();
    
    this.config = {
      enableParallelProcessing: config.enableParallelProcessing ?? this.shouldUseParallelProcessing(),
      parallelProcessingThreshold: config.parallelProcessingThreshold ?? 50,
      maxWorkerThreads: config.maxWorkerThreads ?? Math.max(1, this.systemMetrics.cpuCores - 1),
      enableEnhancedCaching: config.enableEnhancedCaching ?? true,
      enableMemoryOptimization: config.enableMemoryOptimization ?? true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring ?? true
    };
  }

  /**
   * Collect system performance metrics
   */
  private collectSystemMetrics(): SystemMetrics {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    
    return {
      cpuCores: os.cpus().length,
      totalMemoryGB: Math.round(totalMemory / (1024 * 1024 * 1024) * 100) / 100,
      freeMemoryGB: Math.round(freeMemory / (1024 * 1024 * 1024) * 100) / 100,
      memoryPressure: 1 - (freeMemory / totalMemory),
      nodeVersion: process.version,
      platform: os.platform()
    };
  }

  /**
   * Determine if parallel processing should be used based on system capabilities
   */
  private shouldUseParallelProcessing(): boolean {
    // Use parallel processing if we have multiple cores and sufficient memory
    return this.systemMetrics.cpuCores > 2 && 
           this.systemMetrics.totalMemoryGB > 4 && 
           this.systemMetrics.memoryPressure < 0.8;
  }

  /**
   * Analyze codebase and provide performance recommendations
   */
  public analyzeCodebase(fileCount: number, estimatedFileSize: number = 5000): PerformanceRecommendations {
    const reasoning: string[] = [];
    let estimatedSpeedup = 1.0;

    // Parallel processing analysis
    const useParallelProcessing = this.config.enableParallelProcessing && 
                                 fileCount >= this.config.parallelProcessingThreshold;
    
    if (useParallelProcessing) {
      const optimalWorkers = Math.min(
        this.config.maxWorkerThreads,
        Math.ceil(fileCount / 10) // Roughly 10 files per worker
      );
      
      // Estimate speedup based on Amdahl's law (assuming 80% parallelizable work)
      const parallelFraction = 0.8;
      estimatedSpeedup = 1 / ((1 - parallelFraction) + (parallelFraction / optimalWorkers));
      
      reasoning.push(`Parallel processing recommended: ${fileCount} files > ${this.config.parallelProcessingThreshold} threshold`);
      reasoning.push(`Using ${optimalWorkers} worker threads for optimal performance`);
    } else {
      reasoning.push(`Single-threaded processing: ${fileCount} files < ${this.config.parallelProcessingThreshold} threshold`);
    }

    // Memory optimization analysis
    const memoryOptimizations: string[] = [];
    const estimatedMemoryUsage = fileCount * estimatedFileSize * 2; // Rough estimate
    
    if (estimatedMemoryUsage > this.systemMetrics.freeMemoryGB * 1024 * 1024 * 1024 * 0.5) {
      memoryOptimizations.push('Enable aggressive garbage collection');
      memoryOptimizations.push('Use streaming file processing');
      memoryOptimizations.push('Implement memory pressure monitoring');
      reasoning.push('High memory usage expected, enabling memory optimizations');
    }

    // Caching analysis
    const enableCaching = this.config.enableEnhancedCaching;
    if (enableCaching) {
      reasoning.push('Enhanced caching enabled for faster subsequent runs');
      estimatedSpeedup *= 1.2; // Additional 20% speedup from caching
    }

    return {
      useParallelProcessing,
      recommendedWorkerCount: useParallelProcessing ? 
        Math.min(this.config.maxWorkerThreads, Math.ceil(fileCount / 10)) : 1,
      enableCaching,
      memoryOptimizations,
      estimatedSpeedup: Math.round(estimatedSpeedup * 100) / 100,
      reasoning
    };
  }

  /**
   * Create an optimized decorator parser based on recommendations
   */
  public createOptimizedParser(fileCount: number): DecoratorParser {
    const recommendations = this.analyzeCodebase(fileCount);
    
    const parser = new DecoratorParser(
      undefined, // Use default cache directory
      recommendations.useParallelProcessing
    );

    if (process.env.DEBUG_PARSER) {
      console.log('🚀 Performance Optimizer Recommendations:');
      console.log(`   Parallel Processing: ${recommendations.useParallelProcessing ? 'Enabled' : 'Disabled'}`);
      console.log(`   Worker Threads: ${recommendations.recommendedWorkerCount}`);
      console.log(`   Enhanced Caching: ${recommendations.enableCaching ? 'Enabled' : 'Disabled'}`);
      console.log(`   Estimated Speedup: ${recommendations.estimatedSpeedup}x`);
      console.log(`   Memory Optimizations: ${recommendations.memoryOptimizations.length} enabled`);
      
      if (recommendations.reasoning.length > 0) {
        console.log('   Reasoning:');
        recommendations.reasoning.forEach(reason => console.log(`     - ${reason}`));
      }
    }

    return parser;
  }

  /**
   * Create an optimized parallel parser for large codebases
   */
  public createOptimizedParallelParser(fileCount: number): ParallelDecoratorParser {
    const recommendations = this.analyzeCodebase(fileCount);
    
    return new ParallelDecoratorParser({
      maxWorkers: recommendations.recommendedWorkerCount,
      enableMonitoring: this.config.enablePerformanceMonitoring,
      useWorkerThreads: recommendations.useParallelProcessing,
      batchSize: Math.max(5, Math.ceil(fileCount / recommendations.recommendedWorkerCount / 2))
    });
  }

  /**
   * Get current system metrics
   */
  public getSystemMetrics(): SystemMetrics {
    return { ...this.systemMetrics };
  }

  /**
   * Get current configuration
   */
  public getConfig(): Required<PerformanceOptimizerConfig> {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PerformanceOptimizerConfig>): void {
    Object.assign(this.config, newConfig);
  }

  /**
   * Log performance optimization summary
   */
  public logOptimizationSummary(fileCount: number): void {
    const recommendations = this.analyzeCodebase(fileCount);
    const metrics = this.getSystemMetrics();

    console.log('\n🔧 Performance Optimization Summary:');
    console.log(`   System: ${metrics.cpuCores} cores, ${metrics.totalMemoryGB}GB RAM (${Math.round((1 - metrics.memoryPressure) * 100)}% available)`);
    console.log(`   Files to Process: ${fileCount}`);
    console.log(`   Strategy: ${recommendations.useParallelProcessing ? 'Parallel' : 'Single-threaded'} processing`);
    
    if (recommendations.useParallelProcessing) {
      console.log(`   Worker Threads: ${recommendations.recommendedWorkerCount}`);
    }
    
    console.log(`   Estimated Performance Gain: ${recommendations.estimatedSpeedup}x`);
    
    if (recommendations.memoryOptimizations.length > 0) {
      console.log(`   Memory Optimizations: ${recommendations.memoryOptimizations.length} active`);
    }
    
    console.log('');
  }
}

/**
 * Create a performance-optimized decorator parser
 * @param fileCount Estimated number of files to process
 * @param config Optional configuration
 * @returns Optimized decorator parser
 */
export function createOptimizedDecoratorParser(
  fileCount: number = 100,
  config?: PerformanceOptimizerConfig
): DecoratorParser {
  const optimizer = new PerformanceOptimizer(config);
  return optimizer.createOptimizedParser(fileCount);
}

/**
 * Create a performance-optimized parallel decorator parser
 * @param fileCount Estimated number of files to process
 * @param config Optional configuration
 * @returns Optimized parallel decorator parser
 */
export function createOptimizedParallelParser(
  fileCount: number = 100,
  config?: PerformanceOptimizerConfig
): ParallelDecoratorParser {
  const optimizer = new PerformanceOptimizer(config);
  return optimizer.createOptimizedParallelParser(fileCount);
}
