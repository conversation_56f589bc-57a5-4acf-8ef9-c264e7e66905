import { 
  initializeGlobalTemplateSystem, 
  TemplateInitializationOptions,
  TemplateInitializationResult 
} from './template-initialization-service';
import { 
  getGlobalTemplatePerformanceMonitor,
  TemplatePerformanceMonitorConfig 
} from './template-performance-monitor';
import { 
  getGlobalTemplateDataOptimizer,
  TemplateDataOptimizerConfig 
} from './template-data-optimizer';
import { getGlobalTemplateCache } from './template-compilation-cache';

/**
 * Configuration for template optimization integration
 */
export interface TemplateOptimizationConfig {
  /** Template initialization options */
  initialization?: TemplateInitializationOptions;
  /** Performance monitoring options */
  monitoring?: TemplatePerformanceMonitorConfig;
  /** Data optimization options */
  dataOptimization?: TemplateDataOptimizerConfig;
  /** Whether to enable automatic startup initialization (default: true) */
  autoInitialize?: boolean;
  /** Whether to enable performance monitoring (default: true) */
  enableMonitoring?: boolean;
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
}

/**
 * Result of template optimization integration
 */
export interface TemplateOptimizationResult {
  /** Whether integration was successful */
  success: boolean;
  /** Initialization result */
  initializationResult?: TemplateInitializationResult;
  /** Performance monitoring status */
  monitoringStatus: {
    isActive: boolean;
    metricsAvailable: boolean;
  };
  /** Data optimization status */
  dataOptimizationStatus: {
    isActive: boolean;
    cacheSize: number;
    hitRate: number;
  };
  /** Integration errors */
  errors: string[];
  /** Integration warnings */
  warnings: string[];
}

/**
 * Template optimization integration service
 * Coordinates all template optimization components for seamless integration
 */
export class TemplateOptimizationIntegration {
  private config: Required<TemplateOptimizationConfig>;
  private isIntegrated = false;

  constructor(config: TemplateOptimizationConfig = {}) {
    // Default to no logging in production for performance
    const defaultLogging = process.env.NODE_ENV === 'development' ? false : false;

    this.config = {
      autoInitialize: true,
      enableMonitoring: true,
      enableLogging: defaultLogging,
      ...config,
      initialization: {
        enableLogging: config.enableLogging ?? defaultLogging,
        ...(config.initialization || {}),
      },
      monitoring: {
        enableLogging: config.enableLogging ?? defaultLogging,
        ...(config.monitoring || {}),
      },
      dataOptimization: {
        enableLogging: config.enableLogging ?? defaultLogging,
        ...(config.dataOptimization || {}),
      },
    };

    if (this.config.enableLogging) {
      console.log('🚀 TemplateOptimizationIntegration initialized');
    }
  }

  /**
   * Integrate all template optimization components
   * @returns Integration result
   */
  public async integrate(): Promise<TemplateOptimizationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (this.config.enableLogging) {
      console.log('🔄 Starting template optimization integration...');
    }

    try {
      // Step 1: Initialize template system if enabled
      let initializationResult: TemplateInitializationResult | undefined;
      if (this.config.autoInitialize) {
        try {
          initializationResult = await initializeGlobalTemplateSystem(this.config.initialization);
          
          if (!initializationResult.success) {
            errors.push(...initializationResult.errors);
            warnings.push('Template initialization had errors but continued');
          }

          if (this.config.enableLogging) {
            console.log(`✅ Template system: ${initializationResult.precompilationSummary.successfulCompilations} templates pre-compiled`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push(`Template initialization failed: ${errorMessage}`);
          warnings.push('Continuing without template pre-compilation');
        }
      }

      // Step 2: Initialize performance monitoring if enabled
      let monitoringStatus = { isActive: false, metricsAvailable: false };
      if (this.config.enableMonitoring) {
        try {
          const monitor = getGlobalTemplatePerformanceMonitor(this.config.monitoring);
          monitor.startMonitoring();
          
          monitoringStatus = {
            isActive: true,
            metricsAvailable: true,
          };

          if (this.config.enableLogging) {
            console.log('✅ Performance monitoring started');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push(`Performance monitoring initialization failed: ${errorMessage}`);
          warnings.push('Continuing without performance monitoring');
        }
      }

      // Step 3: Initialize data optimization
      let dataOptimizationStatus = { isActive: false, cacheSize: 0, hitRate: 0 };
      try {
        const dataOptimizer = getGlobalTemplateDataOptimizer(this.config.dataOptimization);
        const stats = dataOptimizer.getCacheStats();
        
        dataOptimizationStatus = {
          isActive: true,
          cacheSize: stats.size,
          hitRate: stats.hitRate,
        };

        if (this.config.enableLogging) {
          console.log('✅ Data optimization initialized');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`Data optimization initialization failed: ${errorMessage}`);
        warnings.push('Continuing without data optimization');
      }

      // Step 4: Verify template cache is available
      try {
        const templateCache = getGlobalTemplateCache();
        const cacheStats = templateCache.getStatistics();
        
        if (this.config.enableLogging) {
          console.log(`✅ Template cache: ${cacheStats.entryCount} entries`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`Template cache verification failed: ${errorMessage}`);
      }

      this.isIntegrated = true;

      const result: TemplateOptimizationResult = {
        success: errors.length === 0,
        initializationResult,
        monitoringStatus,
        dataOptimizationStatus,
        errors,
        warnings,
      };

      if (this.config.enableLogging) {
        this.logIntegrationResult(result);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`Integration failed: ${errorMessage}`);

      return {
        success: false,
        monitoringStatus: { isActive: false, metricsAvailable: false },
        dataOptimizationStatus: { isActive: false, cacheSize: 0, hitRate: 0 },
        errors,
        warnings,
      };
    }
  }

  /**
   * Get current optimization status
   * @returns Current status of all optimization components
   */
  public getOptimizationStatus(): {
    isIntegrated: boolean;
    templateCache: {
      isActive: boolean;
      entryCount: number;
      hitRate: number;
      memoryUsage: number;
    };
    performanceMonitoring: {
      isActive: boolean;
      alertCount: number;
    };
    dataOptimization: {
      isActive: boolean;
      cacheSize: number;
      hitRate: number;
    };
  } {
    try {
      // Get template cache status
      const templateCache = getGlobalTemplateCache();
      const cacheStats = templateCache.getStatistics();

      // Get performance monitoring status
      let performanceMonitoring = { isActive: false, alertCount: 0 };
      try {
        const monitor = getGlobalTemplatePerformanceMonitor();
        const metrics = monitor.getMetrics();
        performanceMonitoring = {
          isActive: true,
          alertCount: metrics.alerts.length,
        };
      } catch (error) {
        // Performance monitoring not available
      }

      // Get data optimization status
      let dataOptimization = { isActive: false, cacheSize: 0, hitRate: 0 };
      try {
        const dataOptimizer = getGlobalTemplateDataOptimizer();
        const stats = dataOptimizer.getCacheStats();
        dataOptimization = {
          isActive: true,
          cacheSize: stats.size,
          hitRate: stats.hitRate,
        };
      } catch (error) {
        // Data optimization not available
      }

      return {
        isIntegrated: this.isIntegrated,
        templateCache: {
          isActive: true,
          entryCount: cacheStats.entryCount,
          hitRate: cacheStats.hitRate,
          memoryUsage: cacheStats.estimatedMemoryUsage,
        },
        performanceMonitoring,
        dataOptimization,
      };
    } catch (error) {
      return {
        isIntegrated: false,
        templateCache: { isActive: false, entryCount: 0, hitRate: 0, memoryUsage: 0 },
        performanceMonitoring: { isActive: false, alertCount: 0 },
        dataOptimization: { isActive: false, cacheSize: 0, hitRate: 0 },
      };
    }
  }

  /**
   * Get performance summary across all optimization components
   * @returns Comprehensive performance summary
   */
  public getPerformanceSummary(): {
    overall: 'excellent' | 'good' | 'warning' | 'critical';
    summary: string;
    metrics: {
      templateCompilationHitRate: number;
      dataOptimizationHitRate: number;
      averageCompilationTime: number;
      memoryUsage: number;
      activeAlerts: number;
    };
    recommendations: string[];
  } {
    try {
      const status = this.getOptimizationStatus();
      const recommendations: string[] = [];
      
      // Calculate overall performance score
      let score = 0;
      let maxScore = 0;

      // Template cache performance (40% weight)
      if (status.templateCache.isActive) {
        score += (status.templateCache.hitRate / 100) * 40;
        maxScore += 40;
      }

      // Data optimization performance (30% weight)
      if (status.dataOptimization.isActive) {
        score += (status.dataOptimization.hitRate / 100) * 30;
        maxScore += 30;
      }

      // Monitoring health (30% weight)
      if (status.performanceMonitoring.isActive) {
        const alertPenalty = Math.min(status.performanceMonitoring.alertCount * 10, 30);
        score += Math.max(0, 30 - alertPenalty);
        maxScore += 30;
      }

      const overallPercentage = maxScore > 0 ? (score / maxScore) * 100 : 0;

      let overall: 'excellent' | 'good' | 'warning' | 'critical';
      let summary: string;

      if (overallPercentage >= 90) {
        overall = 'excellent';
        summary = 'Template optimization is performing excellently';
      } else if (overallPercentage >= 75) {
        overall = 'good';
        summary = 'Template optimization is performing well';
      } else if (overallPercentage >= 50) {
        overall = 'warning';
        summary = 'Template optimization has performance concerns';
      } else {
        overall = 'critical';
        summary = 'Template optimization requires immediate attention';
      }

      // Generate recommendations
      if (status.templateCache.hitRate < 80) {
        recommendations.push('Consider pre-compiling more templates to improve cache hit rate');
      }
      
      if (status.dataOptimization.hitRate < 70) {
        recommendations.push('Data optimization cache hit rate is low, consider increasing cache size');
      }
      
      if (status.performanceMonitoring.alertCount > 0) {
        recommendations.push(`Address ${status.performanceMonitoring.alertCount} active performance alert(s)`);
      }
      
      if (status.templateCache.memoryUsage > 50 * 1024 * 1024) { // 50MB
        recommendations.push('Template cache memory usage is high, consider optimization');
      }

      return {
        overall,
        summary,
        metrics: {
          templateCompilationHitRate: status.templateCache.hitRate,
          dataOptimizationHitRate: status.dataOptimization.hitRate,
          averageCompilationTime: 0, // Would need to get from cache stats
          memoryUsage: status.templateCache.memoryUsage,
          activeAlerts: status.performanceMonitoring.alertCount,
        },
        recommendations,
      };
    } catch (error) {
      return {
        overall: 'critical',
        summary: 'Unable to assess template optimization performance',
        metrics: {
          templateCompilationHitRate: 0,
          dataOptimizationHitRate: 0,
          averageCompilationTime: 0,
          memoryUsage: 0,
          activeAlerts: 0,
        },
        recommendations: ['Check template optimization system configuration'],
      };
    }
  }

  /**
   * Log integration result (concise summary only)
   * @param result Integration result to log
   */
  private logIntegrationResult(result: TemplateOptimizationResult): void {
    const status = result.success ? 'completed' : 'completed with issues';
    const templates = result.initializationResult?.precompilationSummary.successfulCompilations ?? 0;
    const savings = result.initializationResult?.estimatedTimeSavings.toFixed(0) ?? '0';

    console.log(`🎉 Template optimization ${status}: ${templates} templates, ~${savings}ms savings/cycle`);

    if (result.warnings.length > 0 || result.errors.length > 0) {
      console.log(`   ⚠️  ${result.warnings.length} warnings, ${result.errors.length} errors`);
    }
  }

  /**
   * Check if optimization is integrated
   * @returns True if integrated
   */
  public isOptimizationIntegrated(): boolean {
    return this.isIntegrated;
  }
}

// Global singleton instance
let globalOptimizationIntegration: TemplateOptimizationIntegration | null = null;

/**
 * Get the global template optimization integration instance
 * @param config Optional integration configuration (only used on first call)
 * @returns Global integration instance
 */
export function getGlobalTemplateOptimizationIntegration(
  config?: TemplateOptimizationConfig
): TemplateOptimizationIntegration {
  if (!globalOptimizationIntegration) {
    globalOptimizationIntegration = new TemplateOptimizationIntegration(config);
  }
  return globalOptimizationIntegration;
}

/**
 * Initialize template optimization system
 * Convenience function for application startup
 * @param config Optional integration configuration
 * @returns Integration result
 */
export async function initializeTemplateOptimization(
  config?: TemplateOptimizationConfig
): Promise<TemplateOptimizationResult> {
  const integration = getGlobalTemplateOptimizationIntegration(config);
  return await integration.integrate();
}

/**
 * Reset the global template optimization integration
 * Useful for testing or when configuration changes
 */
export function resetGlobalTemplateOptimizationIntegration(): void {
  globalOptimizationIntegration = null;
}
