import fs from 'fs-extra';
import path from 'path';
import type { DocumentNode } from 'graphql';

/**
 * Represents a mapping between merged schema positions and original source files
 */
export interface SourceMapping {
  /** The original source file path */
  originalFile: string;
  /** Line number in the original file (1-based) */
  originalLine: number;
  /** Column number in the original file (1-based) */
  originalColumn: number;
  /** Line number in the merged schema (1-based) */
  mergedLine: number;
  /** Column number in the merged schema (1-based) */
  mergedColumn: number;
  /** The content of this line in the original file */
  originalContent: string;
}

/**
 * Information about where a type is used
 */
export interface TypeUsage {
  /** The type name */
  typeName: string;
  /** Files where this type is defined */
  definedIn: string[];
  /** Files where this type is referenced/used */
  referencedIn: string[];
}

/**
 * Enhanced error information with source file context
 */
export interface EnhancedSchemaError {
  /** The original error that occurred */
  originalError: Error;
  /** The source file where the error likely originated */
  sourceFile?: string;
  /** Line number in the original source file */
  originalLine?: number;
  /** Column number in the original source file */
  originalColumn?: number;
  /** Content of the problematic line in the original file */
  originalContent?: string;
  /** Context information for debugging */
  context: {
    /** All schema files involved in the merge */
    allFiles: string[];
    /** Map of file contents for reference */
    fileContents: Map<string, string>;
    /** Suggested files to check based on error analysis */
    suggestedFiles: string[];
    /** Detailed type usage information */
    typeUsages: TypeUsage[];
  };
}

/**
 * Schema file information for source mapping
 */
export interface SchemaFileInfo {
  /** File path */
  filePath: string;
  /** File content */
  content: string;
  /** Parsed GraphQL document */
  document: DocumentNode;
  /** Line count in the file */
  lineCount: number;
}

/**
 * Utility class for enhancing GraphQL schema errors with source file context
 */
export class SchemaErrorEnhancer {
  private sourceMappings: SourceMapping[] = [];
  private schemaFiles: SchemaFileInfo[] = [];
  private mergedSchemaContent: string = '';

  /**
   * Initialize the error enhancer with schema files
   * @param schemaFiles Array of schema file information
   * @param mergedSchema The merged schema content
   */
  public initialize(schemaFiles: SchemaFileInfo[], mergedSchema: string): void {
    this.schemaFiles = schemaFiles;
    this.mergedSchemaContent = mergedSchema;
    this.buildSourceMappings();
  }

  /**
   * Enhance an error with source file context
   * @param error The original error
   * @returns Enhanced error with source context
   */
  public enhanceError(error: Error): EnhancedSchemaError {
    const sourceMapping = this.findSourceMapping(error);
    const suggestedFiles = this.analyzePotentialSources(error);
    const typeUsages = this.analyzeTypeUsages(error);

    const fileContents = new Map<string, string>();
    this.schemaFiles.forEach(file => {
      fileContents.set(file.filePath, file.content);
    });

    return {
      originalError: error,
      sourceFile: sourceMapping?.originalFile,
      originalLine: sourceMapping?.originalLine,
      originalColumn: sourceMapping?.originalColumn,
      originalContent: sourceMapping?.originalContent,
      context: {
        allFiles: this.schemaFiles.map(f => f.filePath),
        fileContents,
        suggestedFiles,
        typeUsages
      }
    };
  }

  /**
   * Build source mappings between merged schema and original files
   */
  private buildSourceMappings(): void {
    this.sourceMappings = [];

    if (!this.mergedSchemaContent) {
      return; // No merged schema to map
    }

    const mergedLines = this.mergedSchemaContent.split('\n');

    // Create a more sophisticated mapping by analyzing GraphQL constructs
    for (const schemaFile of this.schemaFiles) {
      const originalLines = schemaFile.content.split('\n');

      for (let originalLineIndex = 0; originalLineIndex < originalLines.length; originalLineIndex++) {
        const originalLine = originalLines[originalLineIndex];
        const trimmedOriginalLine = originalLine.trim();

        // Skip empty lines and comments for mapping
        if (trimmedOriginalLine === '' || trimmedOriginalLine.startsWith('#')) {
          continue;
        }

        // Look for significant GraphQL constructs
        if (this.isSignificantGraphQLLine(trimmedOriginalLine)) {
          // Find this line in the merged schema
          const mergedLineIndex = mergedLines.findIndex(line =>
            line.trim() === trimmedOriginalLine
          );

          if (mergedLineIndex !== -1) {
            this.sourceMappings.push({
              originalFile: schemaFile.filePath,
              originalLine: originalLineIndex + 1,
              originalColumn: 1,
              mergedLine: mergedLineIndex + 1,
              mergedColumn: 1,
              originalContent: originalLine
            });
          }
        }
      }
    }
  }

  /**
   * Check if a line contains significant GraphQL constructs
   */
  private isSignificantGraphQLLine(line: string): boolean {
    const significantPatterns = [
      /^type\s+\w+/,           // Type definitions
      /^interface\s+\w+/,      // Interface definitions
      /^union\s+\w+/,          // Union definitions
      /^enum\s+\w+/,           // Enum definitions
      /^input\s+\w+/,          // Input definitions
      /^scalar\s+\w+/,         // Scalar definitions
      /^extend\s+type\s+\w+/,  // Type extensions
      /^\w+\s*\(/,             // Field definitions with arguments
      /^\w+\s*:/,              // Simple field definitions
      /^schema\s*{/,           // Schema definition
      /^directive\s+@\w+/      // Directive definitions
    ];

    return significantPatterns.some(pattern => pattern.test(line));
  }

  /**
   * Find the source mapping for an error based on line/column information
   * @param error The error to analyze
   * @returns Source mapping if found
   */
  private findSourceMapping(error: Error): SourceMapping | undefined {
    // Extract line/column information from error message
    const lineColumnMatch = error.message.match(/line (\d+), column (\d+)/i) ||
      error.message.match(/(\d+):(\d+)/);

    if (!lineColumnMatch) {
      return undefined;
    }

    const errorLine = parseInt(lineColumnMatch[1], 10);
    const errorColumn = parseInt(lineColumnMatch[2], 10);

    // Find the closest source mapping
    return this.sourceMappings.find(mapping =>
      mapping.mergedLine === errorLine
    ) || this.sourceMappings.find(mapping =>
      Math.abs(mapping.mergedLine - errorLine) <= 2 // Allow some tolerance
    );
  }

  /**
   * Analyze error message to suggest potential source files
   * @param error The error to analyze
   * @returns Array of suggested file paths to check
   */
  private analyzePotentialSources(error: Error): string[] {
    const suggestions: string[] = [];
    const errorMessage = error.message.toLowerCase();

    // Enhanced type name extraction patterns
    const typeExtractionPatterns = [
      /type\s+"([^"]+)"/gi,           // "type SomeType"
      /type\s+(\w+)/gi,               // type SomeType
      /"([A-Z]\w+)"/g,                // "SomeType" in quotes
      /field\s+"([^"]+)"/gi,          // "field someField"
      /cannot\s+return\s+null\s+for\s+non-nullable\s+field\s+(\w+)/gi, // GraphQL execution errors
      /unknown\s+type\s+"([^"]+)"/gi, // Unknown type errors
      /undefined\s+type\s+(\w+)/gi,   // Undefined type errors
      /expected\s+type\s+(\w+)/gi,    // Expected type errors
      /(\w+)\s+is\s+not\s+defined/gi  // Type not defined errors
    ];

    const extractedTypes = new Set<string>();

    // Extract all potential type names from error message
    for (const pattern of typeExtractionPatterns) {
      const matches = error.message.matchAll(pattern);
      for (const match of matches) {
        const typeName = match[1]?.replace(/[^a-zA-Z0-9]/g, '');
        if (typeName && typeName.length > 1) {
          extractedTypes.add(typeName);
        }
      }
    }

    // Find files that might contain these types
    for (const typeName of extractedTypes) {
      const filesWithDefinitions: string[] = [];
      const filesWithReferences: string[] = [];

      for (const schemaFile of this.schemaFiles) {
        const content = schemaFile.content;

        // Check for type definitions
        const definitionPatterns = [
          new RegExp(`type\\s+${typeName}\\b`, 'i'),
          new RegExp(`interface\\s+${typeName}\\b`, 'i'),
          new RegExp(`union\\s+${typeName}\\b`, 'i'),
          new RegExp(`enum\\s+${typeName}\\b`, 'i'),
          new RegExp(`input\\s+${typeName}\\b`, 'i'),
          new RegExp(`scalar\\s+${typeName}\\b`, 'i'),
          new RegExp(`extend\\s+type\\s+${typeName}\\b`, 'i')
        ];

        // Check for type references (usage)
        const referencePatterns = [
          new RegExp(`:\\s*${typeName}\\b`, 'i'),
          new RegExp(`:\\s*\\[${typeName}\\]`, 'i'),
          new RegExp(`:\\s*${typeName}!`, 'i'),
          new RegExp(`\\(.*?:\\s*${typeName}\\b`, 'i'), // Function arguments
          new RegExp(`implements\\s+.*?${typeName}\\b`, 'i') // Interface implementations
        ];

        const hasDefinition = definitionPatterns.some(pattern => pattern.test(content));
        const hasReference = referencePatterns.some(pattern => pattern.test(content));

        if (hasDefinition) {
          filesWithDefinitions.push(schemaFile.filePath);
        }
        if (hasReference) {
          filesWithReferences.push(schemaFile.filePath);
        }
      }

      // For "unknown type" errors, prioritize files that reference the type
      // (where the error likely originates) over files that might define it
      if (errorMessage.includes('unknown') || errorMessage.includes('undefined') ||
        errorMessage.includes('not defined')) {
        // First suggest files that reference the unknown type
        suggestions.push(...filesWithReferences);
        // Then suggest files that might need to define it
        suggestions.push(...filesWithDefinitions);
      } else {
        // For other errors, prioritize definition files
        suggestions.push(...filesWithDefinitions);
        suggestions.push(...filesWithReferences);
      }
    }

    // Analyze error categories for better suggestions
    if (suggestions.length === 0) {
      if (errorMessage.includes('syntax') || errorMessage.includes('unexpected') ||
        errorMessage.includes('expected') || errorMessage.includes('invalid')) {
        // For syntax errors, prioritize files with recent modifications or complex structures
        const syntaxSuggestions = this.schemaFiles
          .filter(f => this.hasComplexStructures(f.content))
          .map(f => f.filePath);
        suggestions.push(...syntaxSuggestions);

        // If still no suggestions, include all files
        if (suggestions.length === 0) {
          suggestions.push(...this.schemaFiles.map(f => f.filePath));
        }
      } else if (errorMessage.includes('unknown') || errorMessage.includes('undefined') ||
        errorMessage.includes('not defined') || errorMessage.includes('cannot resolve')) {
        // For unknown type errors, suggest files that define types
        suggestions.push(...this.schemaFiles
          .filter(f => this.definesTypes(f.content))
          .map(f => f.filePath));
      } else if (errorMessage.includes('duplicate') || errorMessage.includes('already defined')) {
        // For duplicate definition errors, suggest all files that define types
        suggestions.push(...this.schemaFiles
          .filter(f => this.definesTypes(f.content))
          .map(f => f.filePath));
      }
    }

    return suggestions.slice(0, 8); // Increased limit for better coverage
  }

  /**
   * Analyze type usages across all schema files
   * @param error The error to analyze
   * @returns Array of type usage information
   */
  private analyzeTypeUsages(error: Error): TypeUsage[] {
    const typeUsages: TypeUsage[] = [];

    // Extract type names from error message
    const typeExtractionPatterns = [
      /type\s+"([^"]+)"/gi,           // "type SomeType"
      /type\s+(\w+)/gi,               // type SomeType
      /"([A-Z]\w+)"/g,                // "SomeType" in quotes
      /unknown\s+type\s+"([^"]+)"/gi, // Unknown type errors
      /undefined\s+type\s+(\w+)/gi,   // Undefined type errors
      /(\w+)\s+is\s+not\s+defined/gi  // Type not defined errors
    ];

    const extractedTypes = new Set<string>();

    // Extract all potential type names from error message
    for (const pattern of typeExtractionPatterns) {
      const matches = error.message.matchAll(pattern);
      for (const match of matches) {
        const typeName = match[1]?.replace(/[^a-zA-Z0-9]/g, '');
        if (typeName && typeName.length > 1) {
          extractedTypes.add(typeName);
        }
      }
    }

    // Analyze each extracted type
    for (const typeName of extractedTypes) {
      const definedIn: string[] = [];
      const referencedIn: string[] = [];

      for (const schemaFile of this.schemaFiles) {
        const content = schemaFile.content;

        // Check for type definitions
        const definitionPatterns = [
          new RegExp(`type\\s+${typeName}\\b`, 'i'),
          new RegExp(`interface\\s+${typeName}\\b`, 'i'),
          new RegExp(`union\\s+${typeName}\\b`, 'i'),
          new RegExp(`enum\\s+${typeName}\\b`, 'i'),
          new RegExp(`input\\s+${typeName}\\b`, 'i'),
          new RegExp(`scalar\\s+${typeName}\\b`, 'i'),
          new RegExp(`extend\\s+type\\s+${typeName}\\b`, 'i')
        ];

        // Check for type references (usage)
        const referencePatterns = [
          new RegExp(`:\\s*${typeName}\\b`, 'i'),
          new RegExp(`:\\s*\\[${typeName}\\]`, 'i'),
          new RegExp(`:\\s*${typeName}!`, 'i'),
          new RegExp(`\\(.*?:\\s*${typeName}\\b`, 'i'), // Function arguments
          new RegExp(`implements\\s+.*?${typeName}\\b`, 'i'), // Interface implementations
          new RegExp(`union\\s+\\w+\\s*=.*?${typeName}\\b`, 'i') // Union member
        ];

        const hasDefinition = definitionPatterns.some(pattern => pattern.test(content));
        const hasReference = referencePatterns.some(pattern => pattern.test(content));

        if (hasDefinition) {
          definedIn.push(schemaFile.filePath);
        }
        if (hasReference) {
          referencedIn.push(schemaFile.filePath);
        }
      }

      // Only include types that have some usage information
      if (definedIn.length > 0 || referencedIn.length > 0) {
        typeUsages.push({
          typeName,
          definedIn,
          referencedIn
        });
      }
    }

    return typeUsages;
  }

  /**
   * Check if a file content has complex GraphQL structures
   */
  private hasComplexStructures(content: string): boolean {
    const complexPatterns = [
      /union\s+\w+\s*=/,              // Union definitions
      /interface\s+\w+/,              // Interface definitions
      /extend\s+type/,                // Type extensions
      /directive\s+@\w+/,             // Custom directives
      /input\s+\w+/,                  // Input types
      /enum\s+\w+/,                   // Enum definitions
      /\w+\s*\([^)]+\)\s*:/          // Fields with complex arguments
    ];

    return complexPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Check if a file content defines types
   */
  private definesTypes(content: string): boolean {
    const typeDefinitionPatterns = [
      /type\s+\w+/,
      /interface\s+\w+/,
      /union\s+\w+/,
      /enum\s+\w+/,
      /input\s+\w+/,
      /scalar\s+\w+/
    ];

    return typeDefinitionPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Format an enhanced error for display
   * @param enhancedError The enhanced error to format
   * @returns Formatted error message
   */
  public static formatError(enhancedError: EnhancedSchemaError): string {
    const { originalError, sourceFile, originalLine, originalContent, context } = enhancedError;

    let formattedMessage = '\n' + '='.repeat(80) + '\n';
    formattedMessage += '🚨 GraphQL Schema Error\n';
    formattedMessage += '='.repeat(80) + '\n\n';

    formattedMessage += `❌ Error: ${originalError.message}\n\n`;

    if (sourceFile && originalLine) {
      formattedMessage += `📍 Likely Source:\n`;
      formattedMessage += `   File: ${path.relative(process.cwd(), sourceFile)}\n`;
      formattedMessage += `   Line: ${originalLine}\n`;

      if (originalContent) {
        formattedMessage += `   Content: ${originalContent.trim()}\n`;
      }
      formattedMessage += '\n';
    }

    // Show detailed type usage information - ONLY for problematic types
    const problematicTypes = context.typeUsages.filter(usage => usage.definedIn.length === 0);

    if (problematicTypes.length > 0) {
      formattedMessage += `📋 Missing Types Analysis:\n`;

      problematicTypes.forEach((usage, index) => {
        formattedMessage += `\n   ${index + 1}. Type: ${usage.typeName}\n`;
        formattedMessage += `      ❌ Not defined anywhere\n`;

        if (usage.referencedIn.length > 0) {
          formattedMessage += `      🔗 Referenced in:\n`;
          usage.referencedIn.forEach(file => {
            formattedMessage += `         • ${path.relative(process.cwd(), file)}\n`;
          });
        }
      });
      formattedMessage += '\n';
    }

    if (context.suggestedFiles.length > 0) {
      // Provide more specific guidance based on error type
      if (originalError.message.toLowerCase().includes('unknown type')) {
        formattedMessage += `🔍 Quick Fix Suggestions:\n`;
        context.suggestedFiles.slice(0, 3).forEach((file, index) => {
          if (index === 0) {
            formattedMessage += `   ${index + 1}. ${path.relative(process.cwd(), file)} ← Check for typos in type references\n`;
          } else {
            formattedMessage += `   ${index + 1}. ${path.relative(process.cwd(), file)} ← Consider defining missing types here\n`;
          }
        });
      } else {
        formattedMessage += `🔍 Files to Check:\n`;
        context.suggestedFiles.forEach((file, index) => {
          formattedMessage += `   ${index + 1}. ${path.relative(process.cwd(), file)}\n`;
        });
      }
      formattedMessage += '\n';
    }

    formattedMessage += `📁 All Schema Files (${context.allFiles.length}):\n`;
    context.allFiles.forEach((file, index) => {
      formattedMessage += `   ${index + 1}. ${path.relative(process.cwd(), file)}\n`;
    });
    formattedMessage += '\n';

    formattedMessage += `💡 Debugging Tips:\n`;

    if (originalError.message.toLowerCase().includes('unknown type')) {
      formattedMessage += `   • Check the first suggested file for typos in the type name\n`;
      formattedMessage += `   • Define the missing type in one of your schema files\n`;
      formattedMessage += `   • Verify the type name is spelled correctly where it's referenced\n`;
      formattedMessage += `   • Make sure the type is exported/available in the schema\n`;
    } else if (originalError.message.toLowerCase().includes('syntax')) {
      formattedMessage += `   • Look for missing braces, commas, or parentheses\n`;
      formattedMessage += `   • Check for proper GraphQL syntax in type definitions\n`;
      formattedMessage += `   • Verify field definitions have correct format (name: Type)\n`;
      formattedMessage += `   • Ensure all blocks are properly closed\n`;
    } else {
      formattedMessage += `   • Check for typos in type names and field definitions\n`;
      formattedMessage += `   • Verify all referenced types are defined in your schema files\n`;
      formattedMessage += `   • Look for syntax errors like missing braces or commas\n`;
      formattedMessage += `   • Ensure proper GraphQL syntax in the suggested files above\n`;
    }
    formattedMessage += '\n';

    formattedMessage += '='.repeat(80) + '\n';

    return formattedMessage;
  }
}
