import { execSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs-extra';
import { glob } from 'glob';

/**
 * Supported formatters
 */
export type FormatterType = 'prettier' | 'biome' | 'swc' | 'none';

/**
 * Formatter configuration
 */
export interface FormatterConfig {
  type: FormatterType;
  enabled: boolean;
  extensions: string[];
  timeout: number; // milliseconds
  debug: boolean;
}

/**
 * Formatting result
 */
export interface FormatResult {
  success: boolean;
  filesFormatted: number;
  duration: number;
  errors: string[];
  formatter: FormatterType;
}

/**
 * High-performance batch formatter that supports multiple formatting tools
 * Designed to replace individual file formatting for better performance
 */
export class BatchFormatter {
  private config: FormatterConfig;

  constructor(config: Partial<FormatterConfig> = {}) {
    this.config = {
      type: 'prettier',
      enabled: true,
      extensions: ['.ts', '.js', '.tsx', '.jsx', '.json', '.gql', '.graphql'],
      timeout: 30000, // 30 seconds
      debug: false,
      ...config
    };
  }

  /**
   * Format all files in a directory using the configured formatter
   */
  async formatDirectory(outputDir: string): Promise<FormatResult> {
    if (!this.config.enabled || this.config.type === 'none') {
      return {
        success: true,
        filesFormatted: 0,
        duration: 0,
        errors: [],
        formatter: this.config.type
      };
    }

    const startTime = Date.now();
    
    if (this.config.debug) {
      console.log(`🎨 Starting batch formatting with ${this.config.type}...`);
    }

    try {
      // Get all files to format
      const files = await this.getFilesToFormat(outputDir);
      
      if (files.length === 0) {
        if (this.config.debug) {
          console.log('📦 No files found to format');
        }
        return {
          success: true,
          filesFormatted: 0,
          duration: Date.now() - startTime,
          errors: [],
          formatter: this.config.type
        };
      }

      // Format based on the selected formatter
      const result = await this.runFormatter(outputDir, files);
      
      const duration = Date.now() - startTime;
      
      if (this.config.debug) {
        console.log(`✅ Batch formatting completed: ${result.filesFormatted} files in ${duration}ms`);
      }

      return {
        ...result,
        duration,
        formatter: this.config.type
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (this.config.debug) {
        console.error(`❌ Batch formatting failed: ${errorMessage}`);
      }

      return {
        success: false,
        filesFormatted: 0,
        duration,
        errors: [errorMessage],
        formatter: this.config.type
      };
    }
  }

  /**
   * Get all files that should be formatted
   */
  private async getFilesToFormat(outputDir: string): Promise<string[]> {
    const patterns = this.config.extensions.map(ext => 
      `${outputDir}/**/*${ext}`
    );

    const allFiles: string[] = [];
    
    for (const pattern of patterns) {
      try {
        const files = await glob(pattern, { 
          windowsPathsNoEscape: true,
          ignore: ['**/node_modules/**', '**/.git/**']
        });
        allFiles.push(...files);
      } catch (error) {
        if (this.config.debug) {
          console.warn(`⚠️  Warning: Failed to glob pattern ${pattern}:`, error);
        }
      }
    }

    // Remove duplicates and return
    return [...new Set(allFiles)];
  }

  /**
   * Run the appropriate formatter
   */
  private async runFormatter(outputDir: string, files: string[]): Promise<Omit<FormatResult, 'duration' | 'formatter'>> {
    switch (this.config.type) {
      case 'prettier':
        return this.runPrettier(outputDir, files);
      case 'biome':
        return this.runBiome(outputDir, files);
      case 'swc':
        return this.runSwc(outputDir, files);
      default:
        throw new Error(`Unsupported formatter: ${this.config.type}`);
    }
  }

  /**
   * Run Prettier in batch mode
   */
  private async runPrettier(outputDir: string, files: string[]): Promise<Omit<FormatResult, 'duration' | 'formatter'>> {
    try {
      // Use prettier with glob patterns for maximum efficiency
      const extensions = this.config.extensions.join(',');
      const pattern = `"${outputDir}/**/*.{${extensions.replace(/\./g, '').replace(/,/g, ',')}}"`;
      
      const command = `npx prettier --write ${pattern}`;
      
      if (this.config.debug) {
        console.log(`🔧 Running: ${command}`);
      }

      execSync(command, {
        stdio: this.config.debug ? 'inherit' : 'pipe',
        timeout: this.config.timeout,
        encoding: 'utf8'
      });

      return {
        success: true,
        filesFormatted: files.length,
        errors: []
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        filesFormatted: 0,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Run Biome formatter (if available)
   */
  private async runBiome(outputDir: string, files: string[]): Promise<Omit<FormatResult, 'duration' | 'formatter'>> {
    try {
      const command = `npx @biomejs/biome format --write "${outputDir}"`;
      
      if (this.config.debug) {
        console.log(`🔧 Running: ${command}`);
      }

      execSync(command, {
        stdio: this.config.debug ? 'inherit' : 'pipe',
        timeout: this.config.timeout,
        encoding: 'utf8'
      });

      return {
        success: true,
        filesFormatted: files.length,
        errors: []
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        filesFormatted: 0,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Run SWC formatter (basic implementation)
   */
  private async runSwc(outputDir: string, files: string[]): Promise<Omit<FormatResult, 'duration' | 'formatter'>> {
    // SWC doesn't have a built-in formatter like Prettier
    // This is a placeholder for potential future SWC formatting integration
    if (this.config.debug) {
      console.log('⚠️  SWC formatting not yet implemented, falling back to prettier');
    }
    
    return this.runPrettier(outputDir, files);
  }

  /**
   * Check if a formatter is available
   */
  static async isFormatterAvailable(formatter: FormatterType): Promise<boolean> {
    if (formatter === 'none') return true;
    
    try {
      switch (formatter) {
        case 'prettier':
          execSync('npx prettier --version', { stdio: 'pipe' });
          return true;
        case 'biome':
          execSync('npx @biomejs/biome --version', { stdio: 'pipe' });
          return true;
        case 'swc':
          // Check if @swc/core is available
          require.resolve('@swc/core');
          return true;
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  /**
   * Get the best available formatter (prioritizes speed: Biome > Prettier > SWC)
   */
  static async getBestAvailableFormatter(): Promise<FormatterType> {
    // Priority order: Biome (fastest) -> Prettier (most compatible) -> SWC -> none
    const formatters: FormatterType[] = ['biome', 'prettier', 'swc'];

    for (const formatter of formatters) {
      if (await this.isFormatterAvailable(formatter)) {
        return formatter;
      }
    }

    return 'none';
  }
}

/**
 * Global batch formatter instance
 */
let globalBatchFormatter: BatchFormatter | null = null;

/**
 * Get or create the global batch formatter
 */
export function getGlobalBatchFormatter(config?: Partial<FormatterConfig>): BatchFormatter {
  if (!globalBatchFormatter || config) {
    globalBatchFormatter = new BatchFormatter(config);
  }
  return globalBatchFormatter;
}

/**
 * Format a directory using the global batch formatter
 */
export async function formatDirectory(outputDir: string, config?: Partial<FormatterConfig>): Promise<FormatResult> {
  const formatter = getGlobalBatchFormatter(config);
  return formatter.formatDirectory(outputDir);
}
