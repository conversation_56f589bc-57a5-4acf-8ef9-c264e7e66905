/**
 * Type definitions and interfaces for the TypeScript decorator system
 */

/**
 * Base interface for all decorator metadata
 */
export interface BaseDecoratorData {
  /** Schema identifier for multi-schema support */
  schema?: string;
}

/**
 * Configuration for decorator processing
 */
export interface DecoratorProcessingConfig {
  /** Whether to enable decorator scanning */
  enabled: boolean;
  /** Codebase directory to scan */
  codebaseDir?: string;
  /** File patterns to include in scanning */
  includePatterns: string[];
  /** File patterns to exclude from scanning */
  excludePatterns: string[];
  /** Whether to cache decorator scan results */
  enableCaching: boolean;
  /** Whether decorators should override comment-based directives */
  overrideComments: boolean;
  /** Maximum number of files to scan concurrently */
  maxConcurrency: number;
}

/**
 * Default configuration for decorator processing
 */
export const DEFAULT_DECORATOR_CONFIG: DecoratorProcessingConfig = {
  enabled: false,
  includePatterns: ['**/*.ts', '**/*.tsx'],
  excludePatterns: ['**/node_modules/**', '**/dist/**', '**/*.d.ts', '**/__tests__/**', '**/*.test.ts', '**/*.spec.ts'],
  enableCaching: true,
  overrideComments: true,
  maxConcurrency: 10,
};

/**
 * Validation result for decorator parameters
 */
export interface DecoratorValidationResult {
  /** Whether the decorator is valid */
  valid: boolean;
  /** Array of validation error messages */
  errors: string[];
  /** Array of validation warning messages */
  warnings: string[];
}

/**
 * Enhanced @GQLMethodCall decorator data with validation
 */
export interface GQLMethodCallData extends BaseDecoratorData {
  /** GraphQL type name (required) */
  type: string;
  /** GraphQL field name (optional, inferred from function name if not provided) */
  field?: string;
  /** Method call expression (optional, smart default generated if not provided) */
  call?: string;
  /** Whether to generate async wrapper */
  async?: boolean;
  /** Custom error handling expression */
  errorHandler?: string;
  /** Whether to enable type casting support */
  enableTypeCasting?: boolean;
}

/**
 * Enhanced @GQLImport decorator data with validation
 */
export interface GQLImportData extends BaseDecoratorData {
  /** Import statement (required) */
  importStatement: string;
  /** Whether this import is conditional */
  conditional?: boolean;
  /** Condition expression for conditional imports */
  condition?: string;
}

/**
 * Enhanced @GQLField decorator data with validation
 */
export interface GQLFieldData extends BaseDecoratorData {
  /** Reference type name (required) */
  ref: string;
  /** Field name (required) */
  name: string;
  /** Field type (required) */
  type: string;
  /** Whether the field is optional */
  optional?: boolean;
  /** Import path for the type */
  importPath?: string;
  /** Whether this field should be hidden from schema */
  hidden?: boolean;
}

/**
 * Enhanced @GQLContext decorator data with validation
 */
export interface GQLContextData extends BaseDecoratorData {
  /** Context file path (required) */
  path: string;
  /** Context interface name (required) */
  name: string;
  /** Whether to use default export */
  defaultExport?: boolean;
}

/**
 * Decorator processing statistics
 */
export interface DecoratorProcessingStats {
  /** Total files scanned */
  filesScanned: number;
  /** Total decorators found */
  decoratorsFound: number;
  /** Decorators by type */
  decoratorsByType: Record<string, number>;
  /** Processing time in milliseconds */
  processingTime: number;
  /** Number of validation errors */
  validationErrors: number;
  /** Number of validation warnings */
  validationWarnings: number;
}

/**
 * Decorator processing result
 */
export interface DecoratorProcessingResult {
  /** The decorator container with all found decorators */
  container: import('./decorator-parser').DecoratorContainer;
  /** Processing statistics */
  stats: DecoratorProcessingStats;
  /** Validation results for each decorator */
  validationResults: Array<{
    decorator: import('./decorator-parser').ParsedDecorator;
    result: DecoratorValidationResult;
  }>;
}

/**
 * Schema-specific decorator container for multi-schema support
 */
export interface SchemaDecoratorContainer {
  /** Schema identifier */
  schemaId: string;
  /** Schema-specific decorators */
  decorators: import('./decorator-parser').DecoratorContainer;
}

/**
 * Multi-schema decorator container
 */
export interface MultiSchemaDecoratorContainer {
  /** Default schema decorators (no schema specified) */
  default: import('./decorator-parser').DecoratorContainer;
  /** Schema-specific decorator containers */
  schemas: Record<string, import('./decorator-parser').DecoratorContainer>;
}

/**
 * Decorator cache entry
 */
export interface DecoratorCacheEntry {
  /** File path */
  filePath: string;
  /** File modification time */
  mtime: number;
  /** Cached decorators */
  decorators: import('./decorator-parser').ParsedDecorator[];
  /** Cache timestamp */
  cacheTime: number;
}

/**
 * Decorator cache manager interface
 */
export interface DecoratorCacheManager {
  /** Get cached decorators for a file */
  get(filePath: string): DecoratorCacheEntry | null;
  /** Set cached decorators for a file */
  set(filePath: string, decorators: import('./decorator-parser').ParsedDecorator[]): void;
  /** Check if cache entry is valid */
  isValid(filePath: string): boolean;
  /** Clear cache for a file */
  clear(filePath: string): void;
  /** Clear all cache entries */
  clearAll(): void;
  /** Get cache statistics */
  getStats(): { entries: number; hitRate: number };
}

/**
 * Decorator precedence rules
 */
export interface DecoratorPrecedenceRules {
  /** Whether decorators override comment-based directives */
  decoratorsOverrideComments: boolean;
  /** Whether later decorators override earlier ones */
  laterOverridesEarlier: boolean;
  /** Whether schema-specific decorators override default ones */
  schemaSpecificOverridesDefault: boolean;
}

/**
 * Default precedence rules
 */
export const DEFAULT_PRECEDENCE_RULES: DecoratorPrecedenceRules = {
  decoratorsOverrideComments: true,
  laterOverridesEarlier: true,
  schemaSpecificOverridesDefault: true,
};

/**
 * Decorator processing options
 */
export interface DecoratorProcessingOptions {
  /** Processing configuration */
  config: DecoratorProcessingConfig;
  /** Precedence rules */
  precedenceRules: DecoratorPrecedenceRules;
  /** Whether to enable debug logging */
  debug: boolean;
  /** Custom validation functions */
  customValidators?: Record<string, (data: any) => DecoratorValidationResult>;
}

/**
 * Decorator file watcher configuration
 */
export interface DecoratorWatchConfig {
  /** Whether to enable file watching */
  enabled: boolean;
  /** Debounce delay in milliseconds */
  debounceDelay: number;
  /** Whether to watch for new files */
  watchNewFiles: boolean;
  /** Whether to watch for deleted files */
  watchDeletedFiles: boolean;
  /** Custom file change handler */
  onFileChange?: (filePath: string, changeType: 'added' | 'changed' | 'deleted') => void;
}

/**
 * Decorator import resolution configuration
 */
export interface DecoratorImportResolutionConfig {
  /** Base paths for import resolution */
  basePaths: string[];
  /** Path aliases */
  aliases: Record<string, string>;
  /** Whether to resolve relative paths */
  resolveRelativePaths: boolean;
  /** Whether to validate import paths */
  validateImportPaths: boolean;
}

/**
 * Type guard for GQLMethodCallData
 */
export function isGQLMethodCallData(data: any): data is GQLMethodCallData {
  return data !== null && data !== undefined && typeof data.type === 'string' && (data.call === undefined || typeof data.call === 'string');
}

/**
 * Type guard for GQLImportData
 */
export function isGQLImportData(data: any): data is GQLImportData {
  return data && typeof data.importStatement === 'string';
}

/**
 * Type guard for GQLFieldData
 */
export function isGQLFieldData(data: any): data is GQLFieldData {
  return data && typeof data.ref === 'string' && typeof data.name === 'string' && typeof data.type === 'string';
}

/**
 * Type guard for GQLContextData
 */
export function isGQLContextData(data: any): data is GQLContextData {
  return data && typeof data.path === 'string' && typeof data.name === 'string';
}

/**
 * Utility type for decorator data union
 */
export type DecoratorData = GQLMethodCallData | GQLImportData | GQLFieldData | GQLContextData;

/**
 * Utility type for decorator names
 */
export type DecoratorName = 'GQLMethodCall' | 'GQLImport' | 'GQLField' | 'GQLContext';

/**
 * Decorator metadata with type information
 */
export interface TypedDecoratorMetadata<T extends DecoratorData = DecoratorData> {
  /** The parsed decorator */
  decorator: import('./decorator-parser').ParsedDecorator;
  /** The typed data */
  data: T;
  /** Validation result */
  validation: DecoratorValidationResult;
}
