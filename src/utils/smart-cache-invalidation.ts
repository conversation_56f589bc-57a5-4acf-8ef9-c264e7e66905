import { EventEmitter } from 'events';
import { getGlobalDependencyGraph, type Dependency<PERSON>hange, type DependencyNode } from './dependency-graph';
import { getGlobalCommentDirectiveCache } from './comment-directive-cache';
import { getGlobalTemplateCache } from './template-compilation-cache';
import { getGlobalOptimizedCodeGenService } from '../core/optimized-codegen';

/**
 * Phase 2 Optimization: Smart Cache Invalidation with Dependency Awareness
 * Intelligent cache invalidation that only affects dependent files when schemas change
 */

export interface InvalidationStrategy {
  name: string;
  description: string;
  aggressive: boolean; // Whether to invalidate more broadly for safety
  enabled: boolean;
}

export interface InvalidationResult {
  strategy: string;
  invalidatedCaches: string[];
  affectedFiles: string[];
  dependencyChain: string[];
  timeSaved: number; // Estimated time saved by not regenerating unaffected files
  timestamp: number;
}

export interface CacheInvalidationConfig {
  enableDependencyTracking: boolean;
  enableSmartInvalidation: boolean;
  defaultStrategy: string;
  enableMetrics: boolean;
  enableLogging: boolean;
  maxDependencyDepth: number;
  // Phase 3: Advanced cache management
  enablePredictiveCaching: boolean;
  enableMemoryPressureAwareness: boolean;
  enableAdaptiveInvalidation: boolean;
  cacheWarmingThreshold: number;
  memoryPressureThreshold: number;
  predictiveCacheSize: number;
}

/**
 * Smart cache invalidation system with dependency awareness
 * Phase 3: Enhanced with predictive caching and memory pressure awareness
 */
export class SmartCacheInvalidation extends EventEmitter {
  private config: Required<CacheInvalidationConfig>;
  private dependencyGraph = getGlobalDependencyGraph();
  private directiveCache = getGlobalCommentDirectiveCache();
  private templateCache = getGlobalTemplateCache();
  private codegenService = getGlobalOptimizedCodeGenService();

  private strategies: Map<string, InvalidationStrategy> = new Map();
  private invalidationHistory: InvalidationResult[] = [];
  private maxHistorySize = 100;

  // Phase 3: Advanced cache management
  private predictiveCache = new Map<string, { data: any; confidence: number; lastAccessed: number }>();
  private accessPatterns = new Map<string, { count: number; lastAccess: number; avgInterval: number }>();
  private memoryPressureLevel = 0; // 0-1 scale
  private adaptiveThresholds = new Map<string, number>();

  constructor(config: Partial<CacheInvalidationConfig> = {}) {
    super();

    this.config = {
      enableDependencyTracking: true,
      enableSmartInvalidation: true,
      defaultStrategy: 'dependency-aware',
      enableMetrics: true,
      enableLogging: false,
      maxDependencyDepth: 10,
      // Phase 3: Advanced defaults
      enablePredictiveCaching: true,
      enableMemoryPressureAwareness: true,
      enableAdaptiveInvalidation: true,
      cacheWarmingThreshold: 0.7, // 70% confidence threshold
      memoryPressureThreshold: 0.8, // 80% memory usage threshold
      predictiveCacheSize: 100,
      ...config
    };

    this.initializeStrategies();
    this.setupEventHandlers();
    this.initializePhase3Features();
  }

  /**
   * Initialize invalidation strategies
   */
  private initializeStrategies(): void {
    this.strategies.set('aggressive', {
      name: 'aggressive',
      description: 'Invalidate all caches for maximum safety',
      aggressive: true,
      enabled: true
    });

    this.strategies.set('conservative', {
      name: 'conservative',
      description: 'Only invalidate directly affected files',
      aggressive: false,
      enabled: true
    });

    this.strategies.set('dependency-aware', {
      name: 'dependency-aware',
      description: 'Invalidate based on dependency graph analysis',
      aggressive: false,
      enabled: this.config.enableDependencyTracking
    });

    this.strategies.set('time-based', {
      name: 'time-based',
      description: 'Invalidate based on file modification times',
      aggressive: false,
      enabled: true
    });
  }

  /**
   * Setup event handlers for automatic invalidation
   */
  private setupEventHandlers(): void {
    if (this.config.enableDependencyTracking) {
      this.dependencyGraph.on('dependencyChange', (change: DependencyChange) => {
        this.handleDependencyChange(change);
      });

      this.dependencyGraph.on('nodeModified', (event) => {
        this.handleNodeModification(event);
      });
    }
  }

  /**
   * Invalidate caches based on changed files
   */
  async invalidateCaches(
    changedFiles: string[],
    strategy?: string
  ): Promise<InvalidationResult> {
    const startTime = Date.now();
    const selectedStrategy = strategy || this.config.defaultStrategy;
    
    if (this.config.enableLogging) {
      console.log(`🔄 Starting cache invalidation with ${selectedStrategy} strategy for ${changedFiles.length} files`);
    }

    const strategyConfig = this.strategies.get(selectedStrategy);
    if (!strategyConfig || !strategyConfig.enabled) {
      throw new Error(`Invalid or disabled invalidation strategy: ${selectedStrategy}`);
    }

    let result: InvalidationResult;

    switch (selectedStrategy) {
      case 'dependency-aware':
        result = await this.dependencyAwareInvalidation(changedFiles);
        break;
      case 'conservative':
        result = await this.conservativeInvalidation(changedFiles);
        break;
      case 'time-based':
        result = await this.timeBasedInvalidation(changedFiles);
        break;
      case 'aggressive':
      default:
        result = await this.aggressiveInvalidation(changedFiles);
        break;
    }

    result.strategy = selectedStrategy;
    result.timestamp = Date.now();

    // Record in history
    this.recordInvalidation(result);

    // Emit event
    this.emit('invalidationComplete', result);

    if (this.config.enableLogging) {
      console.log(`✅ Cache invalidation completed in ${Date.now() - startTime}ms`);
      console.log(`📊 Invalidated ${result.invalidatedCaches.length} caches, affected ${result.affectedFiles.length} files`);
    }

    return result;
  }

  /**
   * Dependency-aware invalidation using the dependency graph
   */
  private async dependencyAwareInvalidation(changedFiles: string[]): Promise<InvalidationResult> {
    const invalidatedCaches: string[] = [];
    const affectedFiles = new Set<string>();
    const dependencyChain: string[] = [];

    // Load dependency graph if not already loaded
    await this.dependencyGraph.load();

    for (const filePath of changedFiles) {
      // Find the node for this file
      const nodes = this.dependencyGraph.getAllNodes();
      const node = nodes.find(n => n.filePath === filePath);
      
      if (node) {
        // Get all dependents
        const dependents = this.dependencyGraph.getDependents(node.id);
        dependencyChain.push(...dependents);
        
        // Add to affected files
        affectedFiles.add(filePath);
        for (const dependentId of dependents) {
          const dependentNode = this.dependencyGraph.getNode(dependentId);
          if (dependentNode) {
            affectedFiles.add(dependentNode.filePath);
          }
        }
      } else {
        // File not in dependency graph, use conservative approach
        affectedFiles.add(filePath);
      }
    }

    // Invalidate relevant caches
    const cacheInvalidations = await this.invalidateRelevantCaches(Array.from(affectedFiles));
    invalidatedCaches.push(...cacheInvalidations);

    // Estimate time saved
    const totalFiles = this.dependencyGraph.getAllNodes().length;
    const affectedRatio = affectedFiles.size / Math.max(totalFiles, 1);
    const timeSaved = this.estimateTimeSaved(1 - affectedRatio);

    return {
      strategy: 'dependency-aware',
      invalidatedCaches,
      affectedFiles: Array.from(affectedFiles),
      dependencyChain: [...new Set(dependencyChain)],
      timeSaved,
      timestamp: Date.now()
    };
  }

  /**
   * Conservative invalidation - only directly affected files
   */
  private async conservativeInvalidation(changedFiles: string[]): Promise<InvalidationResult> {
    const invalidatedCaches: string[] = [];
    const affectedFiles = [...changedFiles];

    // Only invalidate caches for the directly changed files
    const cacheInvalidations = await this.invalidateRelevantCaches(changedFiles);
    invalidatedCaches.push(...cacheInvalidations);

    const timeSaved = this.estimateTimeSaved(0.8); // Conservative estimate

    return {
      strategy: 'conservative',
      invalidatedCaches,
      affectedFiles,
      dependencyChain: [],
      timeSaved,
      timestamp: Date.now()
    };
  }

  /**
   * Time-based invalidation using file modification times
   */
  private async timeBasedInvalidation(changedFiles: string[]): Promise<InvalidationResult> {
    const invalidatedCaches: string[] = [];
    const affectedFiles = new Set<string>(changedFiles);

    // Find files that might be affected based on modification times
    const nodes = this.dependencyGraph.getAllNodes();
    const changeTime = Date.now();
    
    for (const node of nodes) {
      // If file was modified recently (within last hour), consider it affected
      if (changeTime - node.lastModified < 3600000) {
        affectedFiles.add(node.filePath);
      }
    }

    const cacheInvalidations = await this.invalidateRelevantCaches(Array.from(affectedFiles));
    invalidatedCaches.push(...cacheInvalidations);

    const timeSaved = this.estimateTimeSaved(0.6);

    return {
      strategy: 'time-based',
      invalidatedCaches,
      affectedFiles: Array.from(affectedFiles),
      dependencyChain: [],
      timeSaved,
      timestamp: Date.now()
    };
  }

  /**
   * Aggressive invalidation - clear all caches
   */
  private async aggressiveInvalidation(changedFiles: string[]): Promise<InvalidationResult> {
    const invalidatedCaches: string[] = [];

    // Clear all caches
    this.directiveCache.clear();
    invalidatedCaches.push('directive-cache');

    this.templateCache.clear();
    invalidatedCaches.push('template-cache');

    this.codegenService.clearCache();
    invalidatedCaches.push('codegen-cache');

    const allNodes = this.dependencyGraph.getAllNodes();
    const affectedFiles = allNodes.map(node => node.filePath);

    return {
      strategy: 'aggressive',
      invalidatedCaches,
      affectedFiles,
      dependencyChain: [],
      timeSaved: 0, // No time saved with aggressive invalidation
      timestamp: Date.now()
    };
  }

  /**
   * Invalidate relevant caches for specific files
   */
  private async invalidateRelevantCaches(filePaths: string[]): Promise<string[]> {
    const invalidatedCaches: string[] = [];

    // Invalidate directive cache for schema files
    const schemaFiles = filePaths.filter(fp => fp.endsWith('.gql') || fp.endsWith('.graphql'));
    if (schemaFiles.length > 0) {
      for (const schemaFile of schemaFiles) {
        this.directiveCache.invalidateFile(schemaFile);
      }
      invalidatedCaches.push('directive-cache');
    }

    // Invalidate template cache for template files
    const templateFiles = filePaths.filter(fp => fp.includes('template') || fp.endsWith('.hbs'));
    if (templateFiles.length > 0) {
      // Template cache doesn't have file-specific invalidation, so clear all
      this.templateCache.clear();
      invalidatedCaches.push('template-cache');
    }

    // Invalidate CodeGen cache if schema files changed
    if (schemaFiles.length > 0) {
      this.codegenService.clearCache();
      invalidatedCaches.push('codegen-cache');
    }

    return invalidatedCaches;
  }

  /**
   * Handle dependency change events
   */
  private async handleDependencyChange(change: DependencyChange): Promise<void> {
    if (this.config.enableSmartInvalidation) {
      const node = this.dependencyGraph.getNode(change.nodeId);
      if (node) {
        await this.invalidateCaches([node.filePath], this.config.defaultStrategy);
      }
    }
  }

  /**
   * Handle node modification events
   */
  private async handleNodeModification(event: { node: DependencyNode; oldHash: string }): Promise<void> {
    if (this.config.enableSmartInvalidation) {
      await this.invalidateCaches([event.node.filePath], this.config.defaultStrategy);
    }
  }

  /**
   * Estimate time saved by selective invalidation
   */
  private estimateTimeSaved(efficiencyRatio: number): number {
    // Rough estimate: assume full regeneration takes 60 seconds for large schema
    const fullRegenerationTime = 60000; // 60 seconds in milliseconds
    return Math.round(fullRegenerationTime * efficiencyRatio);
  }

  /**
   * Record invalidation in history
   */
  private recordInvalidation(result: InvalidationResult): void {
    this.invalidationHistory.push(result);
    
    // Trim history if too large
    if (this.invalidationHistory.length > this.maxHistorySize) {
      this.invalidationHistory = this.invalidationHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Get invalidation statistics
   */
  getStats() {
    const recentInvalidations = this.invalidationHistory.slice(-10);
    const totalTimeSaved = this.invalidationHistory.reduce((sum, inv) => sum + inv.timeSaved, 0);
    
    return {
      config: this.config,
      strategies: Array.from(this.strategies.values()),
      recentInvalidations,
      totalInvalidations: this.invalidationHistory.length,
      totalTimeSaved,
      averageTimeSaved: this.invalidationHistory.length > 0 ? 
        totalTimeSaved / this.invalidationHistory.length : 0
    };
  }

  /**
   * Enable or disable a strategy
   */
  setStrategyEnabled(strategyName: string, enabled: boolean): void {
    const strategy = this.strategies.get(strategyName);
    if (strategy) {
      strategy.enabled = enabled;
      this.emit('strategyChanged', { strategy: strategyName, enabled });
    }
  }

  /**
   * Get available strategies
   */
  getStrategies(): InvalidationStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * Clear invalidation history
   */
  clearHistory(): void {
    this.invalidationHistory = [];
    this.emit('historyCleared');
  }

  // Phase 3: Advanced Cache Management Methods

  /**
   * Initialize Phase 3 features
   */
  private initializePhase3Features(): void {
    if (this.config.enableMemoryPressureAwareness) {
      this.startMemoryPressureMonitoring();
    }

    if (this.config.enablePredictiveCaching) {
      this.startAccessPatternTracking();
    }
  }

  /**
   * Start monitoring memory pressure for adaptive cache management
   */
  private startMemoryPressureMonitoring(): void {
    setInterval(() => {
      const memUsage = process.memoryUsage();
      const totalMemory = memUsage.heapTotal + memUsage.external;
      const usedMemory = memUsage.heapUsed;

      this.memoryPressureLevel = usedMemory / totalMemory;

      if (this.memoryPressureLevel > this.config.memoryPressureThreshold) {
        this.handleMemoryPressure();
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Handle memory pressure by aggressive cache cleanup
   */
  private handleMemoryPressure(): void {
    if (this.config.enableLogging) {
      console.log(`🧠 Memory pressure detected (${Math.round(this.memoryPressureLevel * 100)}%), performing aggressive cache cleanup`);
    }

    // Clear predictive cache
    const predictiveCacheSize = this.predictiveCache.size;
    this.predictiveCache.clear();

    // Clear least recently used items from main caches
    // Note: These methods would need to be implemented in the respective cache classes
    if ('clearLRU' in this.directiveCache) {
      (this.directiveCache as any).clearLRU(0.3);
    }
    if ('clearExpired' in this.templateCache) {
      (this.templateCache as any).clearExpired();
    }

    // Emit memory pressure event
    this.emit('memoryPressure', {
      level: this.memoryPressureLevel,
      predictiveCacheCleared: predictiveCacheSize,
      timestamp: Date.now()
    });
  }

  /**
   * Start tracking access patterns for predictive caching
   */
  private startAccessPatternTracking(): void {
    // Track cache access patterns
    // Note: These event handlers would need to be implemented in the respective cache classes
    if ('on' in this.directiveCache) {
      (this.directiveCache as any).on('cacheHit', (key: string) => {
        this.recordAccess(key);
      });
    }

    if ('on' in this.templateCache) {
      (this.templateCache as any).on('cacheHit', (key: string) => {
        this.recordAccess(key);
      });
    }
  }

  /**
   * Record cache access for pattern analysis
   */
  private recordAccess(key: string): void {
    const now = Date.now();
    const existing = this.accessPatterns.get(key);

    if (existing) {
      const interval = now - existing.lastAccess;
      existing.avgInterval = (existing.avgInterval * existing.count + interval) / (existing.count + 1);
      existing.count++;
      existing.lastAccess = now;
    } else {
      this.accessPatterns.set(key, {
        count: 1,
        lastAccess: now,
        avgInterval: 0
      });
    }

    // Trigger predictive cache warming if pattern is strong
    if (existing && existing.count > 5 && this.shouldWarmCache(key, existing)) {
      this.warmPredictiveCache(key);
    }
  }

  /**
   * Determine if cache should be warmed based on access patterns
   */
  private shouldWarmCache(key: string, pattern: { count: number; lastAccess: number; avgInterval: number }): boolean {
    if (!this.config.enablePredictiveCaching) return false;

    // Calculate confidence based on access frequency and regularity
    const frequency = pattern.count / ((Date.now() - pattern.lastAccess + pattern.avgInterval) / (24 * 60 * 60 * 1000)); // accesses per day
    const regularity = pattern.avgInterval > 0 ? 1 / (pattern.avgInterval / (60 * 60 * 1000)) : 0; // inverse of hours between accesses

    const confidence = Math.min((frequency * 0.6 + regularity * 0.4), 1.0);

    return confidence >= this.config.cacheWarmingThreshold;
  }

  /**
   * Warm predictive cache for frequently accessed items
   */
  private warmPredictiveCache(key: string): void {
    if (this.predictiveCache.size >= this.config.predictiveCacheSize) {
      // Remove least confident entry
      let leastConfident = { key: '', confidence: 1.0 };
      for (const [cacheKey, entry] of this.predictiveCache) {
        if (entry.confidence < leastConfident.confidence) {
          leastConfident = { key: cacheKey, confidence: entry.confidence };
        }
      }
      if (leastConfident.key) {
        this.predictiveCache.delete(leastConfident.key);
      }
    }

    // Calculate confidence for this key
    const pattern = this.accessPatterns.get(key);
    if (!pattern) return;

    const confidence = this.calculateCacheConfidence(pattern);

    // Pre-load data into predictive cache
    try {
      const data = this.preloadCacheData(key);
      this.predictiveCache.set(key, {
        data,
        confidence,
        lastAccessed: Date.now()
      });

      if (this.config.enableLogging) {
        console.log(`🔮 Predictively cached ${key} with ${Math.round(confidence * 100)}% confidence`);
      }
    } catch (error) {
      if (this.config.enableLogging) {
        console.warn(`⚠️ Failed to predictively cache ${key}:`, error);
      }
    }
  }

  /**
   * Calculate cache confidence based on access patterns
   */
  private calculateCacheConfidence(pattern: { count: number; lastAccess: number; avgInterval: number }): number {
    const now = Date.now();
    const daysSinceLastAccess = (now - pattern.lastAccess) / (24 * 60 * 60 * 1000);
    const accessFrequency = pattern.count / Math.max(daysSinceLastAccess, 1);

    // Higher confidence for more frequent and recent access
    const recencyFactor = Math.exp(-daysSinceLastAccess / 7); // Decay over 7 days
    const frequencyFactor = Math.min(accessFrequency / 5, 1); // Normalize to 5 accesses per day

    return Math.min(recencyFactor * 0.6 + frequencyFactor * 0.4, 1.0);
  }

  /**
   * Pre-load cache data (placeholder - implement based on cache type)
   */
  private preloadCacheData(key: string): any {
    // This is a placeholder - in practice, you'd implement specific preloading logic
    // based on the type of cache and the key format
    return { preloaded: true, key, timestamp: Date.now() };
  }

  /**
   * Get predictive cache entry
   */
  getPredictiveCacheEntry(key: string): any {
    const entry = this.predictiveCache.get(key);
    if (entry) {
      entry.lastAccessed = Date.now();
      return entry.data;
    }
    return null;
  }

  /**
   * Adaptive invalidation based on system performance and memory pressure
   */
  async performAdaptiveInvalidation(changes: DependencyChange[]): Promise<InvalidationResult> {
    if (!this.config.enableAdaptiveInvalidation) {
      const changedFiles = changes.map(c => this.dependencyGraph.getNode(c.nodeId)?.filePath).filter(Boolean) as string[];
      return this.invalidateCaches(changedFiles);
    }

    // Choose strategy based on current system state
    let strategy = this.config.defaultStrategy;

    if (this.memoryPressureLevel > 0.7) {
      // High memory pressure - use aggressive invalidation
      strategy = 'aggressive';
    } else if (this.memoryPressureLevel < 0.3 && changes.length < 5) {
      // Low memory pressure and few changes - use conservative invalidation
      strategy = 'conservative';
    } else {
      // Normal conditions - use dependency-aware strategy
      strategy = 'dependency-aware';
    }

    if (this.config.enableLogging) {
      console.log(`🎯 Using adaptive strategy '${strategy}' (memory pressure: ${Math.round(this.memoryPressureLevel * 100)}%)`);
    }

    const changedFiles = changes.map(c => this.dependencyGraph.getNode(c.nodeId)?.filePath).filter(Boolean) as string[];
    return this.invalidateCaches(changedFiles, strategy);
  }

  /**
   * Get cache statistics including Phase 3 metrics
   */
  getEnhancedStats() {
    const baseStats = this.getStats();

    return {
      ...baseStats,
      // Phase 3 metrics
      memoryPressureLevel: this.memoryPressureLevel,
      predictiveCacheSize: this.predictiveCache.size,
      accessPatternsTracked: this.accessPatterns.size,
      adaptiveThresholds: Object.fromEntries(this.adaptiveThresholds),
      phase3Features: {
        predictiveCaching: this.config.enablePredictiveCaching,
        memoryPressureAwareness: this.config.enableMemoryPressureAwareness,
        adaptiveInvalidation: this.config.enableAdaptiveInvalidation
      }
    };
  }
}

/**
 * Global smart cache invalidation instance
 */
let globalSmartCacheInvalidation: SmartCacheInvalidation | null = null;

/**
 * Get or create global smart cache invalidation
 */
export function getGlobalSmartCacheInvalidation(config?: Partial<CacheInvalidationConfig>): SmartCacheInvalidation {
  if (!globalSmartCacheInvalidation) {
    globalSmartCacheInvalidation = new SmartCacheInvalidation(config);
  }
  return globalSmartCacheInvalidation;
}
