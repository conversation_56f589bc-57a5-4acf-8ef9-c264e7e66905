import { getGlobalPerformanceOrchestrator } from './performance-orchestrator';
import { getGlobalPlatformCompatibility } from './platform-compatibility';
import { getGlobalWASMBridge } from './wasm-bridge';
import { getGlobalMemoryMappedFileReader } from './memory-mapped-file-reader';
import fs from 'fs-extra';
import path from 'path';

/**
 * Performance Integration Test Suite
 * Comprehensive testing of all performance optimizations
 */

export interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  metrics?: any;
  error?: string;
}

export interface PerformanceTestReport {
  overallSuccess: boolean;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
  results: TestResult[];
  platformInfo: any;
  recommendations: string[];
}

/**
 * Performance Integration Test Runner
 */
export class PerformanceIntegrationTest {
  private testResults: TestResult[] = [];
  private platformCompatibility = getGlobalPlatformCompatibility();
  private performanceOrchestrator = getGlobalPerformanceOrchestrator({ debug: true });

  /**
   * Run comprehensive performance integration tests
   */
  async runAllTests(): Promise<PerformanceTestReport> {
    console.log('🧪 Starting Performance Integration Tests...');

    const startTime = Date.now();
    this.testResults = [];

    // Test 1: Platform Compatibility
    await this.runTest('Platform Compatibility', () => this.testPlatformCompatibility());

    // Test 2: WASM Integration
    await this.runTest('WASM Integration', () => this.testWASMIntegration());

    // Test 3: Memory-Mapped I/O
    await this.runTest('Memory-Mapped I/O', () => this.testMemoryMappedIO());

    // Test 4: Incremental Generation
    await this.runTest('Incremental Generation', () => this.testIncrementalGeneration());

    // Test 5: Performance Optimization
    await this.runTest('Performance Optimization', () => this.testPerformanceOptimization());

    // Test 6: Graceful Degradation
    await this.runTest('Graceful Degradation', () => this.testGracefulDegradation());

    // Test 7: End-to-End Integration
    await this.runTest('End-to-End Integration', () => this.testEndToEndIntegration());

    const totalDuration = Date.now() - startTime;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = this.testResults.length - passedTests;

    const report: PerformanceTestReport = {
      overallSuccess: failedTests === 0,
      totalTests: this.testResults.length,
      passedTests,
      failedTests,
      totalDuration,
      results: this.testResults,
      platformInfo: this.platformCompatibility.generateCompatibilityReport(),
      recommendations: this.generateTestRecommendations()
    };

    console.log(`🏁 Performance Integration Tests Complete: ${passedTests}/${this.testResults.length} passed in ${totalDuration}ms`);

    return report;
  }

  /**
   * Run individual test with error handling
   */
  private async runTest(testName: string, testFunction: () => Promise<any>): Promise<void> {
    console.log(`🔬 Running test: ${testName}`);
    
    const startTime = Date.now();
    
    try {
      const metrics = await testFunction();
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        testName,
        success: true,
        duration,
        metrics
      });
      
      console.log(`✅ ${testName} passed in ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        testName,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`❌ ${testName} failed in ${duration}ms:`, error);
    }
  }

  /**
   * Test platform compatibility detection
   */
  private async testPlatformCompatibility(): Promise<any> {
    const platformInfo = this.platformCompatibility.getPlatformInfo();
    const features = this.platformCompatibility.getFeatures();
    const performanceTest = await this.platformCompatibility.testPlatformPerformance();

    // Validate platform detection
    if (!platformInfo.platform || !platformInfo.arch) {
      throw new Error('Platform detection failed');
    }

    // Validate feature detection
    if (typeof features.wasmSupported !== 'boolean') {
      throw new Error('Feature detection failed');
    }

    // Validate performance testing
    if (performanceTest.overallScore < 0 || performanceTest.overallScore > 100) {
      throw new Error('Performance test returned invalid score');
    }

    return {
      platformInfo,
      features,
      performanceScore: performanceTest.overallScore
    };
  }

  /**
   * Test WASM integration
   */
  private async testWASMIntegration(): Promise<any> {
    const wasmBridge = getGlobalWASMBridge();
    
    // Test file hash calculation
    const testContent = 'Hello, WASM!';
    const hash = await wasmBridge.calculateFileHash(testContent);
    
    if (!hash || hash.length < 10) {
      throw new Error('WASM file hash calculation failed');
    }

    // Test schema parsing
    const testSchema = `
      type User {
        id: ID!
        name: String!
        email: String @unique
      }
    `;
    
    const parseResult = await wasmBridge.parseSchemaFast(testSchema);
    
    if (!parseResult.types || parseResult.types.length === 0) {
      throw new Error('WASM schema parsing failed');
    }

    // Test template compilation
    const testTemplate = 'Hello {{name}}!';
    const compiledTemplate = await wasmBridge.compileTemplateFast(testTemplate, 'test');
    
    if (!compiledTemplate.includes('ctx.name')) {
      throw new Error('WASM template compilation failed');
    }

    // Test dependency graph calculation
    const nodes = ['A', 'B', 'C'];
    const edges = ['A->B', 'B->C'];
    const graphResult = await wasmBridge.calculateDependencyGraph(nodes, edges);
    
    if (!graphResult.sortedOrder || graphResult.sortedOrder.length !== 3) {
      throw new Error('WASM dependency graph calculation failed');
    }

    return {
      hashCalculation: { success: true, hash },
      schemaParsing: { success: true, types: parseResult.types.length },
      templateCompilation: { success: true, compiled: true },
      dependencyGraph: { success: true, sorted: graphResult.sortedOrder }
    };
  }

  /**
   * Test memory-mapped I/O
   */
  private async testMemoryMappedIO(): Promise<any> {
    const memoryReader = getGlobalMemoryMappedFileReader();
    
    // Create test file
    const testDir = path.join(process.cwd(), 'test-temp');
    await fs.ensureDir(testDir);
    
    const testFile = path.join(testDir, 'test-memory-mapped.txt');
    const testContent = 'This is a test file for memory-mapped I/O testing.\n'.repeat(1000);
    
    await fs.writeFile(testFile, testContent);
    
    try {
      // Test memory-mapped reading
      const result = await memoryReader.readFile(testFile);
      
      if (result.data.toString() !== testContent) {
        throw new Error('Memory-mapped read content mismatch');
      }

      // Test caching
      const cachedResult = await memoryReader.readFile(testFile);
      
      if (!cachedResult.isFromCache) {
        console.warn('⚠️ Expected cached result but got fresh read');
      }

      // Get statistics
      const stats = memoryReader.getStats();
      
      return {
        fileRead: { success: true, size: result.data.length },
        caching: { success: true, fromCache: cachedResult.isFromCache },
        statistics: stats
      };
      
    } finally {
      // Cleanup
      await fs.remove(testDir);
    }
  }

  /**
   * Test incremental generation
   */
  private async testIncrementalGeneration(): Promise<any> {
    // This would test the incremental type processor
    // For now, we'll do a basic validation
    
    const testFiles = ['file1.ts', 'file2.ts', 'file3.ts'];
    
    // Simulate file processing
    const result = await this.performanceOrchestrator.processFiles(testFiles);
    
    if (result.totalTime <= 0) {
      throw new Error('Invalid processing time');
    }

    return {
      filesProcessed: result.processedFiles,
      filesSkipped: result.skippedFiles,
      processingTime: result.totalTime,
      metrics: result.metrics
    };
  }

  /**
   * Test performance optimization
   */
  private async testPerformanceOptimization(): Promise<any> {
    const report = this.performanceOrchestrator.generatePerformanceReport();

    // Validate report structure
    if (!report.metrics || !report.config) {
      throw new Error('Invalid performance report structure');
    }

    // Check if optimizations are working
    const wasmEnabled = report.config.enableWASMIntegration;
    const memoryMappingEnabled = report.config.enableMemoryMappedIO;
    const incrementalEnabled = report.config.enableIncrementalGeneration;

    return {
      wasmEnabled,
      memoryMappingEnabled,
      incrementalEnabled,
      overallPerformance: report.metrics.overallPerformance,
      platformOptimization: report.metrics.overallPerformance.platformOptimization
    };
  }

  /**
   * Test graceful degradation
   */
  private async testGracefulDegradation(): Promise<any> {
    // Test that the system can handle failures gracefully
    const originalConfig = this.performanceOrchestrator.getConfig();

    try {
      // Simulate a failure scenario by disabling features
      this.performanceOrchestrator.updateConfig({
        enableWASMIntegration: false,
        enableMemoryMappedIO: false
      });

      // System should still work with basic features
      const testFiles = ['test.ts'];
      const result = await this.performanceOrchestrator.processFiles(testFiles);

      // Restore original config
      this.performanceOrchestrator.updateConfig(originalConfig);

      return {
        gracefulDegradation: true,
        fallbackWorking: result.totalTime > 0
      };
      
    } catch (error) {
      // Restore original config even if test fails
      this.performanceOrchestrator.updateConfig(originalConfig);
      throw error;
    }
  }

  /**
   * Test end-to-end integration
   */
  private async testEndToEndIntegration(): Promise<any> {
    // Test that all components work together
    const startTime = Date.now();

    // Generate a comprehensive report
    const report = this.performanceOrchestrator.generatePerformanceReport();

    // Validate that all systems are integrated
    const hasIncrementalMetrics = report.metrics.incrementalGeneration !== undefined;
    const hasMemoryMetrics = report.metrics.memoryMappedIO !== undefined;
    const hasWasmMetrics = report.metrics.wasmIntegration !== undefined;
    const hasOverallMetrics = report.metrics.overallPerformance !== undefined;

    if (!hasIncrementalMetrics || !hasMemoryMetrics || !hasWasmMetrics || !hasOverallMetrics) {
      throw new Error('Missing integration metrics');
    }

    const integrationTime = Date.now() - startTime;
    
    return {
      integrationComplete: true,
      integrationTime,
      allSystemsIntegrated: hasIncrementalMetrics && hasMemoryMetrics && hasWasmMetrics && hasOverallMetrics,
      report
    };
  }

  /**
   * Generate test recommendations
   */
  private generateTestRecommendations(): string[] {
    const recommendations: string[] = [];
    const failedTests = this.testResults.filter(r => !r.success);

    if (failedTests.length === 0) {
      recommendations.push('All tests passed! Phase 3 optimizations are working correctly.');
    } else {
      recommendations.push(`${failedTests.length} tests failed. Review the failed tests and address the issues.`);
      
      for (const test of failedTests) {
        recommendations.push(`Fix ${test.testName}: ${test.error}`);
      }
    }

    // Performance recommendations
    const avgDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length;
    if (avgDuration > 5000) {
      recommendations.push('Test execution is slow. Consider optimizing test performance.');
    }

    return recommendations;
  }

  /**
   * Get test results
   */
  getTestResults(): TestResult[] {
    return [...this.testResults];
  }
}

/**
 * Run performance integration tests
 */
export async function runPerformanceIntegrationTests(): Promise<PerformanceTestReport> {
  const testRunner = new PerformanceIntegrationTest();
  return testRunner.runAllTests();
}

// Backward compatibility - deprecated, use runPerformanceIntegrationTests instead
export async function runPhase3IntegrationTests(): Promise<PerformanceTestReport> {
  console.warn('runPhase3IntegrationTests is deprecated, use runPerformanceIntegrationTests instead');
  return runPerformanceIntegrationTests();
}
