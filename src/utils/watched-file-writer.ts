import * as fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';

/**
 * Interface for watch service that can track file modifications
 */
interface WatchServiceInterface {
  markAsGeneratorModified(filePaths: string[]): void;
}

/**
 * Centralized file writer that automatically notifies watch services
 * about generator-initiated file changes to prevent infinite loops
 */
export class WatchedFileWriter {
  private static watchService: WatchServiceInterface | null = null;
  private static writtenFiles: Set<string> = new Set();
  private static enableFormatting: boolean = true;
  private static operationLock: Promise<void> = Promise.resolve();
  private static lockQueue: Array<() => Promise<void>> = [];

  /**
   * Set the watch service to notify about file changes
   */
  static setWatchService(service: WatchServiceInterface): void {
    this.watchService = service;
    console.log('📝 WatchedFileWriter: Watch service registered');
  }

  /**
   * Enable or disable automatic formatting
   */
  static setFormattingEnabled(enabled: boolean): void {
    this.enableFormatting = enabled;
  }

  /**
   * Execute an operation with exclusive access to shared resources
   * @param operation The operation to execute
   * @returns Promise that resolves when the operation completes
   */
  private static async withLock<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.lockQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processLockQueue();
    });
  }

  /**
   * Process the lock queue to ensure operations are executed sequentially
   */
  private static async processLockQueue(): Promise<void> {
    if (this.lockQueue.length === 0) {
      return;
    }

    // Wait for the current operation to complete
    await this.operationLock;

    // Get the next operation from the queue
    const nextOperation = this.lockQueue.shift();
    if (nextOperation) {
      this.operationLock = nextOperation();
      await this.operationLock;

      // Process any remaining operations
      if (this.lockQueue.length > 0) {
        setImmediate(() => this.processLockQueue());
      }
    }
  }

  /**
   * Safely add a file to the written files set
   * @param filePath The file path to add
   */
  private static safeAddWrittenFile(filePath: string): void {
    const resolvedPath = path.resolve(filePath);
    this.writtenFiles.add(resolvedPath);
  }

  /**
   * Safely notify the watch service about file changes
   * @param filePath The file path that was modified
   */
  private static safeNotifyWatchService(filePath: string): void {
    if (this.watchService) {
      const resolvedPath = path.resolve(filePath);
      this.watchService.markAsGeneratorModified([resolvedPath]);
    }
  }

  /**
   * Manually format GraphQL content as a fallback
   */
  private static formatGraphQLContent(content: string): string {
    const lines = content.split('\n');
    const formattedLines: string[] = [];
    let indentLevel = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines
      if (!trimmedLine) {
        formattedLines.push('');
        continue;
      }

      // Decrease indent for closing braces
      if (trimmedLine === '}') {
        indentLevel = Math.max(0, indentLevel - 1);
      }

      // Apply proper indentation
      const indent = '  '.repeat(indentLevel);

      // Fix spacing around colons
      const fixedLine = trimmedLine
        .replace(/\s*:\s*/g, ': ')  // Fix spacing around colons
        .replace(/\s+:/g, ':')      // Remove extra spaces before colons
        .replace(/:([^\s])/g, ': $1'); // Ensure space after colons

      formattedLines.push(indent + fixedLine);

      // Increase indent for opening braces
      if (trimmedLine.endsWith('{')) {
        indentLevel++;
      }
    }

    return formattedLines.join('\n')
      // Clean up multiple consecutive empty lines
      .replace(/\n\s*\n\s*\n/g, '\n\n');
  }

  /**
   * Format a file using prettier if it's a supported file type
   * PERFORMANCE FIX: Skip individual file formatting during generation
   * Use batch formatting instead for better performance
   */
  private static formatFile(filePath: string): void {
    // PERFORMANCE OPTIMIZATION: Skip individual file formatting during generation
    // Individual prettier calls cause massive performance issues (20+ seconds)
    // Use batch formatting after generation instead
    return;

    const ext = path.extname(filePath);
    const supportedExtensions = ['.ts', '.js', '.gql', '.graphql', '.json'];

    if (supportedExtensions.includes(ext)) {
      try {
        if (process.env.DEBUG_PARSER) {
          console.log(`🎨 Formatting file: ${filePath}`);
        }
        const result = execSync(`npx prettier --write "${filePath}"`, {
          stdio: 'pipe',
          encoding: 'utf8'
        });
        if (process.env.DEBUG_PARSER) {
          console.log(`✅ Successfully formatted: ${filePath}`);
        }
      } catch (error) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`⚠️  Warning: Prettier failed for ${filePath}, applying manual formatting`);
        }

        // Apply manual formatting for GraphQL files as fallback
        if (ext === '.gql' || ext === '.graphql') {
          try {
            const content = fs.readFileSync(filePath, 'utf8');
            const formattedContent = this.formatGraphQLContent(content);
            fs.writeFileSync(filePath, formattedContent, 'utf8');
            if (process.env.DEBUG_PARSER) {
              console.log(`✅ Applied manual formatting to: ${filePath}`);
            }
          } catch (manualError) {
            if (process.env.DEBUG_PARSER) {
              console.warn(`⚠️  Manual formatting also failed for ${filePath}:`, manualError);
            }
          }
        }
      }
    }
  }

  /**
   * Clear the watch service reference (thread-safe)
   */
  static async clearWatchService(): Promise<void> {
    await this.withLock(async () => {
      this.watchService = null;
      this.writtenFiles.clear();
    });
    console.log('📝 WatchedFileWriter: Watch service cleared');
  }

  /**
   * Write a file and notify the watch service (synchronous)
   * Note: While this method is synchronous for file operations,
   * the tracking and notification are queued for thread safety
   */
  static writeFileSync(filePath: string, content: string): void {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      fs.ensureDirSync(dir);

      // Write the file
      fs.writeFileSync(filePath, content);

      // Format the file if formatting is enabled
      this.formatFile(filePath);

      // Queue the tracking and notification operations for thread safety
      this.withLock(async () => {
        this.safeAddWrittenFile(filePath);
        this.safeNotifyWatchService(filePath);
      }).catch(error => {
        console.error(`❌ Error in post-write operations for ${filePath}:`, error);
      });

    } catch (error) {
      console.error(`❌ Error writing file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Write a file asynchronously and notify the watch service
   */
  static async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.ensureDir(dir);

      // Write the file
      await fs.writeFile(filePath, content);

      // Format the file if formatting is enabled
      this.formatFile(filePath);

      // Use thread-safe operations for tracking and notification
      await this.withLock(async () => {
        this.safeAddWrittenFile(filePath);
        this.safeNotifyWatchService(filePath);
      });

    } catch (error) {
      console.error(`❌ Error writing file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Copy a file and notify the watch service
   */
  static copyFileSync(src: string, dest: string): void {
    try {
      // Ensure destination directory exists
      const dir = path.dirname(dest);
      fs.ensureDirSync(dir);

      // Copy the file
      fs.copyFileSync(src, dest);
      
      // Queue the tracking and notification operations for thread safety
      this.withLock(async () => {
        this.safeAddWrittenFile(dest);
        this.safeNotifyWatchService(dest);
      }).catch(error => {
        console.error(`❌ Error in post-copy operations for ${dest}:`, error);
      });
      
    } catch (error) {
      console.error(`❌ Error copying file from ${src} to ${dest}:`, error);
      throw error;
    }
  }

  /**
   * Copy a file asynchronously and notify the watch service
   */
  static async copyFile(src: string, dest: string): Promise<void> {
    try {
      // Ensure destination directory exists
      const dir = path.dirname(dest);
      await fs.ensureDir(dir);

      // Copy the file
      await fs.copyFile(src, dest);
      
      // Use thread-safe operations for tracking and notification
      await this.withLock(async () => {
        this.safeAddWrittenFile(dest);
        this.safeNotifyWatchService(dest);
      });
      
    } catch (error) {
      console.error(`❌ Error copying file from ${src} to ${dest}:`, error);
      throw error;
    }
  }

  /**
   * Get all files written during the current session (thread-safe)
   */
  static async getWrittenFiles(): Promise<string[]> {
    return this.withLock(async () => {
      return Array.from(this.writtenFiles);
    });
  }

  /**
   * Clear the list of written files (thread-safe)
   */
  static async clearWrittenFiles(): Promise<void> {
    await this.withLock(async () => {
      this.writtenFiles.clear();
    });
  }

  /**
   * Mark files as written (for external tracking) - thread-safe
   */
  static async markAsWritten(filePaths: string[]): Promise<void> {
    await this.withLock(async () => {
      for (const filePath of filePaths) {
        this.safeAddWrittenFile(filePath);
      }

      // Notify watch service if available
      if (this.watchService) {
        const resolvedPaths = filePaths.map(fp => path.resolve(fp));
        this.watchService.markAsGeneratorModified(resolvedPaths);
      }
    });
  }

  /**
   * Check if a file was written by the generator (thread-safe)
   */
  static async wasWrittenByGenerator(filePath: string): Promise<boolean> {
    return this.withLock(async () => {
      return this.writtenFiles.has(path.resolve(filePath));
    });
  }

  /**
   * Format existing schema files in a directory
   */
  static async formatSchemaFiles(schemaDir: string): Promise<void> {
    try {
      const glob = require('glob');
      const schemaFiles = glob.sync(path.join(schemaDir, '**/*.gql'));

      if (process.env.DEBUG_PARSER) {
        console.log(`🎨 Formatting ${schemaFiles.length} schema files...`);
      }

      for (const filePath of schemaFiles) {
        try {
          if (process.env.DEBUG_PARSER) {
            console.log(`🎨 Formatting schema file: ${filePath}`);
          }

          // Apply manual formatting first (more reliable for GraphQL)
          const content = fs.readFileSync(filePath, 'utf8');
          const formattedContent = this.formatGraphQLContent(content);
          fs.writeFileSync(filePath, formattedContent, 'utf8');

          // Then try prettier for additional formatting
          try {
            execSync(`npx prettier --write "${filePath}"`, { stdio: 'pipe' });
            if (process.env.DEBUG_PARSER) {
              console.log(`✅ Formatted with manual + prettier: ${filePath}`);
            }
          } catch (prettierError) {
            if (process.env.DEBUG_PARSER) {
              console.log(`✅ Formatted with manual formatting: ${filePath}`);
            }
          }
        } catch (error) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`⚠️  Could not format ${filePath}:`, error);
          }
        }
      }

      console.log(`✅ Schema formatting completed`);
    } catch (error) {
      console.error(`❌ Error formatting schema files:`, error);
    }
  }
}

/**
 * Legacy compatibility - direct functions that use the WatchedFileWriter
 */
export const writeFileSync = (filePath: string, content: string): void => {
  WatchedFileWriter.writeFileSync(filePath, content);
};

export const writeFile = async (filePath: string, content: string): Promise<void> => {
  await WatchedFileWriter.writeFile(filePath, content);
};

export const copyFileSync = (src: string, dest: string): void => {
  WatchedFileWriter.copyFileSync(src, dest);
};

export const copyFile = async (src: string, dest: string): Promise<void> => {
  await WatchedFileWriter.copyFile(src, dest);
};
