import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import type { TypeScriptCodeStructure } from './ts-parser';
import { parseTypeScriptFile } from './ts-parser';

/**
 * Configuration options for pattern analysis
 */
export interface PatternAnalyzerOptions {
  detectAwaitPatterns?: boolean;
  detectCastPatterns?: boolean;
  detectImportPatterns?: boolean;
}

/**
 * Detected return pattern in TypeScript code
 */
export interface DetectedPattern {
  type: 'methodCall' | 'import';
  filePath: string;
  fieldContext: FieldContext;
  content: string;
  lineNumber?: number;
  originalStatement: string;
}

/**
 * Context information about the GraphQL field
 */
export interface FieldContext {
  typeName: string;
  fieldName: string;
  schemaFilePath: string;
  functionName: string;
}

/**
 * Return statement pattern information
 */
export interface ReturnPattern {
  patternType: 'methodCall' | 'variable' | 'propertyAccess';
  methodName?: string; // Optional for non-method patterns
  variableName?: string; // For variable patterns
  propertyChain?: string; // For property access patterns
  fullCall: string;
  isAwaited: boolean;
  castType?: string;
  lineNumber: number;
}

/**
 * Import statement pattern information
 */
export interface ImportPattern {
  statement: string;
  specifier: string;
  modulePath: string;
  lineNumber: number;
}

/**
 * Analyzes TypeScript files for patterns that should be synced to schema
 */
export class CodePatternAnalyzer {
  private options: Required<PatternAnalyzerOptions>;

  constructor(options: PatternAnalyzerOptions = {}) {
    this.options = {
      detectAwaitPatterns: true,
      detectCastPatterns: true,
      detectImportPatterns: true,
      ...options,
    };
  }

  /**
   * Analyze a TypeScript file for patterns to sync
   */
  async analyzeFile(filePath: string): Promise<DetectedPattern[]> {
    try {
      if (!fs.existsSync(filePath)) {
        return [];
      }

      // Parse the TypeScript file
      const structure = await parseTypeScriptFile(filePath);
      if (!structure) {
        return [];
      }

      const patterns: DetectedPattern[] = [];

      // Get field context for this file
      const fieldContext = this.resolveFieldContext(filePath, structure);
      if (!fieldContext) {
        console.log(`   ↳ Could not resolve field context for ${filePath}`);
        console.log(`   ↳ Function signature: ${structure.functionSignature}`);
        console.log(`   ↳ File path: ${filePath}`);
        return [];
      }

      // Extract function body from the file content directly
      const functionBody = structure.fileContent ? this.extractFunctionBodyFromContent(structure.fileContent) : null;

      if (functionBody) {
        const returnPatterns = this.extractReturnStatements(functionBody);

        for (const returnPattern of returnPatterns) {
          patterns.push({
            type: 'methodCall',
            filePath,
            fieldContext,
            content: returnPattern.fullCall,
            lineNumber: returnPattern.lineNumber,
            originalStatement: `return ${returnPattern.fullCall};`,
          });
        }
      }

      // Analyze import statements if enabled
      if (this.options.detectImportPatterns && structure.imports) {
        const importPatterns = this.extractImportStatements(structure.imports);
        const returnPatterns = functionBody ? this.extractReturnStatements(functionBody) : [];

        for (const importPattern of importPatterns) {
          // Only include custom imports (not generated ones)
          if (this.isCustomImport(importPattern)) {
            // Check if this import is used in the function body
            const isUsedInFunction = this.isImportUsedInFunction(importPattern, functionBody || '', returnPatterns);

            // Only sync imports that are actually used in the function
            if (isUsedInFunction) {
              patterns.push({
                type: 'import',
                filePath,
                fieldContext,
                content: importPattern.statement,
                lineNumber: importPattern.lineNumber,
                originalStatement: importPattern.statement,
              });
            }
          }
        }
      }

      return patterns;
    } catch (error) {
      console.error(`Error analyzing file ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Extract function body from file content
   */
  private extractFunctionBodyFromContent(fileContent: string): string | null {
    // Look for function body between the first { and last }
    const functionMatch = fileContent.match(/=>\s*{([\s\S]*)}[;\s]*$/);
    if (functionMatch && functionMatch[1]) {
      return functionMatch[1].trim();
    }

    // Fallback: try to find try-catch block
    const tryMatch = fileContent.match(/try\s*{([\s\S]*?)}\s*catch/);
    if (tryMatch && tryMatch[1]) {
      return tryMatch[1].trim();
    }

    return null;
  }

  /**
   * Extract return statement patterns from function body
   */
  extractReturnStatements(functionBody: string): ReturnPattern[] {
    const patterns: ReturnPattern[] = [];

    // Remove comments first to avoid interference
    const cleanedBody = this.removeComments(functionBody);

    // Find all return statements (including multi-line ones)
    const returnStatements = this.findReturnStatements(cleanedBody);

    for (const returnStatement of returnStatements) {
      const pattern = this.parseReturnExpression(returnStatement.expression, returnStatement.lineNumber);
      if (pattern) {
        patterns.push(pattern);
      }
    }

    return patterns;
  }

  /**
   * Remove comments from code while preserving structure
   */
  private removeComments(code: string): string {
    // Remove single-line comments but preserve line structure
    let result = code.replace(/\/\/.*$/gm, '');

    // Remove multi-line comments
    result = result.replace(/\/\*[\s\S]*?\*\//g, '');

    return result;
  }

  /**
   * Find all return statements in the code, including multi-line ones
   */
  private findReturnStatements(code: string): Array<{ expression: string; lineNumber: number }> {
    const statements: Array<{ expression: string; lineNumber: number }> = [];
    const lines = code.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Look for return statement start
      if (line.startsWith('return ')) {
        const returnStatement = this.extractCompleteReturnStatement(lines, i);
        if (returnStatement) {
          statements.push({
            expression: returnStatement.expression,
            lineNumber: i + 1
          });
          // Skip the lines we've already processed
          i = returnStatement.endLineIndex;
        }
      }
    }

    return statements;
  }

  /**
   * Extract a complete return statement that may span multiple lines
   */
  private extractCompleteReturnStatement(lines: string[], startIndex: number): { expression: string; endLineIndex: number } | null {
    let expression = '';
    let braceCount = 0;
    let parenCount = 0;
    let bracketCount = 0;
    let inString = false;
    let stringChar = '';
    let inTemplate = false;
    let templateDepth = 0;

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim();

      if (i === startIndex) {
        // Remove 'return ' from the first line
        const returnMatch = line.match(/^return\s+(.*)$/);
        if (!returnMatch) return null;
        expression = returnMatch[1];
      } else {
        expression += ' ' + line;
      }

      // Track string and template literal state
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        const prevChar = j > 0 ? line[j - 1] : '';

        if (!inString && !inTemplate) {
          if (char === '"' || char === "'" || char === '`') {
            inString = char !== '`';
            inTemplate = char === '`';
            stringChar = char;
            if (inTemplate) templateDepth = 1;
          } else if (char === '(') parenCount++;
          else if (char === ')') parenCount--;
          else if (char === '{') braceCount++;
          else if (char === '}') braceCount--;
          else if (char === '[') bracketCount++;
          else if (char === ']') bracketCount--;
        } else if (inString && char === stringChar && prevChar !== '\\') {
          inString = false;
          stringChar = '';
        } else if (inTemplate && char === '`' && prevChar !== '\\') {
          templateDepth--;
          if (templateDepth === 0) {
            inTemplate = false;
          }
        } else if (inTemplate && char === '`') {
          templateDepth++;
        }
      }

      // Check if statement is complete
      if (!inString && !inTemplate && braceCount === 0 && parenCount === 0 && bracketCount === 0) {
        // Look for semicolon or end of statement
        if (line.endsWith(';') || line.endsWith('}') || i === lines.length - 1) {
          return {
            expression: expression.replace(/;$/, '').trim(),
            endLineIndex: i
          };
        }
      }
    }

    // If we reach here, return what we have (incomplete statement)
    return {
      expression: expression.replace(/;$/, '').trim(),
      endLineIndex: lines.length - 1
    };
  }

  /**
   * Parse a return expression to extract method call or variable/property access information
   */
  private parseReturnExpression(expression: string, lineNumber: number): ReturnPattern | null {
    // Normalize whitespace and remove extra spaces
    expression = expression.replace(/\s+/g, ' ').trim();

    // Handle await patterns
    let isAwaited = false;
    let cleanExpression = expression;

    if (this.options.detectAwaitPatterns && expression.startsWith('await ')) {
      isAwaited = true;
      cleanExpression = expression.substring(6).trim();
    }

    // Handle cast patterns (as Type) - improved regex to handle complex types
    let castType: string | undefined;
    if (this.options.detectCastPatterns) {
      // Match 'as Type' at the end, handling complex types like Array<T>, Promise<U>, etc.
      const castMatch = cleanExpression.match(/^(.+?)\s+as\s+(.+)$/);
      if (castMatch) {
        cleanExpression = castMatch[1].trim();
        castType = castMatch[2].trim();
      }
    }

    // Try to match method call first (highest priority)
    // Improved regex to handle more complex method calls
    const methodMatch = cleanExpression.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s*\(/);
    if (methodMatch) {
      const fullMethodPath = methodMatch[1];

      // Extract just the method name (last part after the last dot)
      const lastDotIndex = fullMethodPath.lastIndexOf('.');
      const methodName = lastDotIndex >= 0 ? fullMethodPath.substring(lastDotIndex + 1) : fullMethodPath;

      // Skip common non-method patterns
      if (this.isBuiltInMethod(fullMethodPath)) {
        return null;
      }

      // For complex expressions, try to extract the complete method call
      let fullCall = this.extractCompleteMethodCall(cleanExpression);

      // Reconstruct with await and cast if present
      if (isAwaited) {
        fullCall = `await ${fullCall}`;
      }
      if (castType) {
        fullCall = `${fullCall} as ${castType}`;
      }

      return {
        patternType: 'methodCall',
        methodName,
        fullCall,
        isAwaited,
        castType,
        lineNumber,
      };
    }

    // If no method call found, check for variable/property access patterns
    // Only allow these if there's a type cast (indicates intentional type annotation)
    if (castType) {
      return this.parseVariableOrPropertyPattern(cleanExpression, isAwaited, castType, lineNumber);
    }

    // No valid pattern found
    return null;
  }

  /**
   * Extract a complete method call, handling complex parameters and chaining
   */
  private extractCompleteMethodCall(expression: string): string {
    // For simple cases, return as-is
    if (!expression.includes('(') || !expression.includes(')')) {
      return expression;
    }

    // Try to find the complete method call by balancing parentheses
    let parenCount = 0;
    let braceCount = 0;
    let bracketCount = 0;
    let inString = false;
    let stringChar = '';
    let inTemplate = false;
    let result = '';

    for (let i = 0; i < expression.length; i++) {
      const char = expression[i];
      const prevChar = i > 0 ? expression[i - 1] : '';

      result += char;

      if (!inString && !inTemplate) {
        if (char === '"' || char === "'" || char === '`') {
          inString = char !== '`';
          inTemplate = char === '`';
          stringChar = char;
        } else if (char === '(') {
          parenCount++;
        } else if (char === ')') {
          parenCount--;
          // If we've closed all parentheses, we might have a complete call
          if (parenCount === 0) {
            // Check if there's more chaining (like .then() or .catch())
            const remaining = expression.substring(i + 1).trim();
            if (remaining.startsWith('.')) {
              // Continue to capture chained calls
              continue;
            } else {
              // This is the end of the method call
              break;
            }
          }
        } else if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
        } else if (char === '[') {
          bracketCount++;
        } else if (char === ']') {
          bracketCount--;
        }
      } else if (inString && char === stringChar && prevChar !== '\\') {
        inString = false;
        stringChar = '';
      } else if (inTemplate && char === '`' && prevChar !== '\\') {
        inTemplate = false;
      }
    }

    return result.trim();
  }

  /**
   * Parse variable or property access patterns (only when type casting is present)
   */
  private parseVariableOrPropertyPattern(
    expression: string,
    isAwaited: boolean,
    castType: string,
    lineNumber: number
  ): ReturnPattern | null {
    // Validate that this is a simple pattern (no complex expressions)
    if (!this.isSimplePattern(expression)) {
      return null;
    }

    // Check for property access pattern (contains dots)
    if (expression.includes('.')) {
      // Property access pattern: obj.prop or obj.nested.prop
      const propertyMatch = expression.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)$/);
      if (!propertyMatch) {
        return null;
      }

      const propertyChain = propertyMatch[1];

      // Reconstruct the full expression
      let fullCall = expression;
      if (isAwaited) {
        fullCall = `await ${fullCall}`;
      }
      fullCall = `${fullCall} as ${castType}`;

      return {
        patternType: 'propertyAccess',
        propertyChain,
        fullCall,
        isAwaited,
        castType,
        lineNumber,
      };
    } else {
      // Simple variable pattern: variableName
      const variableMatch = expression.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*)$/);
      if (!variableMatch) {
        return null;
      }

      const variableName = variableMatch[1];

      // Skip common built-in variables
      if (this.isBuiltInVariable(variableName)) {
        return null;
      }

      // Reconstruct the full expression
      let fullCall = expression;
      if (isAwaited) {
        fullCall = `await ${fullCall}`;
      }
      fullCall = `${fullCall} as ${castType}`;

      return {
        patternType: 'variable',
        variableName,
        fullCall,
        isAwaited,
        castType,
        lineNumber,
      };
    }
  }

  /**
   * Check if an expression is a simple pattern (no complex logic)
   */
  private isSimplePattern(expression: string): boolean {
    // Reject patterns with operators, parentheses (except method calls), brackets, etc.
    const complexPatterns = [
      /[+\-*/%=<>!&|^~]/,  // Arithmetic, comparison, logical operators
      /\?/,                 // Ternary operator
      /\[/,                 // Array access
      /\s+/,                // Multiple words (except for 'as' which is handled separately)
    ];

    return !complexPatterns.some(pattern => pattern.test(expression));
  }

  /**
   * Check if a variable name is a built-in that shouldn't be synced
   */
  private isBuiltInVariable(variableName: string): boolean {
    const builtInVariables = [
      'undefined',
      'null',
      'true',
      'false',
      'this',
      'arguments',
      'console',
      'window',
      'global',
      'process',
    ];

    return builtInVariables.includes(variableName);
  }

  /**
   * Extract import statement patterns
   */
  extractImportStatements(imports: string[]): ImportPattern[] {
    const patterns: ImportPattern[] = [];

    for (let i = 0; i < imports.length; i++) {
      const importStatement = imports[i].trim();
      
      // Parse import statement
      const importMatch = importStatement.match(/import\s+(.+?)\s+from\s+['"]([^'"]+)['"]/);
      if (!importMatch) {
        continue;
      }

      const specifier = importMatch[1].trim();
      const modulePath = importMatch[2];

      patterns.push({
        statement: importStatement,
        specifier,
        modulePath,
        lineNumber: i + 1,
      });
    }

    return patterns;
  }

  /**
   * Resolve field context from file path and structure using reverse mapping
   */
  resolveFieldContext(filePath: string, structure: TypeScriptCodeStructure): FieldContext | null {
    try {
      // Extract information from file path and function signature
      const fileName = path.basename(filePath, '.ts');
      const relativePath = path.relative(process.cwd(), filePath);

      // Parse function name from signature
      const functionMatch = structure.functionSignature.match(/const\s+([a-zA-Z0-9_]+)/);
      if (!functionMatch) {
        return null;
      }

      const functionName = functionMatch[1];

      // Convert kebab-case field name back to camelCase
      const fieldName = this.convertKebabToCamelCase(fileName);

      // Parse the file path to extract type and schema information
      const pathInfo = this.parseGeneratedFilePath(relativePath);
      if (!pathInfo) {
        return null;
      }

      // Find the actual schema file that contains this type
      const schemaFilePath = this.findSchemaFileForType(pathInfo.typeName);
      if (!schemaFilePath) {
        console.log(`   ↳ Could not find schema file for type ${pathInfo.typeName}`);
        return null;
      }

      return {
        typeName: pathInfo.typeName,
        fieldName,
        schemaFilePath,
        functionName,
      };
    } catch (error) {
      console.error('Error resolving field context:', error);
      return null;
    }
  }

  /**
   * Parse a generated file path to extract type and schema information
   * Handles patterns like: output/root-query/post/author.ts, output/root-mutation/user/create-user.ts
   */
  private parseGeneratedFilePath(relativePath: string): { typeName: string; operationType?: string } | null {
    const pathParts = relativePath.split(path.sep).filter(part => part && part !== '.');

    // Remove the output directory part if present
    const outputIndex = pathParts.findIndex(part => part === 'output' || part.includes('generated'));
    const relevantParts = outputIndex >= 0 ? pathParts.slice(outputIndex + 1) : pathParts;

    if (relevantParts.length < 2) {
      return null;
    }

    // Check for operation type patterns (root-query, root-mutation, root-subscription)
    const firstPart = relevantParts[0];
    if (firstPart.startsWith('root-')) {
      const operationType = this.convertKebabToPascalCase(firstPart); // root-query -> RootQuery

      if (relevantParts.length === 2) {
        // Pattern: output/root-{operation}/{field-name}.ts (direct operation field)
        // The type name is the operation type itself (e.g., RootQuery)
        return { typeName: operationType, operationType };
      } else if (relevantParts.length >= 3) {
        // Pattern: output/root-{operation}/{type-name}/{field-name}.ts (nested type field)
        const typeName = this.convertKebabToPascalCase(relevantParts[1]); // post -> Post
        return { typeName, operationType };
      }
    } else if (firstPart === 'types') {
      // Pattern: output/types/{schema-file}/{type-name}/{field-name}.ts
      if (relevantParts.length >= 4) {
        const typeName = this.convertKebabToPascalCase(relevantParts[2]); // user -> User
        return { typeName };
      }
    } else {
      // Pattern: output/{schema-file}/{type-name}/{field-name}.ts
      if (relevantParts.length >= 3) {
        const typeName = this.convertKebabToPascalCase(relevantParts[1]); // user -> User
        return { typeName };
      }
      // Pattern: output/{type-name}/{field-name}.ts
      else if (relevantParts.length >= 2) {
        const typeName = this.convertKebabToPascalCase(relevantParts[0]); // user -> User
        return { typeName };
      }
    }

    return null;
  }

  /**
   * Convert kebab-case to camelCase
   */
  private convertKebabToCamelCase(kebabStr: string): string {
    return kebabStr.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Convert kebab-case to PascalCase
   */
  private convertKebabToPascalCase(kebabStr: string): string {
    return kebabStr
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
  }

  /**
   * Find the actual schema file that contains a given type
   */
  private findSchemaFileForType(typeName: string): string | null {
    try {
      // Get the current working directory to resolve schema paths
      const cwd = process.cwd();
      const schemaDir = path.join(cwd, 'schema');

      if (!fs.existsSync(schemaDir)) {
        return null;
      }

      // Find all .gql files in the schema directory
      const schemaFiles = glob.sync(path.join(schemaDir, '**', '*.{gql,graphql}'), { windowsPathsNoEscape: true });

      // Search for the type definition in each schema file
      for (const schemaFile of schemaFiles) {
        try {
          const content = fs.readFileSync(schemaFile, 'utf8');

          // Look for type definitions: type TypeName, interface TypeName, union TypeName, input TypeName
          const typePattern = new RegExp(`\\b(?:type|interface|union|input)\\s+${typeName}\\b`, 'i');
          if (typePattern.test(content)) {
            // Return relative path from current working directory
            return path.relative(cwd, schemaFile);
          }
        } catch (error) {
          // Skip files that can't be read
          continue;
        }
      }

      // Fallback: try to guess based on naming patterns
      return this.guessSchemaFileForType(typeName);
    } catch (error) {
      console.error('Error finding schema file for type:', error);
      return this.guessSchemaFileForType(typeName);
    }
  }

  /**
   * Guess the schema file for a type based on common patterns
   */
  private guessSchemaFileForType(typeName: string): string | null {
    // Handle special GraphQL operation types
    if (typeName === 'RootQuery' || typeName === 'Query') {
      return 'schema/root_query.gql';
    }
    if (typeName === 'RootMutation' || typeName === 'Mutation') {
      return 'schema/root_mutation.gql';
    }
    if (typeName === 'RootSubscription' || typeName === 'Subscription') {
      return 'schema/root_subscription.gql';
    }

    // For regular types, try common patterns
    const kebabTypeName = typeName.replace(/([A-Z])/g, '-$1').toLowerCase().slice(1);

    const possiblePaths = [
      `schema/${kebabTypeName}.gql`,
      `schema/types/${kebabTypeName}.gql`,
      `schema/${typeName.toLowerCase()}.gql`,
      `schema/types/${typeName.toLowerCase()}.gql`,
      'schema/root_query.gql', // Many types are defined in root_query.gql
      'schema/root_mutation.gql', // Some types are defined in root_mutation.gql
    ];

    // Check which files actually exist
    for (const possiblePath of possiblePaths) {
      const fullPath = path.join(process.cwd(), possiblePath);
      if (fs.existsSync(fullPath)) {
        return possiblePath;
      }
    }

    // Default fallback
    return 'schema/root_query.gql';
  }

  /**
   * Check if a method name is a built-in that shouldn't be synced
   */
  private isBuiltInMethod(methodName: string): boolean {
    const builtInMethods = [
      'console.log',
      'console.error',
      'console.warn',
      'JSON.parse',
      'JSON.stringify',
      'Object.keys',
      'Object.values',
      'Array.from',
      'Promise.resolve',
      'Promise.reject',
      'Error',
      'throw',
    ];

    return builtInMethods.some(builtin => methodName.startsWith(builtin));
  }

  /**
   * Check if an import is custom (not generated) and should be synced to schema
   */
  private isCustomImport(importPattern: ImportPattern): boolean {
    const generatedPatterns = [
      '../generated/',
      './generated/',
      '@generated/',
      'graphql',
      '@graphql-tools',
      'apollo-server',
    ];

    // Skip generated imports
    if (generatedPatterns.some(pattern => importPattern.modulePath.includes(pattern))) {
      return false;
    }

    // Skip Context imports if they should be handled by @context directive
    if (this.isContextImport(importPattern)) {
      return false;
    }

    return true;
  }

  /**
   * Check if an import is a Context import that should be handled by @context directive
   */
  private isContextImport(importPattern: ImportPattern): boolean {
    // Check if this is importing Context type
    const isContextType = importPattern.specifier.includes('Context') &&
                         !importPattern.specifier.includes(','); // Not a mixed import

    // Check if it's from a context-related path
    const isContextPath = importPattern.modulePath.includes('@types/context') ||
                         importPattern.modulePath.includes('/context') ||
                         importPattern.modulePath.endsWith('context');

    return isContextType && isContextPath;
  }

  /**
   * Check if an import is actually used in the function body
   */
  private isImportUsedInFunction(
    importPattern: ImportPattern,
    functionBody: string,
    returnPatterns: ReturnPattern[]
  ): boolean {
    // Extract imported names from the import statement
    const importedNames = this.extractImportedNames(importPattern);

    // Check if any imported name is used in the function body
    for (const importedName of importedNames) {
      // Check in function body
      if (functionBody.includes(importedName)) {
        return true;
      }

      // Check in return patterns (for methodCall patterns)
      for (const returnPattern of returnPatterns) {
        if (returnPattern.fullCall.includes(importedName)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Extract imported names from an import pattern
   */
  private extractImportedNames(importPattern: ImportPattern): string[] {
    const names: string[] = [];
    const specifier = importPattern.specifier;

    // Handle different import types
    if (specifier.includes('{')) {
      // Named imports: { name1, name2 }
      const namedMatch = specifier.match(/\{\s*([^}]+)\s*\}/);
      if (namedMatch) {
        const namedImports = namedMatch[1].split(',').map(name => name.trim());
        names.push(...namedImports);
      }
    } else if (specifier.includes(',')) {
      // Mixed imports: defaultName, { name1, name2 }
      const parts = specifier.split(',');
      names.push(parts[0].trim()); // Default import

      const namedPart = parts.slice(1).join(',');
      const namedMatch = namedPart.match(/\{\s*([^}]+)\s*\}/);
      if (namedMatch) {
        const namedImports = namedMatch[1].split(',').map(name => name.trim());
        names.push(...namedImports);
      }
    } else {
      // Default import: defaultName
      names.push(specifier.trim());
    }

    return names;
  }
}
