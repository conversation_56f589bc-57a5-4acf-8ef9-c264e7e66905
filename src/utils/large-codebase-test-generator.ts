import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

/**
 * Configuration for test codebase generation
 */
export interface TestCodebaseConfig {
  outputDir?: string;
  numSchemaFiles?: number;
  numTypesPerFile?: number;
  numFieldsPerType?: number;
  directiveFrequency?: number; // 0-1, probability of directive on each field
  includeInterfaces?: boolean;
  includeUnions?: boolean;
  includeComplexTypes?: boolean;
  enableLogging?: boolean;
}

/**
 * Statistics about generated test codebase
 */
export interface TestCodebaseStats {
  totalFiles: number;
  totalTypes: number;
  totalFields: number;
  totalDirectives: number;
  estimatedSize: number; // in bytes
  generationTime: number; // in milliseconds
}

/**
 * Generates large test codebases for performance testing
 */
export class LargeCodebaseTestGenerator {
  private config: Required<TestCodebaseConfig>;
  private stats: TestCodebaseStats = {
    totalFiles: 0,
    totalTypes: 0,
    totalFields: 0,
    totalDirectives: 0,
    estimatedSize: 0,
    generationTime: 0
  };

  constructor(config: TestCodebaseConfig = {}) {
    this.config = {
      outputDir: path.join(os.tmpdir(), 'gql-perf-test-schemas'),
      numSchemaFiles: 50,
      numTypesPerFile: 10,
      numFieldsPerType: 8,
      directiveFrequency: 0.6,
      includeInterfaces: true,
      includeUnions: true,
      includeComplexTypes: true,
      enableLogging: false,
      ...config
    };
  }

  /**
   * Generate a large test codebase
   */
  async generateTestCodebase(): Promise<TestCodebaseStats> {
    const startTime = Date.now();
    
    if (this.config.enableLogging) {
      console.log(`🏗️ Generating large test codebase in: ${this.config.outputDir}`);
    }

    // Reset stats
    this.stats = {
      totalFiles: 0,
      totalTypes: 0,
      totalFields: 0,
      totalDirectives: 0,
      estimatedSize: 0,
      generationTime: 0
    };

    try {
      // Ensure output directory exists
      await fs.ensureDir(this.config.outputDir);
      
      // Generate schema files
      const filePromises: Promise<void>[] = [];
      
      for (let i = 0; i < this.config.numSchemaFiles; i++) {
        filePromises.push(this.generateSchemaFile(i));
      }

      await Promise.all(filePromises);

      // Generate root schema file
      await this.generateRootSchemaFile();

      this.stats.generationTime = Date.now() - startTime;

      if (this.config.enableLogging) {
        console.log(`✅ Generated test codebase: ${this.stats.totalFiles} files, ${this.stats.totalTypes} types, ${this.stats.totalFields} fields`);
      }

      return { ...this.stats };
    } catch (error) {
      console.error('❌ Error generating test codebase:', error);
      throw error;
    }
  }

  /**
   * Generate a single schema file following the existing schema structure patterns
   */
  private async generateSchemaFile(fileIndex: number): Promise<void> {
    const domains = ['User', 'Product', 'Order', 'Payment', 'Inventory', 'Analytics', 'Notification', 'Content', 'Media', 'Security'];
    const domain = domains[fileIndex % domains.length];

    // Create domain-specific directory structure
    const domainDir = path.join(this.config.outputDir, 'types', domain.toLowerCase());
    await fs.ensureDir(domainDir);

    const fileName = `${domain.toLowerCase()}.gql`;
    const filePath = path.join(domainDir, fileName);

    let content = `# ${domain} domain schema - Generated test file ${fileIndex}\n\n`;

    // Generate interfaces first (like Node, Auditable)
    if (this.config.includeInterfaces && fileIndex % 3 === 0) {
      const interfaceContent = this.generateDomainInterface(domain, fileIndex);
      content += interfaceContent + '\n\n';
      this.stats.totalTypes++;
    }

    // Generate main domain types
    for (let typeIndex = 0; typeIndex < this.config.numTypesPerFile; typeIndex++) {
      const typeName = `${domain}${typeIndex > 0 ? typeIndex : ''}`;
      const typeContent = this.generateRealisticType(typeName, domain, fileIndex, typeIndex);
      content += typeContent + '\n\n';
      this.stats.totalTypes++;
    }

    // Generate input types
    const inputContent = this.generateInputTypes(domain, fileIndex);
    content += inputContent + '\n\n';
    this.stats.totalTypes++;

    // Generate unions if enabled
    if (this.config.includeUnions && fileIndex % 5 === 0) {
      const unionContent = this.generateDomainUnion(domain, fileIndex);
      content += unionContent + '\n';
      this.stats.totalTypes++;
    }

    await fs.writeFile(filePath, content.trim() + '\n');
    this.stats.totalFiles++;
    this.stats.estimatedSize += Buffer.byteLength(content, 'utf8');
  }

  /**
   * Generate a realistic GraphQL type following existing patterns
   */
  private generateRealisticType(typeName: string, domain: string, fileIndex: number, typeIndex: number): string {
    let content = '';

    // Add realistic type-level directives
    if (Math.random() < this.config.directiveFrequency * 0.4) {
      content += this.generateRealisticTypeDirectives(typeName, domain);
    }

    // Implement common interfaces like Node, Auditable
    const interfaces = this.getInterfacesForType(domain, typeIndex);
    const implementsClause = interfaces.length > 0 ? ` implements ${interfaces.join(' & ')}` : '';

    content += `type ${typeName}${implementsClause} {\n`;

    // Generate realistic fields based on domain
    const fields = this.getFieldsForDomain(domain, typeIndex);
    for (const field of fields) {
      const fieldContent = this.generateRealisticField(field, domain, fileIndex, typeIndex);
      content += fieldContent;
      this.stats.totalFields++;
    }

    content += '}';
    return content;
  }

  /**
   * Generate a GraphQL interface
   */
  private generateInterface(interfaceName: string, fileIndex: number): string {
    let content = '';
    
    // Add interface-level directives
    if (Math.random() < this.config.directiveFrequency * 0.4) {
      content += this.generateInterfaceDirectives(interfaceName);
    }

    content += `interface ${interfaceName} {\n`;

    // Generate interface fields
    const numFields = Math.floor(this.config.numFieldsPerType * 0.6);
    for (let fieldIndex = 0; fieldIndex < numFields; fieldIndex++) {
      const fieldName = `interfaceField${fieldIndex}`;
      const fieldContent = this.generateField(fieldName, fileIndex, 0, fieldIndex, true);
      content += fieldContent;
      this.stats.totalFields++;
    }

    content += '}';
    return content;
  }

  /**
   * Generate a GraphQL union
   */
  private generateUnion(unionName: string, fileIndex: number): string {
    const memberTypes = [];
    const numMembers = Math.min(5, Math.floor(this.config.numTypesPerFile * 0.3));
    
    for (let i = 0; i < numMembers; i++) {
      memberTypes.push(`Type${fileIndex}_${i}`);
    }

    let content = '';
    
    // Add union-level directives
    if (Math.random() < this.config.directiveFrequency * 0.2) {
      content += `  # @methodCall(resolve${unionName}Type(obj))\n`;
      this.stats.totalDirectives++;
    }

    content += `union ${unionName} = ${memberTypes.join(' | ')}`;
    return content;
  }

  /**
   * Generate a field with potential directives
   */
  private generateField(
    fieldName: string, 
    fileIndex: number, 
    typeIndex: number, 
    fieldIndex: number,
    isInterface: boolean = false
  ): string {
    let content = '';
    
    // Add field directives based on frequency
    if (Math.random() < this.config.directiveFrequency) {
      content += this.generateFieldDirectives(fieldName, fileIndex, typeIndex, fieldIndex);
    }

    // Generate field definition
    const fieldType = this.generateFieldType(fieldIndex, isInterface);
    content += `  ${fieldName}: ${fieldType}\n`;

    return content;
  }

  /**
   * Generate field type (scalar, object, list, etc.)
   */
  private generateFieldType(fieldIndex: number, isInterface: boolean = false): string {
    const types = ['String', 'Int', 'Float', 'Boolean', 'ID'];

    if (this.config.includeComplexTypes && !isInterface) {
      // Use only standard GraphQL types for compatibility
      types.push('String', 'Int', 'Float');
    }

    const baseType = types[fieldIndex % types.length];
    
    // Add list wrapper occasionally
    if (Math.random() < 0.3) {
      return `[${baseType}!]!`;
    }
    
    // Add nullable/non-nullable
    if (Math.random() < 0.7) {
      return `${baseType}!`;
    }
    
    return baseType;
  }

  /**
   * Generate type-level directives
   */
  private generateTypeDirectives(typeName: string): string {
    const directives = [];
    
    if (Math.random() < 0.5) {
      directives.push(`# @import(import { ${typeName}Service } from '../services/${typeName.toLowerCase()}-service')`);
    }
    
    if (Math.random() < 0.3) {
      directives.push(`# @methodCall(${typeName}Service.resolveType(obj))`);
    }

    this.stats.totalDirectives += directives.length;
    return directives.length > 0 ? directives.join('\n') + '\n' : '';
  }

  /**
   * Generate interface-level directives
   */
  private generateInterfaceDirectives(interfaceName: string): string {
    const directives = [];
    
    if (Math.random() < 0.6) {
      directives.push(`# @import(import { ${interfaceName}Resolver } from '../resolvers/${interfaceName.toLowerCase()}-resolver')`);
    }

    this.stats.totalDirectives += directives.length;
    return directives.length > 0 ? directives.join('\n') + '\n' : '';
  }

  /**
   * Generate field-level directives
   */
  private generateFieldDirectives(
    fieldName: string, 
    fileIndex: number, 
    typeIndex: number, 
    fieldIndex: number
  ): string {
    const directives = [];
    
    // Method call directive
    if (Math.random() < 0.7) {
      const methodName = `get${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`;
      const callPattern = Math.random() < 0.5 ? `${methodName}(obj.id)` : `${methodName}({obj, args, context})`;
      directives.push(`  # @methodCall(${callPattern})`);
    }
    
    // Import directive
    if (Math.random() < 0.4) {
      const serviceName = `Field${fileIndex}_${typeIndex}_Service`;
      directives.push(`  # @import(import { ${serviceName} } from '../services/field-services')`);
    }
    
    // Field directive
    if (Math.random() < 0.2) {
      const hiddenFieldName = `hidden${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`;
      directives.push(`  # @field(${hiddenFieldName}: String)`);
    }

    this.stats.totalDirectives += directives.length;
    return directives.length > 0 ? directives.join('\n') + '\n' : '';
  }

  /**
   * Generate root schema files following existing structure
   */
  private async generateRootSchemaFile(): Promise<void> {
    // Generate schema.gql
    await this.generateMainSchemaFile();

    // Generate root_query.gql
    await this.generateRootQueryFile();

    // Generate root_mutation.gql
    await this.generateRootMutationFile();
  }

  /**
   * Generate main schema.gql file
   */
  private async generateMainSchemaFile(): Promise<void> {
    let content = `# @context(path: "@types/context", name: "Context")\n`;
    content += `schema {\n`;
    content += `  query: RootQuery\n`;
    content += `  mutation: RootMutation\n`;
    content += `}\n`;

    const schemaPath = path.join(this.config.outputDir, 'schema.gql');
    await fs.writeFile(schemaPath, content);
    this.stats.totalFiles++;
    this.stats.estimatedSize += Buffer.byteLength(content, 'utf8');
  }

  /**
   * Generate root_query.gql file
   */
  private async generateRootQueryFile(): Promise<void> {
    let content = `type RootQuery {\n`;

    const domains = ['User', 'Product', 'Order', 'Payment', 'Inventory'];

    for (const domain of domains.slice(0, Math.min(5, this.config.numSchemaFiles))) {
      // Single item queries
      content += `  # @methodCall(${domain}Service.findById(args.id))\n`;
      content += `  ${domain.toLowerCase()}(id: ID!): ${domain}\n`;

      // List queries with arguments
      content += `  # @methodCall(${domain}Service.findAll({context, args, obj}))\n`;
      content += `  ${domain.toLowerCase()}s(limit: Int = 10, offset: Int = 0): [${domain}!]!\n`;

      this.stats.totalDirectives += 2;
      this.stats.totalFields += 2;
    }

    // Add search functionality
    content += `  # @methodCall(SearchService.search({context, args, obj}))\n`;
    content += `  search(query: String!, type: String): [SearchResult!]!\n`;

    content += `}\n\n`;

    // Add related types
    content += `type SearchResult {\n`;
    content += `  id: ID!\n`;
    content += `  type: String!\n`;
    content += `  title: String!\n`;
    content += `  description: String\n`;
    content += `}\n\n`;

    // Add Post type (referenced in existing schema)
    content += `type Post {\n`;
    content += `  id: ID!\n`;
    content += `  title: String!\n`;
    content += `  content: String!\n`;
    content += `  # @methodCall(UserService.findById(obj.authorId))\n`;
    content += `  author: User!\n`;
    content += `  # @methodCall(CommentService.findByPostId({context, args, obj}))\n`;
    content += `  comments(limit: Int = 10, offset: Int = 0): [String!]!\n`;
    content += `}\n`;

    this.stats.totalDirectives += 3;
    this.stats.totalFields += 9;
    this.stats.totalTypes += 2;

    const queryPath = path.join(this.config.outputDir, 'root_query.gql');
    await fs.writeFile(queryPath, content);
    this.stats.totalFiles++;
    this.stats.estimatedSize += Buffer.byteLength(content, 'utf8');
  }

  /**
   * Generate root_mutation.gql file
   */
  private async generateRootMutationFile(): Promise<void> {
    let content = `type RootMutation {\n`;

    const domains = ['User', 'Product', 'Order', 'Payment'];

    for (const domain of domains.slice(0, Math.min(4, this.config.numSchemaFiles))) {
      // Create mutations
      content += `  # @methodCall(${domain}Service.create(args.input))\n`;
      content += `  create${domain}(input: ${domain}Input!): ${domain}!\n`;

      // Update mutations
      content += `  # @methodCall(${domain}Service.update(args.id, args.input))\n`;
      content += `  update${domain}(id: ID!, input: ${domain}Input!): ${domain}\n`;

      // Delete mutations
      content += `  # @methodCall(${domain}Service.delete(args.id))\n`;
      content += `  delete${domain}(id: ID!): Boolean!\n`;

      this.stats.totalDirectives += 3;
      this.stats.totalFields += 3;
    }

    content += `}\n`;

    const mutationPath = path.join(this.config.outputDir, 'root_mutation.gql');
    await fs.writeFile(mutationPath, content);
    this.stats.totalFiles++;
    this.stats.estimatedSize += Buffer.byteLength(content, 'utf8');
  }

  /**
   * Clean up generated test codebase
   */
  async cleanup(): Promise<void> {
    try {
      await fs.remove(this.config.outputDir);
      
      if (this.config.enableLogging) {
        console.log(`🧹 Cleaned up test codebase: ${this.config.outputDir}`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up test codebase:', error);
    }
  }

  /**
   * Get generation statistics
   */
  getStats(): TestCodebaseStats {
    return { ...this.stats };
  }

  /**
   * Get output directory path
   */
  getOutputDir(): string {
    return this.config.outputDir;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<TestCodebaseConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Generate realistic domain interface following existing patterns
   */
  private generateDomainInterface(domain: string, fileIndex: number): string {
    let content = '';

    const interfaceName = `${domain}Interface`;

    // Add interface-level directives
    if (Math.random() < this.config.directiveFrequency * 0.6) {
      content += `# @import(import { ${domain}Service } from '@/services/${domain.toLowerCase()}-service';)\n`;
      this.stats.totalDirectives++;
    }

    content += `interface ${interfaceName} {\n`;

    // Common interface fields
    content += `  # @methodCall(get${domain}Id(obj.id))\n`;
    content += `  id: ID!\n`;
    this.stats.totalDirectives++;
    this.stats.totalFields++;

    if (Math.random() < 0.7) {
      content += `  # @methodCall(get${domain}Metadata(obj.id))\n`;
      content += `  # @import(import { get${domain}Metadata } from '@/services/${domain.toLowerCase()}-metadata';)\n`;
      content += `  metadata: String\n`;
      this.stats.totalDirectives += 2;
      this.stats.totalFields++;
    }

    content += '}';
    return content;
  }

  /**
   * Generate realistic type directives
   */
  private generateRealisticTypeDirectives(typeName: string, domain: string): string {
    const directives = [];

    if (Math.random() < 0.6) {
      directives.push(`# @import(import { ${typeName}Service } from '@/services/${domain.toLowerCase()}/${typeName.toLowerCase()}-service';)`);
    }

    if (Math.random() < 0.4) {
      directives.push(`# @import(import { ${typeName}Resolver } from '@/resolvers/${domain.toLowerCase()}/${typeName.toLowerCase()}-resolver';)`);
    }

    this.stats.totalDirectives += directives.length;
    return directives.length > 0 ? directives.join('\n') + '\n' : '';
  }

  /**
   * Get interfaces that a type should implement
   */
  private getInterfacesForType(domain: string, typeIndex: number): string[] {
    const interfaces = ['Node'];

    if (domain === 'User' || domain === 'Product' || domain === 'Order') {
      interfaces.push('Auditable');
    }

    if (typeIndex === 0) {
      interfaces.push(`${domain}Interface`);
    }

    return interfaces;
  }

  /**
   * Get realistic fields for a domain
   */
  private getFieldsForDomain(domain: string, typeIndex: number): Array<{name: string, type: string, hasArgs?: boolean}> {
    const commonFields = [
      { name: 'id', type: 'ID!' },
      { name: 'createdAt', type: 'String!' },
      { name: 'updatedAt', type: 'String!' }
    ];

    const domainFields: Record<string, Array<{name: string, type: string, hasArgs?: boolean}>> = {
      User: [
        { name: 'name', type: 'String!' },
        { name: 'email', type: 'String!' },
        { name: 'avatar', type: 'String' },
        { name: 'posts', type: '[Post!]!', hasArgs: true },
        { name: 'followers', type: '[User!]!', hasArgs: true },
        { name: 'workflowField', type: 'String' }
      ],
      Product: [
        { name: 'name', type: 'String!' },
        { name: 'description', type: 'String' },
        { name: 'price', type: 'Float!' },
        { name: 'category', type: 'String!' },
        { name: 'inventory', type: 'Int!' },
        { name: 'reviews', type: '[Review!]!', hasArgs: true }
      ],
      Order: [
        { name: 'orderNumber', type: 'String!' },
        { name: 'status', type: 'OrderStatus!' },
        { name: 'total', type: 'Float!' },
        { name: 'items', type: '[OrderItem!]!' },
        { name: 'customer', type: 'User!' },
        { name: 'payments', type: '[Payment!]!' }
      ],
      Payment: [
        { name: 'amount', type: 'Float!' },
        { name: 'currency', type: 'String!' },
        { name: 'method', type: 'PaymentMethod!' },
        { name: 'status', type: 'PaymentStatus!' },
        { name: 'transactionId', type: 'String!' }
      ]
    };

    const fields = [...commonFields];
    const specificFields = domainFields[domain] || [
      { name: 'name', type: 'String!' },
      { name: 'description', type: 'String' },
      { name: 'status', type: 'String!' }
    ];

    // Add some domain-specific fields
    const numSpecificFields = Math.min(specificFields.length, this.config.numFieldsPerType - commonFields.length);
    fields.push(...specificFields.slice(0, numSpecificFields));

    return fields;
  }

  /**
   * Generate realistic field with appropriate directives
   */
  private generateRealisticField(
    field: {name: string, type: string, hasArgs?: boolean},
    domain: string,
    fileIndex: number,
    typeIndex: number
  ): string {
    let content = '';

    // Add field-level directives based on field type and domain
    if (Math.random() < this.config.directiveFrequency) {
      content += this.generateRealisticFieldDirectives(field, domain);
    }

    // Generate field definition with arguments if needed
    let fieldDef = `  ${field.name}`;
    if (field.hasArgs) {
      fieldDef += `(limit: Int = 10, offset: Int = 0)`;
    }
    fieldDef += `: ${field.type}\n`;

    content += fieldDef;
    return content;
  }

  /**
   * Generate realistic field directives following existing patterns
   */
  private generateRealisticFieldDirectives(
    field: {name: string, type: string, hasArgs?: boolean},
    domain: string
  ): string {
    const directives = [];

    // Method call directive - realistic patterns
    if (Math.random() < 0.8) {
      let methodCall = '';

      if (field.name === 'id') {
        methodCall = `get${domain}Id(obj.id)`;
      } else if (field.name.includes('workflow') || field.name.includes('Workflow')) {
        methodCall = `await CustomWorkflowService.getWorkflowData(obj.id)`;
      } else if (field.hasArgs) {
        methodCall = `get${field.name.charAt(0).toUpperCase() + field.name.slice(1)}({context, args, obj})`;
      } else if (field.type.includes('[') || field.type.includes('!]')) {
        methodCall = `${domain}Service.get${field.name.charAt(0).toUpperCase() + field.name.slice(1)}(obj.id)`;
      } else {
        const patterns = [
          `get${field.name.charAt(0).toUpperCase() + field.name.slice(1)}(obj.id)`,
          `${domain}Service.resolve${field.name.charAt(0).toUpperCase() + field.name.slice(1)}(obj)`,
          `await ${domain}Service.fetch${field.name.charAt(0).toUpperCase() + field.name.slice(1)}(obj.id)`
        ];
        methodCall = patterns[Math.floor(Math.random() * patterns.length)];
      }

      directives.push(`  # @methodCall(${methodCall})`);
    }

    // Import directive
    if (Math.random() < 0.4) {
      const serviceName = `${field.name.charAt(0).toUpperCase() + field.name.slice(1)}Service`;
      directives.push(`  # @import(import { ${serviceName} } from '@/services/${domain.toLowerCase()}/${field.name}-service';)`);
    }

    // Field directive for hidden fields
    if (Math.random() < 0.2) {
      const hiddenFieldName = `hidden${field.name.charAt(0).toUpperCase() + field.name.slice(1)}`;
      const hiddenFieldType = field.type.replace('!', '').replace('[', '').replace(']', '');
      directives.push(`  # @field(${hiddenFieldName}: ${hiddenFieldType})`);
    }

    this.stats.totalDirectives += directives.length;
    return directives.length > 0 ? directives.join('\n') + '\n' : '';
  }

  /**
   * Generate input types for domain
   */
  private generateInputTypes(domain: string, fileIndex: number): string {
    let content = `input ${domain}Input {\n`;

    const inputFields = [
      'name: String!',
      'description: String',
      'status: String'
    ];

    if (domain === 'User') {
      inputFields.push('email: String!', 'password: String!');
    } else if (domain === 'Product') {
      inputFields.push('price: Float!', 'category: String!');
    } else if (domain === 'Order') {
      inputFields.push('customerId: ID!', 'items: [OrderItemInput!]!');
    }

    for (const field of inputFields.slice(0, 4)) {
      content += `  ${field}\n`;
      this.stats.totalFields++;
    }

    content += '}';
    return content;
  }

  /**
   * Generate domain-specific union
   */
  private generateDomainUnion(domain: string, fileIndex: number): string {
    const unionName = `${domain}Union`;
    const memberTypes = [`${domain}`, `${domain}Metadata`, `${domain}Summary`];

    let content = '';

    // Add union-level directives
    if (Math.random() < this.config.directiveFrequency * 0.3) {
      content += `  # @methodCall(resolve${unionName}Type(obj))\n`;
      this.stats.totalDirectives++;
    }

    content += `union ${unionName} = ${memberTypes.join(' | ')}`;
    return content;
  }
}

/**
 * Quick generation presets for common test scenarios
 */
export const TestCodebasePresets = {
  small: {
    numSchemaFiles: 10,
    numTypesPerFile: 5,
    numFieldsPerType: 4,
    directiveFrequency: 0.5
  },
  medium: {
    numSchemaFiles: 25,
    numTypesPerFile: 8,
    numFieldsPerType: 6,
    directiveFrequency: 0.6
  },
  large: {
    numSchemaFiles: 50,
    numTypesPerFile: 12,
    numFieldsPerType: 8,
    directiveFrequency: 0.7
  },
  xlarge: {
    numSchemaFiles: 100,
    numTypesPerFile: 15,
    numFieldsPerType: 10,
    directiveFrequency: 0.8
  },
  massive: {
    numSchemaFiles: 300,
    numTypesPerFile: 20,
    numFieldsPerType: 12,
    directiveFrequency: 0.9
  },
  enterprise: {
    numSchemaFiles: 500,
    numTypesPerFile: 25,
    numFieldsPerType: 15,
    directiveFrequency: 0.95
  }
};

/**
 * Generate test codebase with preset configuration
 */
export async function generateTestCodebaseWithPreset(
  preset: keyof typeof TestCodebasePresets,
  outputDir?: string
): Promise<TestCodebaseStats> {
  const config = {
    ...TestCodebasePresets[preset],
    outputDir,
    enableLogging: true
  };
  
  const generator = new LargeCodebaseTestGenerator(config);
  return await generator.generateTestCodebase();
}
