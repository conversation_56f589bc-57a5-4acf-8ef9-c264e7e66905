import * as fs from 'fs-extra';
import * as path from 'path';
import { chalkLoader, type ChalkInstance } from './chalk-loader';
import { listSchemaFiles } from '@utils/schema-loader';
import { FieldDirectiveProcessor } from '@utils/field-directive-processor';
import type { FieldDirectiveField } from '@utils/directive-parser';

/**
 * Error information for a @field directive validation error
 */
export interface FieldDirectiveError {
    /** The file where the error occurred */
    filePath: string;
    /** The line number where the error occurred (if available) */
    line?: number;
    /** The type name with the error */
    typeName?: string;
    /** The field name with the error (if field-level) */
    fieldName?: string;
    /** The error message */
    message: string;
    /** The severity of the error (error or warning) */
    severity: 'error' | 'warning';
}

/**
 * Results of validating @field directives
 */
export interface ValidationResult {
    /** List of validation errors */
    errors: FieldDirectiveError[];
    /** Whether validation passed (no errors) */
    passed: boolean;
    /** Statistics about the validation */
    stats: {
        /** Number of schema files processed */
        filesProcessed: number;
        /** Number of types processed */
        typesProcessed: number;
        /** Number of fields processed */
        fieldsProcessed: number;
        /** Number of @field directives processed */
        fieldDirectivesProcessed: number;
    };
}

/**
 * Types of validation checks to perform
 */
export interface ValidationOptions {
    /** Check if imported types exist (requires typeImportRoot) */
    checkImportPaths?: boolean;
    /** Root directory for type imports */
    typeImportRoot?: string;
    /** Check for syntax errors in @field directives */
    checkSyntax?: boolean;
    /** Check for duplicated field names in @field directives */
    checkDuplicates?: boolean;
    /** Treat warnings as errors */
    strict?: boolean;
    /** Enable verbose output */
    verbose?: boolean;
}

/**
 * Validates @field directives in GraphQL schema files
 */
export class FieldDirectiveValidator {
    /**
     * Get chalk instance with fallback
     */
    private static getChalk(): ChalkInstance {
        return chalkLoader.getChalkSync();
    }

    /**
     * Validates @field directives in all schema files
     * @param schemaPath Path to the schema directory or file
     * @param options Validation options
     * @returns Validation result
     */
    public static async validateAll(
        schemaPath: string,
        options: ValidationOptions = {}
    ): Promise<ValidationResult> {
        // Initialize result
        const result: ValidationResult = {
            errors: [],
            passed: true,
            stats: {
                filesProcessed: 0,
                typesProcessed: 0,
                fieldsProcessed: 0,
                fieldDirectivesProcessed: 0,
            },
        };

        // Get all schema files
        const schemaFiles = listSchemaFiles(schemaPath);
        if (schemaFiles.length === 0) {
            result.errors.push({
                filePath: schemaPath,
                message: `No schema files found at ${schemaPath}`,
                severity: 'error',
            });
            result.passed = false;
            return result;
        }

        // Process each schema file
        for (const schemaFile of schemaFiles) {
            if (options.verbose) {
                console.log(`Processing schema file: ${path.basename(schemaFile)}`);
            }

            result.stats.filesProcessed++;

            // Initialize line tracking variables
            let lineNumber = 0;
            let currentTypeName = '';

            try {
                // Read the schema file
                const schemaContent = await fs.readFile(schemaFile, 'utf8');
                const lines = schemaContent.split('\n');

                // Find all type definitions in the schema
                // Simple regex to match type, interface, or input definitions
                const typeNameRegex = /^(type|interface|input)\s+([A-Za-z][A-Za-z0-9_]*)/;

                // Process each line to find type definitions
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    const typeMatch = line.match(typeNameRegex);

                    if (typeMatch) {
                        const typeName = typeMatch[2];
                        lineNumber = i + 1;
                        currentTypeName = typeName;
                        result.stats.typesProcessed++;

                        // Validate type-level @field directives
                        await this.validateTypeFieldDirectives(
                            schemaFile,
                            typeName,
                            lines,
                            i,
                            result,
                            options
                        );

                        // Find fields in the type definition
                        let j = i + 1;
                        while (j < lines.length && !lines[j].includes('}')) {
                            // Check if the line defines a field (not a comment or empty line)
                            if (!lines[j].trim().startsWith('#') &&
                                lines[j].trim() !== '' &&
                                lines[j].includes(':')) {
                                result.stats.fieldsProcessed++;

                                // Extract field name
                                const fieldMatch = lines[j].match(/^\s*([A-Za-z][A-Za-z0-9_]*)/);
                                if (fieldMatch) {
                                    const fieldName = fieldMatch[1];

                                    // Check the line above for field-level @field directive
                                    if (j > 0 && lines[j - 1].includes('@field(')) {
                                        // Validate field-level @field directive
                                        await this.validateFieldFieldDirective(
                                            schemaFile,
                                            typeName,
                                            fieldName,
                                            lines,
                                            j - 1,
                                            result,
                                            options
                                        );
                                    }
                                }
                            }
                            j++;
                        }
                    }
                }
            } catch (error: any) {
                result.errors.push({
                    filePath: schemaFile,
                    line: lineNumber,
                    typeName: currentTypeName,
                    message: `Error processing @field directive: ${error.message}`,
                    severity: 'error',
                });
                result.passed = false;
            }
        }

        // Determine if validation passed
        const errorCount = result.errors.filter(e => e.severity === 'error').length;
        const warningCount = result.errors.filter(e => e.severity === 'warning').length;

        // In strict mode, warnings count as errors
        result.passed = options.strict ? errorCount === 0 && warningCount === 0 : errorCount === 0;

        return result;
    }

    /**
     * Validates type-level @field directives
     */
    private static async validateTypeFieldDirectives(
        schemaFile: string,
        typeName: string,
        lines: string[],
        typeLineIndex: number,
        result: ValidationResult,
        options: ValidationOptions
    ): Promise<void> {
        // Look for @field directives before the type definition
        for (let i = Math.max(0, typeLineIndex - 10); i < typeLineIndex; i++) {
            const line = lines[i];
            if (line.includes('@field(')) {
                result.stats.fieldDirectivesProcessed++;

                // Validate syntax if enabled
                if (options.checkSyntax !== false) {
                    this.validateFieldSyntax(
                        line,
                        schemaFile,
                        typeName,
                        i + 1,
                        result
                    );
                }

                // Get field directive fields for this type
                try {
                    const fieldDirectiveFields = await FieldDirectiveProcessor.extractFieldDirectiveFields({
                        fieldFields: []
                    });

                    // Validate duplicates if enabled
                    if (options.checkDuplicates !== false) {
                        this.checkDuplicateFields(
                            fieldDirectiveFields,
                            schemaFile,
                            typeName,
                            i + 1,
                            result
                        );
                    }

                    // Validate import paths if enabled
                    if (options.checkImportPaths && options.typeImportRoot) {
                        await this.validateImportPaths(
                            fieldDirectiveFields,
                            schemaFile,
                            typeName,
                            i + 1,
                            options.typeImportRoot,
                            result
                        );
                    }
                } catch (error: any) {
                    result.errors.push({
                        filePath: schemaFile,
                        line: i + 1,
                        typeName,
                        message: `Error processing @field directive: ${error.message}`,
                        severity: 'error',
                    });
                    result.passed = false;
                }
            }
        }
    }

    /**
     * Validates field-level @field directives
     */
    private static async validateFieldFieldDirective(
        schemaFile: string,
        typeName: string,
        fieldName: string,
        lines: string[],
        directiveLineIndex: number,
        result: ValidationResult,
        options: ValidationOptions
    ): Promise<void> {
        const line = lines[directiveLineIndex];
        result.stats.fieldDirectivesProcessed++;

        // Validate syntax if enabled
        if (options.checkSyntax !== false) {
            this.validateFieldFieldSyntax(
                line,
                schemaFile,
                typeName,
                fieldName,
                directiveLineIndex + 1,
                result
            );
        }

        // Get field override for this specific field
        try {
            const fieldOverride = await FieldDirectiveProcessor.getFieldOverride(
                typeName,
                fieldName,
                schemaFile,
                options.verbose
            );

            if (fieldOverride) {
                // Validate import path if enabled
                if (options.checkImportPaths && options.typeImportRoot && fieldOverride.importPath) {
                    await this.validateSingleImportPath(
                        fieldOverride,
                        schemaFile,
                        typeName,
                        fieldName,
                        directiveLineIndex + 1,
                        options.typeImportRoot,
                        result
                    );
                }
            }
        } catch (error: any) {
            result.errors.push({
                filePath: schemaFile,
                line: directiveLineIndex + 1,
                typeName,
                fieldName,
                message: `Error processing field-level @field directive: ${error.message}`,
                severity: 'error',
            });
            result.passed = false;
        }
    }

    /**
     * Validates @field directive syntax
     */
    private static validateFieldSyntax(
        content: string,
        filePath: string,
        typeName: string,
        line: number,
        result: ValidationResult
    ): void {
        // Basic syntax validation for @field directive
        const fieldDirectiveRegex = /@field\s*\(\s*([^)]+)\s*\)/;
        const match = content.match(fieldDirectiveRegex);

        if (!match) {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: 'Invalid @field directive syntax',
                severity: 'error',
            });
            result.passed = false;
            return;
        }

        const directiveContent = match[1];

        // Validate field directive format: fieldName: Type "importPath"
        const fieldFormatRegex = /^\s*([a-zA-Z][a-zA-Z0-9_]*)\s*:\s*(.+?)(?:\s+"([^"]+)"\s*)?$/;
        const fieldMatch = directiveContent.match(fieldFormatRegex);

        if (!fieldMatch) {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: 'Invalid @field directive format. Expected: fieldName: Type "importPath"',
                severity: 'error',
            });
            result.passed = false;
            return;
        }

        const [, fieldName, fieldType, importPath] = fieldMatch;

        // Validate field name
        if (!fieldName || !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(fieldName)) {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: `Invalid field name "${fieldName}" in @field directive`,
                severity: 'error',
            });
            result.passed = false;
        }

        // Validate field type
        if (!fieldType || fieldType.trim() === '') {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: `Missing field type in @field directive for field "${fieldName}"`,
                severity: 'error',
            });
            result.passed = false;
        }

        // Validate import path format if provided
        if (importPath && (!importPath.startsWith('./') && !importPath.startsWith('../') && !importPath.startsWith('@'))) {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: `Invalid import path "${importPath}" in @field directive. Should start with './', '../', or '@'`,
                severity: 'warning',
            });
        }
    }

    /**
     * Validates field-level @field directive syntax
     */
    private static validateFieldFieldSyntax(
        content: string,
        filePath: string,
        typeName: string,
        fieldName: string,
        line: number,
        result: ValidationResult
    ): void {
        // Field-level @field directives should override the field type
        const fieldDirectiveRegex = /@field\s*\(\s*([^)]+)\s*\)/;
        const match = content.match(fieldDirectiveRegex);

        if (!match) {
            result.errors.push({
                filePath,
                line,
                typeName,
                fieldName,
                message: 'Invalid field-level @field directive syntax',
                severity: 'error',
            });
            result.passed = false;
            return;
        }

        const directiveContent = match[1];

        // For field-level directives, we expect just the type and optional import path
        const fieldTypeRegex = /^\s*(.+?)(?:\s+"([^"]+)"\s*)?$/;
        const typeMatch = directiveContent.match(fieldTypeRegex);

        if (!typeMatch) {
            result.errors.push({
                filePath,
                line,
                typeName,
                fieldName,
                message: 'Invalid field-level @field directive format. Expected: Type "importPath"',
                severity: 'error',
            });
            result.passed = false;
            return;
        }

        const [, fieldType, importPath] = typeMatch;

        // Validate field type
        if (!fieldType || fieldType.trim() === '') {
            result.errors.push({
                filePath,
                line,
                typeName,
                fieldName,
                message: `Missing field type in field-level @field directive`,
                severity: 'error',
            });
            result.passed = false;
        }

        // Validate import path format if provided
        if (importPath && (!importPath.startsWith('./') && !importPath.startsWith('../') && !importPath.startsWith('@'))) {
            result.errors.push({
                filePath,
                line,
                typeName,
                fieldName,
                message: `Invalid import path "${importPath}" in field-level @field directive. Should start with './', '../', or '@'`,
                severity: 'warning',
            });
        }
    }

    /**
     * Checks for duplicate field names in @field directives
     */
    private static checkDuplicateFields(
        fieldDirectiveFields: FieldDirectiveField[],
        filePath: string,
        typeName: string,
        line: number,
        result: ValidationResult
    ): void {
        const fieldNames = new Set<string>();
        const duplicates = new Set<string>();

        for (const field of fieldDirectiveFields) {
            if (fieldNames.has(field.name)) {
                duplicates.add(field.name);
            } else {
                fieldNames.add(field.name);
            }
        }

        for (const duplicateField of duplicates) {
            result.errors.push({
                filePath,
                line,
                typeName,
                message: `Duplicate field "${duplicateField}" in @field directive`,
                severity: 'error',
            });
            result.passed = false;
        }
    }

    /**
     * Validates import paths for @field directive fields
     */
    private static async validateImportPaths(
        fieldDirectiveFields: FieldDirectiveField[],
        filePath: string,
        typeName: string,
        line: number,
        typeImportRoot: string,
        result: ValidationResult
    ): Promise<void> {
        for (const field of fieldDirectiveFields) {
            if (field.importPath) {
                await this.validateSingleImportPath(
                    field,
                    filePath,
                    typeName,
                    undefined,
                    line,
                    typeImportRoot,
                    result
                );
            }
        }
    }

    /**
     * Validates a single import path
     */
    private static async validateSingleImportPath(
        fieldDirectiveField: FieldDirectiveField,
        filePath: string,
        typeName: string,
        fieldName: string | undefined,
        line: number,
        typeImportRoot: string,
        result: ValidationResult
    ): Promise<void> {
        const importPath = fieldDirectiveField.importPath || fieldDirectiveField.path;
        if (!importPath) return;

        try {
            // Resolve the import path
            let resolvedPath: string;

            if (importPath.startsWith('@')) {
                // Handle alias imports (e.g., @types/user)
                resolvedPath = path.resolve(typeImportRoot, importPath.substring(1));
            } else if (importPath.startsWith('./') || importPath.startsWith('../')) {
                // Handle relative imports
                resolvedPath = path.resolve(path.dirname(filePath), importPath);
            } else {
                // Handle absolute imports
                resolvedPath = path.resolve(typeImportRoot, importPath);
            }

            // Try common TypeScript file extensions
            const extensions = ['.ts', '.tsx', '.d.ts', '/index.ts', '/index.tsx', '/index.d.ts'];
            let fileExists = false;

            for (const ext of extensions) {
                const fullPath = resolvedPath + ext;
                if (await fs.pathExists(fullPath)) {
                    fileExists = true;
                    break;
                }
            }

            if (!fileExists) {
                result.errors.push({
                    filePath,
                    line,
                    typeName,
                    fieldName,
                    message: `Import path "${importPath}" for field "${fieldDirectiveField.name}" could not be resolved`,
                    severity: 'warning',
                });
            }
        } catch (error: any) {
            result.errors.push({
                filePath,
                line,
                typeName,
                fieldName,
                message: `Error validating import path "${importPath}": ${error.message}`,
                severity: 'warning',
            });
        }
    }

    /**
     * Prints validation results to the console
     */
    public static printValidationResults(
        result: ValidationResult
    ): void {
        const chalk = this.getChalk();
        console.log('\n' + chalk.bold('@field Directive Validation Results'));
        console.log('='.repeat(50));

        // Print statistics
        console.log(chalk.blue('Statistics:'));
        console.log(`  Files processed: ${result.stats.filesProcessed}`);
        console.log(`  Types processed: ${result.stats.typesProcessed}`);
        console.log(`  Fields processed: ${result.stats.fieldsProcessed}`);
        console.log(`  @field directives processed: ${result.stats.fieldDirectivesProcessed}`);

        // Print errors and warnings
        const errors = result.errors.filter(e => e.severity === 'error');
        const warnings = result.errors.filter(e => e.severity === 'warning');

        if (errors.length > 0) {
            console.log('\n' + chalk.bold(chalk.red(`Errors (${errors.length}):`)));
            for (const error of errors) {
                console.log(chalk.red(`  ✗ ${path.basename(error.filePath)}:${error.line || '?'} - ${error.message}`));
                if (error.typeName) {
                    console.log(chalk.gray(`    Type: ${error.typeName}${error.fieldName ? `, Field: ${error.fieldName}` : ''}`));
                }
            }
        }

        if (warnings.length > 0) {
            console.log('\n' + chalk.bold(chalk.yellow(`Warnings (${warnings.length}):`)));
            for (const warning of warnings) {
                console.log(chalk.yellow(`  ⚠ ${path.basename(warning.filePath)}:${warning.line || '?'} - ${warning.message}`));
                if (warning.typeName) {
                    console.log(chalk.gray(`    Type: ${warning.typeName}${warning.fieldName ? `, Field: ${warning.fieldName}` : ''}`));
                }
            }
        }

        // Print final result
        console.log('\n' + '='.repeat(50));
        if (result.passed) {
            console.log(chalk.bold(chalk.green('✓ Validation passed!')));
        } else {
            console.log(chalk.bold(chalk.red('✗ Validation failed!')));
        }
    }
}

/**
 * CLI function to validate @field directives
 */
export async function validateFieldDirectives(args: string[]): Promise<void> {
    const chalk = chalkLoader.getChalkSync();

    // Parse command line arguments
    const schemaPath = args.find(arg => !arg.startsWith('--')) || './schema';
    const verbose = args.includes('--verbose');
    const strict = args.includes('--strict');
    const checkImports = args.includes('--check-imports');
    const importRootArg = args.find(arg => arg.startsWith('--import-root='));
    const importRoot = importRootArg ? importRootArg.split('=')[1] : './src';

    console.log(chalk.bold(chalk.blue('Validating @field directives...')));
    console.log(`Schema path: ${schemaPath}`);
    console.log(`Import root: ${importRoot}`);
    console.log(`Check imports: ${checkImports}`);
    console.log(`Strict mode: ${strict}`);
    console.log(`Verbose: ${verbose}`);

    // Run validation
    const result = await FieldDirectiveValidator.validateAll(schemaPath, {
        checkImportPaths: checkImports,
        typeImportRoot: importRoot,
        checkSyntax: true,
        checkDuplicates: true,
        strict,
        verbose,
    });

    // Print results
    FieldDirectiveValidator.printValidationResults(result);

    // Exit with appropriate code
    if (!result.passed) {
        process.exit(1);
    }
} 