/**
 * Custom naming convention for GraphQL Code Generator
 *
 * This function handles the specific issue where argument types like:
 * - RootMutationactivatedEscalationArgs -> RootMutationActivatedEscalationArgs
 * - RootMutationcreateChatArgs -> RootMutationCreateChatArgs
 * - SSOProfile -> SSOProfile (preserves acronyms)
 *
 * Based on GraphQL Code Generator best practices:
 * https://the-guild.dev/graphql/codegen/docs/config-reference/naming-convention
 *
 * Usage in codegen.ts:
 * ```typescript
 * namingConvention: {
 *   typeNames: './src/utils/custom-naming-convention',
 *   enumValues: 'keep'
 * }
 * ```
 */

const { pascalCase } = require('change-case-all');

/**
 * Custom naming function that preserves acronyms and fixes argument type casing
 * @param {string} str - The string to transform
 * @returns {string} - The transformed string
 */
function customNamingConvention(str) {
  // If the string is empty or null, return as-is
  if (!str) return str;

  // Debug logging for enum transformation issues
  const isDebug = process.env.DEBUG_NAMING_CONVENTION === 'true';
  if (isDebug) {
    console.log(`[CustomNaming] Input: "${str}"`);
  }

  // Handle argument types: RootMutation/Query/Subscription + fieldName + Args
  const argTypePattern = /^(Root(?:Query|Mutation|Subscription))([a-z])([a-zA-Z0-9]*)(Args)$/;
  const argTypeMatch = str.match(argTypePattern);
  
  if (argTypeMatch) {
    const [, prefix, firstChar, restOfName, suffix] = argTypeMatch;
    const fieldName = firstChar + restOfName;
    const properFieldName = firstChar.toUpperCase() + restOfName;
    return `${prefix}${properFieldName}${suffix}`;
  }

  // Handle types that should preserve acronyms (like SSOClient, SSOProfile)
  // If the string contains consecutive uppercase letters, preserve them
  // Enhanced regex to catch more acronym patterns including those at the start
  if (/[A-Z]{2,}/.test(str) || /^[A-Z]{2,}[A-Z][a-z]/.test(str)) {
    if (isDebug) {
      console.log(`[CustomNaming] Preserving acronym: "${str}" -> "${str}"`);
    }
    return str; // Keep as-is to preserve acronyms
  }

  // For regular types, use pascalCase but be conservative
  // Only transform if it's clearly not already in the correct format
  if (/^[A-Z][a-zA-Z0-9]*$/.test(str)) {
    return str; // Already in PascalCase, keep as-is
  }

  // For other cases, apply pascalCase transformation
  const result = pascalCase(str);
  if (isDebug) {
    console.log(`[CustomNaming] PascalCase transform: "${str}" -> "${result}"`);
  }
  return result;
}

module.exports = customNamingConvention;
