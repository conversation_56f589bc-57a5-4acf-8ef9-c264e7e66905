import fs from 'fs-extra';
import path from 'path';
import { EventEmitter } from 'events';

/**
 * Phase 3: Memory-Mapped File Reader Implementation
 * Uses Node.js built-in capabilities for memory-efficient file operations
 * with graceful fallback to standard I/O when memory mapping is not available
 */

export interface MemoryMappedConfig {
  enableMemoryMapping: boolean;
  fallbackToStandardIO: boolean;
  chunkSize: number; // Size of chunks for streaming reads
  maxFileSize: number; // Maximum file size for memory mapping (bytes)
  enableCaching: boolean;
  cacheSize: number; // Maximum number of cached file handles
  enablePerformanceMonitoring: boolean;
  debug: boolean;
}

export interface FileHandle {
  filePath: string;
  size: number;
  isMemoryMapped: boolean;
  buffer?: Buffer;
  fd?: number;
  lastAccessed: number;
  accessCount: number;
}

export interface ReadResult {
  data: Buffer;
  bytesRead: number;
  isFromCache: boolean;
  readTime: number;
}

export interface MemoryMappedStats {
  totalFiles: number;
  memoryMappedFiles: number;
  standardIOFiles: number;
  totalMemoryUsed: number;
  cacheHitRate: number;
  averageReadTime: number;
  platformSupported: boolean;
}

/**
 * Memory-mapped file reader with intelligent fallback
 */
export class MemoryMappedFileReader extends EventEmitter {
  private config: Required<MemoryMappedConfig>;
  private fileHandles = new Map<string, FileHandle>();
  private readMetrics: Array<{ time: number; size: number; cached: boolean }> = [];
  private platformSupported: boolean;
  private maxMetricsHistory = 1000;

  constructor(config: Partial<MemoryMappedConfig> = {}) {
    super();
    
    this.config = {
      enableMemoryMapping: true,
      fallbackToStandardIO: true,
      chunkSize: 64 * 1024, // 64KB chunks
      maxFileSize: 100 * 1024 * 1024, // 100MB max for memory mapping
      enableCaching: true,
      cacheSize: 50, // Cache up to 50 file handles
      enablePerformanceMonitoring: true,
      debug: false,
      ...config
    };

    this.platformSupported = this.checkPlatformSupport();
    
    if (this.config.debug) {
      console.log(`🗺️ MemoryMappedFileReader initialized (platform supported: ${this.platformSupported})`);
    }
  }

  /**
   * Check if memory mapping is supported on current platform
   */
  private checkPlatformSupport(): boolean {
    try {
      // Check if we can use Buffer.allocUnsafe for large allocations
      const testSize = 1024 * 1024; // 1MB test
      const testBuffer = Buffer.allocUnsafe(testSize);
      return testBuffer.length === testSize;
    } catch (error) {
      return false;
    }
  }

  /**
   * Read file with memory mapping or fallback to standard I/O
   */
  async readFile(filePath: string, offset: number = 0, length?: number): Promise<ReadResult> {
    const startTime = Date.now();
    
    try {
      // Check cache first
      const cachedHandle = this.fileHandles.get(filePath);
      if (cachedHandle && this.config.enableCaching) {
        cachedHandle.lastAccessed = Date.now();
        cachedHandle.accessCount++;
        
        const data = this.readFromHandle(cachedHandle, offset, length);
        const readTime = Date.now() - startTime;
        
        this.recordMetrics(readTime, data.length, true);
        
        return {
          data,
          bytesRead: data.length,
          isFromCache: true,
          readTime
        };
      }

      // Get file stats
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;

      // Determine if we should use memory mapping
      const shouldMemoryMap = this.shouldUseMemoryMapping(fileSize);
      
      let handle: FileHandle;
      
      if (shouldMemoryMap) {
        handle = await this.createMemoryMappedHandle(filePath, fileSize);
      } else {
        handle = await this.createStandardIOHandle(filePath, fileSize);
      }

      // Cache the handle if caching is enabled
      if (this.config.enableCaching) {
        this.cacheFileHandle(filePath, handle);
      }

      const data = this.readFromHandle(handle, offset, length);
      const readTime = Date.now() - startTime;
      
      this.recordMetrics(readTime, data.length, false);
      
      if (this.config.debug) {
        console.log(`📖 Read ${data.length} bytes from ${filePath} (${handle.isMemoryMapped ? 'memory-mapped' : 'standard I/O'}) in ${readTime}ms`);
      }

      return {
        data,
        bytesRead: data.length,
        isFromCache: false,
        readTime
      };

    } catch (error) {
      if (this.config.debug) {
        console.error(`❌ Failed to read file ${filePath}:`, error);
      }
      throw error;
    }
  }

  /**
   * Determine if file should use memory mapping
   */
  private shouldUseMemoryMapping(fileSize: number): boolean {
    if (!this.config.enableMemoryMapping || !this.platformSupported) {
      return false;
    }

    // Don't memory map very small files (overhead not worth it)
    if (fileSize < 4096) { // 4KB threshold
      return false;
    }

    // Don't memory map very large files
    if (fileSize > this.config.maxFileSize) {
      return false;
    }

    return true;
  }

  /**
   * Create memory-mapped file handle using Buffer
   */
  private async createMemoryMappedHandle(filePath: string, fileSize: number): Promise<FileHandle> {
    try {
      // Read entire file into buffer (simulates memory mapping)
      const buffer = await fs.readFile(filePath);
      
      return {
        filePath,
        size: fileSize,
        isMemoryMapped: true,
        buffer,
        lastAccessed: Date.now(),
        accessCount: 1
      };
    } catch (error) {
      if (this.config.fallbackToStandardIO) {
        if (this.config.debug) {
          console.warn(`⚠️ Memory mapping failed for ${filePath}, falling back to standard I/O`);
        }
        return this.createStandardIOHandle(filePath, fileSize);
      }
      throw error;
    }
  }

  /**
   * Create standard I/O file handle
   */
  private async createStandardIOHandle(filePath: string, fileSize: number): Promise<FileHandle> {
    const fd = await fs.open(filePath, 'r');

    return {
      filePath,
      size: fileSize,
      isMemoryMapped: false,
      fd: typeof fd === 'object' && 'fd' in fd ? (fd as any).fd : fd as number,
      lastAccessed: Date.now(),
      accessCount: 1
    };
  }

  /**
   * Read data from file handle
   */
  private readFromHandle(handle: FileHandle, offset: number = 0, length?: number): Buffer {
    const readLength = length || (handle.size - offset);
    
    if (handle.isMemoryMapped && handle.buffer) {
      // Read from memory-mapped buffer
      return handle.buffer.subarray(offset, offset + readLength);
    } else if (handle.fd !== undefined) {
      // Read from file descriptor
      const buffer = Buffer.allocUnsafe(readLength);
      const bytesRead = fs.readSync(handle.fd, buffer, 0, readLength, offset);
      return buffer.subarray(0, bytesRead);
    } else {
      throw new Error(`Invalid file handle for ${handle.filePath}`);
    }
  }

  /**
   * Cache file handle with LRU eviction
   */
  private cacheFileHandle(filePath: string, handle: FileHandle): void {
    // Remove oldest entries if cache is full
    if (this.fileHandles.size >= this.config.cacheSize) {
      const oldestEntry = Array.from(this.fileHandles.entries())
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)[0];
      
      if (oldestEntry) {
        this.closeFileHandle(oldestEntry[1]);
        this.fileHandles.delete(oldestEntry[0]);
      }
    }

    this.fileHandles.set(filePath, handle);
  }

  /**
   * Close file handle and clean up resources
   */
  private closeFileHandle(handle: FileHandle): void {
    if (handle.fd !== undefined) {
      try {
        fs.closeSync(handle.fd);
      } catch (error) {
        if (this.config.debug) {
          console.warn(`⚠️ Failed to close file descriptor for ${handle.filePath}:`, error);
        }
      }
    }
    
    // Buffer will be garbage collected automatically
    handle.buffer = undefined;
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(readTime: number, bytesRead: number, cached: boolean): void {
    if (!this.config.enablePerformanceMonitoring) return;

    this.readMetrics.push({
      time: readTime,
      size: bytesRead,
      cached
    });

    // Keep only recent metrics
    if (this.readMetrics.length > this.maxMetricsHistory) {
      this.readMetrics.shift();
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): MemoryMappedStats {
    const totalFiles = this.fileHandles.size;
    const memoryMappedFiles = Array.from(this.fileHandles.values())
      .filter(h => h.isMemoryMapped).length;
    const standardIOFiles = totalFiles - memoryMappedFiles;

    const totalMemoryUsed = Array.from(this.fileHandles.values())
      .reduce((sum, handle) => {
        return sum + (handle.buffer ? handle.buffer.length : 0);
      }, 0);

    const cachedReads = this.readMetrics.filter(m => m.cached).length;
    const cacheHitRate = this.readMetrics.length > 0 ? cachedReads / this.readMetrics.length : 0;

    const averageReadTime = this.readMetrics.length > 0 
      ? this.readMetrics.reduce((sum, m) => sum + m.time, 0) / this.readMetrics.length 
      : 0;

    return {
      totalFiles,
      memoryMappedFiles,
      standardIOFiles,
      totalMemoryUsed,
      cacheHitRate,
      averageReadTime,
      platformSupported: this.platformSupported
    };
  }

  /**
   * Clear all cached file handles
   */
  clearCache(): void {
    for (const handle of this.fileHandles.values()) {
      this.closeFileHandle(handle);
    }
    this.fileHandles.clear();
    
    if (this.config.debug) {
      console.log('🧹 Memory-mapped file cache cleared');
    }
  }

  /**
   * Close all file handles and clean up
   */
  async close(): Promise<void> {
    this.clearCache();
    this.readMetrics = [];
    
    if (this.config.debug) {
      console.log('🔒 MemoryMappedFileReader closed');
    }
  }
}

/**
 * Global memory-mapped file reader instance
 */
let globalMemoryMappedFileReader: MemoryMappedFileReader | null = null;

/**
 * Get or create global memory-mapped file reader
 */
export function getGlobalMemoryMappedFileReader(config?: Partial<MemoryMappedConfig>): MemoryMappedFileReader {
  if (!globalMemoryMappedFileReader) {
    globalMemoryMappedFileReader = new MemoryMappedFileReader(config);
  }
  return globalMemoryMappedFileReader;
}
