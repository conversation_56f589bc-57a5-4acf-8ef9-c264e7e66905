import type { ParsedDirective, DirectiveContainer, FieldDirectiveField } from '@utils/directive-parser';
import path from 'path';

/**
 * Data extracted from an import directive
 */
export interface ImportData {
  /** The raw import statement */
  statement: string;
  /** The import specifier (what's being imported) */
  specifier: string;
  /** The module path */
  path: string;
}

/**
 * Data extracted from a methodCall directive
 */
export interface MethodCallData {
  /** The method being called */
  method: string;
  /** The full method call including arguments */
  fullCall: string;
}

/**
 * Data extracted from a resolver directive
 */
export interface ResolverData {
  /** The resolver function call */
  fullCall: string;
}

/**
 * Data extracted from a context directive
 */
export interface ContextData {
  /** Path to the context module */
  path: string;
  /** Name of the context type */
  name: string;
  /** Whether the context uses default export */
  isDefaultExport: boolean;
}

/**
 * Data extracted from an alias directive
 */
export interface AliasData {
  /** Import path for the alias */
  path: string;
}

/**
 * Processes directive data and applies it to resolver generation
 */
export class DirectiveProcessor {
  /**
   * Process an import directive to extract the import statement details
   * @param directive The parsed import directive
   * @returns The extracted import data
   */
  public static processImportDirective(directive: ParsedDirective): ImportData | null {
    try {
      let importStatement = directive.content.trim();

      // Remove trailing semicolon if present (common in schema comments)
      if (importStatement.endsWith(';')) {
        importStatement = importStatement.slice(0, -1).trim();
      }

      // Try different import patterns in order of specificity
      // Make regex patterns more flexible to handle whitespace variations

      // 1. Mixed imports: import defaultName, { named1, named2 } from "path"
      const mixedMatch = importStatement.match(/^import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*,\s*\{\s*([^}]+)\s*\}\s+from\s+["']([^"']+)["']\s*$/);
      if (mixedMatch) {
        const [, defaultName, namedImports, path] = mixedMatch;
        return {
          statement: `${importStatement};`, // Ensure semicolon for consistency
          specifier: `${defaultName.trim()}, { ${namedImports.trim()} }`,
          path: path.trim(),
        };
      }

      // 2. Named imports: import { specifier1, specifier2 } from "path"
      const namedMatch = importStatement.match(/^import\s+\{\s*([^}]+)\s*\}\s+from\s+["']([^"']+)["']\s*$/);
      if (namedMatch) {
        const [, specifier, path] = namedMatch;
        return {
          statement: `${importStatement};`, // Ensure semicolon for consistency
          specifier: specifier.trim(),
          path: path.trim(),
        };
      }

      // 3. Default imports: import defaultName from "path"
      const defaultMatch = importStatement.match(/^import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from\s+["']([^"']+)["']\s*$/);
      if (defaultMatch) {
        const [, defaultName, path] = defaultMatch;
        return {
          statement: `${importStatement};`, // Ensure semicolon for consistency
          specifier: defaultName.trim(),
          path: path.trim(),
        };
      }

      // 4. Namespace imports: import * as name from "path"
      const namespaceMatch = importStatement.match(/^import\s+\*\s+as\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from\s+["']([^"']+)["']\s*$/);
      if (namespaceMatch) {
        const [, namespaceName, path] = namespaceMatch;
        return {
          statement: `${importStatement};`, // Ensure semicolon for consistency
          specifier: `* as ${namespaceName.trim()}`,
          path: path.trim(),
        };
      }

      console.warn(`Invalid import directive format: ${directive.raw}`);
      console.warn(`Supported formats:
        - Default import: import defaultName from "path"
        - Named imports: import { name1, name2 } from "path"
        - Mixed imports: import defaultName, { name1, name2 } from "path"
        - Namespace imports: import * as name from "path"`);
      console.warn(`Processed content: "${importStatement}"`);
      return null;
    } catch (error) {
      console.error(`Error processing import directive: ${error}`);
      return null;
    }
  }

  /**
   * Process a methodCall directive to extract the method call details
   * @param directive The parsed methodCall directive
   * @returns The extracted method call data
   */
  public static processMethodCallDirective(directive: ParsedDirective): MethodCallData | null {
    try {
      const methodCall = directive.content.trim();

      // For methodCall directives, we accept any valid JavaScript expression including:
      // - Simple calls: methodName(args)
      // - Property access: obj.method(args)
      // - Async calls: await obj.method(args)
      // - Complex expressions: await context.dataSources.userAPI.getById(obj.authorId)
      // - Variable/property access with type casting: result as SomeType, obj.prop as SomeType

      // Check if the content contains function calls OR type casting
      const hasMethodCall = methodCall.includes('(') && methodCall.includes(')');
      const hasTypeCasting = methodCall.includes(' as ');

      if (!hasMethodCall && !hasTypeCasting) {
        console.warn(`Invalid methodCall directive format: ${directive.raw}`);
        console.warn(`MethodCall directive must contain either a function call or type casting`);
        return null;
      }

      // Extract a meaningful method name for identification purposes
      // Try different patterns to find the actual method being called or variable being accessed
      let method = 'unknown';

      if (hasMethodCall) {
        // Pattern 1: Simple method call - methodName(
        const simpleMatch = methodCall.match(/([a-zA-Z][a-zA-Z0-9_]*)\s*\(/);
        if (simpleMatch) {
          method = simpleMatch[1];
        } else {
          // Pattern 2: Property access - obj.methodName( or obj.prop.methodName(
          const propertyMatch = methodCall.match(/\.([a-zA-Z][a-zA-Z0-9_]*)\s*\(/);
          if (propertyMatch) {
            method = propertyMatch[1];
          } else {
            // Pattern 3: Extract any identifier before parentheses as fallback
            const fallbackMatch = methodCall.match(/([a-zA-Z][a-zA-Z0-9_]*)\s*\(/);
            if (fallbackMatch) {
              method = fallbackMatch[1];
            }
          }
        }
      } else if (hasTypeCasting) {
        // For type casting patterns, extract the variable or property name
        const castingPattern = methodCall.match(/^(?:await\s+)?([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\s+as\s+/);
        if (castingPattern) {
          const fullPath = castingPattern[1];
          // Extract the last part of the property chain or the variable name
          const lastDotIndex = fullPath.lastIndexOf('.');
          method = lastDotIndex >= 0 ? fullPath.substring(lastDotIndex + 1) : fullPath;
        }
      }

      return {
        method: method.trim(),
        fullCall: methodCall
      };
    } catch (error) {
      console.error(`Error processing methodCall directive: ${error}`);
      return null;
    }
  }

  /**
   * Process a context directive to extract context details
   * @param directive The parsed context directive
   * @returns The extracted context data
   */
  public static processContextDirective(directive: ParsedDirective): ContextData | null {
    try {
      const content = directive.content.trim();

      // Extract path and name from context directive
      // Format: {path: "path/to/context", name: "ContextName"}
      // Or: {path: "path/to/context", name: "{ContextName}"}

      // Extract path
      const pathMatch = content.match(/path:\s*["']([^"']+)["']/);
      if (!pathMatch) {
        console.warn(`Invalid context directive path format: ${directive.raw}`);
        return null;
      }

      // Extract name
      const nameMatch = content.match(/name:\s*["']([^"']+)["']/);
      if (!nameMatch) {
        console.warn(`Invalid context directive name format: ${directive.raw}`);
        return null;
      }

      const path = pathMatch[1].trim();
      const rawName = nameMatch[1].trim();

      // Check if the name indicates default export (wrapped in {})
      const isDefaultExport = rawName.startsWith('{') && rawName.endsWith('}');
      const name = isDefaultExport ? rawName.slice(1, -1) : rawName;

      return {
        path,
        name,
        isDefaultExport
      };
    } catch (error) {
      console.error(`Error processing context directive: ${error}`);
      return null;
    }
  }

  /**
   * Process an alias directive to extract the path
   * @param directive The parsed alias directive
   * @returns The extracted alias data
   */
  public static processAliasDirective(directive: ParsedDirective): AliasData | null {
    try {
      const content = directive.content.trim();

      // Extract path from alias directive
      // Format: path: "@path/to/resolver"
      const pathMatch = content.match(/path:\s*["']([^"']+)["']/);
      if (!pathMatch) {
        console.warn(`Invalid alias directive path format: ${directive.raw}`);
        return null;
      }

      const path = pathMatch[1].trim();

      return {
        path
      };
    } catch (error) {
      console.error(`Error processing alias directive: ${error}`);
      return null;
    }
  }

  /**
   * Transform an import statement to use the correct path relative to the output file
   * @param importStatement The original import statement
   * @param schemaFilePath The absolute path to the schema file containing the directive
   * @param outputFilePath The absolute path to the output file where this import will be used
   * @returns The transformed import statement with the correct relative path
   */
  public static transformImportStatement(
    importStatement: string,
    schemaFilePath: string,
    outputFilePath: string
  ): string {
    try {
      console.log(`Transforming import: ${importStatement}`);
      console.log(`Schema file: ${schemaFilePath}`);
      console.log(`Output file: ${outputFilePath}`);

      // Extract the import path
      const pathMatch = importStatement.match(/from\s+['"]([^'"]+)['"]/);
      if (!pathMatch || !pathMatch[1]) {
        console.log(`No path match found in import statement`);
        return importStatement;
      }

      const originalPath = pathMatch[1];

      // Skip transformation for package imports (those starting with @)
      if (originalPath.startsWith('@')) {
        console.log(`Skipping package import: ${originalPath}`);
        return importStatement;
      }

      // Only transform relative paths (starting with ./ or ../)
      if (originalPath.startsWith('./') || originalPath.startsWith('../')) {
        // Get the absolute directory of both files
        const schemaDir = path.dirname(schemaFilePath);
        const outputDir = path.dirname(outputFilePath);

        console.log(`Schema directory: ${schemaDir}`);
        console.log(`Output directory: ${outputDir}`);

        // Calculate the absolute path of the imported module relative to the schema file
        const absoluteImportPath = path.resolve(schemaDir, originalPath);
        console.log(`Absolute import path: ${absoluteImportPath}`);

        // Calculate the new relative path from the output file to the imported module
        let newRelativePath = path.relative(outputDir, absoluteImportPath);

        // Ensure the path has a leading ./ if needed
        if (!newRelativePath.startsWith('.') && !newRelativePath.startsWith('/')) {
          newRelativePath = './' + newRelativePath;
        }

        // Normalize path separators for cross-platform compatibility
        newRelativePath = newRelativePath.replace(/\\/g, '/');

        console.log(`New relative path: ${newRelativePath}`);

        // Replace the original path with the new path
        const result = importStatement.replace(originalPath, newRelativePath);
        console.log(`Transformed import: ${result}`);
        return result;
      }

      console.log(`Not a relative path, keeping original: ${originalPath}`);
      return importStatement;
    } catch (error) {
      console.error(`Error transforming import path: ${error}`);
      return importStatement;
    }
  }

  /**
   * Transform import statement for decorator-based imports using codebase directory as base
   * @param importStatement The original import statement
   * @param codebaseDir The absolute path to the codebase directory
   * @param outputFilePath The absolute path to the output file where this import will be used
   * @returns The transformed import statement with the correct relative path
   */
  public static transformDecoratorImportStatement(
    importStatement: string,
    codebaseDir: string,
    outputFilePath: string
  ): string {
    try {
      console.log(`Transforming decorator import: ${importStatement}`);
      console.log(`Codebase directory: ${codebaseDir}`);
      console.log(`Output file: ${outputFilePath}`);

      // Extract the import path
      const pathMatch = importStatement.match(/from\s+['"]([^'"]+)['"]/);
      if (!pathMatch || !pathMatch[1]) {
        console.log(`No path match found in import statement`);
        return importStatement;
      }

      const originalPath = pathMatch[1];

      // Skip transformation for package imports (those starting with @, or absolute paths)
      if (originalPath.startsWith('@') || originalPath.startsWith('/') || !originalPath.startsWith('.')) {
        console.log(`Skipping non-relative import: ${originalPath}`);
        return importStatement;
      }

      // Only transform relative paths (starting with ./ or ../)
      if (originalPath.startsWith('./') || originalPath.startsWith('../')) {
        // Get the output directory
        const outputDir = path.dirname(outputFilePath);

        console.log(`Codebase directory: ${codebaseDir}`);
        console.log(`Output directory: ${outputDir}`);

        // Calculate the absolute path of the imported module relative to the codebase directory
        const absoluteImportPath = path.resolve(codebaseDir, originalPath);
        console.log(`Absolute import path: ${absoluteImportPath}`);

        // Calculate the new relative path from the output file to the imported module
        let newRelativePath = path.relative(outputDir, absoluteImportPath);

        // Ensure the path has a leading ./ if needed
        if (!newRelativePath.startsWith('.') && !newRelativePath.startsWith('/')) {
          newRelativePath = './' + newRelativePath;
        }

        // Normalize path separators for cross-platform compatibility
        newRelativePath = newRelativePath.replace(/\\/g, '/');

        console.log(`New relative path: ${newRelativePath}`);

        // Replace the original path with the new path
        const result = importStatement.replace(originalPath, newRelativePath);
        console.log(`Transformed decorator import: ${result}`);
        return result;
      }

      console.log(`Not a relative path, keeping original: ${originalPath}`);
      return importStatement;
    } catch (error) {
      console.error(`Error transforming decorator import statement: ${error}`);
      return importStatement;
    }
  }

  /**
   * Extract import statements from @field directives
   * @param directives The directive container with field fields
   * @returns Array of import statements generated from field directive import paths
   */
  public static extractFieldImportStatements(directives: DirectiveContainer): string[] {
    try {
      const imports: string[] = [];

      // Group imports by path to avoid duplicate imports
      const importsByPath: Map<string, Set<string>> = new Map();

      // Process all field directive fields with import paths
      for (const field of directives.fieldFields) {
        if (field.importPath) {
          // Get or create the set of imports for this path
          const imports = importsByPath.get(field.importPath) || new Set<string>();

          if (field.exportedName) {
            // If an exported name is specified and different from the type
            if (field.exportedName !== field.type) {
              imports.add(`${field.exportedName} as ${field.type}`);
            } else {
              imports.add(field.exportedName);
            }
          } else {
            // Use the type name as the import name
            imports.add(field.type);
          }

          importsByPath.set(field.importPath, imports);
        }
      }

      // Generate import statements
      for (const [importPath, types] of importsByPath.entries()) {
        const typesArray = Array.from(types).sort(); // Convert Set to sorted array
        imports.push(`import { ${typesArray.join(', ')} } from '${importPath}';`);
      }

      return imports;
    } catch (error) {
      console.error(`Error extracting field import statements: ${error}`);
      return [];
    }
  }

  /**
   * Extract import statements from field directive fields (backward compatibility)
   * @param fieldFields Array of field directive fields
   * @returns Array of import statements
   */
  public static extractFieldDirectiveImportStatements(fieldFields: FieldDirectiveField[]): string[] {
    const imports: string[] = [];
    const typesByImportPath: Record<string, Set<string>> = {};

    for (const field of fieldFields) {
      // Handle both importPath and path properties
      const importPath = field.importPath || field.path;

      if (importPath) {
        // Use Set to automatically deduplicate types for each import path
        if (!typesByImportPath[importPath]) {
          typesByImportPath[importPath] = new Set<string>();
        }

        if (field.exportedName && field.exportedName !== field.type) {
          // Named export with different name
          typesByImportPath[importPath].add(`${field.exportedName} as ${field.type}`);
        } else {
          // Named export with same name - Clean up the type name by removing quotes
          const cleanTypeName = field.type.replace(/^["']|["']$/g, '');
          typesByImportPath[importPath].add(cleanTypeName);
        }
      }
    }

    // Generate import statements
    for (const importPath in typesByImportPath) {
      const typesSet = typesByImportPath[importPath];
      const typesArray = Array.from(typesSet).sort(); // Convert Set to sorted array
      // Clean up the import path by removing quotes
      const cleanImportPath = importPath.replace(/^["']|["']$/g, '');
      imports.push(`import { ${typesArray.join(', ')} } from '${cleanImportPath}';`);
    }

    return imports;
  }

  /**
   * Extract all import statements from directives
   * @param directives The directive container
   * @returns Array of import statements
   */
  public static extractImportStatements(directives: DirectiveContainer): string[] {
    try {
      const imports: string[] = [];

      // Extract from regular import directives
      for (const directive of directives.imports) {
        const result = this.processImportDirective(directive);
        if (result) {
          imports.push(result.statement);
        }
      }

      // Extract from field directive fields
      const fieldDirectiveImports = this.extractFieldImportStatements(directives);
      imports.push(...fieldDirectiveImports);

      // Deduplicate and merge import statements from the same module
      return this.deduplicateImportStatements(imports);
    } catch (error) {
      console.error(`Error extracting import statements: ${error}`);
      return [];
    }
  }

  /**
   * Extract transformed import statements from all directives (regular @import and @field)
   * @param directives The directive container
   * @param schemaFilePath Absolute path to the schema file
   * @param targetPath Absolute path to the target file
   * @returns Array of transformed import statements
   */
  public static extractTransformedImportStatements(
    directives: DirectiveContainer,
    schemaFilePath: string,
    targetPath: string
  ): string[] {
    try {
      const allImports: string[] = [];

      // Process regular @import directives
      const importStatements = this.extractImportStatements(directives);
      for (const importStatement of importStatements) {
        const transformedImport = this.transformImportStatement(
          importStatement,
          schemaFilePath,
          targetPath
        );
        allImports.push(transformedImport);
      }

      // Process @field directive imports
      const fieldDirectiveImports = this.extractFieldImportStatements(directives);
      for (const importStatement of fieldDirectiveImports) {
        const transformedImport = this.transformImportStatement(
          importStatement,
          schemaFilePath,
          targetPath
        );
        allImports.push(transformedImport);
      }

      // Deduplicate and merge import statements from the same module
      return this.deduplicateImportStatements(allImports);
    } catch (error) {
      console.error(`Error extracting transformed import statements: ${error}`);
      return [];
    }
  }

  /**
   * Extract and transform import statements from decorator directives using codebase directory as base
   * @param directives The directive container from decorators
   * @param codebaseDir The absolute path to the codebase directory
   * @param targetPath The absolute path to the output file where these imports will be used
   * @returns Array of transformed import statements
   */
  public static extractTransformedDecoratorImportStatements(
    directives: DirectiveContainer,
    codebaseDir: string,
    targetPath: string
  ): string[] {
    try {
      const allImports: string[] = [];

      // Process decorator @import directives
      const importStatements = this.extractImportStatements(directives);
      for (const importStatement of importStatements) {
        const transformedImport = this.transformDecoratorImportStatement(
          importStatement,
          codebaseDir,
          targetPath
        );
        allImports.push(transformedImport);
      }

      // Process decorator @field directive imports
      const fieldDirectiveImports = this.extractFieldImportStatements(directives);
      for (const importStatement of fieldDirectiveImports) {
        const transformedImport = this.transformDecoratorImportStatement(
          importStatement,
          codebaseDir,
          targetPath
        );
        allImports.push(transformedImport);
      }

      // Deduplicate and merge import statements from the same module
      return this.deduplicateImportStatements(allImports);
    } catch (error) {
      console.error(`Error extracting transformed decorator import statements: ${error}`);
      return [];
    }
  }

  /**
   * Deduplicate import statements by merging imports from the same module and removing duplicate types
   * @param imports Array of import statements
   * @returns Array of deduplicated import statements
   */
  private static deduplicateImportStatements(imports: string[]): string[] {
    const importsByPath: Map<string, {
      namedImports: Set<string>,
      defaultImport?: string,
      namespaceImport?: string
    }> = new Map();

    for (const importStatement of imports) {
      // Extract module path first
      const pathMatch = importStatement.match(/from\s+['"]([^'"]+)['"]/);
      if (!pathMatch) {
        // If we can't parse the import statement, keep it as-is
        console.warn(`Could not parse import statement for deduplication: ${importStatement}`);
        const uniqueKey = `__unparsed__${imports.indexOf(importStatement)}`;
        importsByPath.set(uniqueKey, { namedImports: new Set([importStatement]) });
        continue;
      }

      const modulePath = pathMatch[1];

      // Initialize import info for this module if not exists
      if (!importsByPath.has(modulePath)) {
        importsByPath.set(modulePath, { namedImports: new Set() });
      }

      const importInfo = importsByPath.get(modulePath)!;

      // Parse different import types
      // 1. Named imports: import { name1, name2 } from "path"
      const namedMatch = importStatement.match(/import\s*\{\s*([^}]+)\s*\}\s*from/);
      if (namedMatch) {
        const types = namedMatch[1].split(',').map(type => type.trim());
        for (const type of types) {
          if (type.trim()) {
            importInfo.namedImports.add(type.trim());
          }
        }
        continue;
      }

      // 2. Default imports: import defaultName from "path"
      const defaultMatch = importStatement.match(/^import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from/);
      if (defaultMatch) {
        const defaultName = defaultMatch[1];
        if (importInfo.defaultImport && importInfo.defaultImport !== defaultName) {
          console.warn(`Conflicting default imports for ${modulePath}: ${importInfo.defaultImport} vs ${defaultName}`);
        }
        importInfo.defaultImport = defaultName;
        continue;
      }

      // 3. Namespace imports: import * as name from "path"
      const namespaceMatch = importStatement.match(/^import\s+\*\s+as\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from/);
      if (namespaceMatch) {
        const namespaceName = namespaceMatch[1];
        if (importInfo.namespaceImport && importInfo.namespaceImport !== namespaceName) {
          console.warn(`Conflicting namespace imports for ${modulePath}: ${importInfo.namespaceImport} vs ${namespaceName}`);
        }
        importInfo.namespaceImport = namespaceName;
        continue;
      }

      // 4. Mixed imports: import defaultName, { named1, named2 } from "path"
      const mixedMatch = importStatement.match(/^import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*,\s*\{\s*([^}]+)\s*\}\s+from/);
      if (mixedMatch) {
        const [, defaultName, namedImports] = mixedMatch;
        if (importInfo.defaultImport && importInfo.defaultImport !== defaultName) {
          console.warn(`Conflicting default imports for ${modulePath}: ${importInfo.defaultImport} vs ${defaultName}`);
        }
        importInfo.defaultImport = defaultName;

        const types = namedImports.split(',').map(type => type.trim());
        for (const type of types) {
          if (type.trim()) {
            importInfo.namedImports.add(type.trim());
          }
        }
        continue;
      }

      // If we can't parse the specific import type, treat as unparsed
      console.warn(`Could not parse import type for deduplication: ${importStatement}`);
      const uniqueKey = `__unparsed__${imports.indexOf(importStatement)}`;
      importsByPath.set(uniqueKey, { namedImports: new Set([importStatement]) });
    }

    // Reconstruct import statements
    const deduplicatedImports: string[] = [];

    for (const [modulePath, importInfo] of importsByPath.entries()) {
      if (modulePath.startsWith('__unparsed__')) {
        // Add unparsed statements as-is
        deduplicatedImports.push(...Array.from(importInfo.namedImports));
      } else {
        // Reconstruct import statement based on what we have
        const parts: string[] = [];

        if (importInfo.defaultImport) {
          parts.push(importInfo.defaultImport);
        }

        if (importInfo.namespaceImport) {
          parts.push(`* as ${importInfo.namespaceImport}`);
        }

        if (importInfo.namedImports.size > 0) {
          const namedTypes = Array.from(importInfo.namedImports).sort();
          parts.push(`{ ${namedTypes.join(', ')} }`);
        }

        if (parts.length > 0) {
          deduplicatedImports.push(`import ${parts.join(', ')} from '${modulePath}';`);
        }
      }
    }

    return deduplicatedImports;
  }

  /**
   * Extract method call from directive container
   * @param directives The directive container
   * @returns The method call or null if not found
   */
  public static extractMethodCall(directives: DirectiveContainer): string | null {
    // Use the last methodCall directive if multiple exist
    if (directives.methodCalls.length === 0) {
      return null;
    }

    const lastMethodCall = directives.methodCalls[directives.methodCalls.length - 1];
    const methodCallData = this.processMethodCallDirective(lastMethodCall);

    return methodCallData ? methodCallData.fullCall : null;
  }

  /**
   * Extract resolver call from directive container
   * @param directives The directive container
   * @returns The resolver call or null if not found
   */
  public static extractResolverCall(directives: DirectiveContainer): string | null {
    // Check if there are any resolver directives
    if (directives.others.resolver === undefined || directives.others.resolver.length === 0) {
      return null;
    }

    // Use the last resolver directive if multiple exist
    const lastResolverDirective = directives.others.resolver[directives.others.resolver.length - 1];
    const content = lastResolverDirective.content.trim();

    // Need to extract just the function call part, ignoring any additional parameters
    // Format could be: resolveFunction(obj) or resolveFunction(obj), useTypeName: true
    // We need to properly handle nested parentheses and brackets to find the end of the function call
    const functionCallEnd = this.findFunctionCallEnd(content);
    if (functionCallEnd > 0 && functionCallEnd < content.length) {
      // There are additional parameters after the resolver function
      return content.substring(0, functionCallEnd).trim();
    }

    return content;
  }

  /**
   * Find the end of a function call by properly matching parentheses and brackets
   * @param content The content to parse
   * @returns The index where the function call ends, or -1 if not found
   */
  private static findFunctionCallEnd(content: string): number {
    let depth = 0;
    let inString = false;
    let stringChar = '';
    let i = 0;

    // Find the first opening parenthesis
    while (i < content.length && content[i] !== '(') {
      i++;
    }

    if (i >= content.length) {
      // No opening parenthesis found, return -1 to indicate no function call
      return -1;
    }

    // Start from the opening parenthesis
    for (; i < content.length; i++) {
      const char = content[i];

      // Handle string literals
      if (!inString && (char === '"' || char === "'" || char === '`')) {
        inString = true;
        stringChar = char;
        continue;
      }

      if (inString) {
        if (char === stringChar && content[i - 1] !== '\\') {
          inString = false;
          stringChar = '';
        }
        continue;
      }

      // Handle parentheses and brackets
      if (char === '(' || char === '[' || char === '{') {
        depth++;
      } else if (char === ')' || char === ']' || char === '}') {
        depth--;

        // If we've closed all parentheses/brackets, we've found the end of the function call
        if (depth === 0) {
          return i + 1;
        }
      }
    }

    // If we reach here, the parentheses/brackets weren't properly closed
    return -1;
  }

  /**
   * Extract useTypeName flag from resolver directive
   * @param directives The directive container
   * @returns Whether to use __typename check (defaults to false)
   */
  public static extractUseTypeName(directives: DirectiveContainer): boolean {
    // Check if there are any resolver directives
    if (directives.others.resolver === undefined || directives.others.resolver.length === 0) {
      return false;
    }

    // Use the last resolver directive if multiple exist
    const lastResolverDirective = directives.others.resolver[directives.others.resolver.length - 1];
    const content = lastResolverDirective.content.trim();

    // Check if the directive includes the useTypeName flag
    // Format could be: resolver(resolveFunction, useTypeName: true)
    // or resolver(resolveFunction, { useTypeName: true })
    return content.includes('useTypeName: true') || content.includes('"useTypeName": true') ||
      content.includes("'useTypeName': true");
  }

  /**
   * Extract context information from directives
   * @param directives The directive container
   * @returns The extracted context data, or null if not found
   */
  public static extractContextInfo(directives: DirectiveContainer): ContextData | null {
    // Check if there's a context directive
    if (directives.others.context !== undefined && directives.others.context.length > 0) {
      const contextDirective = directives.others.context[0];
      return this.processContextDirective(contextDirective);
    }

    return null;
  }

  /**
   * Extract schema-level alias path from directives
   * @param directives The directive container
   * @returns The extracted schema-level alias path, or null if not found
   */
  public static extractSchemaAliasPath(directives: DirectiveContainer): string | null {
    // Check if there's an alias directive at the schema level
    if (directives.others.alias !== undefined && directives.others.alias.length > 0) {
      // Use the last alias directive if multiple exist (giving priority to the last one)
      const aliasDirective = directives.others.alias[directives.others.alias.length - 1];
      console.log(`Processing schema-level alias directive: ${JSON.stringify(aliasDirective)}`);
      const aliasData = this.processAliasDirective(aliasDirective);

      if (aliasData) {
        console.log(`Found schema-level alias directive with path: ${aliasData.path}`);
        return aliasData.path;
      }
    }

    return null;
  }

  /**
   * Extract alias path information from directives
   * @param directives The directive container
   * @returns The extracted alias path, or null if not found
   */
  public static extractAliasPath(directives: DirectiveContainer): string | null {
    // Check if there's an alias directive
    if (directives.others.alias !== undefined && directives.others.alias.length > 0) {
      // Use the last alias directive if multiple exist (giving priority to the last one)
      const aliasDirective = directives.others.alias[directives.others.alias.length - 1];
      console.log(`Processing alias directive: ${JSON.stringify(aliasDirective)}`);
      const aliasData = this.processAliasDirective(aliasDirective);

      if (aliasData) {
        console.log(`Found alias directive with path: ${aliasData.path}`);
        return aliasData.path;
      }
    } else {
      console.log(`No alias directives found in the directive container`);
      console.log(`Available directive types: ${Object.keys(directives.others).join(', ') || 'none'}`);
    }

    return null;
  }
}