import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import { FieldDirectiveProcessor } from './field-directive-processor';
import { isScalarTypeName } from '../utils/type-converters';
import { WatchedFileWriter } from './watched-file-writer';

/**
 * Filter schema content to remove @field fields from the final schema.gql
 * @field fields should only exist in TypeScript types, not in the GraphQL schema
 * @param schemaContent The schema content to filter
 * @param schemaSourcePath Path to the source schema files for context
 * @returns Filtered schema content without @field fields
 */
export function filterSchemaContent(schemaContent: string, schemaSourcePath: string): string {
  try {
    if (!schemaSourcePath) {
      return schemaContent;
    }

    console.log(`Filtering @field fields from schema using source: ${schemaSourcePath}`);

    const lines = schemaContent.split('\n');
    // For now, we'll do a simple filtering that removes @field directive comments
    // The actual @field fields are handled during post-processing of TypeScript types,
    // since @field fields are added during post-processing, not during schema merging
    const filteredLines = lines.filter(line => {
      const trimmedLine = line.trim();

      // Skip @field directive comments
      if (trimmedLine.startsWith('# @field(')) {
        if (process.env.DEBUG) {
          console.log(`Removing @field directive comment: ${trimmedLine}`);
        }
        return false;
      }

      return true;
      // Note: We don't filter actual field definitions here because
      // @field fields are added during TypeScript post-processing, not in the schema
    });

    return filteredLines.join('\n');
  } catch (error) {
    console.error('Error filtering schema content:', error);
    return schemaContent;
  }
}

/**
 * Filter schema content to remove @field fields from the final schema.gql
 * @field fields should only exist in TypeScript types, not in the GraphQL schema
 */
export class SchemaFilter {
  /**
   * Filter a schema file to remove @field fields and transform malformed unions
   * @param schemaContent The original schema content
   * @param schemaSourcePath Path to the schema file or directory for directive extraction
   * @param debug Enable debug logging
   * @returns Filtered schema content without @field fields and with transformed unions
   */
  public static async filterFieldDirectiveFields(
    schemaContent: string,
    schemaSourcePath: string,
    debug: boolean = false
  ): Promise<string> {
    if (debug) {
      console.log(
        `Filtering @field fields and transforming malformed unions from schema using source: ${schemaSourcePath}`
      );
    }

    // First, filter @field directive comments
    const lines = schemaContent.split('\n');
    const filteredLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Skip @field directive comments
      if (trimmedLine.startsWith('# @field(')) {
        if (debug) {
          console.log(`Removing @field directive comment: ${trimmedLine}`);
        }
        continue;
      }

      // Keep all other lines (including fields)
      // @field fields are added during TypeScript post-processing, not in the schema
      filteredLines.push(line);
    }

    let filteredContent = filteredLines.join('\n');

    // Transform malformed unions with scalar types
    filteredContent = await this.transformMalformedUnions(filteredContent, schemaSourcePath, debug);

    if (debug) {
      console.log(
        `Schema filtering completed. Original lines: ${lines.length}, Filtered lines: ${filteredLines.length}`
      );
    }

    return filteredContent;
  }

  /**
   * Transform malformed unions that contain scalar types into valid GraphQL unions
   * @param schemaContent The schema content to transform
   * @param schemaSourcePath Path to the schema source for context
   * @param debug Enable debug logging
   * @returns Transformed schema content with valid unions
   */
  public static async transformMalformedUnions(
    schemaContent: string,
    schemaSourcePath: string,
    debug: boolean = false
  ): Promise<string> {
    if (debug) {
      console.log('Transforming malformed unions with scalar types...');
    }

    const lines = schemaContent.split('\n');
    const transformedLines: string[] = [];
    const generatedWrapperTypes: string[] = [];
    const processedWrapperTypes = new Set<string>();

    // Regular expression to match union definitions
    const unionRegex = /^(\s*union\s+)(\w+)(\s*=\s*)(.+)$/;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Check if this line defines a union
      const unionMatch = trimmedLine.match(unionRegex);
      if (unionMatch) {
        const [, unionPrefix, unionName, equalsPart, unionTypes] = unionMatch;

        if (debug) {
          console.log(`Found union definition: ${unionName} = ${unionTypes}`);
        }

        // Parse union member types
        const memberTypes = unionTypes.split('|').map(type => type.trim());
        const transformedTypes: string[] = [];

        // Check each member type
        for (const memberType of memberTypes) {
          if (this.isScalarType(memberType, schemaSourcePath)) {
            // This is a scalar type - needs to be wrapped
            const wrapperTypeName = `${memberType}Value`;
            transformedTypes.push(wrapperTypeName);

            // Generate wrapper type if not already processed
            if (!processedWrapperTypes.has(wrapperTypeName)) {
              const wrapperType = `type ${wrapperTypeName} {\n  value: ${memberType}!\n}`;
              generatedWrapperTypes.push(wrapperType);
              processedWrapperTypes.add(wrapperTypeName);

              if (debug) {
                console.log(`Generated wrapper type for scalar ${memberType}: ${wrapperTypeName}`);
              }
            }
          } else {
            // Keep non-scalar types as-is
            transformedTypes.push(memberType);
          }
        }

        // Reconstruct the union definition with transformed types
        const transformedUnion = `${unionPrefix}${unionName}${equalsPart}${transformedTypes.join(' | ')}`;
        transformedLines.push(transformedUnion);

        if (debug) {
          console.log(`Transformed union ${unionName}: ${transformedTypes.join(' | ')}`);
        }
      } else {
        // Keep non-union lines as-is
        transformedLines.push(line);
      }
    }

    // Combine generated wrapper types with transformed schema
    let result = transformedLines.join('\n');

    if (generatedWrapperTypes.length > 0) {
      // Add wrapper types at the beginning of the schema
      const wrapperTypesSection = `# Generated wrapper types for scalar values in unions\n${generatedWrapperTypes.join('\n\n')}\n\n`;
      result = wrapperTypesSection + result;

      if (debug) {
        console.log(`Added ${generatedWrapperTypes.length} wrapper types to schema`);
      }
    }

    return result;
  }

  /**
   * Check if a type name is a scalar type
   * @param typeName The type name to check
   * @param schemaSourcePath Path to schema source for context
   * @returns True if the type is a scalar type
   */
  private static isScalarType(typeName: string, schemaSourcePath: string): boolean {
    // Extract schema root directory for scalar type detection
    let schemaRoot: string;
    try {
      if (fs.statSync(schemaSourcePath).isFile()) {
        schemaRoot = path.dirname(schemaSourcePath);
      } else {
        schemaRoot = schemaSourcePath;
      }
    } catch {
      // If path doesn't exist, use current directory
      schemaRoot = process.cwd();
    }

    return isScalarTypeName(typeName, schemaRoot);
  }

  /**
   * Check if a field has a @field override (field-level @field directive)
   */
  private static async hasFieldDirectiveOverride(
    typeName: string,
    fieldName: string,
    schemaSourcePath: string,
    debug: boolean = false
  ): Promise<boolean> {
    try {
      // If schemaSourcePath is a directory, we need to find the specific file containing this type
      const actualSchemaFile = await this.findSchemaFileForType(typeName, schemaSourcePath, debug);
      if (!actualSchemaFile) {
        if (debug) {
          console.warn(`Could not find schema file containing type ${typeName}`);
        }
        return false;
      }

      const fieldOverride = await FieldDirectiveProcessor.getFieldOverride(
        typeName,
        fieldName,
        actualSchemaFile,
        debug
      );
      return fieldOverride !== null;
    } catch (error) {
      if (debug) {
        console.error(`Error checking field @field override for ${typeName}.${fieldName}:`, error);
      }
      return false;
    }
  }

  /**
   * Check if a field is a @field-only field (added by type-level @field, not in original schema)
   */
  private static async isFieldDirectiveOnlyField(
    typeName: string,
    fieldName: string,
    schemaSourcePath: string,
    debug: boolean = false
  ): Promise<boolean> {
    try {
      // If schemaSourcePath is a directory, we need to find the specific file containing this type
      const actualSchemaFile = await this.findSchemaFileForType(typeName, schemaSourcePath, debug);
      if (!actualSchemaFile) {
        if (debug) {
          console.warn(`Could not find schema file containing type ${typeName}`);
        }
        return false;
      }

      // Get all @field fields for this type
      const fieldDirectiveFields = await FieldDirectiveProcessor.extractFieldDirectiveFields(
        { fieldFields: [] } // This needs to be updated based on actual implementation
      );

      // Check if this field name matches any @field field
      return fieldDirectiveFields.some(fieldField => fieldField.name === fieldName);
    } catch (error) {
      if (debug) {
        console.error(
          `Error checking if field is @field-only for ${typeName}.${fieldName}:`,
          error
        );
      }
      return false;
    }
  }

  /**
   * Find the schema file that contains a specific type definition
   */
  private static async findSchemaFileForType(
    typeName: string,
    schemaSourcePath: string,
    debug: boolean = false
  ): Promise<string | null> {
    try {
      // If it's a single file, return it directly
      if (fs.statSync(schemaSourcePath).isFile()) {
        return schemaSourcePath;
      }

      // If it's a directory, search for the type in all schema files
      const schemaFiles = glob.sync(path.join(schemaSourcePath, '**', '*.{gql,graphql}'), {
        windowsPathsNoEscape: true,
      });

      for (const schemaFile of schemaFiles) {
        try {
          const content = fs.readFileSync(schemaFile, 'utf8');
          // Look for type definition
          if (content.includes(`type ${typeName}`)) {
            if (debug) {
              console.log(`Found type ${typeName} in ${schemaFile}`);
            }
            return schemaFile;
          }
        } catch (error) {
          if (debug) {
            console.warn(`Error reading schema file ${schemaFile}:`, error);
          }
          continue;
        }
      }

      return null;
    } catch (error) {
      if (debug) {
        console.error(`Error finding schema file for type ${typeName}:`, error);
      }
      return null;
    }
  }

  /**
   * Filter multiple schema files and write filtered versions
   * @param schemaFiles Array of schema file paths
   * @param outputDir Directory to write filtered schema files
   * @param debug Enable debug logging
   */
  public static async filterSchemaFiles(
    schemaFiles: string[],
    outputDir: string,
    debug: boolean = false
  ): Promise<void> {
    if (debug) {
      console.log(`Filtering ${schemaFiles.length} schema files...`);
    }

    // Ensure output directory exists
    fs.ensureDirSync(outputDir);

    for (const schemaFile of schemaFiles) {
      try {
        // Read original schema content
        const originalContent = fs.readFileSync(schemaFile, 'utf8');

        // Filter @field fields
        const filteredContent = await this.filterFieldDirectiveFields(
          originalContent,
          schemaFile,
          debug
        );

        // Write filtered content to output directory
        const relativePath = path.relative(path.dirname(schemaFile), schemaFile);
        const outputPath = path.join(outputDir, relativePath);

        // Ensure output subdirectory exists
        fs.ensureDirSync(path.dirname(outputPath));

        // Write filtered schema
        WatchedFileWriter.writeFileSync(outputPath, filteredContent);

        if (debug) {
          console.log(`Filtered schema written to: ${outputPath}`);
        }
      } catch (error) {
        console.error(`Error filtering schema file ${schemaFile}:`, error);
      }
    }
  }
}
