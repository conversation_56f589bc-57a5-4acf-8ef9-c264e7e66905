/**
 * TypeMapBuilder - Centralized service for building complete type maps
 * 
 * This service implements the first phase of the two-phase processing architecture.
 * It scans all TypeScript files in parallel to build a complete type map containing
 * all decorators, imports, method calls, and field information without generating code.
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import * as crypto from 'crypto';
import { promisify } from 'util';
import { Worker } from 'worker_threads';
import * as os from 'os';

import type {
  TypeMap,
  TypeMapConfig,
  TypeMapBuildResult,
  TypeMapMetrics,
  ParsedFileInfo,
  MethodCallInfo,
  ImportInfo,
  FieldInfo,
  ContextInfo
} from './type-map';
import { DecoratorParser } from './decorator-parser';
import { MemoryPressureMonitor } from './memory-pressure-monitor';
import type { DecoratorContainer } from './decorator-parser';

// Use glob.glob directly as it returns a Promise in newer versions

/**
 * TypeMapBuilder - Builds complete type maps from TypeScript files
 */
export class TypeMapBuilder {
  private config: TypeMapConfig;
  private memoryMonitor: MemoryPressureMonitor;
  private decoratorParser: DecoratorParser;
  private workers: Worker[] = [];
  private isShuttingDown = false;

  constructor(config: TypeMapConfig) {
    this.config = {
      ...config,
      maxWorkers: config.maxWorkers || Math.max(1, os.cpus().length - 1),
      enableCaching: config.enableCaching ?? true,
      cacheDirectory: config.cacheDirectory || path.join(process.cwd(), '.gql-generator-cache'),
      memoryPressureThreshold: config.memoryPressureThreshold || 0.8,
      includePatterns: config.includePatterns || ['**/*.ts', '**/*.tsx'],
      excludePatterns: config.excludePatterns || ['**/node_modules/**', '**/dist/**', '**/*.d.ts']
    };

    this.memoryMonitor = new MemoryPressureMonitor({
      warningThreshold: this.config.memoryPressureThreshold! * 0.8,
      criticalThreshold: this.config.memoryPressureThreshold!,
      monitoringInterval: 1000,
      enableGCTrigger: true,
      enableLogging: this.config.debug || false,
      enableMemoryMappedTracking: false,
      memoryMappedThreshold: 0.8,
      enableAutomaticFallback: true,
      enablePredictiveManagement: false,
      adaptiveThresholds: false
    });

    this.decoratorParser = new DecoratorParser(
      this.config.cacheDirectory,
      true // Enable parallel processing
    );
  }

  /**
   * Build a complete type map from TypeScript files
   */
  async buildTypeMap(): Promise<TypeMapBuildResult> {
    const startTime = Date.now();
    const warnings: string[] = [];
    const errors: string[] = [];

    try {
      if (this.config.debug) {
        console.log('🚀 Starting type map building...');
        console.log(`📁 Base directory: ${this.config.baseDirectory}`);
        console.log(`👥 Max workers: ${this.config.maxWorkers}`);
      }

      // Start memory monitoring
      await this.memoryMonitor.start();

      // Check cache first
      if (this.config.enableCaching) {
        const cachedMap = await this.loadFromCache();
        if (cachedMap) {
          if (this.config.debug) {
            console.log('✅ Loaded type map from cache');
          }
          return {
            typeMap: cachedMap,
            fromCache: true,
            buildDuration: Date.now() - startTime,
            warnings,
            errors
          };
        }
      }

      // Find all TypeScript files
      const files = await this.findTypeScriptFiles();
      
      if (this.config.debug) {
        console.log(`📄 Found ${files.length} TypeScript files`);
      }

      if (files.length === 0) {
        warnings.push('No TypeScript files found to process');
      }

      // Build type map
      const typeMap = await this.buildTypeMapFromFiles(files);

      // Save to cache
      if (this.config.enableCaching) {
        await this.saveToCache(typeMap);
      }

      const buildDuration = Date.now() - startTime;

      if (this.config.debug) {
        console.log(`✅ Type map built in ${buildDuration}ms`);
        this.logMetrics(typeMap.metrics);
      }

      return {
        typeMap,
        fromCache: false,
        buildDuration,
        warnings,
        errors
      };

    } catch (error) {
      errors.push(`Type map building failed: ${error}`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Find all TypeScript files matching the configuration
   */
  private async findTypeScriptFiles(): Promise<string[]> {
    const allFiles: string[] = [];

    // Process include patterns
    for (const pattern of this.config.includePatterns) {
      const fullPattern = path.join(this.config.baseDirectory, pattern);
      const files = await glob.glob(fullPattern, {
        ignore: this.config.excludePatterns.map(p => path.join(this.config.baseDirectory, p)),
        absolute: true
      }) as string[];
      allFiles.push(...files);
    }

    // Remove duplicates and ensure files exist
    const uniqueFiles = [...new Set(allFiles)];
    const existingFiles: string[] = [];

    for (const file of uniqueFiles) {
      try {
        const stats = await fs.stat(file);
        if (stats.isFile()) {
          existingFiles.push(file);
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn(`File not accessible: ${file}`);
        }
      }
    }

    return existingFiles;
  }

  /**
   * Build type map from a list of files
   */
  private async buildTypeMapFromFiles(files: string[]): Promise<TypeMap> {
    const typeMap: TypeMap = {
      version: '1.0.0',
      generatedAt: Date.now(),
      schemaId: this.config.schemaId,
      baseDirectory: this.config.baseDirectory,
      files: new Map(),
      methodCalls: new Map(),
      imports: new Map(),
      fields: new Map(),
      contexts: [],
      metrics: {
        totalFiles: files.length,
        successfullyParsed: 0,
        failedToParse: 0,
        totalParseTime: 0,
        averageParseTime: 0,
        peakMemoryUsage: 0,
        parserStats: {
          oxc: { count: 0, totalTime: 0 },
          swc: { count: 0, totalTime: 0 },
          typescript: { count: 0, totalTime: 0 }
        }
      }
    };

    // Only scan for decorators if explicitly enabled
    if (this.config.enableDecorators) {
      if (this.config.debug) {
        console.log('🔍 Scanning TypeScript files for decorators and type information...');
      }

      const decoratorContainer = await this.decoratorParser.scanCodebase(this.config.baseDirectory);

      // Update metrics from decorator parser
      typeMap.metrics.successfullyParsed = files.length;
      typeMap.metrics.totalParseTime = Date.now() - Date.now(); // Will be updated later

      // Convert decorator container to type map format
      this.convertDecoratorContainerToTypeMap(decoratorContainer, typeMap);
    } else {
      if (this.config.debug) {
        console.log('📁 Codebase directory provided but decorators not enabled - skipping TypeScript scanning');
      }

      // Just update basic metrics without scanning
      typeMap.metrics.successfullyParsed = files.length;
      typeMap.metrics.totalParseTime = 0;
    }

    // Calculate metrics
    this.calculateMetrics(typeMap);

    return typeMap;
  }

  /**
   * Merge parallel scan results into the main type map
   */
  private mergeParallelScanResults(scanTypeMap: Partial<TypeMap>, targetTypeMap: TypeMap): void {
    // Merge files
    if (scanTypeMap.files) {
      for (const [filePath, fileInfo] of scanTypeMap.files) {
        targetTypeMap.files.set(filePath, fileInfo);
      }
    }

    // Merge method calls
    if (scanTypeMap.methodCalls) {
      for (const [typeName, typeMethodCalls] of scanTypeMap.methodCalls) {
        if (!targetTypeMap.methodCalls.has(typeName)) {
          targetTypeMap.methodCalls.set(typeName, new Map());
        }
        const targetTypeMethodCalls = targetTypeMap.methodCalls.get(typeName)!;
        for (const [fieldName, methodCalls] of typeMethodCalls) {
          targetTypeMethodCalls.set(fieldName, methodCalls);
        }
      }
    }

    // Merge imports
    if (scanTypeMap.imports) {
      for (const [filePath, imports] of scanTypeMap.imports) {
        targetTypeMap.imports.set(filePath, imports);
      }
    }

    // Merge fields
    if (scanTypeMap.fields) {
      for (const [typeName, fields] of scanTypeMap.fields) {
        targetTypeMap.fields.set(typeName, fields);
      }
    }

    // Merge contexts
    if (scanTypeMap.contexts) {
      targetTypeMap.contexts.push(...scanTypeMap.contexts);
    }
  }

  /**
   * Convert decorator container to type map format (legacy fallback)
   */
  private convertDecoratorContainerToTypeMap(
    decoratorContainer: DecoratorContainer,
    typeMap: TypeMap
  ): void {
    // Process method calls
    for (const methodCallData of decoratorContainer.methodCalls || []) {
      const methodCallInfo = this.convertToMethodCallInfo(methodCallData);

      if (!typeMap.methodCalls.has(methodCallInfo.typeName)) {
        typeMap.methodCalls.set(methodCallInfo.typeName, new Map());
      }

      const typeMethodCalls = typeMap.methodCalls.get(methodCallInfo.typeName)!;
      if (!typeMethodCalls.has(methodCallInfo.fieldName)) {
        typeMethodCalls.set(methodCallInfo.fieldName, []);
      }

      typeMethodCalls.get(methodCallInfo.fieldName)!.push(methodCallInfo);
    }

    // Process imports
    for (const importData of decoratorContainer.imports || []) {
      const importInfo = this.convertToImportInfo(importData);
      const filePath = importInfo.sourceFile.filePath;

      if (!typeMap.imports.has(filePath)) {
        typeMap.imports.set(filePath, []);
      }
      typeMap.imports.get(filePath)!.push(importInfo);
    }

    // Process fields
    for (const fieldData of decoratorContainer.fields || []) {
      const fieldInfo = this.convertToFieldInfo(fieldData);

      if (!typeMap.fields.has(fieldInfo.typeName)) {
        typeMap.fields.set(fieldInfo.typeName, []);
      }
      typeMap.fields.get(fieldInfo.typeName)!.push(fieldInfo);
    }

    // Process contexts
    for (const contextData of decoratorContainer.contexts || []) {
      typeMap.contexts.push(this.convertToContextInfo(contextData));
    }
  }

  /**
   * Convert to MethodCallInfo format
   */
  private convertToMethodCallInfo(methodCallData: any): MethodCallInfo {
    // Extract type and field information from the decorator data
    const decorator = methodCallData.decorator;
    const data = methodCallData.data;

    return {
      typeName: data?.typeName || this.extractTypeFromTarget(decorator?.target) || 'Unknown',
      fieldName: data?.fieldName || this.extractFieldFromTarget(decorator?.target) || 'unknown',
      call: data?.call || '',
      importPath: data?.importPath,
      schemaId: data?.schema,
      sourceFile: this.createFileInfo(decorator?.filePath || ''),
      lineNumber: decorator?.lineNumber || 0,
      async: data?.async,
      enableTypeCasting: data?.enableTypeCasting
    };
  }

  /**
   * Convert to ImportInfo format
   */
  private convertToImportInfo(importData: any): ImportInfo {
    const decorator = importData.decorator;
    const data = importData.data;

    return {
      statement: data?.statement || '',
      module: data?.module || '',
      namedImports: data?.namedImports || [],
      defaultImport: data?.defaultImport,
      namespaceImport: data?.namespaceImport,
      sourceFile: this.createFileInfo(decorator?.filePath || ''),
      lineNumber: decorator?.lineNumber || 0
    };
  }

  /**
   * Convert to FieldInfo format
   */
  private convertToFieldInfo(fieldData: any): FieldInfo {
    const decorator = fieldData.decorator;
    const data = fieldData.data;

    return {
      typeName: data?.typeName || this.extractTypeFromTarget(decorator?.target) || 'Unknown',
      fieldName: data?.fieldName || this.extractFieldFromTarget(decorator?.target) || 'unknown',
      fieldType: data?.type || 'unknown',
      importPath: data?.importPath,
      optional: data?.optional,
      schemaId: data?.schema,
      sourceFile: this.createFileInfo(decorator?.filePath || ''),
      lineNumber: decorator?.lineNumber || 0
    };
  }

  /**
   * Convert to ContextInfo format
   */
  private convertToContextInfo(contextData: any): ContextInfo {
    const decorator = contextData.decorator;
    const data = contextData.data;

    return {
      contextType: data?.name || 'unknown',
      importPath: data?.path,
      sourceFile: this.createFileInfo(decorator?.filePath || ''),
      lineNumber: decorator?.lineNumber || 0
    };
  }

  /**
   * Extract type name from decorator target
   */
  private extractTypeFromTarget(target: string | undefined): string | undefined {
    if (!target) return undefined;

    // Simple extraction - look for class or interface declarations
    const classMatch = target.match(/(?:class|interface)\s+(\w+)/);
    if (classMatch) return classMatch[1];

    // Look for type annotations
    const typeMatch = target.match(/:\s*(\w+)/);
    if (typeMatch) return typeMatch[1];

    return undefined;
  }

  /**
   * Extract field name from decorator target
   */
  private extractFieldFromTarget(target: string | undefined): string | undefined {
    if (!target) return undefined;

    // Look for property or method declarations
    const propertyMatch = target.match(/(?:get|set)?\s*(\w+)\s*[(:]/);
    if (propertyMatch) return propertyMatch[1];

    return undefined;
  }

  /**
   * Create file info from file path
   */
  private createFileInfo(filePath: string): ParsedFileInfo {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf8');
      const contentHash = crypto.createHash('sha256').update(content).digest('hex');
      
      return {
        filePath,
        size: stats.size,
        lastModified: stats.mtime.getTime(),
        contentHash,
        parsedAt: Date.now(),
        parser: 'decorator-parser',
        parseDuration: 0 // Will be updated by actual parsing
      };
    } catch (error) {
      return {
        filePath,
        size: 0,
        lastModified: 0,
        contentHash: '',
        parsedAt: Date.now(),
        parser: 'error',
        parseDuration: 0
      };
    }
  }

  /**
   * Calculate performance metrics
   */
  private calculateMetrics(typeMap: TypeMap): void {
    const metrics = typeMap.metrics;
    
    // Calculate averages
    if (metrics.totalFiles > 0) {
      metrics.averageParseTime = metrics.totalParseTime / metrics.totalFiles;
    }

    // Get current memory usage as peak
    const memUsage = process.memoryUsage();
    metrics.peakMemoryUsage = memUsage.heapUsed;
  }

  /**
   * Load type map from cache
   */
  private async loadFromCache(): Promise<TypeMap | null> {
    try {
      const cacheFile = this.getCacheFilePath();
      if (await fs.pathExists(cacheFile)) {
        const cached = await fs.readJson(cacheFile);
        // TODO: Add cache validation and invalidation logic
        return cached;
      }
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to load from cache:', error);
      }
    }
    return null;
  }

  /**
   * Save type map to cache
   */
  private async saveToCache(typeMap: TypeMap): Promise<void> {
    try {
      await fs.ensureDir(this.config.cacheDirectory!);
      const cacheFile = this.getCacheFilePath();
      await fs.writeJson(cacheFile, typeMap, { spaces: 2 });
    } catch (error) {
      if (this.config.debug) {
        console.warn('Failed to save to cache:', error);
      }
    }
  }

  /**
   * Get cache file path
   */
  private getCacheFilePath(): string {
    const schemaId = this.config.schemaId || 'default';
    return path.join(this.config.cacheDirectory!, `type-map-${schemaId}.json`);
  }

  /**
   * Log performance metrics
   */
  private logMetrics(metrics: TypeMapMetrics): void {
    console.log('📊 Type Map Building Metrics:');
    console.log(`   Total files: ${metrics.totalFiles}`);
    console.log(`   Successfully parsed: ${metrics.successfullyParsed}`);
    console.log(`   Failed to parse: ${metrics.failedToParse}`);
    console.log(`   Total parse time: ${metrics.totalParseTime}ms`);
    console.log(`   Average parse time: ${Math.round(metrics.averageParseTime)}ms per file`);
    console.log(`   Peak memory usage: ${Math.round(metrics.peakMemoryUsage / 1024 / 1024)}MB`);
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop memory monitoring
    await this.memoryMonitor.stop();
    
    // Cleanup workers
    for (const worker of this.workers) {
      await worker.terminate();
    }
    this.workers = [];
  }
}

/**
 * Global type map builder instance
 */
let globalTypeMapBuilder: TypeMapBuilder | null = null;

/**
 * Get or create global type map builder
 */
export function getGlobalTypeMapBuilder(config?: TypeMapConfig): TypeMapBuilder {
  if (!globalTypeMapBuilder && config) {
    globalTypeMapBuilder = new TypeMapBuilder(config);
  }
  if (!globalTypeMapBuilder) {
    throw new Error('TypeMapBuilder not initialized. Provide config on first call.');
  }
  return globalTypeMapBuilder;
}

/**
 * Reset global type map builder (for testing)
 */
export function resetGlobalTypeMapBuilder(): void {
  globalTypeMapBuilder = null;
}
