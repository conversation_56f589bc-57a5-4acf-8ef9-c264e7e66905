import * as fs from 'fs-extra';
import * as path from 'path';
import { parseSync as swcParseSync } from '@swc/core';
import { UI } from './ui';
import { SafeFileModifier, type FileModificationResult } from './safe-file-modifier';
import type { DirectiveMethodMapping } from './migration-service';
import { AliasConfigUtils, type AliasConfig } from './alias-config';

/**
 * Options for decorator injection
 */
export interface DecoratorInjectionOptions {
  /** Enable verbose logging */
  verbose?: boolean;
  /** Schema identifier for multi-schema support */
  schemaId?: string;
  /** Prevent duplicate decorators */
  preventDuplicates?: boolean;
  /** Alias configuration for import path handling */
  aliasConfig?: AliasConfig;
  /** Create backups before modifying files */
  createBackups?: boolean;
}

/**
 * Result of decorator injection operation
 */
export interface DecoratorInjectionResult {
  /** Whether the injection was successful */
  success: boolean;
  /** File path that was modified */
  filePath: string;
  /** Number of decorators added */
  decoratorsAdded: number;
  /** Number of decorators skipped (duplicates) */
  decoratorsSkipped: number;
  /** Error message if injection failed */
  error?: string;
  /** List of decorators that were added */
  addedDecorators: string[];
}

/**
 * Information about an existing decorator
 */
interface ExistingDecorator {
  /** Decorator name */
  name: string;
  /** Line number */
  lineNumber: number;
  /** Full decorator text */
  text: string;
  /** Target function/method name */
  targetName: string;
}

/**
 * TypeScript decorator injector for adding GQL decorators to functions
 */
export class DecoratorInjector {
  private options: DecoratorInjectionOptions;
  private fileModifier: SafeFileModifier;

  constructor(options: DecoratorInjectionOptions = {}) {
    this.options = {
      verbose: false,
      preventDuplicates: true,
      createBackups: false,
      ...options
    };

    this.fileModifier = new SafeFileModifier({
      createBackup: this.options.createBackups,
      verbose: this.options.verbose,
      validateAfterModification: true
    });
  }

  /**
   * Inject decorators into a TypeScript file based on directive mappings
   * @param filePath Path to the TypeScript file
   * @param mappings Array of directive mappings to convert to decorators
   * @returns Result of the injection operation
   */
  public async injectDecorators(
    filePath: string,
    mappings: DirectiveMethodMapping[]
  ): Promise<DecoratorInjectionResult> {
    try {
      if (this.options.verbose) {
        UI.info(`🎯 Injecting decorators into ${path.relative(process.cwd(), filePath)}`);
      }

      // Debug: Check all mappings for this file before filtering
      const allMappingsForFile = mappings.filter(m => m.typescriptFilePath === filePath);
      console.log(`[DEBUG] All mappings for file ${path.relative(process.cwd(), filePath)}:`);
      for (const mapping of allMappingsForFile) {
        console.log(`[DEBUG]   - ${mapping.directiveType}: ${mapping.directiveContent} (resolved: ${mapping.resolved}, methodName: ${mapping.methodName}, hasResolvedMethod: ${!!mapping.resolvedMethod})`);
      }

      // Filter mappings for this file that are properly resolved
      // Note: @field and @import directives don't need resolvedMethod data
      const fileMappings = mappings.filter(m =>
        m.typescriptFilePath === filePath &&
        m.resolved &&
        (m.directiveType === 'methodCall' ? (m.resolvedMethod && m.methodName) : true)
      );

      console.log(`[DEBUG] After filtering: ${fileMappings.length} mappings for file ${path.relative(process.cwd(), filePath)}:`);
      for (const mapping of fileMappings) {
        console.log(`[DEBUG]   - ${mapping.directiveType}: ${mapping.directiveContent} (methodName: ${mapping.methodName})`);
      }

      if (this.options.verbose) {
        const totalMappings = mappings.filter(m => m.typescriptFilePath === filePath).length;
        const resolvedMappings = mappings.filter(m => m.typescriptFilePath === filePath && m.resolved).length;
        UI.info(`   Found ${totalMappings} total mappings, ${resolvedMappings} resolved, ${fileMappings.length} ready for injection`);
      }

      if (fileMappings.length === 0) {
        const totalForFile = mappings.filter(m => m.typescriptFilePath === filePath).length;
        if (this.options.verbose && totalForFile > 0) {
          UI.warning(`⚠️ Found ${totalForFile} mappings for ${filePath} but none are properly resolved`);
        }
        return {
          success: true,
          filePath,
          decoratorsAdded: 0,
          decoratorsSkipped: 0,
          addedDecorators: []
        };
      }

      // Read file content
      const content = await fs.readFile(filePath, 'utf8');

      // Find existing decorators in the file to prevent duplicates
      const existingDecorators = this.options.preventDuplicates
        ? await this.findExistingDecorators(content)
        : [];

      if (this.options.verbose && existingDecorators.length > 0) {
        UI.info(`   Found ${existingDecorators.length} existing decorators in ${path.relative(process.cwd(), filePath)}`);
        existingDecorators.forEach(decorator => {
          UI.info(`     - ${decorator.name} for ${decorator.targetName} at line ${decorator.lineNumber}`);
        });
      }

      // Generate decorators for each mapping
      const decoratorsToAdd: Array<{
        decorator: string;
        targetFunction: string;
        lineNumber: number;
        mapping: DirectiveMethodMapping;
      }> = [];

      const skippedMappings: { mapping: DirectiveMethodMapping; reason: string }[] = [];

      for (const mapping of fileMappings) {
        try {
          // Validate mapping has required data based on directive type
          if (this.options.verbose) {
            UI.info(`🔍 Processing mapping: ${mapping.directiveType} - ${mapping.directiveContent}`);
          }

          if (mapping.directiveType === 'methodCall') {
            // methodCall directives need method name and resolved method data
            if (this.options.verbose) {
              UI.info(`🔍 Processing methodCall: ${mapping.directiveContent} (methodName: ${mapping.methodName}, hasResolvedMethod: ${!!mapping.resolvedMethod})`);
            }

            if (!mapping.methodName) {
              const reason = 'Missing method name';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for mapping: ${mapping.directiveType}`);
              }
              continue;
            }

            if (!mapping.resolvedMethod) {
              const reason = 'Missing resolved method data';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for ${mapping.methodName}`);
              }
              continue;
            }

            // For methodCall directives, we need to find the exact function in this file
            // and place the decorator directly above it
            const targetFunctionName = mapping.methodName;
            const targetLineNumber = this.findFunctionDefinition(content, targetFunctionName);

            if (!targetLineNumber || targetLineNumber < 1) {
              const reason = `Function ${targetFunctionName} not found in file`;
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason}`);
              }
              continue;
            }

            const decorator = this.generateDecorator(mapping);
            if (!decorator) {
              const reason = 'Failed to generate decorator';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for ${mapping.methodName}`);
              }
              continue;
            }

            // Check for duplicate decorators (comprehensive check against both existing and current batch)
            const isDuplicateInBatch = decoratorsToAdd.some(existing =>
              existing.decorator === decorator &&
              existing.lineNumber === targetLineNumber &&
              existing.targetFunction === targetFunctionName
            );

            const isDuplicateInFile = this.options.preventDuplicates &&
              this.isDuplicateDecorator(decorator, targetFunctionName, existingDecorators);

            if (isDuplicateInBatch || isDuplicateInFile) {
              const reason = isDuplicateInBatch ? 'Duplicate decorator in current batch' : 'Duplicate decorator already exists in file';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for ${targetFunctionName}: ${decorator}`);
              }
              continue;
            }

            decoratorsToAdd.push({
              decorator,
              targetFunction: targetFunctionName,
              lineNumber: targetLineNumber,
              mapping
            });

            if (this.options.verbose) {
              UI.info(`   ✓ Prepared @GQLMethodCall decorator for ${targetFunctionName} at line ${targetLineNumber}`);
            }
            continue;
          } else if (mapping.directiveType === 'field' || mapping.directiveType === 'import') {
            // @field and @import directives don't need method resolution
            // They can be added to any TypeScript file as standalone decorators
            // We'll add them to the first TypeScript file found, or create a dedicated file

            // For now, we'll add them to the first available TypeScript file
            // In a real implementation, you might want to add them to a specific file
            // or create a dedicated decorators file

            if (this.options.verbose) {
              UI.info(`📝 Processing ${mapping.directiveType} directive: ${mapping.directiveContent}`);
            }

            // These directives will be added as standalone decorators
            // We'll use line 1 as a placeholder since they don't target specific functions
            const targetLineNumber = 1;

            const decorator = this.generateDecorator(mapping);
            if (!decorator) {
              const reason = 'Failed to generate decorator';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for ${mapping.directiveType} directive`);
              }
              continue;
            }

            // Check for duplicates for field and import directives too
            // Use the same unique identifier system as findTargetFunctionName
            let targetFunctionName: string;
            if (mapping.directiveType === 'field') {
              // Create unique identifier for field decorators
              const [fieldName, fieldType] = mapping.directiveContent.split(':').map(s => s.trim());
              targetFunctionName = `standalone_field_${mapping.typeName}_${fieldName}_${fieldType}`;
            } else if (mapping.directiveType === 'import') {
              // Create unique identifier for import decorators
              const importHash = mapping.directiveContent.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
              targetFunctionName = `standalone_import_${importHash}`;
            } else {
              targetFunctionName = `${mapping.directiveType}_directive`;
            }

            const isDuplicateInBatch = decoratorsToAdd.some(existing =>
              existing.decorator === decorator &&
              existing.targetFunction === targetFunctionName
            );

            const isDuplicateInFile = this.options.preventDuplicates &&
              this.isDuplicateDecorator(decorator, targetFunctionName, existingDecorators);

            if (isDuplicateInBatch || isDuplicateInFile) {
              const reason = isDuplicateInBatch ? 'Duplicate decorator in current batch' : 'Duplicate decorator already exists in file';
              skippedMappings.push({ mapping, reason });
              if (this.options.verbose) {
                UI.warning(`⚠️ ${reason} for ${mapping.directiveType} directive: ${decorator}`);
              }
              continue;
            }

            decoratorsToAdd.push({
              decorator,
              targetFunction: targetFunctionName,
              lineNumber: targetLineNumber,
              mapping
            });

            if (this.options.verbose) {
              UI.info(`   ✓ Prepared ${mapping.directiveType} decorator: ${decorator}`);
            }
            continue;
          }

        } catch (error) {
          const reason = `Processing error: ${error}`;
          skippedMappings.push({ mapping, reason });
          if (this.options.verbose) {
            UI.warning(`⚠️ ${reason} for ${mapping.methodName || 'unknown method'}`);
          }
        }
      }

      // Report skipped mappings with detailed information
      if (skippedMappings.length > 0) {
        const duplicateCount = skippedMappings.filter(s => s.reason.includes('Duplicate')).length;
        const otherCount = skippedMappings.length - duplicateCount;

        if (duplicateCount > 0) {
          UI.info(`📋 Skipped ${duplicateCount} duplicate decorator${duplicateCount > 1 ? 's' : ''} (already exist${duplicateCount === 1 ? 's' : ''} in file)`);
        }

        if (this.options.verbose && skippedMappings.length > 0) {
          UI.warning(`⚠️ Skipped ${skippedMappings.length}/${fileMappings.length} mappings:`);
          skippedMappings.forEach(({ mapping, reason }) => {
            const identifier = mapping.methodName || mapping.directiveType || 'unknown';
            UI.warning(`   - ${identifier}: ${reason}`);
          });
        } else if (otherCount > 0) {
          UI.warning(`⚠️ Skipped ${otherCount} mapping${otherCount > 1 ? 's' : ''} due to errors (use --verbose for details)`);
        }
      }

      if (decoratorsToAdd.length === 0) {
        if (this.options.verbose) {
          UI.warning(`⚠️ No decorators to add for ${filePath} (${fileMappings.length} mappings processed)`);
        }
        return {
          success: true,
          filePath,
          decoratorsAdded: 0,
          decoratorsSkipped: fileMappings.length,
          addedDecorators: []
        };
      }

      if (this.options.verbose) {
        UI.info(`   Inserting ${decoratorsToAdd.length} decorators into ${filePath}`);
      }

      // Apply decorators to the file
      const modificationResult = await this.fileModifier.modifyFile(filePath, (originalContent) => {
        return this.insertDecorators(originalContent, decoratorsToAdd);
      });

      if (!modificationResult.success) {
        return {
          success: false,
          filePath,
          decoratorsAdded: 0,
          decoratorsSkipped: 0,
          error: modificationResult.error,
          addedDecorators: []
        };
      }

      const addedDecorators = decoratorsToAdd.map(d => d.decorator);

      if (this.options.verbose) {
        UI.success(`✅ Added ${decoratorsToAdd.length} decorators to ${path.relative(process.cwd(), filePath)}`);
        addedDecorators.forEach(decorator => {
          UI.info(`   + ${decorator}`);
        });
      }

      return {
        success: true,
        filePath,
        decoratorsAdded: decoratorsToAdd.length,
        decoratorsSkipped: fileMappings.length - decoratorsToAdd.length,
        addedDecorators
      };

    } catch (error) {
      return {
        success: false,
        filePath,
        decoratorsAdded: 0,
        decoratorsSkipped: 0,
        error: `Failed to inject decorators: ${error}`,
        addedDecorators: []
      };
    }
  }

  /**
   * Generate a decorator string from a directive mapping
   */
  private generateDecorator(mapping: DirectiveMethodMapping): string | null {
    switch (mapping.directiveType) {
      case 'methodCall':
        return this.generateMethodCallDecorator(mapping);
      case 'import':
        return this.generateImportDecorator(mapping);
      case 'field':
        return this.generateFieldDecorator(mapping);
      default:
        return null;
    }
  }

  /**
   * Generate @GQLMethodCall decorator
   */
  private generateMethodCallDecorator(mapping: DirectiveMethodMapping): string {
    const schemaParam = this.options.schemaId ? `, schema: "${this.options.schemaId}"` : '';
    const fieldParam = mapping.fieldName ? `, field: "${mapping.fieldName}"` : '';
    
    return `@GQLMethodCall({ type: "${mapping.typeName}"${fieldParam}, call: "${mapping.directiveContent}"${schemaParam} })`;
  }

  /**
   * Generate @GQLImport decorator
   */
  private generateImportDecorator(mapping: DirectiveMethodMapping): string {
    // Process the import statement to handle alias imports properly
    const processedImport = this.processImportStatement(mapping.directiveContent);
    return `@GQLImport("${processedImport}")`;
  }

  /**
   * Generate @GQLField decorator
   */
  private generateFieldDecorator(mapping: DirectiveMethodMapping): string {
    const schemaParam = this.options.schemaId ? `, schema: "${this.options.schemaId}"` : '';
    
    // Parse field content (format: "fieldName: FieldType")
    const [fieldName, fieldType] = mapping.directiveContent.split(':').map(s => s.trim());
    
    return `@GQLField({ ref: "${mapping.typeName}", name: "${fieldName}", type: "${fieldType}"${schemaParam} })`;
  }

  /**
   * Find existing decorators in the file content
   */
  private async findExistingDecorators(content: string): Promise<ExistingDecorator[]> {
    const decorators: ExistingDecorator[] = [];
    
    try {
      // Use regex to find GQL decorators
      const decoratorRegex = /@(GQL(?:MethodCall|Import|Field))\s*\([^)]*\)/g;
      const lines = content.split('\n');
      
      let match;
      while ((match = decoratorRegex.exec(content)) !== null) {
        const decoratorText = match[0];
        const decoratorName = match[1];
        const lineNumber = content.substring(0, match.index).split('\n').length;
        
        // Find the target function name
        const targetName = this.findTargetFunctionName(lines, lineNumber);
        
        decorators.push({
          name: decoratorName,
          lineNumber,
          text: decoratorText,
          targetName: targetName || ''
        });
      }
    } catch (error) {
      if (this.options.verbose) {
        UI.warning(`⚠️ Error finding existing decorators: ${error}`);
      }
    }
    
    return decorators;
  }

  /**
   * Find the target function name for a decorator
   */
  private findTargetFunctionName(lines: string[], decoratorLineNumber: number): string | null {
    // First check if this is a standalone decorator (GQLField or GQLImport)
    const decoratorLine = lines[decoratorLineNumber - 1];
    if (decoratorLine) {
      const decoratorMatch = decoratorLine.match(/@(GQL(?:Field|Import))/);
      if (decoratorMatch) {
        // For standalone decorators, use a special identifier based on the decorator content
        const decoratorType = decoratorMatch[1];
        if (decoratorType === 'GQLField') {
          // Extract field parameters to create a unique identifier
          const fieldMatch = decoratorLine.match(/ref:\s*"([^"]*)".*name:\s*"([^"]*)".*type:\s*"([^"]*)"/);
          if (fieldMatch) {
            const [, ref, name, type] = fieldMatch;
            return `standalone_field_${ref}_${name}_${type}`;
          }
          return 'standalone_field_directive';
        } else if (decoratorType === 'GQLImport') {
          // Extract import statement to create a unique identifier
          const importMatch = decoratorLine.match(/@GQLImport\("([^"]*)"\)/);
          if (importMatch) {
            const importStatement = importMatch[1];
            // Create a hash-like identifier from the import statement
            const importHash = importStatement.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
            return `standalone_import_${importHash}`;
          }
          return 'standalone_import_directive';
        }
      }
    }

    // Look for the function declaration after the decorator
    for (let i = decoratorLineNumber; i < Math.min(decoratorLineNumber + 5, lines.length); i++) {
      const line = lines[i];

      // Function declaration patterns
      const patterns = [
        /export\s+(?:const|function)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/,
        /(?:const|function)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/,
        /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[:=]\s*(?:async\s+)?\(/
      ];

      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return match[1];
        }
      }
    }

    return null;
  }



  /**
   * Insert decorators into the file content
   */
  private insertDecorators(
    content: string,
    decoratorsToAdd: Array<{
      decorator: string;
      targetFunction: string;
      lineNumber: number;
      mapping: DirectiveMethodMapping;
    }>
  ): string {
    const lines = content.split('\n');

    // Sort decorators to process function-specific decorators first, then standalone decorators
    // This ensures function-specific decorators are placed at their correct positions before
    // standalone decorators are inserted at the top (which would shift line numbers)
    const sortedDecorators = decoratorsToAdd.sort((a, b) => {
      // Function-specific decorators (methodCall) should be processed first
      const aIsStandalone = a.mapping.directiveType === 'field' || a.mapping.directiveType === 'import';
      const bIsStandalone = b.mapping.directiveType === 'field' || b.mapping.directiveType === 'import';

      if (!aIsStandalone && bIsStandalone) return -1; // a (function-specific) comes first
      if (aIsStandalone && !bIsStandalone) return 1;  // b (function-specific) comes first

      // For decorators of the same type, sort by line number in reverse order
      return b.lineNumber - a.lineNumber;
    });

    for (const { decorator, lineNumber, mapping } of sortedDecorators) {
      if (mapping.directiveType === 'field') {
        // For @field decorators, find the type definition and add the decorator above it
        const fieldContent = mapping.directiveContent;
        let referencedTypeName = '';

        // Extract the type name from the field content
        if (fieldContent.includes(':')) {
          const parts = fieldContent.split(':');
          if (parts.length >= 2) {
            referencedTypeName = parts[1].trim();
            // Remove any import path information if present
            if (referencedTypeName.includes('"')) {
              referencedTypeName = referencedTypeName.split('"')[0].trim();
            }
          }
        } else {
          referencedTypeName = fieldContent.trim();
        }

        // Find the line where this type is defined
        let typeDefLineIndex = -1;
        if (referencedTypeName) {
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            // More precise regex matching for type definitions
            const typeRegex = new RegExp(`^(export\\s+)?(interface|type|class)\\s+${referencedTypeName}\\b`);
            if (typeRegex.test(line)) {
              typeDefLineIndex = i;
              if (this.options.verbose) {
                UI.info(`   📍 Found type definition ${referencedTypeName} at line ${i + 1}: ${line}`);
              }
              break;
            }
          }
        }

        let insertIndex = 0;
        if (typeDefLineIndex >= 0) {
          // Insert above the type definition
          insertIndex = typeDefLineIndex;

          // Move up to find the best insertion point (above any existing decorators/comments)
          while (insertIndex > 0) {
            const prevLine = lines[insertIndex - 1].trim();
            if (prevLine === '' ||
                prevLine.startsWith('@') ||
                prevLine.startsWith('//') ||
                prevLine.startsWith('/*') ||
                prevLine.startsWith('*')) {
              insertIndex--;
            } else {
              break;
            }
          }

          if (this.options.verbose) {
            UI.info(`   📍 Inserting @GQLField decorator above ${referencedTypeName} at line ${insertIndex + 1}`);
          }
        } else {
          // If type not found, try to find a related method call or function
          insertIndex = this.findBestInsertionPointForFieldDirective(lines, mapping, referencedTypeName);

          if (this.options.verbose) {
            if (insertIndex > 0) {
              UI.info(`   📍 Type definition ${referencedTypeName} not found, placing decorator near related content at line ${insertIndex + 1}`);
            } else {
              UI.warning(`   ⚠️ Type definition ${referencedTypeName} not found, adding decorator at top of file`);
            }
          }
        }

        // Insert the decorator with no indentation
        lines.splice(insertIndex, 0, decorator);
      } else if (mapping.directiveType === 'import') {
        // For @import decorators, add them at the top of the file after imports
        let insertIndex = 0;

        // Skip past any existing imports and comments at the top
        while (insertIndex < lines.length) {
          const line = lines[insertIndex].trim();
          if (line.startsWith('import ') ||
              line.startsWith('//') ||
              line.startsWith('/*') ||
              line.startsWith('*') ||
              line === '' ||
              line.startsWith('@GQL')) {
            insertIndex++;
          } else {
            break;
          }
        }

        // Insert the decorator at the top level (no indentation)
        lines.splice(insertIndex, 0, decorator);
      } else {
        // For @methodCall decorators, insert above the function
        const functionLineIndex = lineNumber - 1;


        // Find the appropriate indentation by looking at the function line
        const functionLine = lines[functionLineIndex];
        const indentation = functionLine.match(/^(\s*)/)?.[1] || '';

        // For methodCall decorators, place them directly above the function
        // Do NOT move up past any lines - place exactly at the function line
        let insertIndex = functionLineIndex;

        // Insert the decorator with proper indentation above the function
        lines.splice(insertIndex, 0, `${indentation}${decorator}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Process import statement to handle alias imports properly
   */
  private processImportStatement(importStatement: string): string {
    // If no alias config is provided, return the import statement as-is
    if (!this.options.aliasConfig) {
      return importStatement;
    }

    try {
      // Parse the import statement to extract the import path
      const importMatch = importStatement.match(/from\s+['"]([^'"]+)['"]/);
      if (!importMatch) {
        return importStatement; // Not a valid import statement, return as-is
      }

      const originalPath = importMatch[1];

      // Check if the path is already an alias (starts with @)
      if (originalPath.startsWith('@')) {
        return importStatement; // Already an alias, no need to process
      }

      // Check if the path is a relative path that could be converted to an alias
      if (originalPath.startsWith('./') || originalPath.startsWith('../')) {
        // For migration, we typically want to preserve the original import paths
        // unless they can be converted to the configured alias
        return importStatement;
      }

      // For absolute paths or module imports, return as-is
      return importStatement;

    } catch (error) {
      if (this.options.verbose) {
        UI.warning(`⚠️ Error processing import statement: ${error}`);
      }
      return importStatement;
    }
  }

  /**
   * Find the best insertion point for a field directive when the custom type is not found
   */
  private findBestInsertionPointForFieldDirective(
    lines: string[],
    mapping: DirectiveMethodMapping,
    referencedTypeName: string
  ): number {
    // Strategy 1: Look for existing @GQLMethodCall decorators for the same GraphQL type
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.includes('@GQLMethodCall') && line.includes(`type: "${mapping.typeName}"`)) {
        if (this.options.verbose) {
          UI.info(`   📍 Found related @GQLMethodCall for ${mapping.typeName} at line ${i + 1}`);
        }
        return i;
      }
    }

    // Strategy 2: Look for functions that might be related to the GraphQL type
    const typeLower = mapping.typeName.toLowerCase();
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Check for function names that contain the type name
      const functionPatterns = [
        new RegExp(`function\\s+.*${typeLower}`, 'i'),
        new RegExp(`const\\s+.*${typeLower}.*=`, 'i'),
        new RegExp(`export\\s+.*${typeLower}`, 'i')
      ];

      for (const pattern of functionPatterns) {
        if (pattern.test(line)) {
          if (this.options.verbose) {
            UI.info(`   📍 Found related function for ${mapping.typeName} at line ${i + 1}: ${line}`);
          }
          return i;
        }
      }
    }

    // Strategy 3: Look for any existing @GQLField decorators to group with them
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.includes('@GQLField')) {
        if (this.options.verbose) {
          UI.info(`   📍 Found existing @GQLField decorator at line ${i + 1}, grouping with it`);
        }
        return i + 1; // Insert after the existing field decorator
      }
    }

    // Strategy 4: Default to top of file after imports
    let insertIndex = 0;
    while (insertIndex < lines.length) {
      const line = lines[insertIndex].trim();
      if (line.startsWith('import ') ||
          line.startsWith('//') ||
          line.startsWith('/*') ||
          line.startsWith('*') ||
          line === '' ||
          line.startsWith('@GQL')) {
        insertIndex++;
      } else {
        break;
      }
    }

    return insertIndex;
  }

  /**
   * Find the line number of a function definition in file content
   */
  private findFunctionDefinition(content: string, functionName: string): number | null {
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Function declaration patterns to match
      const patterns = [
        // export function functionName
        new RegExp(`^export\\s+function\\s+${functionName}\\s*\\(`),
        // function functionName
        new RegExp(`^function\\s+${functionName}\\s*\\(`),
        // export const functionName =
        new RegExp(`^export\\s+const\\s+${functionName}\\s*[=:]`),
        // const functionName =
        new RegExp(`^const\\s+${functionName}\\s*[=:]`),
        // functionName: function
        new RegExp(`^${functionName}\\s*:\\s*function`),
        // functionName = function
        new RegExp(`^${functionName}\\s*=\\s*function`),
        // async function functionName
        new RegExp(`^export\\s+async\\s+function\\s+${functionName}\\s*\\(`),
        new RegExp(`^async\\s+function\\s+${functionName}\\s*\\(`)
      ];

      for (const pattern of patterns) {
        if (pattern.test(line)) {
          return i + 1; // Return 1-based line number
        }
      }
    }

    return null;
  }

  /**
   * Extract decorator name from decorator string
   */
  private getDecoratorName(decorator: string): string {
    const match = decorator.match(/@(GQL\w+)/);
    return match ? match[1] : '';
  }

  /**
   * Check if a decorator is a duplicate of an existing decorator
   */
  private isDuplicateDecorator(
    newDecorator: string,
    targetFunction: string,
    existingDecorators: ExistingDecorator[]
  ): boolean {
    const newDecoratorName = this.getDecoratorName(newDecorator);

    for (const existing of existingDecorators) {
      // Check if decorator names match
      if (existing.name !== newDecoratorName) {
        continue;
      }

      // For standalone decorators, prioritize content comparison over target function matching
      const isStandaloneDecorator = targetFunction.startsWith('standalone_');
      const isExistingStandalone = existing.targetName.startsWith('standalone_');

      if (isStandaloneDecorator || isExistingStandalone) {
        // For standalone decorators, compare the decorator content directly
        if (this.compareDecoratorParameters(newDecorator, existing.text)) {
          if (this.options.verbose) {
            UI.info(`   🔍 Found duplicate standalone decorator: ${newDecorator}`);
          }
          return true;
        }
        continue;
      }

      // Check if target functions match for non-standalone decorators
      if (existing.targetName !== targetFunction) {
        continue;
      }

      // For more precise comparison, extract and compare parameters
      if (this.compareDecoratorParameters(newDecorator, existing.text)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Compare decorator parameters to determine if they are functionally equivalent
   */
  private compareDecoratorParameters(decorator1: string, decorator2: string): boolean {
    try {
      // Extract parameter objects from both decorators
      const params1 = this.extractDecoratorParameters(decorator1);
      const params2 = this.extractDecoratorParameters(decorator2);

      if (!params1 || !params2) {
        // If we can't parse parameters, fall back to string comparison
        return decorator1.trim() === decorator2.trim();
      }

      // Compare key parameters based on decorator type
      const decoratorType = this.getDecoratorName(decorator1);

      switch (decoratorType) {
        case 'GQLMethodCall':
          return this.compareMethodCallParameters(params1, params2);
        case 'GQLImport':
          return this.compareImportParameters(params1, params2);
        case 'GQLField':
          return this.compareFieldParameters(params1, params2);
        default:
          return decorator1.trim() === decorator2.trim();
      }
    } catch (error) {
      // If comparison fails, fall back to string comparison
      return decorator1.trim() === decorator2.trim();
    }
  }

  /**
   * Extract parameters from a decorator string
   */
  private extractDecoratorParameters(decorator: string): Record<string, any> | null {
    try {
      // Extract the content between parentheses
      const match = decorator.match(/@\w+\s*\(([^)]+)\)/);
      if (!match) {
        return null;
      }

      const paramString = match[1];

      // Simple parameter parsing - this handles basic object notation
      // For more complex cases, we might need a proper parser
      const params: Record<string, any> = {};

      // Match key: "value" or key: value patterns
      const paramMatches = paramString.match(/(\w+):\s*"([^"]*)"|\w+:\s*([^,}]+)/g);
      if (paramMatches) {
        for (const paramMatch of paramMatches) {
          const [key, ...valueParts] = paramMatch.split(':');
          const value = valueParts.join(':').trim();

          // Remove quotes if present
          const cleanValue = value.replace(/^["']|["']$/g, '').trim();
          params[key.trim()] = cleanValue;
        }
      }

      return params;
    } catch (error) {
      return null;
    }
  }

  /**
   * Compare GQLMethodCall parameters
   */
  private compareMethodCallParameters(params1: Record<string, any>, params2: Record<string, any>): boolean {
    return params1.type === params2.type &&
           params1.field === params2.field &&
           params1.call === params2.call &&
           (params1.schema || 'default') === (params2.schema || 'default');
  }

  /**
   * Compare GQLImport parameters
   */
  private compareImportParameters(params1: Record<string, any>, params2: Record<string, any>): boolean {
    // For GQLImport, the main parameter is the import statement
    return Object.values(params1)[0] === Object.values(params2)[0];
  }

  /**
   * Compare GQLField parameters
   */
  private compareFieldParameters(params1: Record<string, any>, params2: Record<string, any>): boolean {
    return params1.ref === params2.ref &&
           params1.name === params2.name &&
           params1.type === params2.type &&
           (params1.schema || 'default') === (params2.schema || 'default');
  }
}
