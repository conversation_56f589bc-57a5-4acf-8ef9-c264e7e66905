/**
 * Configuration for import alias support
 */
export interface AliasConfig {
  /** The alias to use for codebase imports (e.g., "@user_codebase") */
  codebaseAlias: string;
  /** The absolute path to the codebase directory */
  codebasePath: string;
}

/**
 * Utility functions for alias configuration
 */
export class AliasConfigUtils {
  /**
   * Generate a default alias based on the codebase directory name
   * @param codebasePath The absolute path to the codebase directory
   * @returns A default alias string
   */
  static generateDefaultAlias(codebasePath: string): string {
    const path = require('path');
    const dirName = path.basename(codebasePath);
    
    // Convert directory name to a valid alias format
    // Remove special characters and convert to lowercase
    const cleanName = dirName
      .replace(/[^a-zA-Z0-9_-]/g, '_')
      .toLowerCase();
    
    return `@${cleanName}`;
  }

  /**
   * Validate alias format
   * @param alias The alias to validate
   * @returns True if valid, false otherwise
   */
  static validateAlias(alias: string): boolean {
    // <PERSON><PERSON> must start with @ and contain only alphanumeric characters, underscores, hyphens, and forward slashes
    const aliasPattern = /^@[a-zA-Z0-9_\-\/]+$/;
    
    // Check basic pattern
    if (!aliasPattern.test(alias)) {
      return false;
    }
    
    // Prevent path traversal attempts
    if (alias.includes('..') || alias.includes('./')) {
      return false;
    }
    
    // Prevent common security issues
    if (alias.includes('//') || alias.endsWith('/')) {
      return false;
    }
    
    return true;
  }

  /**
   * Create an alias configuration from options
   * @param codebasePath The absolute path to the codebase directory
   * @param explicitAlias Optional explicit alias provided by user
   * @returns AliasConfig or null if no alias should be used
   */
  static createAliasConfig(
    codebasePath: string | undefined,
    explicitAlias: string | undefined
  ): AliasConfig | null {
    if (!codebasePath) {
      return null;
    }

    let alias: string;
    
    if (explicitAlias) {
      if (!this.validateAlias(explicitAlias)) {
        throw new Error(`Invalid alias format: ${explicitAlias}`);
      }
      alias = explicitAlias;
    } else {
      alias = this.generateDefaultAlias(codebasePath);
    }

    return {
      codebaseAlias: alias,
      codebasePath: codebasePath
    };
  }

  /**
   * Check if a file path is within the codebase directory
   * @param filePath The file path to check
   * @param codebasePath The codebase directory path
   * @returns True if the file is within the codebase
   */
  static isWithinCodebase(filePath: string, codebasePath: string): boolean {
    const path = require('path');
    const relativePath = path.relative(codebasePath, filePath);
    
    // If the relative path starts with '..' or is absolute, it's outside the codebase
    return !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
  }

  /**
   * Convert a file path to an alias-based import path
   * @param filePath The absolute file path
   * @param aliasConfig The alias configuration
   * @returns The alias-based import path or null if not applicable
   */
  static convertToAliasImport(
    filePath: string,
    aliasConfig: AliasConfig
  ): string | null {
    const path = require('path');
    
    if (!this.isWithinCodebase(filePath, aliasConfig.codebasePath)) {
      return null;
    }

    // Get the relative path from codebase to the file
    const relativePath = path.relative(aliasConfig.codebasePath, filePath);
    
    // Remove file extension
    const pathWithoutExtension = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Convert to alias-based import
    const aliasImport = `${aliasConfig.codebaseAlias}/${pathWithoutExtension}`;
    
    // Normalize path separators to forward slashes
    return aliasImport.replace(/\\/g, '/');
  }
}
