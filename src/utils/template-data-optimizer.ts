import { TemplateSanitizer } from './template-sanitizer';

/**
 * Optimized template data preparation and transformation utilities
 * Reduces overhead in template data processing through caching and efficient algorithms
 */

/**
 * Cache for sanitized template data
 */
interface DataCacheEntry {
  /** Sanitized data result */
  sanitizedData: any;
  /** Timestamp when cached */
  timestamp: number;
  /** Hash of original data for cache validation */
  dataHash: string;
}

/**
 * Configuration for template data optimization
 */
export interface TemplateDataOptimizerConfig {
  /** Whether to enable data caching (default: true) */
  enableCaching?: boolean;
  /** Cache TTL in milliseconds (default: 5 minutes) */
  cacheTtl?: number;
  /** Maximum cache size (default: 1000 entries) */
  maxCacheSize?: number;
  /** Whether to enable performance tracking (default: true) */
  enablePerformanceTracking?: boolean;
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
}

/**
 * Performance metrics for data optimization
 */
export interface DataOptimizationMetrics {
  /** Total data preparation operations */
  totalOperations: number;
  /** Cache hits */
  cacheHits: number;
  /** Cache misses */
  cacheMisses: number;
  /** Cache hit rate percentage */
  hitRate: number;
  /** Average preparation time in milliseconds */
  averagePreparationTime: number;
  /** Total time saved by caching in milliseconds */
  totalTimeSaved: number;
  /** Current cache size */
  cacheSize: number;
}

/**
 * Optimized template data processor
 * Provides high-performance template data preparation with caching and optimization
 */
export class TemplateDataOptimizer {
  private dataCache = new Map<string, DataCacheEntry>();
  private config: Required<TemplateDataOptimizerConfig>;
  private metrics: DataOptimizationMetrics = {
    totalOperations: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    averagePreparationTime: 0,
    totalTimeSaved: 0,
    cacheSize: 0,
  };
  private preparationTimes: number[] = [];

  constructor(config: TemplateDataOptimizerConfig = {}) {
    this.config = {
      enableCaching: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutes
      maxCacheSize: 1000,
      enablePerformanceTracking: true,
      enableLogging: false,
      ...config,
    };

    if (this.config.enableLogging) {
      console.log('🚀 TemplateDataOptimizer initialized');
    }
  }

  /**
   * Generate a hash for template data
   * @param data Data to hash
   * @returns Hash string
   */
  private generateDataHash(data: any): string {
    // Simple hash function for template data
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Check if cache entry is valid
   * @param entry Cache entry to check
   * @returns True if entry is valid
   */
  private isCacheEntryValid(entry: DataCacheEntry): boolean {
    return Date.now() - entry.timestamp < this.config.cacheTtl;
  }

  /**
   * Evict expired cache entries
   */
  private evictExpiredEntries(): void {
    const now = Date.now();
    let evicted = 0;

    for (const [key, entry] of this.dataCache.entries()) {
      if (now - entry.timestamp > this.config.cacheTtl) {
        this.dataCache.delete(key);
        evicted++;
      }
    }

    // Only log evictions in development mode
    if (evicted > 0 && this.config.enableLogging && process.env.NODE_ENV === 'development') {
      console.log(`🗑️  Evicted ${evicted} expired data entries`);
    }
  }

  /**
   * Evict LRU entries if cache is full
   */
  private evictLruEntries(): void {
    while (this.dataCache.size >= this.config.maxCacheSize) {
      // Find oldest entry
      let oldestKey: string | null = null;
      let oldestTimestamp = Date.now();

      for (const [key, entry] of this.dataCache.entries()) {
        if (entry.timestamp < oldestTimestamp) {
          oldestTimestamp = entry.timestamp;
          oldestKey = key;
        }
      }

      if (oldestKey) {
        this.dataCache.delete(oldestKey);
      } else {
        break; // Safety break
      }
    }
  }

  /**
   * Update performance metrics
   * @param preparationTime Time taken for data preparation
   * @param cacheHit Whether this was a cache hit
   */
  private updateMetrics(preparationTime: number, cacheHit: boolean): void {
    if (!this.config.enablePerformanceTracking) return;

    this.metrics.totalOperations++;
    
    if (cacheHit) {
      this.metrics.cacheHits++;
      this.metrics.totalTimeSaved += this.metrics.averagePreparationTime;
    } else {
      this.metrics.cacheMisses++;
      this.preparationTimes.push(preparationTime);
      
      // Keep only last 1000 preparation times for average calculation
      if (this.preparationTimes.length > 1000) {
        this.preparationTimes.shift();
      }
      
      // Update average preparation time
      this.metrics.averagePreparationTime = 
        this.preparationTimes.reduce((sum, time) => sum + time, 0) / this.preparationTimes.length;
    }

    // Update hit rate
    this.metrics.hitRate = this.metrics.totalOperations > 0
      ? (this.metrics.cacheHits / this.metrics.totalOperations) * 100
      : 0;

    // Update cache size
    this.metrics.cacheSize = this.dataCache.size;
  }

  /**
   * Optimize and prepare template data with caching
   * @param data Raw template data
   * @param sanitizationConfig Optional sanitization configuration
   * @returns Optimized template data
   */
  public prepareTemplateData(
    data: Record<string, any>,
    sanitizationConfig?: any
  ): Record<string, any> {
    const startTime = Date.now();
    const dataHash = this.generateDataHash({ data, sanitizationConfig });
    const cacheKey = `${dataHash}`;

    // Check cache first
    if (this.config.enableCaching) {
      const cachedEntry = this.dataCache.get(cacheKey);
      if (cachedEntry && this.isCacheEntryValid(cachedEntry)) {
        // Verify data hasn't changed
        if (cachedEntry.dataHash === dataHash) {
          const preparationTime = Date.now() - startTime;
          this.updateMetrics(preparationTime, true);
          
          // Skip verbose cache hit logging to avoid performance impact
          if (this.config.enableLogging && process.env.NODE_ENV === 'development') {
            console.log(`✅ Data cache hit: ${cacheKey.substring(0, 8)}...`);
          }
          
          return cachedEntry.sanitizedData;
        }
      }
    }

    // Cache miss - prepare data
    const preparationStart = Date.now();
    
    try {
      // Optimize data preparation process
      const optimizedData = this.optimizeDataPreparation(data, sanitizationConfig);
      const preparationTime = Date.now() - preparationStart;

      // Cache the result if caching is enabled
      if (this.config.enableCaching) {
        // Evict expired entries first
        this.evictExpiredEntries();
        
        // Evict LRU entries if cache is full
        this.evictLruEntries();

        // Add to cache
        const cacheEntry: DataCacheEntry = {
          sanitizedData: optimizedData,
          timestamp: Date.now(),
          dataHash,
        };
        
        this.dataCache.set(cacheKey, cacheEntry);
      }

      this.updateMetrics(preparationTime, false);

      // Skip verbose preparation logging to avoid performance impact
      if (this.config.enableLogging && process.env.NODE_ENV === 'development') {
        console.log(`🔄 Data prepared: ${cacheKey.substring(0, 8)}... (${preparationTime}ms)`);
      }

      return optimizedData;
    } catch (error) {
      if (this.config.enableLogging) {
        console.error(`❌ Template data preparation failed for key: ${cacheKey.substring(0, 8)}...`, error);
      }
      throw error;
    }
  }

  /**
   * Optimized data preparation implementation
   * @param data Raw template data
   * @param sanitizationConfig Sanitization configuration
   * @returns Optimized data
   */
  private optimizeDataPreparation(
    data: Record<string, any>,
    sanitizationConfig?: any
  ): Record<string, any> {
    // Fast path for simple data
    if (this.isSimpleData(data)) {
      return this.prepareSimpleData(data);
    }

    // Complex data preparation with optimization
    const optimizedData: Record<string, any> = {};

    // Process data in batches for better performance
    const entries = Object.entries(data);
    const batchSize = 50; // Process 50 properties at a time

    for (let i = 0; i < entries.length; i += batchSize) {
      const batch = entries.slice(i, i + batchSize);
      
      for (const [key, value] of batch) {
        optimizedData[key] = this.optimizeValue(key, value, sanitizationConfig);
      }
    }

    return optimizedData;
  }

  /**
   * Check if data is simple (no complex processing needed)
   * @param data Data to check
   * @returns True if data is simple
   */
  private isSimpleData(data: Record<string, any>): boolean {
    const keys = Object.keys(data);
    
    // Consider data simple if it has few properties and no complex values
    if (keys.length > 20) return false;
    
    for (const key of keys) {
      const value = data[key];
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        return false; // Complex nested object
      }
      if (Array.isArray(value) && value.length > 10) {
        return false; // Large array
      }
    }
    
    return true;
  }

  /**
   * Prepare simple data with minimal processing
   * @param data Simple data to prepare
   * @returns Prepared data
   */
  private prepareSimpleData(data: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      // Basic sanitization for simple values
      if (typeof value === 'string') {
        result[key] = this.sanitizeString(value);
      } else if (Array.isArray(value)) {
        result[key] = value.map(item => 
          typeof item === 'string' ? this.sanitizeString(item) : item
        );
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * Optimize a single value
   * @param key Property key
   * @param value Property value
   * @param sanitizationConfig Sanitization configuration
   * @returns Optimized value
   */
  private optimizeValue(key: string, value: any, sanitizationConfig?: any): any {
    if (typeof value === 'string') {
      return this.optimizeStringValue(key, value, sanitizationConfig);
    } else if (Array.isArray(value)) {
      return this.optimizeArrayValue(key, value, sanitizationConfig);
    } else if (typeof value === 'object' && value !== null) {
      return this.optimizeObjectValue(key, value, sanitizationConfig);
    } else {
      return value; // Primitive values pass through
    }
  }

  /**
   * Optimize string value
   * @param key Property key
   * @param value String value
   * @param sanitizationConfig Sanitization configuration
   * @returns Optimized string
   */
  private optimizeStringValue(key: string, value: string, sanitizationConfig?: any): string {
    // Use optimized sanitization for known template fields
    if (this.isKnownTemplateField(key)) {
      return this.sanitizeKnownField(key, value);
    }
    
    // Use full sanitization for unknown fields
    const result = TemplateSanitizer.sanitizeTemplateValue(value, sanitizationConfig);
    return result.isSafe ? (result.sanitizedValue || value) : value;
  }

  /**
   * Optimize array value
   * @param key Property key
   * @param value Array value
   * @param sanitizationConfig Sanitization configuration
   * @returns Optimized array
   */
  private optimizeArrayValue(key: string, value: any[], sanitizationConfig?: any): any[] {
    // Optimize array processing based on size
    if (value.length < 10) {
      // Small arrays - process normally
      return value.map(item => this.optimizeValue(`${key}[]`, item, sanitizationConfig));
    } else {
      // Large arrays - use batch processing
      const result: any[] = [];
      const batchSize = 20;
      
      for (let i = 0; i < value.length; i += batchSize) {
        const batch = value.slice(i, i + batchSize);
        result.push(...batch.map(item => this.optimizeValue(`${key}[]`, item, sanitizationConfig)));
      }
      
      return result;
    }
  }

  /**
   * Optimize object value
   * @param key Property key
   * @param value Object value
   * @param sanitizationConfig Sanitization configuration
   * @returns Optimized object
   */
  private optimizeObjectValue(key: string, value: Record<string, any>, sanitizationConfig?: any): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [subKey, subValue] of Object.entries(value)) {
      result[subKey] = this.optimizeValue(`${key}.${subKey}`, subValue, sanitizationConfig);
    }
    
    return result;
  }

  /**
   * Check if a field is a known template field
   * @param key Field key
   * @returns True if known field
   */
  private isKnownTemplateField(key: string): boolean {
    const knownFields = [
      'typeName', 'fieldName', 'contextName', 'importPath', 'description',
      'camelCaseFieldName', 'capitalizedFieldName', 'returnTypeAnnotation',
      'schemaFilePath', 'resolverSignature', 'directiveMethodCall'
    ];
    
    return knownFields.includes(key);
  }

  /**
   * Sanitize known template field with optimized processing
   * @param key Field key
   * @param value Field value
   * @returns Sanitized value
   */
  private sanitizeKnownField(key: string, value: string): string {
    // Optimized sanitization for known fields
    switch (key) {
      case 'typeName':
      case 'fieldName':
      case 'contextName':
      case 'camelCaseFieldName':
      case 'capitalizedFieldName':
        // These should be valid identifiers
        return this.sanitizeIdentifier(value);
      
      case 'importPath':
      case 'schemaFilePath':
        // These should be valid file paths
        return this.sanitizeFilePath(value);
      
      case 'directiveMethodCall':
        // This should be a valid method call
        return this.sanitizeMethodCall(value);
      
      default:
        // Use basic string sanitization
        return this.sanitizeString(value);
    }
  }

  /**
   * Basic string sanitization
   * @param value String to sanitize
   * @returns Sanitized string
   */
  private sanitizeString(value: string): string {
    // Basic HTML entity escaping for template safety
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }

  /**
   * Sanitize identifier
   * @param value Identifier to sanitize
   * @returns Sanitized identifier
   */
  private sanitizeIdentifier(value: string): string {
    // Remove non-identifier characters
    return value.replace(/[^a-zA-Z0-9_$]/g, '');
  }

  /**
   * Sanitize file path
   * @param value File path to sanitize
   * @returns Sanitized file path
   */
  private sanitizeFilePath(value: string): string {
    // Basic path sanitization
    return value.replace(/[<>:"|?*]/g, '');
  }

  /**
   * Sanitize method call
   * @param value Method call to sanitize
   * @returns Sanitized method call
   */
  private sanitizeMethodCall(value: string): string {
    // Basic method call sanitization
    return value.replace(/[<>]/g, '');
  }

  /**
   * Get current optimization metrics
   * @returns Current metrics
   */
  public getMetrics(): DataOptimizationMetrics {
    return { ...this.metrics };
  }

  /**
   * Clear the data cache
   */
  public clearCache(): void {
    this.dataCache.clear();
    this.metrics.cacheSize = 0;
    
    if (this.config.enableLogging) {
      console.log('🧹 Template data cache cleared');
    }
  }

  /**
   * Get cache statistics
   * @returns Cache statistics
   */
  public getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    totalOperations: number;
  } {
    return {
      size: this.dataCache.size,
      maxSize: this.config.maxCacheSize,
      hitRate: this.metrics.hitRate,
      totalOperations: this.metrics.totalOperations,
    };
  }
}

// Global singleton instance
let globalDataOptimizer: TemplateDataOptimizer | null = null;

/**
 * Get the global template data optimizer instance
 * @param config Optional optimizer configuration (only used on first call)
 * @returns Global optimizer instance
 */
export function getGlobalTemplateDataOptimizer(
  config?: TemplateDataOptimizerConfig
): TemplateDataOptimizer {
  if (!globalDataOptimizer) {
    globalDataOptimizer = new TemplateDataOptimizer(config);
  }
  return globalDataOptimizer;
}

/**
 * Reset the global template data optimizer
 * Useful for testing or when configuration changes
 */
export function resetGlobalTemplateDataOptimizer(): void {
  globalDataOptimizer = null;
}
