import { DecoratorParser } from './decorator-parser';
import { parseTypeScriptFile, getParserPerformanceMetrics, resetParserPerformanceMetrics } from './ts-parser';
import { getGlobalTemplateCache, CacheStatistics } from './template-compilation-cache';
import { getGlobalInitializationService } from './template-initialization-service';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

/**
 * Performance benchmark utility for testing the optimizations
 */
export class PerformanceBenchmark {
  private tempDir: string = '';

  /**
   * Run a comprehensive performance benchmark
   */
  async runBenchmark(): Promise<void> {
    console.log('🚀 Starting Performance Benchmark for gql-generator optimizations...\n');

    this.tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'gql-perf-benchmark-'));

    try {
      await this.benchmarkTypeScriptParser();
      await this.benchmarkDecoratorParser();
      await this.benchmarkFilePrefiltering();
      await this.benchmarkTemplateCompilation();

      console.log('✅ Performance benchmark completed successfully!');
    } finally {
      await fs.remove(this.tempDir);
    }
  }

  /**
   * Benchmark the enhanced TypeScript parser
   */
  private async benchmarkTypeScriptParser(): Promise<void> {
    console.log('📊 Benchmarking Enhanced TypeScript Parser...');
    
    // Create test files with different complexity levels
    const testFiles = [
      {
        name: 'simple-resolver.ts',
        content: `
export const simpleResolver = () => {
  return { id: '1', name: 'Simple' };
};`
      },
      {
        name: 'complex-resolver.ts',
        content: `
import { User, Context } from '../types';

export const complexResolver = async (
  args: { id: string; options?: { include?: string[] } },
  context: Context
): Promise<User | null> => {
  try {
    const user = await context.userService.findById(args.id);
    if (args.options?.include?.includes('profile')) {
      user.profile = await context.profileService.getProfile(user.id);
    }
    return user;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw new Error('User not found');
  }
};

export default complexResolver;`
      },
      {
        name: 'arrow-function.ts',
        content: `
export const arrowResolver = async (args: any) => {
  const result = await someAsyncOperation(args);
  return result as UserType;
};`
      }
    ];

    resetParserPerformanceMetrics();
    const startTime = Date.now();
    
    for (const testFile of testFiles) {
      const filePath = path.join(this.tempDir, testFile.name);
      await fs.writeFile(filePath, testFile.content);
      
      const result = await parseTypeScriptFile(filePath);
      console.log(`  ✓ Parsed ${testFile.name}: ${result ? 'Success' : 'Failed'}`);
    }
    
    const duration = Date.now() - startTime;
    const metrics = getParserPerformanceMetrics();
    
    console.log(`  📈 Results:`);
    console.log(`    - Total time: ${duration}ms`);
    console.log(`    - SWC successes: ${metrics.swcSuccessCount}`);
    console.log(`    - Fallbacks: ${metrics.fallbackCount}`);
    console.log(`    - Success rate: ${Math.round((metrics.swcSuccessCount / (metrics.swcSuccessCount + metrics.fallbackCount)) * 100)}%`);
    console.log('');
  }

  /**
   * Benchmark the enhanced decorator parser
   */
  private async benchmarkDecoratorParser(): Promise<void> {
    console.log('📊 Benchmarking Enhanced Decorator Parser...');
    
    // Create test files with decorators
    const decoratorFiles = [
      {
        name: 'simple-decorators.ts',
        content: `
@GQLMethodCall({ type: "Query", field: "user" })
export const getUser = () => null;

@GQLImport("import { UserService } from '../services'")
export const userImport = null;`
      },
      {
        name: 'complex-decorators.ts',
        content: `
@GQLMethodCall({
  type: "Query",
  field: "complexQuery",
  call: \`
    complexService.process({
      id: args.id,
      options: {
        nested: true,
        callback: () => console.log('done')
      }
    })
  \`,
  async: true,
  enableTypeCasting: true
})
export const complexQuery = (args: any) => null;

@GQLField({
  ref: "User",
  name: "computedField",
  type: "String",
  hidden: true
})
export const computedField = null;`
      }
    ];

    // Create some files without decorators to test pre-filtering
    const nonDecoratorFiles = [
      {
        name: 'utils.ts',
        content: 'export const helper = () => "helper";'
      },
      {
        name: 'types.ts',
        content: 'export interface User { id: string; }'
      },
      {
        name: 'constants.ts',
        content: 'export const CONSTANT = "value";'
      }
    ];

    // Write all files
    for (const file of [...decoratorFiles, ...nonDecoratorFiles]) {
      await fs.writeFile(path.join(this.tempDir, file.name), file.content);
    }

    const parser = new DecoratorParser();
    const startTime = Date.now();
    
    const result = await parser.scanCodebase(this.tempDir);
    const duration = Date.now() - startTime;
    const metrics = parser.getPerformanceMetrics();
    
    console.log(`  📈 Results:`);
    console.log(`    - Total time: ${duration}ms`);
    console.log(`    - Files processed: ${metrics.filesProcessed}`);
    console.log(`    - Files skipped: ${metrics.filesSkippedByPreFilter}`);
    console.log(`    - Pre-filter efficiency: ${metrics.preFilterEfficiency}%`);
    console.log(`    - Decorators found: ${metrics.decoratorsFound}`);
    console.log(`    - oxc-parser successes: ${metrics.oxcParsingSuccessCount}`);
    console.log(`    - @swc/core successes: ${metrics.swcParsingSuccessCount}`);
    console.log(`    - Regex parsing count: ${metrics.regexParsingCount}`);
    console.log(`    - Cache hit rate: ${metrics.cacheHitRate}%`);
    console.log(`    - Average time per file: ${metrics.averageTimePerFile.toFixed(2)}ms`);

    if (metrics.astVsRegexSpeedRatio > 0) {
      console.log(`    - AST vs Regex speed ratio: ${metrics.astVsRegexSpeedRatio.toFixed(2)}x`);
    }
    console.log('');
  }

  /**
   * Benchmark file pre-filtering efficiency
   */
  private async benchmarkFilePrefiltering(): Promise<void> {
    console.log('📊 Benchmarking File Pre-filtering...');
    
    // Create a larger set of files to test pre-filtering
    const fileCount = 50;
    const decoratorFileCount = 10;
    
    // Create files with decorators
    for (let i = 0; i < decoratorFileCount; i++) {
      await fs.writeFile(path.join(this.tempDir, `decorator-file-${i}.ts`), `
@GQLMethodCall({ type: "Query", field: "field${i}" })
export const resolver${i} = () => null;
      `);
    }
    
    // Create files without decorators
    for (let i = 0; i < fileCount - decoratorFileCount; i++) {
      await fs.writeFile(path.join(this.tempDir, `regular-file-${i}.ts`), `
export const function${i} = () => "result${i}";
export interface Type${i} { id: string; }
      `);
    }

    const parser = new DecoratorParser();
    const startTime = Date.now();
    
    const result = await parser.scanCodebase(this.tempDir);
    const duration = Date.now() - startTime;
    const metrics = parser.getPerformanceMetrics();
    
    console.log(`  📈 Results:`);
    console.log(`    - Total files created: ${fileCount}`);
    console.log(`    - Files with decorators: ${decoratorFileCount}`);
    console.log(`    - Files processed: ${metrics.filesProcessed}`);
    console.log(`    - Files skipped: ${metrics.filesSkippedByPreFilter}`);
    console.log(`    - Pre-filter efficiency: ${metrics.preFilterEfficiency}%`);
    console.log(`    - Total time: ${duration}ms`);
    console.log(`    - Decorators found: ${metrics.decoratorsFound}`);
    console.log(`    - Expected decorators: ${decoratorFileCount}`);
    console.log(`    - Accuracy: ${metrics.decoratorsFound === decoratorFileCount ? '✅ Perfect' : '⚠️  Mismatch'}`);
    console.log('');
  }

  /**
   * Benchmark template compilation performance
   */
  private async benchmarkTemplateCompilation(): Promise<void> {
    console.log('📊 Benchmarking Template Compilation Performance...');

    // Get the global template cache
    const templateCache = getGlobalTemplateCache({
      enableLogging: false,
      enablePerformanceTracking: true,
    });

    // Clear cache to start fresh
    templateCache.clear();

    // Test templates of varying complexity
    const testTemplates = [
      {
        name: 'Simple Template',
        content: 'Hello {{name}}!',
        iterations: 1000,
      },
      {
        name: 'Medium Template',
        content: `
{{#if hasData}}
  <div class="{{className}}">
    {{#each items}}
      <span>{{this.name}}: {{this.value}}</span>
    {{/each}}
  </div>
{{else}}
  <p>No data available</p>
{{/if}}`,
        iterations: 500,
      },
      {
        name: 'Complex Template (Resolver-like)',
        content: `{{#if directiveImports}}
{{#each directiveImports}}
{{safeRaw this}}
{{/each}}
{{/if}}
import { {{#if needsResolversImport}}{{typeName}}Resolvers, {{/if}}{{#if hasArgs}}{{typeName}}{{capitalizedFieldName}}Args, {{/if}}ResolversParentTypes{{#if needsReturnTypeImport}}, {{typesToImport}}{{/if}} } from '{{importPathToGenerated}}';
import { {{contextName}} } from '{{importPathToContext}}';

/**
 * {{description}}
 * Resolved {{resolverType}}: \`{{resolverSignature}}\`
 * Defined in: \`{{schemaFilePath}}\`
 */
const {{camelCaseFieldName}} = async (
  obj: ResolversParentTypes['{{typeName}}'],
  args{{#if hasArgs}}: {{typeName}}{{capitalizedFieldName}}Args{{else}}: Record<string, never>{{/if}},
  context: {{contextName}}
): Promise<{{safeRaw returnTypeAnnotation}}> => {
{{#if directiveMethodCall}}
  return {{safeRaw directiveMethodCall}};
{{else}}
  try {
    // TODO: Implement your resolver logic here
    {{defaultReturnStatement}}
  } catch (error) {
    console.error(\`Error in {{fieldName}} resolver:\`, error);
    throw error;
  }
{{/if}}
}

export { {{camelCaseFieldName}} };
export default {{camelCaseFieldName}};`,
        iterations: 100,
      },
    ];

    let totalCachedTime = 0;
    let totalUncachedTime = 0;

    for (const template of testTemplates) {
      console.log(`  🔄 Testing ${template.name}...`);

      // Benchmark uncached compilation (first run)
      const uncachedStart = Date.now();
      for (let i = 0; i < template.iterations; i++) {
        templateCache.compile(template.content + `_${i}`); // Make each unique to avoid cache hits
      }
      const uncachedTime = Date.now() - uncachedStart;
      totalUncachedTime += uncachedTime;

      // Clear cache and benchmark cached compilation
      templateCache.clear();

      // Pre-compile once to warm cache
      templateCache.compile(template.content);

      // Benchmark cached compilation (subsequent runs)
      const cachedStart = Date.now();
      for (let i = 0; i < template.iterations; i++) {
        templateCache.compile(template.content); // Same template for cache hits
      }
      const cachedTime = Date.now() - cachedStart;
      totalCachedTime += cachedTime;

      const speedup = uncachedTime / Math.max(cachedTime, 1);

      console.log(`    - Uncached: ${uncachedTime}ms (${template.iterations} compilations)`);
      console.log(`    - Cached: ${cachedTime}ms (${template.iterations} cache hits)`);
      console.log(`    - Speedup: ${speedup.toFixed(2)}x`);
    }

    // Get final cache statistics
    const cacheStats: CacheStatistics = templateCache.getStatistics();

    console.log(`  📈 Overall Results:`);
    console.log(`    - Total uncached time: ${totalUncachedTime}ms`);
    console.log(`    - Total cached time: ${totalCachedTime}ms`);
    console.log(`    - Overall speedup: ${(totalUncachedTime / Math.max(totalCachedTime, 1)).toFixed(2)}x`);
    console.log(`    - Cache hit rate: ${cacheStats.hitRate.toFixed(1)}%`);
    console.log(`    - Cache entries: ${cacheStats.entryCount}`);
    console.log(`    - Estimated memory usage: ${Math.round(cacheStats.estimatedMemoryUsage / 1024)}KB`);
    console.log(`    - Total time saved: ${cacheStats.totalTimeSaved.toFixed(0)}ms`);
    console.log('');
  }
}

/**
 * Run the performance benchmark
 */
export async function runPerformanceBenchmark(): Promise<void> {
  const benchmark = new PerformanceBenchmark();
  await benchmark.runBenchmark();
}

// Allow running directly
if (require.main === module) {
  runPerformanceBenchmark().catch(console.error);
}
