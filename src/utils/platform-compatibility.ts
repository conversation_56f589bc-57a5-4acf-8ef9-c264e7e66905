import os from 'os';
import { execSync } from 'child_process';
import { EventEmitter } from 'events';

/**
 * Phase 3: Cross-Platform Compatibility Validation
 * Comprehensive platform detection and feature validation for WASM and memory-mapped operations
 */

export interface PlatformInfo {
  platform: string;
  arch: string;
  nodeVersion: string;
  v8Version: string;
  totalMemory: number;
  availableMemory: number;
  cpuCount: number;
  cpuModel: string;
}

export interface CompatibilityFeatures {
  wasmSupported: boolean;
  memoryMappingSupported: boolean;
  workerThreadsSupported: boolean;
  bigIntSupported: boolean;
  sharedArrayBufferSupported: boolean;
  atomicsSupported: boolean;
  performanceAPISupported: boolean;
}

export interface CompatibilityReport {
  platform: PlatformInfo;
  features: CompatibilityFeatures;
  recommendations: string[];
  optimizations: Array<{
    feature: string;
    enabled: boolean;
    reason: string;
    impact: 'high' | 'medium' | 'low';
  }>;
  fallbackStrategies: Array<{
    feature: string;
    fallback: string;
    performance: number; // 0-1 scale relative to optimal
  }>;
}

/**
 * Platform compatibility validator
 */
export class PlatformCompatibility extends EventEmitter {
  private platformInfo: PlatformInfo;
  private features: CompatibilityFeatures;
  private initialized = false;

  constructor() {
    super();
    this.platformInfo = this.detectPlatformInfo();
    this.features = this.detectFeatures();
    this.initialized = true;
  }

  /**
   * Detect platform information
   */
  private detectPlatformInfo(): PlatformInfo {
    const platform = os.platform();
    const arch = os.arch();
    const nodeVersion = process.version;
    const v8Version = process.versions.v8;
    const totalMemory = os.totalmem();
    const availableMemory = os.freemem();
    const cpuCount = os.cpus().length;
    const cpuModel = os.cpus()[0]?.model || 'Unknown';

    return {
      platform,
      arch,
      nodeVersion,
      v8Version,
      totalMemory,
      availableMemory,
      cpuCount,
      cpuModel
    };
  }

  /**
   * Detect available features
   */
  private detectFeatures(): CompatibilityFeatures {
    return {
      wasmSupported: this.detectWASMSupport(),
      memoryMappingSupported: this.detectMemoryMappingSupport(),
      workerThreadsSupported: this.detectWorkerThreadsSupport(),
      bigIntSupported: this.detectBigIntSupport(),
      sharedArrayBufferSupported: this.detectSharedArrayBufferSupported(),
      atomicsSupported: this.detectAtomicsSupport(),
      performanceAPISupported: this.detectPerformanceAPISupport()
    };
  }

  /**
   * Detect WASM support
   */
  private detectWASMSupport(): boolean {
    try {
      // Check if WebAssembly is available
      if (typeof WebAssembly === 'undefined') {
        return false;
      }

      // Try to create a simple WASM module
      const wasmCode = new Uint8Array([
        0x00, 0x61, 0x73, 0x6d, // WASM magic number
        0x01, 0x00, 0x00, 0x00  // WASM version
      ]);

      const module = new WebAssembly.Module(wasmCode);
      return module instanceof WebAssembly.Module;
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect memory mapping support
   */
  private detectMemoryMappingSupport(): boolean {
    try {
      // Check if we can allocate large buffers
      const testSize = 64 * 1024 * 1024; // 64MB
      const buffer = Buffer.allocUnsafe(testSize);
      return buffer.length === testSize;
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect worker threads support
   */
  private detectWorkerThreadsSupport(): boolean {
    try {
      require('worker_threads');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect BigInt support
   */
  private detectBigIntSupport(): boolean {
    try {
      return typeof BigInt !== 'undefined';
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect SharedArrayBuffer support
   */
  private detectSharedArrayBufferSupported(): boolean {
    try {
      return typeof SharedArrayBuffer !== 'undefined';
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect Atomics support
   */
  private detectAtomicsSupport(): boolean {
    try {
      return typeof Atomics !== 'undefined';
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect Performance API support
   */
  private detectPerformanceAPISupport(): boolean {
    try {
      return typeof performance !== 'undefined' && 
             typeof performance.now === 'function';
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate comprehensive compatibility report
   */
  generateCompatibilityReport(): CompatibilityReport {
    const recommendations = this.generateRecommendations();
    const optimizations = this.generateOptimizations();
    const fallbackStrategies = this.generateFallbackStrategies();

    return {
      platform: this.platformInfo,
      features: this.features,
      recommendations,
      optimizations,
      fallbackStrategies
    };
  }

  /**
   * Generate platform-specific recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    // Node.js version recommendations
    const nodeVersionMajor = parseInt(this.platformInfo.nodeVersion.split('.')[0].substring(1));
    if (nodeVersionMajor < 18) {
      recommendations.push('Consider upgrading to Node.js 18+ for better WASM and performance features');
    }

    // Memory recommendations
    const memoryGB = this.platformInfo.totalMemory / (1024 * 1024 * 1024);
    if (memoryGB < 4) {
      recommendations.push('System has limited memory. Consider disabling memory-mapped operations for large files');
    } else if (memoryGB > 16) {
      recommendations.push('System has abundant memory. Enable aggressive memory-mapped caching for optimal performance');
    }

    // Platform-specific recommendations
    switch (this.platformInfo.platform) {
      case 'darwin':
        recommendations.push('macOS detected. All features should work optimally');
        break;
      case 'linux':
        recommendations.push('Linux detected. Ensure proper permissions for memory mapping');
        break;
      case 'win32':
        recommendations.push('Windows detected. Some WASM features may have reduced performance');
        break;
      default:
        recommendations.push('Unsupported platform detected. Fallback strategies will be used');
    }

    // Feature-specific recommendations
    if (!this.features.wasmSupported) {
      recommendations.push('WASM not supported. All operations will use JavaScript fallbacks');
    }

    if (!this.features.workerThreadsSupported) {
      recommendations.push('Worker threads not supported. Parallel processing will be limited');
    }

    return recommendations;
  }

  /**
   * Generate optimization settings
   */
  private generateOptimizations(): Array<{
    feature: string;
    enabled: boolean;
    reason: string;
    impact: 'high' | 'medium' | 'low';
  }> {
    const optimizations = [];

    // WASM optimization
    optimizations.push({
      feature: 'WASM',
      enabled: this.features.wasmSupported,
      reason: this.features.wasmSupported ? 'Platform supports WASM' : 'WASM not available',
      impact: 'high' as const
    });

    // Memory mapping optimization
    optimizations.push({
      feature: 'MemoryMapping',
      enabled: this.features.memoryMappingSupported && this.platformInfo.totalMemory > 2 * 1024 * 1024 * 1024,
      reason: this.features.memoryMappingSupported ? 'Sufficient memory available' : 'Limited memory or unsupported',
      impact: 'medium' as const
    });

    // Worker threads optimization
    optimizations.push({
      feature: 'WorkerThreads',
      enabled: this.features.workerThreadsSupported && this.platformInfo.cpuCount > 2,
      reason: this.features.workerThreadsSupported ? 'Multi-core system detected' : 'Single core or unsupported',
      impact: 'medium' as const
    });

    // Performance API optimization
    optimizations.push({
      feature: 'PerformanceMonitoring',
      enabled: this.features.performanceAPISupported,
      reason: this.features.performanceAPISupported ? 'Performance API available' : 'Performance API not supported',
      impact: 'low' as const
    });

    return optimizations;
  }

  /**
   * Generate fallback strategies
   */
  private generateFallbackStrategies(): Array<{
    feature: string;
    fallback: string;
    performance: number;
  }> {
    const strategies = [];

    if (!this.features.wasmSupported) {
      strategies.push({
        feature: 'WASM',
        fallback: 'JavaScript implementations',
        performance: 0.3 // 30% of WASM performance
      });
    }

    if (!this.features.memoryMappingSupported) {
      strategies.push({
        feature: 'MemoryMapping',
        fallback: 'Standard file I/O with caching',
        performance: 0.6 // 60% of memory-mapped performance
      });
    }

    if (!this.features.workerThreadsSupported) {
      strategies.push({
        feature: 'WorkerThreads',
        fallback: 'Sequential processing with setImmediate',
        performance: 0.4 // 40% of parallel performance
      });
    }

    return strategies;
  }

  /**
   * Test platform performance
   */
  async testPlatformPerformance(): Promise<{
    cpuScore: number;
    memoryScore: number;
    ioScore: number;
    overallScore: number;
  }> {
    const cpuScore = await this.testCPUPerformance();
    const memoryScore = await this.testMemoryPerformance();
    const ioScore = await this.testIOPerformance();
    
    const overallScore = (cpuScore + memoryScore + ioScore) / 3;

    return {
      cpuScore,
      memoryScore,
      ioScore,
      overallScore
    };
  }

  /**
   * Test CPU performance
   */
  private async testCPUPerformance(): Promise<number> {
    const startTime = Date.now();
    
    // Simple CPU-intensive task
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
      result += Math.sqrt(i);
    }
    
    const duration = Date.now() - startTime;
    
    // Score based on duration (lower is better, normalized to 0-100)
    return Math.max(0, 100 - duration / 10);
  }

  /**
   * Test memory performance
   */
  private async testMemoryPerformance(): Promise<number> {
    const startTime = Date.now();
    
    try {
      // Test memory allocation and access
      const size = 10 * 1024 * 1024; // 10MB
      const buffer = Buffer.allocUnsafe(size);
      
      // Write to buffer
      for (let i = 0; i < size; i += 1024) {
        buffer[i] = i % 256;
      }
      
      // Read from buffer
      let sum = 0;
      for (let i = 0; i < size; i += 1024) {
        sum += buffer[i];
      }
      
      const duration = Date.now() - startTime;
      return Math.max(0, 100 - duration / 5);
    } catch (error) {
      return 0; // Memory allocation failed
    }
  }

  /**
   * Test I/O performance
   */
  private async testIOPerformance(): Promise<number> {
    const startTime = Date.now();
    
    try {
      const fs = require('fs');
      const path = require('path');
      const testFile = path.join(os.tmpdir(), 'gql-generator-io-test.tmp');
      
      // Write test
      const testData = Buffer.alloc(1024 * 1024, 'test'); // 1MB
      fs.writeFileSync(testFile, testData);
      
      // Read test
      const readData = fs.readFileSync(testFile);
      
      // Cleanup
      fs.unlinkSync(testFile);
      
      const duration = Date.now() - startTime;
      return Math.max(0, 100 - duration / 20);
    } catch (error) {
      return 0; // I/O test failed
    }
  }

  /**
   * Get platform info
   */
  getPlatformInfo(): PlatformInfo {
    return { ...this.platformInfo };
  }

  /**
   * Get detected features
   */
  getFeatures(): CompatibilityFeatures {
    return { ...this.features };
  }

  /**
   * Check if a specific feature is supported
   */
  isFeatureSupported(feature: keyof CompatibilityFeatures): boolean {
    return this.features[feature];
  }

  /**
   * Get optimal configuration for current platform
   */
  getOptimalConfiguration(): {
    enableWASM: boolean;
    enableMemoryMapping: boolean;
    enableWorkerThreads: boolean;
    maxConcurrency: number;
    memoryThreshold: number;
  } {
    const memoryGB = this.platformInfo.totalMemory / (1024 * 1024 * 1024);
    
    return {
      enableWASM: this.features.wasmSupported,
      enableMemoryMapping: this.features.memoryMappingSupported && memoryGB > 2,
      enableWorkerThreads: this.features.workerThreadsSupported && this.platformInfo.cpuCount > 2,
      maxConcurrency: Math.min(this.platformInfo.cpuCount, 8),
      memoryThreshold: Math.floor(memoryGB * 0.8 * 1024 * 1024 * 1024) // 80% of available memory
    };
  }
}

/**
 * Global platform compatibility instance
 */
let globalPlatformCompatibility: PlatformCompatibility | null = null;

/**
 * Get or create global platform compatibility checker
 */
export function getGlobalPlatformCompatibility(): PlatformCompatibility {
  if (!globalPlatformCompatibility) {
    globalPlatformCompatibility = new PlatformCompatibility();
  }
  return globalPlatformCompatibility;
}
