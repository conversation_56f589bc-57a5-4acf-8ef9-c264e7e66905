import { EventEmitter } from 'events';
import { getGlobalStreamingBatchProcessor, type StreamingBatchConfig } from './streaming-batch-integration';
import { getGlobalMemoryMonitor } from './memory-pressure-monitor';
import type { TemplateChunk } from './streaming-template-renderer';

/**
 * Phase 2 Optimization: Large Schema Processing Optimizer
 * Specialized optimizations for schemas with hundreds of types
 */

export interface LargeSchemaConfig {
  typeCountThreshold: number; // Consider "large" when exceeding this count
  enableTypeGrouping: boolean; // Group related types for batch processing
  enablePriorityProcessing: boolean; // Process high-priority types first
  enableProgressiveGeneration: boolean; // Generate in phases
  maxConcurrentTypes: number; // Limit concurrent type processing
  enableMemoryOptimization: boolean; // Aggressive memory management
  debug: boolean;
}

export interface TypeGroup {
  name: string;
  types: string[];
  priority: number;
  estimatedComplexity: number;
  dependencies: string[];
}

export interface SchemaAnalysis {
  totalTypes: number;
  typeGroups: TypeGroup[];
  complexityScore: number;
  recommendedStrategy: 'standard' | 'streaming' | 'progressive' | 'chunked';
  estimatedMemoryUsage: number;
  recommendedBatchSize: number;
}

/**
 * Large schema processing optimizer
 */
export class LargeSchemaOptimizer extends EventEmitter {
  private config: Required<LargeSchemaConfig>;
  private memoryMonitor = getGlobalMemoryMonitor();

  constructor(config: Partial<LargeSchemaConfig> = {}) {
    super();
    
    this.config = {
      typeCountThreshold: 100,
      enableTypeGrouping: true,
      enablePriorityProcessing: true,
      enableProgressiveGeneration: true,
      maxConcurrentTypes: 50,
      enableMemoryOptimization: true,
      debug: false,
      ...config
    };
  }

  /**
   * Analyze schema complexity and recommend processing strategy
   */
  analyzeSchema(templateChunks: TemplateChunk[]): SchemaAnalysis {
    const totalTypes = templateChunks.length;
    
    if (this.config.debug) {
      console.log(`🔍 Analyzing schema with ${totalTypes} types...`);
    }

    // Group types by category and complexity
    const typeGroups = this.config.enableTypeGrouping ? 
      this.groupTypesByCategory(templateChunks) : 
      [{ name: 'all', types: templateChunks.map(c => c.templateName), priority: 1, estimatedComplexity: 1, dependencies: [] }];

    // Calculate complexity score
    const complexityScore = this.calculateComplexityScore(templateChunks, typeGroups);

    // Determine processing strategy
    let recommendedStrategy: 'standard' | 'streaming' | 'progressive' | 'chunked' = 'standard';
    if (totalTypes > this.config.typeCountThreshold) {
      if (complexityScore > 0.8) {
        recommendedStrategy = 'progressive';
      } else if (complexityScore > 0.6) {
        recommendedStrategy = 'chunked';
      } else {
        recommendedStrategy = 'streaming';
      }
    }

    // Estimate memory usage and recommend batch size
    const estimatedMemoryUsage = this.estimateMemoryUsage(templateChunks);
    const recommendedBatchSize = this.calculateOptimalBatchSize(totalTypes, complexityScore, estimatedMemoryUsage);

    const analysis: SchemaAnalysis = {
      totalTypes,
      typeGroups,
      complexityScore,
      recommendedStrategy,
      estimatedMemoryUsage,
      recommendedBatchSize
    };

    if (this.config.debug) {
      console.log(`📊 Schema analysis complete:`, {
        strategy: recommendedStrategy,
        complexity: Math.round(complexityScore * 100) + '%',
        groups: typeGroups.length,
        batchSize: recommendedBatchSize
      });
    }

    this.emit('analysisComplete', analysis);
    return analysis;
  }

  /**
   * Process large schema using optimized strategy
   */
  async processLargeSchema(templateChunks: TemplateChunk[]): Promise<void> {
    const analysis = this.analyzeSchema(templateChunks);
    
    if (this.config.debug) {
      console.log(`🚀 Processing large schema using ${analysis.recommendedStrategy} strategy`);
    }

    // Configure streaming processor based on analysis
    const streamingConfig: StreamingBatchConfig = {
      batchSize: analysis.recommendedBatchSize,
      maxMemoryUsage: Math.min(analysis.estimatedMemoryUsage * 1.5, 1024 * 1024 * 1024), // Cap at 1GB
      enableAdaptiveBatching: true,
      enableMemoryMonitoring: this.config.enableMemoryOptimization,
      debug: this.config.debug
    };

    const processor = getGlobalStreamingBatchProcessor(streamingConfig);

    try {
      switch (analysis.recommendedStrategy) {
        case 'progressive':
          await this.processProgressive(templateChunks, analysis, processor);
          break;
        case 'chunked':
          await this.processChunked(templateChunks, analysis, processor);
          break;
        case 'streaming':
          await this.processStreaming(templateChunks, processor);
          break;
        default:
          await this.processStandard(templateChunks, processor);
      }
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Progressive processing: Generate in phases based on priority and dependencies
   */
  private async processProgressive(
    templateChunks: TemplateChunk[],
    analysis: SchemaAnalysis,
    processor: any
  ): Promise<void> {
    if (this.config.debug) {
      console.log(`📈 Starting progressive processing with ${analysis.typeGroups.length} phases`);
    }

    // Sort groups by priority and dependencies
    const sortedGroups = this.sortGroupsByDependencies(analysis.typeGroups);

    for (let i = 0; i < sortedGroups.length; i++) {
      const group = sortedGroups[i];
      const groupChunks = templateChunks.filter(chunk => 
        group.types.includes(chunk.templateName)
      );

      if (this.config.debug) {
        console.log(`📦 Processing phase ${i + 1}/${sortedGroups.length}: ${group.name} (${groupChunks.length} types)`);
      }

      // Check memory pressure before each phase
      if (this.config.enableMemoryOptimization) {
        const memoryMetrics = this.memoryMonitor.getCurrentMetrics();
        if (memoryMetrics.memoryPressure > 0.8) {
          if (this.config.debug) {
            console.log('⏸️  Waiting for memory recovery before next phase...');
          }
          await this.memoryMonitor.waitForMemoryRecovery(0.6, 30000);
        }
      }

      await processor.processTemplatesAdaptive(groupChunks);
      
      this.emit('phaseComplete', {
        phase: i + 1,
        totalPhases: sortedGroups.length,
        groupName: group.name,
        typesProcessed: groupChunks.length
      });
    }
  }

  /**
   * Chunked processing: Split into manageable chunks
   */
  private async processChunked(
    templateChunks: TemplateChunk[],
    analysis: SchemaAnalysis,
    processor: any
  ): Promise<void> {
    const chunkSize = Math.min(this.config.maxConcurrentTypes, analysis.recommendedBatchSize);
    const chunks = this.chunkArray(templateChunks, chunkSize);

    if (this.config.debug) {
      console.log(`🔢 Processing ${chunks.length} chunks of ~${chunkSize} types each`);
    }

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      
      if (this.config.debug) {
        console.log(`📦 Processing chunk ${i + 1}/${chunks.length} (${chunk.length} types)`);
      }

      await processor.processTemplatesAdaptive(chunk);
      
      this.emit('chunkComplete', {
        chunk: i + 1,
        totalChunks: chunks.length,
        typesProcessed: chunk.length
      });

      // Brief pause between chunks for system recovery
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  /**
   * Streaming processing: Use streaming with optimized configuration
   */
  private async processStreaming(templateChunks: TemplateChunk[], processor: any): Promise<void> {
    if (this.config.debug) {
      console.log(`🌊 Processing ${templateChunks.length} types with streaming optimization`);
    }

    await processor.processTemplatesAdaptive(templateChunks);
  }

  /**
   * Standard processing: Use regular batch processing
   */
  private async processStandard(templateChunks: TemplateChunk[], processor: any): Promise<void> {
    if (this.config.debug) {
      console.log(`📋 Processing ${templateChunks.length} types with standard batch processing`);
    }

    await processor.processTemplates(templateChunks);
  }

  /**
   * Group types by category for better processing organization
   */
  private groupTypesByCategory(templateChunks: TemplateChunk[]): TypeGroup[] {
    const groups: Record<string, TypeGroup> = {};

    for (const chunk of templateChunks) {
      const category = this.categorizeType(chunk.templateName);
      
      if (!groups[category]) {
        groups[category] = {
          name: category,
          types: [],
          priority: this.getCategoryPriority(category),
          estimatedComplexity: 0,
          dependencies: []
        };
      }

      groups[category].types.push(chunk.templateName);
      groups[category].estimatedComplexity += this.estimateTypeComplexity(chunk);
    }

    // Normalize complexity scores
    Object.values(groups).forEach(group => {
      group.estimatedComplexity = group.estimatedComplexity / group.types.length;
    });

    return Object.values(groups);
  }

  /**
   * Categorize type based on template name
   */
  private categorizeType(templateName: string): string {
    if (templateName.includes('query')) return 'queries';
    if (templateName.includes('mutation')) return 'mutations';
    if (templateName.includes('subscription')) return 'subscriptions';
    if (templateName.includes('interface')) return 'interfaces';
    if (templateName.includes('union')) return 'unions';
    if (templateName.includes('enum')) return 'enums';
    if (templateName.includes('input')) return 'inputs';
    return 'types';
  }

  /**
   * Get processing priority for category
   */
  private getCategoryPriority(category: string): number {
    const priorities: Record<string, number> = {
      'enums': 1,      // Highest priority - simple types
      'inputs': 2,     // Input types
      'interfaces': 3, // Interfaces before implementations
      'types': 4,      // Regular types
      'unions': 5,     // Union types
      'queries': 6,    // Query resolvers
      'mutations': 7,  // Mutation resolvers
      'subscriptions': 8 // Lowest priority
    };
    return priorities[category] || 5;
  }

  /**
   * Estimate complexity of a single type
   */
  private estimateTypeComplexity(chunk: TemplateChunk): number {
    // Simple heuristic based on data size and template complexity
    const dataSize = JSON.stringify(chunk.data).length;
    const templateComplexity = chunk.templateName.split('/').length; // Path depth
    
    return Math.min(1, (dataSize / 10000) + (templateComplexity / 10));
  }

  /**
   * Calculate overall complexity score
   */
  private calculateComplexityScore(templateChunks: TemplateChunk[], typeGroups: TypeGroup[]): number {
    const avgComplexity = typeGroups.reduce((sum, group) => sum + group.estimatedComplexity, 0) / typeGroups.length;
    const sizeComplexity = Math.min(1, templateChunks.length / 500); // Normalize to 500 types
    
    return (avgComplexity + sizeComplexity) / 2;
  }

  /**
   * Estimate memory usage for template chunks
   */
  private estimateMemoryUsage(templateChunks: TemplateChunk[]): number {
    let totalSize = 0;
    
    for (const chunk of templateChunks) {
      // Estimate memory usage: data size + template overhead + output buffer
      const dataSize = JSON.stringify(chunk.data).length;
      const estimatedOutput = dataSize * 3; // Rough multiplier for generated code
      totalSize += dataSize + estimatedOutput + 1024; // 1KB overhead per chunk
    }
    
    return totalSize;
  }

  /**
   * Calculate optimal batch size based on analysis
   */
  private calculateOptimalBatchSize(typeCount: number, complexity: number, memoryUsage: number): number {
    // Base batch size
    let batchSize = 20;
    
    // Adjust for type count
    if (typeCount > 500) batchSize = 10;
    else if (typeCount > 200) batchSize = 15;
    
    // Adjust for complexity
    if (complexity > 0.8) batchSize = Math.floor(batchSize * 0.5);
    else if (complexity > 0.6) batchSize = Math.floor(batchSize * 0.7);
    
    // Adjust for memory usage
    const memoryGB = memoryUsage / (1024 * 1024 * 1024);
    if (memoryGB > 1) batchSize = Math.floor(batchSize * 0.5);
    else if (memoryGB > 0.5) batchSize = Math.floor(batchSize * 0.7);
    
    return Math.max(5, batchSize); // Minimum batch size of 5
  }

  /**
   * Sort groups by dependencies and priority
   */
  private sortGroupsByDependencies(groups: TypeGroup[]): TypeGroup[] {
    return groups.sort((a, b) => {
      // First by priority
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // Then by complexity (simpler first)
      return a.estimatedComplexity - b.estimatedComplexity;
    });
  }

  /**
   * Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get optimizer statistics
   */
  getStats() {
    return {
      config: this.config,
      memoryMonitor: this.memoryMonitor.getStats()
    };
  }
}

/**
 * Global large schema optimizer instance
 */
let globalLargeSchemaOptimizer: LargeSchemaOptimizer | null = null;

/**
 * Get or create global large schema optimizer
 */
export function getGlobalLargeSchemaOptimizer(config?: Partial<LargeSchemaConfig>): LargeSchemaOptimizer {
  if (!globalLargeSchemaOptimizer) {
    globalLargeSchemaOptimizer = new LargeSchemaOptimizer(config);
  }
  return globalLargeSchemaOptimizer;
}
