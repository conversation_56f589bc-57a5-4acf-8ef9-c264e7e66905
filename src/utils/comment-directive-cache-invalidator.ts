import * as chokidar from 'chokidar';
import * as path from 'path';
import { getGlobalCommentDirectiveCache } from './comment-directive-cache';
import { getGlobalBatchDirectiveParser } from './batch-directive-parser';

/**
 * File change event types
 */
export type FileChangeEvent = 'add' | 'change' | 'unlink';

/**
 * Cache invalidation options
 */
export interface CacheInvalidationOptions {
  watchPatterns?: string[];
  ignorePatterns?: string[];
  enableLogging?: boolean;
  debounceMs?: number;
}

/**
 * Cache invalidation statistics
 */
export interface CacheInvalidationStats {
  filesWatched: number;
  invalidationEvents: number;
  lastInvalidationTime: number | null;
  watcherActive: boolean;
}

/**
 * Automatic cache invalidation system for comment directive caches
 * Watches schema files and invalidates cache when files change
 */
export class CommentDirectiveCacheInvalidator {
  private watcher: chokidar.FSWatcher | null = null;
  private cache = getGlobalCommentDirectiveCache();
  private batchParser = getGlobalBatchDirectiveParser();
  private options: Required<CacheInvalidationOptions>;
  private stats: CacheInvalidationStats = {
    filesWatched: 0,
    invalidationEvents: 0,
    lastInvalidationTime: null,
    watcherActive: false
  };
  private debounceTimers = new Map<string, NodeJS.Timeout>();

  constructor(options: CacheInvalidationOptions = {}) {
    this.options = {
      watchPatterns: ['**/*.gql', '**/*.graphql'],
      ignorePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**'],
      enableLogging: false,
      debounceMs: 100,
      ...options
    };
  }

  /**
   * Start watching schema files for changes
   */
  async startWatching(basePaths: string[] = ['./schema']): Promise<void> {
    if (this.watcher) {
      await this.stopWatching();
    }

    try {
      // Create file patterns for watching
      const watchPatterns: string[] = [];
      for (const basePath of basePaths) {
        for (const pattern of this.options.watchPatterns) {
          watchPatterns.push(path.join(basePath, pattern));
        }
      }

      this.watcher = chokidar.watch(watchPatterns, {
        ignored: this.options.ignorePatterns,
        persistent: true,
        ignoreInitial: true,
        awaitWriteFinish: {
          stabilityThreshold: 100,
          pollInterval: 50
        }
      });

      // Set up event handlers
      this.watcher.on('add', (filePath) => this.handleFileChange(filePath, 'add'));
      this.watcher.on('change', (filePath) => this.handleFileChange(filePath, 'change'));
      this.watcher.on('unlink', (filePath) => this.handleFileChange(filePath, 'unlink'));
      
      this.watcher.on('ready', () => {
        this.stats.watcherActive = true;
        this.stats.filesWatched = this.watcher?.getWatched() ? 
          Object.values(this.watcher.getWatched()).reduce((total, files) => total + files.length, 0) : 0;
        
        if (this.options.enableLogging) {
          console.log(`👁️ CommentDirectiveCacheInvalidator: Watching ${this.stats.filesWatched} files`);
        }
      });

      this.watcher.on('error', (error) => {
        console.error('❌ CommentDirectiveCacheInvalidator: Watcher error:', error);
      });

    } catch (error) {
      console.error('❌ CommentDirectiveCacheInvalidator: Failed to start watching:', error);
      throw error;
    }
  }

  /**
   * Stop watching files
   */
  async stopWatching(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = null;
      this.stats.watcherActive = false;
      
      // Clear any pending debounce timers
      for (const timer of this.debounceTimers.values()) {
        clearTimeout(timer);
      }
      this.debounceTimers.clear();

      if (this.options.enableLogging) {
        console.log('🛑 CommentDirectiveCacheInvalidator: Stopped watching');
      }
    }
  }

  /**
   * Handle file change events with debouncing
   */
  private handleFileChange(filePath: string, event: FileChangeEvent): void {
    // Clear existing debounce timer for this file
    const existingTimer = this.debounceTimers.get(filePath);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new debounce timer
    const timer = setTimeout(() => {
      this.invalidateFileCache(filePath, event);
      this.debounceTimers.delete(filePath);
    }, this.options.debounceMs);

    this.debounceTimers.set(filePath, timer);
  }

  /**
   * Invalidate cache for a specific file
   */
  private invalidateFileCache(filePath: string, event: FileChangeEvent): void {
    try {
      const normalizedPath = path.resolve(filePath);
      
      // Invalidate file content cache
      this.cache.invalidateFile(normalizedPath);
      
      // Clear batch parser cache for this file
      this.batchParser.clearCache();
      
      // Update statistics
      this.stats.invalidationEvents++;
      this.stats.lastInvalidationTime = Date.now();

      if (this.options.enableLogging) {
        console.log(`🗑️ Cache invalidated for ${event}: ${normalizedPath}`);
      }

      // Emit invalidation event for other systems to listen to
      this.emitInvalidationEvent(normalizedPath, event);

    } catch (error) {
      console.error(`❌ Error invalidating cache for ${filePath}:`, error);
    }
  }

  /**
   * Manually invalidate cache for specific files
   */
  invalidateFiles(filePaths: string[]): void {
    for (const filePath of filePaths) {
      this.invalidateFileCache(filePath, 'change');
    }
  }

  /**
   * Invalidate all caches
   */
  invalidateAll(): void {
    this.cache.clear();
    this.batchParser.clearCache();
    this.stats.invalidationEvents++;
    this.stats.lastInvalidationTime = Date.now();

    if (this.options.enableLogging) {
      console.log('🧹 All caches invalidated');
    }
  }

  /**
   * Get invalidation statistics
   */
  getStatistics(): CacheInvalidationStats {
    return { ...this.stats };
  }

  /**
   * Check if a file is being watched
   */
  isWatching(filePath?: string): boolean {
    if (!this.watcher) return false;
    
    if (filePath) {
      const watched = this.watcher.getWatched();
      const dir = path.dirname(path.resolve(filePath));
      const file = path.basename(filePath);
      return watched[dir]?.includes(file) || false;
    }
    
    return this.stats.watcherActive;
  }

  /**
   * Get list of watched files
   */
  getWatchedFiles(): string[] {
    if (!this.watcher) return [];
    
    const watched = this.watcher.getWatched();
    const files: string[] = [];
    
    for (const [dir, fileList] of Object.entries(watched)) {
      for (const file of fileList) {
        files.push(path.join(dir, file));
      }
    }
    
    return files;
  }

  /**
   * Emit invalidation event for external listeners
   */
  private emitInvalidationEvent(filePath: string, event: FileChangeEvent): void {
    // This could be extended to use EventEmitter for external listeners
    // For now, we'll just log the event
    if (this.options.enableLogging) {
      console.log(`📡 Invalidation event emitted: ${event} ${filePath}`);
    }
  }

  /**
   * Add additional watch patterns
   */
  addWatchPatterns(patterns: string[]): void {
    this.options.watchPatterns.push(...patterns);
    
    if (this.options.enableLogging) {
      console.log(`➕ Added watch patterns: ${patterns.join(', ')}`);
    }
  }

  /**
   * Remove watch patterns
   */
  removeWatchPatterns(patterns: string[]): void {
    for (const pattern of patterns) {
      const index = this.options.watchPatterns.indexOf(pattern);
      if (index > -1) {
        this.options.watchPatterns.splice(index, 1);
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`➖ Removed watch patterns: ${patterns.join(', ')}`);
    }
  }

  /**
   * Update debounce timing
   */
  setDebounceMs(ms: number): void {
    this.options.debounceMs = ms;
    
    if (this.options.enableLogging) {
      console.log(`⏱️ Updated debounce timing to ${ms}ms`);
    }
  }

  /**
   * Enable or disable logging
   */
  setLogging(enabled: boolean): void {
    this.options.enableLogging = enabled;
  }
}

// Global invalidator instance
let globalCacheInvalidator: CommentDirectiveCacheInvalidator | null = null;

/**
 * Get the global cache invalidator instance
 */
export function getGlobalCacheInvalidator(options?: CacheInvalidationOptions): CommentDirectiveCacheInvalidator {
  if (!globalCacheInvalidator) {
    globalCacheInvalidator = new CommentDirectiveCacheInvalidator(options);
  }
  return globalCacheInvalidator;
}

/**
 * Reset the global invalidator (useful for testing)
 */
export function resetGlobalCacheInvalidator(): void {
  if (globalCacheInvalidator) {
    globalCacheInvalidator.stopWatching().catch(console.error);
  }
  globalCacheInvalidator = null;
}

/**
 * Auto-start cache invalidation for common schema paths
 */
export async function autoStartCacheInvalidation(
  schemaPaths: string[] = ['./schema'],
  options?: CacheInvalidationOptions
): Promise<CommentDirectiveCacheInvalidator> {
  const invalidator = getGlobalCacheInvalidator(options);
  await invalidator.startWatching(schemaPaths);
  return invalidator;
}
