import Handlebars from 'handlebars';
import crypto from 'crypto';

/**
 * Cache entry for compiled templates
 */
interface CacheEntry {
  /** Compiled Handlebars template function */
  template: HandlebarsTemplateDelegate;
  /** Timestamp when the entry was created */
  createdAt: number;
  /** Timestamp when the entry was last accessed */
  lastAccessedAt: number;
  /** Number of times this template has been accessed */
  accessCount: number;
  /** Size of the original template string in bytes */
  templateSize: number;
}

/**
 * Cache statistics for monitoring and optimization
 */
export interface CacheStatistics {
  /** Total number of cache hits */
  hits: number;
  /** Total number of cache misses */
  misses: number;
  /** Cache hit rate as a percentage */
  hitRate: number;
  /** Total number of entries in the cache */
  entryCount: number;
  /** Total memory usage estimate in bytes */
  estimatedMemoryUsage: number;
  /** Number of evictions performed */
  evictions: number;
  /** Average compilation time in milliseconds */
  averageCompilationTime: number;
  /** Total compilation time saved by caching in milliseconds */
  totalTimeSaved: number;
}

/**
 * Configuration options for the template compilation cache
 */
export interface CacheOptions {
  /** Maximum number of entries in the cache (default: 1000) */
  maxEntries?: number;
  /** Maximum memory usage in bytes (default: 50MB) */
  maxMemoryUsage?: number;
  /** Time-to-live for cache entries in milliseconds (default: 1 hour) */
  ttl?: number;
  /** Whether to enable performance tracking (default: true) */
  enablePerformanceTracking?: boolean;
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
}

/**
 * High-performance template compilation cache with LRU eviction
 * Optimizes Handlebars template compilation by caching compiled templates
 */
export class TemplateCompilationCache {
  private cache = new Map<string, CacheEntry>();
  private accessOrder: string[] = [];
  private statistics: CacheStatistics = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    entryCount: 0,
    estimatedMemoryUsage: 0,
    evictions: 0,
    averageCompilationTime: 0,
    totalTimeSaved: 0,
  };
  private compilationTimes: number[] = [];
  private readonly options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      maxEntries: 1000,
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      ttl: 60 * 60 * 1000, // 1 hour
      enablePerformanceTracking: true,
      enableLogging: false,
      ...options,
    };

    if (this.options.enableLogging) {
      console.log('🚀 TemplateCompilationCache initialized with options:', this.options);
    }
  }

  /**
   * Generate a cache key from template content
   * @param templateContent The template string content
   * @returns A unique cache key
   */
  private generateCacheKey(templateContent: string): string {
    return crypto.createHash('sha256').update(templateContent).digest('hex');
  }

  /**
   * Update access order for LRU eviction
   * @param key The cache key that was accessed
   */
  private updateAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(key);
  }

  /**
   * Check if a cache entry has expired
   * @param entry The cache entry to check
   * @returns True if the entry has expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.createdAt > this.options.ttl;
  }

  /**
   * Estimate memory usage of a cache entry
   * @param entry The cache entry
   * @returns Estimated memory usage in bytes
   */
  private estimateEntrySize(entry: CacheEntry): number {
    // Rough estimation: template size + overhead for compiled function
    return entry.templateSize + 1024; // 1KB overhead estimate
  }

  /**
   * Perform cache eviction based on LRU and memory constraints
   */
  private evictIfNeeded(): void {
    const now = Date.now();
    let evicted = 0;

    // First, remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
          this.accessOrder.splice(index, 1);
        }
        evicted++;
      }
    }

    // Then, evict LRU entries if we exceed limits
    while (
      this.cache.size > this.options.maxEntries ||
      this.statistics.estimatedMemoryUsage > this.options.maxMemoryUsage
    ) {
      if (this.accessOrder.length === 0) break;

      const oldestKey = this.accessOrder.shift()!;
      const entry = this.cache.get(oldestKey);
      if (entry) {
        this.cache.delete(oldestKey);
        evicted++;
      }
    }

    if (evicted > 0) {
      this.statistics.evictions += evicted;
      this.updateStatistics();

      // Only log evictions in development mode
      if (this.options.enableLogging && process.env.NODE_ENV === 'development') {
        console.log(`🗑️  Evicted ${evicted} entries`);
      }
    }
  }

  /**
   * Update cache statistics
   */
  private updateStatistics(): void {
    this.statistics.entryCount = this.cache.size;
    this.statistics.hitRate = this.statistics.hits + this.statistics.misses > 0
      ? (this.statistics.hits / (this.statistics.hits + this.statistics.misses)) * 100
      : 0;

    // Calculate estimated memory usage
    let totalMemory = 0;
    for (const entry of this.cache.values()) {
      totalMemory += this.estimateEntrySize(entry);
    }
    this.statistics.estimatedMemoryUsage = totalMemory;

    // Calculate average compilation time
    if (this.compilationTimes.length > 0) {
      this.statistics.averageCompilationTime = 
        this.compilationTimes.reduce((sum, time) => sum + time, 0) / this.compilationTimes.length;
    }
  }

  /**
   * Compile a template with caching
   * @param templateContent The template string to compile
   * @param compileOptions Optional Handlebars compile options
   * @returns Compiled template function
   */
  public compile(
    templateContent: string,
    compileOptions: CompileOptions = { noEscape: true }
  ): HandlebarsTemplateDelegate {
    const cacheKey = this.generateCacheKey(templateContent);
    const now = Date.now();

    // Check cache first
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry && !this.isExpired(cachedEntry)) {
      // Cache hit
      cachedEntry.lastAccessedAt = now;
      cachedEntry.accessCount++;
      this.updateAccessOrder(cacheKey);
      this.statistics.hits++;

      if (this.options.enablePerformanceTracking && this.statistics.averageCompilationTime > 0) {
        this.statistics.totalTimeSaved += this.statistics.averageCompilationTime;
      }

      // Skip verbose cache hit logging to avoid performance impact
      if (this.options.enableLogging && process.env.NODE_ENV === 'development') {
        console.log(`✅ Cache hit: ${cacheKey.substring(0, 8)}...`);
      }

      return cachedEntry.template;
    }

    // Cache miss - compile the template
    this.statistics.misses++;
    const compilationStart = Date.now();
    
    try {
      const compiledTemplate = Handlebars.compile(templateContent, compileOptions);
      const compilationTime = Date.now() - compilationStart;

      if (this.options.enablePerformanceTracking) {
        this.compilationTimes.push(compilationTime);
        // Keep only the last 1000 compilation times for average calculation
        if (this.compilationTimes.length > 1000) {
          this.compilationTimes.shift();
        }
      }

      // Create cache entry
      const entry: CacheEntry = {
        template: compiledTemplate,
        createdAt: now,
        lastAccessedAt: now,
        accessCount: 1,
        templateSize: Buffer.byteLength(templateContent, 'utf8'),
      };

      // Add to cache
      this.cache.set(cacheKey, entry);
      this.updateAccessOrder(cacheKey);

      // Perform eviction if needed
      this.evictIfNeeded();
      this.updateStatistics();

      // Skip verbose compilation logging to avoid performance impact
      if (this.options.enableLogging && process.env.NODE_ENV === 'development') {
        console.log(`🔄 Compiled: ${cacheKey.substring(0, 8)}... (${compilationTime}ms)`);
      }

      return compiledTemplate;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`❌ Template compilation failed for key: ${cacheKey.substring(0, 8)}...`, error);
      }
      throw error;
    }
  }

  /**
   * Get current cache statistics
   * @returns Current cache statistics
   */
  public getStatistics(): CacheStatistics {
    this.updateStatistics();
    return { ...this.statistics };
  }

  /**
   * Clear the entire cache
   */
  public clear(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.statistics = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      entryCount: 0,
      estimatedMemoryUsage: 0,
      evictions: 0,
      averageCompilationTime: 0,
      totalTimeSaved: 0,
    };
    this.compilationTimes = [];

    if (this.options.enableLogging) {
      console.log('🧹 Template cache cleared');
    }
  }

  /**
   * Get cache entry count
   * @returns Number of entries in the cache
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * Check if the cache has a specific template
   * @param templateContent The template content to check
   * @returns True if the template is cached
   */
  public has(templateContent: string): boolean {
    const cacheKey = this.generateCacheKey(templateContent);
    const entry = this.cache.get(cacheKey);
    return entry !== undefined && !this.isExpired(entry);
  }

  /**
   * Remove a specific template from the cache
   * @param templateContent The template content to remove
   * @returns True if the template was removed
   */
  public delete(templateContent: string): boolean {
    const cacheKey = this.generateCacheKey(templateContent);
    const deleted = this.cache.delete(cacheKey);
    
    if (deleted) {
      const index = this.accessOrder.indexOf(cacheKey);
      if (index > -1) {
        this.accessOrder.splice(index, 1);
      }
      this.updateStatistics();
    }

    return deleted;
  }
}

// Global singleton instance
let globalCache: TemplateCompilationCache | null = null;

/**
 * Get the global template compilation cache instance
 * @param options Optional cache configuration (only used on first call)
 * @returns Global cache instance
 */
export function getGlobalTemplateCache(options?: CacheOptions): TemplateCompilationCache {
  if (!globalCache) {
    globalCache = new TemplateCompilationCache(options);
  }
  return globalCache;
}

/**
 * Reset the global template compilation cache
 * Useful for testing or when configuration changes
 */
export function resetGlobalTemplateCache(): void {
  globalCache = null;
}
