/**
 * Worker thread for parallel type map scanning
 * 
 * This worker handles individual file scanning for the parallel type map scanner.
 * It uses the existing TypeScript parsing infrastructure but focuses only on
 * building type information without any code generation.
 */

import { parentPort } from 'worker_threads';
import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import { parseTypeScriptFile } from './ts-parser';

import type {
  ParsedFileInfo,
  MethodCallInfo,
  ImportInfo,
  FieldInfo,
  ContextInfo
} from './type-map';

/**
 * Worker task interface
 */
interface WorkerTask {
  id: string;
  filePath: string;
  type: 'scan-file';
}

/**
 * Worker result interface
 */
interface WorkerResult {
  taskId: string;
  success: boolean;
  filePath: string;
  result?: {
    methodCalls: MethodCallInfo[];
    imports: ImportInfo[];
    fields: FieldInfo[];
    contexts: ContextInfo[];
    fileInfo: ParsedFileInfo;
  };
  error?: string;
  duration: number;
}

/**
 * Scan a single TypeScript file for type information
 */
async function scanFile(filePath: string): Promise<{
  methodCalls: MethodCallInfo[];
  imports: ImportInfo[];
  fields: FieldInfo[];
  contexts: ContextInfo[];
  fileInfo: ParsedFileInfo;
}> {
  const startTime = Date.now();
  
  // Get file stats
  const stats = await fs.stat(filePath);
  const content = await fs.readFile(filePath, 'utf8');
  const contentHash = crypto.createHash('sha256').update(content).digest('hex');
  
  // Create file info
  const fileInfo: ParsedFileInfo = {
    filePath,
    size: stats.size,
    lastModified: stats.mtime.getTime(),
    contentHash,
    parsedAt: Date.now(),
    parser: 'worker',
    parseDuration: 0 // Will be updated below
  };

  // Parse the TypeScript file
  const parsedStructure = await parseTypeScriptFile(filePath);

  // Extract type information
  const methodCalls: MethodCallInfo[] = [];
  const imports: ImportInfo[] = [];
  const fields: FieldInfo[] = [];
  const contexts: ContextInfo[] = [];

  if (parsedStructure) {
    // Extract imports
    for (const importStatement of parsedStructure.imports || []) {
      const importInfo = parseImportStatement(importStatement, fileInfo);
      if (importInfo) {
        imports.push(importInfo);
      }
    }

    // Extract decorators and directives from content
    const decoratorInfo = extractDecoratorsFromContent(content, fileInfo);
    methodCalls.push(...decoratorInfo.methodCalls);
    fields.push(...decoratorInfo.fields);
    contexts.push(...decoratorInfo.contexts);
  }

  // Update parse duration
  fileInfo.parseDuration = Date.now() - startTime;

  return {
    methodCalls,
    imports,
    fields,
    contexts,
    fileInfo
  };
}

/**
 * Parse import statement to extract import information
 */
function parseImportStatement(importStatement: string, fileInfo: ParsedFileInfo): ImportInfo | null {
  try {
    // Simple regex-based import parsing
    const importRegex = /import\s+(?:(\w+)(?:\s*,\s*)?)?(?:\{\s*([^}]+)\s*\})?(?:\s*as\s+(\w+))?\s+from\s+['"]([^'"]+)['"]/;
    const match = importStatement.match(importRegex);
    
    if (!match) return null;
    
    const [, defaultImport, namedImportsStr, namespaceImport, module] = match;
    const namedImports = namedImportsStr 
      ? namedImportsStr.split(',').map(s => s.trim()).filter(Boolean)
      : [];

    return {
      statement: importStatement,
      module,
      namedImports,
      defaultImport,
      namespaceImport,
      sourceFile: fileInfo,
      lineNumber: 0 // TODO: Extract actual line number
    };
  } catch (error) {
    return null;
  }
}

/**
 * Extract decorator information from file content
 */
function extractDecoratorsFromContent(content: string, fileInfo: ParsedFileInfo): {
  methodCalls: MethodCallInfo[];
  fields: FieldInfo[];
  contexts: ContextInfo[];
} {
  const methodCalls: MethodCallInfo[] = [];
  const fields: FieldInfo[] = [];
  const contexts: ContextInfo[] = [];

  // Extract @GQLMethodCall decorators
  const methodCallRegex = /@GQLMethodCall\s*\(\s*([^)]+)\s*\)/g;
  let match;
  
  while ((match = methodCallRegex.exec(content)) !== null) {
    try {
      const args = match[1];
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      // Parse arguments (simplified)
      const methodCallInfo = parseMethodCallArgs(args, fileInfo, lineNumber);
      if (methodCallInfo) {
        methodCalls.push(methodCallInfo);
      }
    } catch (error) {
      // Skip invalid decorators
    }
  }

  // Extract @GQLField decorators
  const fieldRegex = /@GQLField\s*\(\s*([^)]+)\s*\)/g;
  
  while ((match = fieldRegex.exec(content)) !== null) {
    try {
      const args = match[1];
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      // Parse arguments (simplified)
      const fieldInfo = parseFieldArgs(args, fileInfo, lineNumber);
      if (fieldInfo) {
        fields.push(fieldInfo);
      }
    } catch (error) {
      // Skip invalid decorators
    }
  }

  // Extract @GQLContext decorators
  const contextRegex = /@GQLContext\s*\(\s*([^)]+)\s*\)/g;
  
  while ((match = contextRegex.exec(content)) !== null) {
    try {
      const args = match[1];
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      // Parse arguments (simplified)
      const contextInfo = parseContextArgs(args, fileInfo, lineNumber);
      if (contextInfo) {
        contexts.push(contextInfo);
      }
    } catch (error) {
      // Skip invalid decorators
    }
  }

  return { methodCalls, fields, contexts };
}

/**
 * Parse method call decorator arguments
 */
function parseMethodCallArgs(args: string, fileInfo: ParsedFileInfo, lineNumber: number): MethodCallInfo | null {
  try {
    // Simple argument parsing - in practice, this would be more sophisticated
    const cleanArgs = args.replace(/['"]/g, '').trim();
    
    return {
      typeName: 'Unknown', // Would be extracted from context
      fieldName: 'unknown', // Would be extracted from context
      call: cleanArgs,
      sourceFile: fileInfo,
      lineNumber
    };
  } catch (error) {
    return null;
  }
}

/**
 * Parse field decorator arguments
 */
function parseFieldArgs(args: string, fileInfo: ParsedFileInfo, lineNumber: number): FieldInfo | null {
  try {
    // Simple argument parsing
    const cleanArgs = args.replace(/['"]/g, '').trim();
    
    return {
      typeName: 'Unknown', // Would be extracted from context
      fieldName: 'unknown', // Would be extracted from context
      fieldType: cleanArgs,
      sourceFile: fileInfo,
      lineNumber
    };
  } catch (error) {
    return null;
  }
}

/**
 * Parse context decorator arguments
 */
function parseContextArgs(args: string, fileInfo: ParsedFileInfo, lineNumber: number): ContextInfo | null {
  try {
    // Simple argument parsing
    const cleanArgs = args.replace(/['"]/g, '').trim();
    
    return {
      contextType: cleanArgs,
      sourceFile: fileInfo,
      lineNumber
    };
  } catch (error) {
    return null;
  }
}

/**
 * Handle incoming messages from main thread
 */
if (parentPort) {
  parentPort.on('message', async (task: WorkerTask) => {
    const startTime = Date.now();
    
    try {
      const result = await scanFile(task.filePath);
      
      const workerResult: WorkerResult = {
        taskId: task.id,
        success: true,
        filePath: task.filePath,
        result,
        duration: Date.now() - startTime
      };
      
      parentPort!.postMessage(workerResult);
    } catch (error) {
      const workerResult: WorkerResult = {
        taskId: task.id,
        success: false,
        filePath: task.filePath,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      };
      
      parentPort!.postMessage(workerResult);
    }
  });
}
