import { TemplateRegistryEntry } from './template-precompilation-service';

/**
 * Comprehensive catalog of all template constants used in the gql-generator codebase
 * This catalog is used for template pre-compilation and optimization
 */

// Import all template constants
import { RESOLVER_TEMPLATE } from '@generators/templates/resolver-template';
import { RESOLVE_TYPE_TEMPLATE } from '@generators/templates/resolve-type-template';
import { 
  INDIVIDUAL_MUTATION_TEST_TEMPLATE, 
  GROUPED_MUTATION_TEST_TEMPLATE 
} from '@generators/test-generators/templates/mutation-test-template';
import { MOCK_CONTEXT_TEMPLATE } from '@generators/test-generators/templates/mock-context-template';

/**
 * Template catalog with metadata for pre-compilation
 */
export const TEMPLATE_CATALOG: TemplateRegistryEntry[] = [
  // Core resolver templates (highest priority)
  {
    id: 'resolver-template',
    content: RESOLVER_TEMPLATE,
    description: 'Main resolver template for field resolvers',
    modulePath: '@generators/templates/resolver-template',
    priority: 100, // Highest priority - most frequently used
  },
  {
    id: 'resolve-type-template',
    content: RESOLVE_TYPE_TEMPLATE,
    description: 'Template for interface/union/object __resolveType functions',
    modulePath: '@generators/templates/resolve-type-template',
    priority: 90, // High priority - used for type resolution
  },

  // Test generation templates (medium priority)
  {
    id: 'individual-mutation-test-template',
    content: INDIVIDUAL_MUTATION_TEST_TEMPLATE,
    description: 'Template for individual mutation test files',
    modulePath: '@generators/test-generators/templates/mutation-test-template',
    priority: 70, // Medium priority - used for test generation
  },
  {
    id: 'grouped-mutation-test-template',
    content: GROUPED_MUTATION_TEST_TEMPLATE,
    description: 'Template for grouped mutation test files',
    modulePath: '@generators/test-generators/templates/mutation-test-template',
    priority: 65, // Medium priority - used for test generation
  },
  {
    id: 'mock-context-template',
    content: MOCK_CONTEXT_TEMPLATE,
    description: 'Template for generating mock context files for tests',
    modulePath: '@generators/test-generators/templates/mock-context-template',
    priority: 60, // Medium priority - used for test utilities
  },
];

/**
 * Template usage statistics for optimization insights
 */
export interface TemplateUsageStats {
  /** Template ID */
  id: string;
  /** Estimated usage frequency (compilations per generation cycle) */
  estimatedUsageFrequency: number;
  /** Template complexity score (1-10, higher = more complex) */
  complexityScore: number;
  /** Average template size in bytes */
  averageSize: number;
  /** Generator modules that use this template */
  usedByModules: string[];
}

/**
 * Usage statistics for template optimization
 */
export const TEMPLATE_USAGE_STATS: TemplateUsageStats[] = [
  {
    id: 'resolver-template',
    estimatedUsageFrequency: 500, // Very high - used for every field resolver
    complexityScore: 8, // Complex with many conditionals and helpers
    averageSize: 2048, // ~2KB template
    usedByModules: [
      'field-resolver-generator',
      'object-type-processor',
      'interface-union-processor',
    ],
  },
  {
    id: 'resolve-type-template',
    estimatedUsageFrequency: 50, // Medium - used for interfaces/unions
    complexityScore: 6, // Moderately complex
    averageSize: 1024, // ~1KB template
    usedByModules: [
      'type-resolver-generator',
      'interface-union-processor',
    ],
  },
  {
    id: 'individual-mutation-test-template',
    estimatedUsageFrequency: 100, // Medium-high - used for each mutation
    complexityScore: 7, // Complex with test setup logic
    averageSize: 1536, // ~1.5KB template
    usedByModules: [
      'mutation-test-generator',
    ],
  },
  {
    id: 'grouped-mutation-test-template',
    estimatedUsageFrequency: 20, // Lower - used for grouped tests
    complexityScore: 6, // Moderately complex
    averageSize: 1280, // ~1.25KB template
    usedByModules: [
      'mutation-test-generator',
    ],
  },
  {
    id: 'mock-context-template',
    estimatedUsageFrequency: 10, // Low - used once per context type
    complexityScore: 4, // Simple template
    averageSize: 768, // ~0.75KB template
    usedByModules: [
      'mutation-test-generator',
    ],
  },
];

/**
 * Get all template registry entries for pre-compilation
 * @returns Array of template registry entries
 */
export function getAllTemplateEntries(): TemplateRegistryEntry[] {
  return [...TEMPLATE_CATALOG];
}

/**
 * Get template entries sorted by priority (highest first)
 * @returns Array of template registry entries sorted by priority
 */
export function getTemplateEntriesByPriority(): TemplateRegistryEntry[] {
  return [...TEMPLATE_CATALOG].sort((a, b) => (b.priority || 0) - (a.priority || 0));
}

/**
 * Get template entries sorted by estimated usage frequency
 * @returns Array of template registry entries sorted by usage frequency
 */
export function getTemplateEntriesByUsage(): TemplateRegistryEntry[] {
  const usageMap = new Map(TEMPLATE_USAGE_STATS.map(stat => [stat.id, stat.estimatedUsageFrequency]));
  
  return [...TEMPLATE_CATALOG].sort((a, b) => {
    const usageA = usageMap.get(a.id) || 0;
    const usageB = usageMap.get(b.id) || 0;
    return usageB - usageA;
  });
}

/**
 * Get high-priority templates for immediate pre-compilation
 * @param minPriority Minimum priority threshold (default: 80)
 * @returns Array of high-priority template entries
 */
export function getHighPriorityTemplates(minPriority: number = 80): TemplateRegistryEntry[] {
  return TEMPLATE_CATALOG.filter(entry => (entry.priority || 0) >= minPriority);
}

/**
 * Get templates used by a specific generator module
 * @param moduleName Name of the generator module
 * @returns Array of template entries used by the module
 */
export function getTemplatesForModule(moduleName: string): TemplateRegistryEntry[] {
  const moduleTemplateIds = TEMPLATE_USAGE_STATS
    .filter(stat => stat.usedByModules.includes(moduleName))
    .map(stat => stat.id);
  
  return TEMPLATE_CATALOG.filter(entry => moduleTemplateIds.includes(entry.id));
}

/**
 * Get template usage statistics
 * @param templateId Template ID to get stats for
 * @returns Template usage statistics or undefined if not found
 */
export function getTemplateUsageStats(templateId: string): TemplateUsageStats | undefined {
  return TEMPLATE_USAGE_STATS.find(stat => stat.id === templateId);
}

/**
 * Calculate total estimated compilation time saved by pre-compilation
 * @param averageCompilationTimeMs Average compilation time per template in milliseconds
 * @returns Estimated time saved in milliseconds per generation cycle
 */
export function calculateEstimatedTimeSavings(averageCompilationTimeMs: number = 5): number {
  return TEMPLATE_USAGE_STATS.reduce((total, stat) => {
    return total + (stat.estimatedUsageFrequency * averageCompilationTimeMs);
  }, 0);
}

/**
 * Get template complexity analysis
 * @returns Array of templates sorted by complexity score (highest first)
 */
export function getTemplateComplexityAnalysis(): Array<{
  id: string;
  complexityScore: number;
  estimatedUsageFrequency: number;
  optimizationPotential: number;
}> {
  return TEMPLATE_USAGE_STATS
    .map(stat => ({
      id: stat.id,
      complexityScore: stat.complexityScore,
      estimatedUsageFrequency: stat.estimatedUsageFrequency,
      optimizationPotential: stat.complexityScore * stat.estimatedUsageFrequency,
    }))
    .sort((a, b) => b.optimizationPotential - a.optimizationPotential);
}

/**
 * Validate that all templates in the catalog are accessible
 * @returns Validation results
 */
export function validateTemplateCatalog(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  for (const entry of TEMPLATE_CATALOG) {
    // Check if template content is valid
    if (!entry.content || typeof entry.content !== 'string') {
      errors.push(`Template '${entry.id}' has invalid or missing content`);
      continue;
    }

    // Check if template content is not empty
    if (entry.content.trim().length === 0) {
      errors.push(`Template '${entry.id}' has empty content`);
      continue;
    }

    // Check for basic Handlebars syntax validity
    const openBraces = (entry.content.match(/\{\{/g) || []).length;
    const closeBraces = (entry.content.match(/\}\}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push(`Template '${entry.id}' has mismatched Handlebars braces: ${openBraces} opening, ${closeBraces} closing`);
    }

    // Check if usage stats exist for this template
    const hasUsageStats = TEMPLATE_USAGE_STATS.some(stat => stat.id === entry.id);
    if (!hasUsageStats) {
      warnings.push(`Template '${entry.id}' has no usage statistics defined`);
    }

    // Check for reasonable priority values
    if (entry.priority !== undefined && (entry.priority < 0 || entry.priority > 100)) {
      warnings.push(`Template '${entry.id}' has unusual priority value: ${entry.priority}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Get template catalog summary for monitoring and reporting
 * @returns Template catalog summary
 */
export function getTemplateCatalogSummary(): {
  totalTemplates: number;
  totalEstimatedUsage: number;
  averageComplexity: number;
  totalEstimatedSize: number;
  highPriorityCount: number;
} {
  const totalTemplates = TEMPLATE_CATALOG.length;
  const totalEstimatedUsage = TEMPLATE_USAGE_STATS.reduce((sum, stat) => sum + stat.estimatedUsageFrequency, 0);
  const averageComplexity = TEMPLATE_USAGE_STATS.reduce((sum, stat) => sum + stat.complexityScore, 0) / TEMPLATE_USAGE_STATS.length;
  const totalEstimatedSize = TEMPLATE_USAGE_STATS.reduce((sum, stat) => sum + stat.averageSize, 0);
  const highPriorityCount = TEMPLATE_CATALOG.filter(entry => (entry.priority || 0) >= 80).length;

  return {
    totalTemplates,
    totalEstimatedUsage,
    averageComplexity: Math.round(averageComplexity * 100) / 100,
    totalEstimatedSize,
    highPriorityCount,
  };
}
