import { 
  TemplatePrecompilationService, 
  getGlobalPrecompilationService,
  PrecompilationSummary,
  PrecompilationOptions 
} from './template-precompilation-service';
import { 
  TemplateCompilationCache, 
  getGlobalTemplateCache,
  CacheOptions 
} from './template-compilation-cache';
import { 
  getAllTemplateEntries,
  getHighPriorityTemplates,
  validateTemplateCatalog,
  getTemplateCatalogSummary,
  calculateEstimatedTimeSavings
} from './template-catalog';

/**
 * Configuration options for template initialization
 */
export interface TemplateInitializationOptions {
  /** Cache configuration options */
  cacheOptions?: CacheOptions;
  /** Pre-compilation configuration options */
  precompilationOptions?: PrecompilationOptions;
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
  /** Whether to pre-compile only high-priority templates (default: false) */
  highPriorityOnly?: boolean;
  /** Whether to validate template catalog before initialization (default: true) */
  validateCatalog?: boolean;
  /** Whether to run initialization asynchronously (default: true) */
  async?: boolean;
}

/**
 * Result of template initialization process
 */
export interface TemplateInitializationResult {
  /** Whether initialization was successful */
  success: boolean;
  /** Pre-compilation summary */
  precompilationSummary: PrecompilationSummary;
  /** Cache statistics after initialization */
  cacheStatistics: any;
  /** Template catalog validation results */
  catalogValidation?: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
  /** Estimated time savings per generation cycle */
  estimatedTimeSavings: number;
  /** Total initialization time in milliseconds */
  initializationTime: number;
  /** Any errors encountered during initialization */
  errors: string[];
}

/**
 * Service for initializing template compilation cache and pre-compilation
 * Handles startup template warming and cache configuration
 */
export class TemplateInitializationService {
  private cache: TemplateCompilationCache;
  private precompilationService: TemplatePrecompilationService;
  private readonly options: Required<TemplateInitializationOptions>;
  private isInitialized = false;

  constructor(options: TemplateInitializationOptions = {}) {
    this.options = {
      cacheOptions: {},
      precompilationOptions: {},
      enableLogging: false,
      highPriorityOnly: false,
      validateCatalog: true,
      async: true,
      ...options,
    };

    // Initialize cache and pre-compilation service
    this.cache = getGlobalTemplateCache(this.options.cacheOptions);
    this.precompilationService = getGlobalPrecompilationService(this.options.precompilationOptions);

    if (this.options.enableLogging) {
      console.log('🚀 TemplateInitializationService created');
    }
  }

  /**
   * Initialize template system with pre-compilation
   * @returns Initialization result
   */
  public async initialize(): Promise<TemplateInitializationResult> {
    const startTime = Date.now();
    const errors: string[] = [];

    if (this.options.enableLogging) {
      console.log('🔄 Starting template system initialization...');
    }

    try {
      // Step 1: Validate template catalog if enabled
      let catalogValidation;
      if (this.options.validateCatalog) {
        catalogValidation = validateTemplateCatalog();
        
        if (!catalogValidation.isValid) {
          errors.push(...catalogValidation.errors);
          
          if (this.options.enableLogging) {
            console.error('❌ Template catalog validation failed:', catalogValidation.errors);
          }
          
          return {
            success: false,
            precompilationSummary: {
              totalTemplates: 0,
              successfulCompilations: 0,
              failedCompilations: 0,
              totalTime: 0,
              averageCompilationTime: 0,
              results: [],
              errors: catalogValidation.errors,
            },
            cacheStatistics: this.cache.getStatistics(),
            catalogValidation,
            estimatedTimeSavings: 0,
            initializationTime: Date.now() - startTime,
            errors,
          };
        }

        if (this.options.enableLogging && catalogValidation.warnings.length > 0) {
          console.warn('⚠️  Template catalog warnings:', catalogValidation.warnings);
        }
      }

      // Step 2: Register templates for pre-compilation
      const templatesToRegister = this.options.highPriorityOnly 
        ? getHighPriorityTemplates() 
        : getAllTemplateEntries();

      this.precompilationService.registerTemplates(templatesToRegister);

      if (this.options.enableLogging) {
        const catalogSummary = getTemplateCatalogSummary();
        console.log(`📊 Template catalog summary:`);
        console.log(`   - Total templates: ${catalogSummary.totalTemplates}`);
        console.log(`   - High priority templates: ${catalogSummary.highPriorityCount}`);
        console.log(`   - Templates to pre-compile: ${templatesToRegister.length}`);
        console.log(`   - Estimated total usage: ${catalogSummary.totalEstimatedUsage} compilations/cycle`);
        console.log(`   - Average complexity: ${catalogSummary.averageComplexity}/10`);
      }

      // Step 3: Pre-compile templates
      const precompilationSummary = await this.precompilationService.precompileAll();

      // Step 4: Calculate estimated time savings
      const estimatedTimeSavings = calculateEstimatedTimeSavings(
        precompilationSummary.averageCompilationTime
      );

      // Step 5: Get final cache statistics
      const cacheStatistics = this.cache.getStatistics();

      const initializationTime = Date.now() - startTime;
      this.isInitialized = true;

      const result: TemplateInitializationResult = {
        success: precompilationSummary.failedCompilations === 0,
        precompilationSummary,
        cacheStatistics,
        catalogValidation,
        estimatedTimeSavings,
        initializationTime,
        errors: [...errors, ...precompilationSummary.errors],
      };

      if (this.options.enableLogging) {
        this.logInitializationResult(result);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`Initialization failed: ${errorMessage}`);

      if (this.options.enableLogging) {
        console.error('❌ Template initialization failed:', error);
      }

      return {
        success: false,
        precompilationSummary: {
          totalTemplates: 0,
          successfulCompilations: 0,
          failedCompilations: 0,
          totalTime: 0,
          averageCompilationTime: 0,
          results: [],
          errors: [errorMessage],
        },
        cacheStatistics: this.cache.getStatistics(),
        estimatedTimeSavings: 0,
        initializationTime: Date.now() - startTime,
        errors,
      };
    }
  }

  /**
   * Initialize template system synchronously (blocking)
   * @returns Initialization result
   */
  public initializeSync(): TemplateInitializationResult {
    // For synchronous initialization, we'll use a simplified approach
    // that doesn't support all async features but provides basic functionality
    const startTime = Date.now();
    const errors: string[] = [];

    if (this.options.enableLogging) {
      console.log('🔄 Starting synchronous template system initialization...');
    }

    try {
      // Validate catalog if enabled
      let catalogValidation;
      if (this.options.validateCatalog) {
        catalogValidation = validateTemplateCatalog();
        
        if (!catalogValidation.isValid) {
          errors.push(...catalogValidation.errors);
          
          if (this.options.enableLogging) {
            console.error('❌ Template catalog validation failed:', catalogValidation.errors);
          }
        }
      }

      // Register and pre-compile templates synchronously
      const templatesToRegister = this.options.highPriorityOnly 
        ? getHighPriorityTemplates() 
        : getAllTemplateEntries();

      let successfulCompilations = 0;
      let failedCompilations = 0;
      const compilationTimes: number[] = [];

      for (const template of templatesToRegister) {
        try {
          const compilationStart = Date.now();
          this.cache.compile(template.content);
          const compilationTime = Date.now() - compilationStart;
          compilationTimes.push(compilationTime);
          successfulCompilations++;
        } catch (error) {
          failedCompilations++;
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push(`Failed to compile template '${template.id}': ${errorMessage}`);
        }
      }

      const totalTime = Date.now() - startTime;
      const averageCompilationTime = compilationTimes.length > 0 
        ? compilationTimes.reduce((sum, time) => sum + time, 0) / compilationTimes.length 
        : 0;

      const precompilationSummary: PrecompilationSummary = {
        totalTemplates: templatesToRegister.length,
        successfulCompilations,
        failedCompilations,
        totalTime,
        averageCompilationTime,
        results: [], // Simplified for sync mode
        errors,
      };

      const estimatedTimeSavings = calculateEstimatedTimeSavings(averageCompilationTime);
      const cacheStatistics = this.cache.getStatistics();

      this.isInitialized = true;

      const result: TemplateInitializationResult = {
        success: failedCompilations === 0,
        precompilationSummary,
        cacheStatistics,
        catalogValidation,
        estimatedTimeSavings,
        initializationTime: totalTime,
        errors,
      };

      if (this.options.enableLogging) {
        this.logInitializationResult(result);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`Synchronous initialization failed: ${errorMessage}`);

      return {
        success: false,
        precompilationSummary: {
          totalTemplates: 0,
          successfulCompilations: 0,
          failedCompilations: 0,
          totalTime: 0,
          averageCompilationTime: 0,
          results: [],
          errors: [errorMessage],
        },
        cacheStatistics: this.cache.getStatistics(),
        estimatedTimeSavings: 0,
        initializationTime: Date.now() - startTime,
        errors,
      };
    }
  }

  /**
   * Log initialization result details
   * @param result Initialization result to log
   */
  private logInitializationResult(result: TemplateInitializationResult): void {
    console.log(`🎉 Template initialization completed in ${result.initializationTime}ms:`);
    console.log(`   ✅ Successful compilations: ${result.precompilationSummary.successfulCompilations}`);
    console.log(`   ❌ Failed compilations: ${result.precompilationSummary.failedCompilations}`);
    console.log(`   📊 Average compilation time: ${result.precompilationSummary.averageCompilationTime.toFixed(2)}ms`);
    console.log(`   💾 Cache entries: ${result.cacheStatistics.entryCount}`);
    console.log(`   💰 Estimated time savings: ${result.estimatedTimeSavings.toFixed(0)}ms per generation cycle`);
    
    if (result.errors.length > 0) {
      console.log(`   ⚠️  Errors: ${result.errors.length}`);
    }
  }

  /**
   * Check if the template system has been initialized
   * @returns True if initialized
   */
  public isTemplateSystemInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get current cache instance
   * @returns Template compilation cache
   */
  public getCache(): TemplateCompilationCache {
    return this.cache;
  }

  /**
   * Get current pre-compilation service
   * @returns Template pre-compilation service
   */
  public getPrecompilationService(): TemplatePrecompilationService {
    return this.precompilationService;
  }

  /**
   * Reset the initialization state
   * Useful for testing or re-initialization
   */
  public reset(): void {
    this.isInitialized = false;
    this.cache.clear();
    this.precompilationService.clearRegistry();

    if (this.options.enableLogging) {
      console.log('🔄 Template initialization service reset');
    }
  }
}

// Global singleton instance
let globalInitializationService: TemplateInitializationService | null = null;

/**
 * Get the global template initialization service instance
 * @param options Optional service configuration (only used on first call)
 * @returns Global service instance
 */
export function getGlobalInitializationService(
  options?: TemplateInitializationOptions
): TemplateInitializationService {
  if (!globalInitializationService) {
    globalInitializationService = new TemplateInitializationService(options);
  }
  return globalInitializationService;
}

/**
 * Initialize the global template system
 * Convenience function for application startup
 * @param options Optional initialization options
 * @returns Initialization result
 */
export async function initializeGlobalTemplateSystem(
  options?: TemplateInitializationOptions
): Promise<TemplateInitializationResult> {
  const service = getGlobalInitializationService(options);
  return await service.initialize();
}

/**
 * Initialize the global template system synchronously
 * Convenience function for synchronous application startup
 * @param options Optional initialization options
 * @returns Initialization result
 */
export function initializeGlobalTemplateSystemSync(
  options?: TemplateInitializationOptions
): TemplateInitializationResult {
  const service = getGlobalInitializationService(options);
  return service.initializeSync();
}

/**
 * Reset the global template initialization service
 * Useful for testing or when configuration changes
 */
export function resetGlobalInitializationService(): void {
  globalInitializationService = null;
}
