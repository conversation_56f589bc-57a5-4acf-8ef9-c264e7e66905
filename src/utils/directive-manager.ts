import type { DirectiveContainer, ParsedDirective } from '@utils/directive-parser';
import { DirectiveParser } from '@utils/directive-parser';
import type { DirectiveHandler } from '@utils/directive-registry';
import { DirectiveRegistry } from '@utils/directive-registry';
import { DirectiveConfigManager } from '../config/directive-config';
import type { DecoratorContainer } from '@utils/decorator-parser';
import { DecoratorProcessor } from '@utils/decorator-processor';
import type { DecoratorPrecedenceRules } from '@utils/decorator-types';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '@utils/decorator-types';

/**
 * Manager for directive system that provides unified access to all directive functionality
 */
export class DirectiveManager {
  private static instance: DirectiveManager;
  private configManager: DirectiveConfigManager;
  private directiveCache: Map<string, DirectiveContainer> = new Map();
  private decoratorProcessor?: DecoratorProcessor;
  private precedenceRules: DecoratorPrecedenceRules = DEFAULT_PRECEDENCE_RULES;

  private constructor() {
    this.configManager = DirectiveConfigManager.getInstance();
  }

  /**
   * Get the instance of the directive manager
   */
  public static getInstance(): DirectiveManager {
    if (!DirectiveManager.instance) {
      DirectiveManager.instance = new DirectiveManager();
    }
    return DirectiveManager.instance;
  }

  /**
   * Register a directive handler
   * @param handler The directive handler to register
   */
  public registerHandler(handler: DirectiveHandler): void {
    DirectiveRegistry.register(handler);
  }

  /**
   * Register multiple directive handlers
   * @param handlers Array of directive handlers to register
   */
  public registerHandlers(handlers: DirectiveHandler[]): void {
    handlers.forEach(handler => this.registerHandler(handler));
  }

  /**
   * Get directives for a type
   * @param schemaFilePath Path to the schema file
   * @param typeName Name of the type
   * @returns Directive container for the type
   */
  public async getTypeDirectives(
    schemaFilePath: string,
    typeName: string
  ): Promise<DirectiveContainer> {
    const cacheKey = `${schemaFilePath}:${typeName}`;

    // Check cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives && this.directiveCache.has(cacheKey)) {
      return this.directiveCache.get(cacheKey)!;
    }

    const directives = await DirectiveParser.extractDirectivesFromSchema(schemaFilePath, typeName);

    // Cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives) {
      this.directiveCache.set(cacheKey, directives);
    }

    return directives;
  }

  /**
   * Get directives for a field
   * @param schemaFilePath Path to the schema file
   * @param typeName Name of the type
   * @param fieldName Name of the field
   * @returns Directive container for the field
   */
  public async getFieldDirectives(
    schemaFilePath: string,
    typeName: string,
    fieldName: string
  ): Promise<DirectiveContainer> {
    const cacheKey = `${schemaFilePath}:${typeName}:${fieldName}`;

    // Check cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives && this.directiveCache.has(cacheKey)) {
      return this.directiveCache.get(cacheKey)!;
    }

    const directives = await DirectiveParser.extractDirectivesFromSchema(
      schemaFilePath,
      typeName,
      fieldName
    );

    // Cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives) {
      this.directiveCache.set(cacheKey, directives);
    }

    return directives;
  }

  /**
   * Get schema-level directives
   * @param schemaFilePath Path to the schema file
   * @returns Directive container for the schema
   */
  public async getSchemaDirectives(schemaFilePath: string): Promise<DirectiveContainer> {
    const cacheKey = `${schemaFilePath}:schema`;

    // Check cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives && this.directiveCache.has(cacheKey)) {
      return this.directiveCache.get(cacheKey)!;
    }

    const directives = await DirectiveParser.extractSchemaDirectives(schemaFilePath);

    // Cache if preprocessing is enabled
    if (this.configManager.getConfig().preprocessDirectives) {
      this.directiveCache.set(cacheKey, directives);
    }

    return directives;
  }

  /**
   * Process directives with registered handlers
   * @param directives Directive container to process
   * @param directiveName Optional name of specific directive to process
   * @returns Processed results map
   */
  public processDirectives<T = any>(
    directives: DirectiveContainer,
    directiveName?: string
  ): Map<string, T[]> {
    const results = new Map<string, T[]>();

    // Process specific directive if specified
    if (directiveName) {
      if (directiveName === 'import') {
        this.processNamedDirectives(directives.imports, results);
      } else if (directiveName === 'methodCall') {
        this.processNamedDirectives(directives.methodCalls, results);
      } else if (directives.others[directiveName]) {
        this.processNamedDirectives(directives.others[directiveName], results);
      }
      return results;
    }

    // Process all directives
    this.processNamedDirectives(directives.imports, results);
    this.processNamedDirectives(directives.methodCalls, results);

    // Process other directives
    for (const [_directiveName, directiveList] of Object.entries(directives.others)) {
      this.processNamedDirectives(directiveList, results);
    }

    return results;
  }

  /**
   * Process a list of named directives
   * @param directives List of directives to process
   * @param results Results map to update
   */
  private processNamedDirectives<T = any>(
    directives: ParsedDirective[],
    results: Map<string, T[]>
  ): void {
    if (!directives || directives.length === 0) {
      return;
    }

    for (const directive of directives) {
      const result = DirectiveRegistry.process<T>(directive);
      if (result !== null) {
        if (!results.has(directive.name)) {
          results.set(directive.name, []);
        }
        results.get(directive.name)!.push(result);
      }
    }
  }

  /**
   * Get directives from decorator metadata for a type
   * @param decoratorMetadata The decorator container
   * @param typeName Name of the type
   * @param schemaId Optional schema identifier
   * @returns Directive container for the type
   */
  public getDecoratorDirectivesForType(
    decoratorMetadata: DecoratorContainer,
    typeName: string,
    schemaId?: string
  ): DirectiveContainer {
    if (!this.decoratorProcessor) {
      this.initializeDecoratorProcessor();
    }

    try {
      // Convert decorator metadata to directives
      const decoratorDirectives = this.decoratorProcessor!.convertToDirectiveContainer(
        decoratorMetadata,
        schemaId
      );

      // Filter for the specific type
      return this.filterDirectivesForType(decoratorDirectives, typeName);
    } catch (error) {
      console.error(`Error processing decorator directives for type ${typeName}:`, error);
      return this.createEmptyDirectiveContainer();
    }
  }

  /**
   * Get directives from decorator metadata for a field
   * @param decoratorMetadata The decorator container
   * @param typeName Name of the type
   * @param fieldName Name of the field
   * @param schemaId Optional schema identifier
   * @returns Directive container for the field
   */
  public getDecoratorDirectivesForField(
    decoratorMetadata: DecoratorContainer,
    typeName: string,
    fieldName: string,
    schemaId?: string
  ): DirectiveContainer {
    if (!this.decoratorProcessor) {
      this.initializeDecoratorProcessor();
    }

    try {
      // Convert decorator metadata to directives
      const decoratorDirectives = this.decoratorProcessor!.convertToDirectiveContainer(
        decoratorMetadata,
        schemaId
      );

      // Filter for the specific type and field
      return this.filterDirectivesForField(decoratorDirectives, typeName, fieldName);
    } catch (error) {
      console.error(`Error processing decorator directives for field ${typeName}.${fieldName}:`, error);
      return this.createEmptyDirectiveContainer();
    }
  }

  /**
   * Merge decorator and comment-based directives with precedence rules
   * @param decoratorDirectives Directives from decorators
   * @param commentDirectives Directives from comments
   * @param context Optional context for conflict reporting
   * @returns Merged directive container
   */
  public mergeDirectives(
    decoratorDirectives: DirectiveContainer,
    commentDirectives: DirectiveContainer,
    context?: { typeName?: string; fieldName?: string }
  ): DirectiveContainer {
    if (!this.decoratorProcessor) {
      this.initializeDecoratorProcessor();
    }

    // Detect conflicts before merging
    this.detectAndReportConflicts(decoratorDirectives, commentDirectives, context);

    return this.decoratorProcessor!.mergeDirectives(decoratorDirectives, commentDirectives);
  }

  /**
   * Detect and report conflicts between decorator and comment directives
   * @param decoratorDirectives Directives from decorators
   * @param commentDirectives Directives from comments
   * @param context Optional context for reporting
   */
  private detectAndReportConflicts(
    decoratorDirectives: DirectiveContainer,
    commentDirectives: DirectiveContainer,
    context?: { typeName?: string; fieldName?: string }
  ): void {
    const contextStr = context
      ? `${context.typeName}${context.fieldName ? `.${context.fieldName}` : ''}`
      : 'unknown location';

    // Check for method call conflicts
    if (decoratorDirectives.methodCalls.length > 0 && commentDirectives.methodCalls.length > 0) {
      console.warn(
        `⚠️  Conflict detected at ${contextStr}: Both decorator (@GQLMethodCall) and comment (@methodCall) directives found. Decorator will take precedence.`
      );
    }

    // Check for import conflicts
    if (decoratorDirectives.imports.length > 0 && commentDirectives.imports.length > 0) {
      console.warn(
        `⚠️  Conflict detected at ${contextStr}: Both decorator (@GQLImport) and comment (@import) directives found. Decorator will take precedence.`
      );
    }

    // Check for field conflicts
    if (decoratorDirectives.fieldFields.length > 0 && commentDirectives.fieldFields.length > 0) {
      console.warn(
        `⚠️  Conflict detected at ${contextStr}: Both decorator (@GQLField) and comment (@field) directives found. Decorator will take precedence.`
      );
    }
  }

  /**
   * Clear the directive cache
   */
  public clearCache(): void {
    this.directiveCache.clear();
  }

  /**
   * Set custom precedence rules for decorator processing
   * @param rules The precedence rules to use
   */
  public setPrecedenceRules(rules: DecoratorPrecedenceRules): void {
    this.precedenceRules = rules;
    // Reset the decorator processor to use new rules
    this.decoratorProcessor = undefined;
  }

  /**
   * Get current precedence rules
   * @returns Current precedence rules
   */
  public getPrecedenceRules(): DecoratorPrecedenceRules {
    return this.precedenceRules;
  }

  /**
   * Initialize the decorator processor
   */
  private initializeDecoratorProcessor(): void {
    if (!this.decoratorProcessor) {
      this.decoratorProcessor = new DecoratorProcessor(
        DEFAULT_DECORATOR_CONFIG,
        this.precedenceRules
      );
    }
  }

  /**
   * Create an empty directive container
   */
  private createEmptyDirectiveContainer(): DirectiveContainer {
    return {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {},
    };
  }

  /**
   * Filter directives for a specific type
   * @param directives The directive container to filter
   * @param typeName The type name to filter for
   * @returns Filtered directive container
   */
  private filterDirectivesForType(directives: DirectiveContainer, typeName: string): DirectiveContainer {
    const filtered = this.createEmptyDirectiveContainer();

    // Include all imports (they're global)
    filtered.imports = [...directives.imports];

    // Filter method calls for this type
    filtered.methodCalls = directives.methodCalls.filter(methodCall => {
      // This is a simplified filter - in practice, we'd need to parse the decorator metadata
      // to determine which type the method call belongs to
      return this.isMethodCallForType(methodCall, typeName);
    });

    // Filter field directives for this type
    // Note: FieldDirectiveField doesn't have a 'ref' property, so include all for now
    filtered.fieldFields = [...directives.fieldFields];

    // Copy other directives
    filtered.others = { ...directives.others };

    return filtered;
  }

  /**
   * Filter directives for a specific field
   * @param directives The directive container to filter
   * @param typeName The type name
   * @param fieldName The field name
   * @returns Filtered directive container
   */
  private filterDirectivesForField(
    directives: DirectiveContainer,
    typeName: string,
    fieldName: string
  ): DirectiveContainer {
    const filtered = this.createEmptyDirectiveContainer();

    // Include all imports (they're global)
    filtered.imports = [...directives.imports];

    // Filter method calls for this specific field
    filtered.methodCalls = directives.methodCalls.filter(methodCall => {
      return this.isMethodCallForField(methodCall, typeName, fieldName);
    });

    // Filter field directives for this specific field
    filtered.fieldFields = directives.fieldFields.filter(
      field => field.name === fieldName
    );

    // Copy other directives
    filtered.others = { ...directives.others };

    return filtered;
  }

  /**
   * Check if a method call is for a specific type
   * @param methodCall The method call directive
   * @param typeName The type name
   * @returns True if the method call is for the type
   */
  private isMethodCallForType(methodCall: ParsedDirective, typeName: string): boolean {
    // This is a simplified implementation
    // In practice, we'd need to parse the decorator metadata to get type information
    const content = methodCall.content || '';
    return content.includes(typeName);
  }

  /**
   * Check if a method call is for a specific field
   * @param methodCall The method call directive
   * @param typeName The type name
   * @param fieldName The field name
   * @returns True if the method call is for the field
   */
  private isMethodCallForField(methodCall: ParsedDirective, typeName: string, fieldName: string): boolean {
    // This is a simplified implementation
    // In practice, we'd need to parse the decorator metadata to get type and field information
    const content = methodCall.content || '';
    return content.includes(typeName) && content.includes(fieldName);
  }

  /**
   * Load and register custom directive handlers from a specified path
   * @param customHandlersPath Path to custom directive handlers
   */
  public async loadCustomHandlers(customHandlersPath?: string): Promise<void> {
    const handlersPath = customHandlersPath ?? this.configManager.getConfig().customHandlersPath;

    if (!handlersPath) {
      return;
    }

    try {
      // This would dynamically import custom handlers
      // Implementation depends on the module system used
      console.log(`Loading custom directive handlers from ${handlersPath}`);

      // Example implementation
      // const handlers = await import(handlersPath);
      // if (handlers.default && Array.isArray(handlers.default)) {
      //   this.registerHandlers(handlers.default);
      // }
    } catch (error) {
      console.error(`Failed to load custom directive handlers: ${error}`);
    }
  }
}
