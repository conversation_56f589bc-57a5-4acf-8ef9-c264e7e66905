import * as fs from 'fs-extra';
import * as path from 'path';
import * as glob from 'glob';
import { parseSync as swcParseSync, Module } from '@swc/core';
import * as oxc from 'oxc-parser';

/**
 * Enhanced performance metrics for decorator parsing optimization
 */
interface EnhancedDecoratorParsingMetrics {
  // Overall metrics
  totalDuration: number;
  filesProcessed: number;
  decoratorsFound: number;

  // Pre-filtering metrics
  preFilterDuration: number;
  filesSkippedByPreFilter: number;
  preFilterEfficiency: number; // percentage of files skipped

  // Parsing method metrics
  oxcParsingSuccessCount: number;
  oxcParsingFailureCount: number;
  swcParsingSuccessCount: number;
  swcParsingFailureCount: number;
  regexParsingCount: number;
  oxcParsingDuration: number;
  swcParsingDuration: number;
  regexParsingDuration: number;

  // Cache metrics
  cacheHits: number;
  cacheMisses: number;
  cacheHitRate: number;

  // Performance comparison
  averageTimePerFile: number;
  astVsRegexSpeedRatio: number; // How much faster AST parsing is

  // Memory metrics (if available)
  memoryUsage?: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
}

/**
 * Represents a parsed decorator from TypeScript code
 */
export interface ParsedDecorator {
  /** Name of the decorator (e.g., 'GQLMethodCall', 'GQLImport') */
  name: string;
  /** The decorator arguments as a string */
  arguments: string;
  /** The raw decorator string including @ symbol */
  raw: string;
  /** File path where the decorator was found */
  filePath: string;
  /** Line number where the decorator appears */
  lineNumber: number;
  /** The target element (function, class, property, etc.) */
  target: string;
}

/**
 * Represents parsed parameters for @GQLMethodCall decorator
 */
export interface GQLMethodCallData {
  /** GraphQL type name */
  type: string;
  /** GraphQL field name */
  field?: string;
  /** Method call expression (optional for smart defaults) */
  call?: string;
  /** Schema identifier for multi-schema support */
  schema?: string;
  /** Whether to generate async wrapper */
  async?: boolean;
  /** Custom error handling expression */
  errorHandler?: string;
  /** Whether to enable type casting support */
  enableTypeCasting?: boolean;
}

/**
 * Represents parsed parameters for @GQLImport decorator
 */
export interface GQLImportData {
  /** Import statement */
  importStatement: string;
  /** Schema identifier for multi-schema support */
  schema?: string;
  /** Whether this import is conditional */
  conditional?: boolean;
  /** Condition expression for conditional imports */
  condition?: string;
}

/**
 * Represents parsed parameters for @GQLField decorator
 */
export interface GQLFieldData {
  /** Reference type name */
  ref: string;
  /** Field name */
  name: string;
  /** Field type (can be auto-inferred from decorated interface/type) */
  type: string;
  /** Schema identifier for multi-schema support */
  schema?: string;
  /** Whether the field is optional */
  optional?: boolean;
  /** Import path for the type */
  importPath?: string;
  /** Whether this field should be hidden from schema */
  hidden?: boolean;
}

/**
 * Represents parsed parameters for @GQLContext decorator
 */
export interface GQLContextData {
  /** Context file path */
  path: string;
  /** Context interface name */
  name: string;
  /** Schema identifier for multi-schema support */
  schema?: string;
}

/**
 * Container for all decorator metadata from a codebase scan
 */
export interface DecoratorContainer {
  /** @GQLMethodCall decorators */
  methodCalls: Array<{ decorator: ParsedDecorator; data: GQLMethodCallData }>;
  /** @GQLImport decorators */
  imports: Array<{ decorator: ParsedDecorator; data: GQLImportData }>;
  /** @GQLField decorators */
  fields: Array<{ decorator: ParsedDecorator; data: GQLFieldData }>;
  /** @GQLContext decorators */
  contexts: Array<{ decorator: ParsedDecorator; data: GQLContextData }>;
  /** Other decorators for future extensibility */
  others: Record<string, Array<{ decorator: ParsedDecorator; data: any }>>;
}

/**
 * Optimized regex patterns for decorator detection with better performance
 */
const OPTIMIZED_DECORATOR_PATTERNS = {
  // Fast pre-check pattern - just look for @GQL prefix
  hasGQLDecorator: /@GQL/,

  // Optimized main pattern - more efficient than the original
  // Uses atomic groups and possessive quantifiers where possible
  allGQLDecorators: /@(GQL(?:MethodCall|Import|Field|Context))\s*\(\s*([^()]*(?:\([^()]*\)[^()]*)*)\s*\)/g,

  // Individual patterns for specific matching (if needed)
  GQLMethodCall: /@GQLMethodCall\s*\(\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\)/g,
  GQLImport: /@GQLImport\s*\(\s*(?:"[^"]*"|'[^']*'|[^)]*)\s*\)/g,
  GQLField: /@GQLField\s*\(\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\)/g,
  GQLContext: /@GQLContext\s*\(\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}\s*\)/g
};

/**
 * Enhanced file cache entry for tracking modification times and parsed results
 */
interface FileCacheEntry {
  /** File modification time in milliseconds */
  mtime: number;
  /** File size in bytes for additional validation */
  size: number;
  /** Content hash for additional validation */
  contentHash?: string;
  /** Cached decorator results for this file */
  decorators: {
    methodCalls: Array<{ decorator: ParsedDecorator; data: GQLMethodCallData }>;
    imports: Array<{ decorator: ParsedDecorator; data: GQLImportData }>;
    fields: Array<{ decorator: ParsedDecorator; data: GQLFieldData }>;
    contexts: Array<{ decorator: ParsedDecorator; data: GQLContextData }>;
  };
  /** Cache creation timestamp */
  cachedAt: number;
  /** Dependencies (imported files) that could affect this cache entry */
  dependencies?: string[];
  /** Last validation timestamp for dependency checking */
  lastValidated?: number;
}

/**
 * Persistent cache storage for decorator parsing results
 */
interface PersistentCacheData {
  version: string;
  createdAt: number;
  entries: Record<string, FileCacheEntry>;
}

/**
 * Performance metrics for decorator parsing
 */
export interface DecoratorParsingMetrics {
  /** Total scan duration in milliseconds */
  totalDuration: number;
  /** Number of files processed */
  filesProcessed: number;
  /** Number of cache hits */
  cacheHits: number;
  /** Number of cache misses */
  cacheMisses: number;
  /** Cache hit rate as percentage */
  cacheHitRate: number;
  /** Number of decorators found by type */
  decoratorsFound: {
    methodCalls: number;
    imports: number;
    fields: number;
    contexts: number;
  };
  /** Average processing time per file in milliseconds */
  avgTimePerFile: number;
}

/**
 * TypeScript decorator parser using enhanced regex for fast multi-line decorator detection
 */
export class DecoratorParser {
  /** Cache for file modification times and parsed results */
  private fileCache: Map<string, FileCacheEntry> = new Map();

  /** Path to persistent cache file */
  private cacheFilePath: string;

  constructor(cacheDir?: string, private enableParallelProcessing: boolean = false) {
    // Set up persistent cache file path
    const defaultCacheDir = path.join(process.cwd(), '.gql-generator-cache');
    this.cacheFilePath = path.join(cacheDir || defaultCacheDir, 'decorator-cache.json');

    // Load existing cache on initialization
    this.loadPersistentCache();

    // Set up memory monitoring
    this.setupMemoryMonitoring();
  }

  /**
   * Set up memory monitoring and management
   */
  private setupMemoryMonitoring(): void {
    // Enable garbage collection if available
    if (global.gc && process.env.DEBUG_PARSER) {
      console.log('Garbage collection available - memory optimization enabled');
    }

    // Monitor memory usage periodically during long operations
    this.memoryMonitoringInterval = setInterval(() => {
      const memUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);

      // Trigger GC if heap usage is high
      if (heapUsedMB > 500 && global.gc) { // 500MB threshold
        global.gc();
        if (process.env.DEBUG_PARSER) {
          console.log(`Memory cleanup triggered at ${heapUsedMB}MB heap usage`);
        }
      }
    }, 5000); // Check every 5 seconds
  }

  private memoryMonitoringInterval?: NodeJS.Timeout;

  /** Performance tracking metrics */
  private performanceMetrics: EnhancedDecoratorParsingMetrics = {
    totalDuration: 0,
    filesProcessed: 0,
    decoratorsFound: 0,
    preFilterDuration: 0,
    filesSkippedByPreFilter: 0,
    preFilterEfficiency: 0,
    oxcParsingSuccessCount: 0,
    oxcParsingFailureCount: 0,
    swcParsingSuccessCount: 0,
    swcParsingFailureCount: 0,
    regexParsingCount: 0,
    oxcParsingDuration: 0,
    swcParsingDuration: 0,
    regexParsingDuration: 0,
    cacheHits: 0,
    cacheMisses: 0,
    cacheHitRate: 0,
    averageTimePerFile: 0,
    astVsRegexSpeedRatio: 0,
  };



  /**
   * Load persistent cache from disk
   */
  private async loadPersistentCache(): Promise<void> {
    try {
      if (await fs.pathExists(this.cacheFilePath)) {
        const cacheData: PersistentCacheData = await fs.readJson(this.cacheFilePath);

        // Validate cache version and age (extended to 7 days for better performance)
        const cacheAge = Date.now() - cacheData.createdAt;
        if (cacheAge < 7 * 24 * 60 * 60 * 1000) { // 7 days
          // Load cache entries with normalized paths for cross-platform compatibility
          for (const [filePath, entry] of Object.entries(cacheData.entries)) {
            const normalizedPath = this.normalizeFilePath(filePath);
            this.fileCache.set(normalizedPath, entry);
          }
          if (process.env.DEBUG_PARSER) {
            console.log(`Loaded ${Object.keys(cacheData.entries).length} entries from persistent cache`);
          }
        } else {
          if (process.env.DEBUG_PARSER) {
            console.log('Persistent cache expired, starting fresh');
          }
        }
      }
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn('Failed to load persistent cache:', error);
      }
    }
  }

  /**
   * Save persistent cache to disk with optimized format
   */
  private async savePersistentCache(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.cacheFilePath));

      // Only cache entries that are recent and likely to be reused
      const recentEntries = new Map<string, FileCacheEntry>();
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours

      for (const [filePath, entry] of this.fileCache.entries()) {
        if (entry.cachedAt > cutoffTime) {
          // Ensure paths are normalized when saving to cache
          const normalizedPath = this.normalizeFilePath(filePath);
          recentEntries.set(normalizedPath, entry);
        }
      }

      const cacheData: PersistentCacheData = {
        version: '1.1.0', // Updated version for enhanced cache format
        createdAt: Date.now(),
        entries: Object.fromEntries(recentEntries.entries())
      };

      // Use compact JSON format for better performance
      await fs.writeJson(this.cacheFilePath, cacheData, { spaces: 0 });

      if (process.env.DEBUG_PARSER) {
        const savedEntries = recentEntries.size;
        const totalEntries = this.fileCache.size;
        console.log(`Saved ${savedEntries}/${totalEntries} cache entries to persistent storage`);
      }
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn('Failed to save persistent cache:', error);
      }
    }
  }

  /**
   * Get current performance metrics
   * @returns Current performance metrics
   */
  public getPerformanceMetrics(): EnhancedDecoratorParsingMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Reset performance metrics
   */
  public resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      totalDuration: 0,
      filesProcessed: 0,
      decoratorsFound: 0,
      preFilterDuration: 0,
      filesSkippedByPreFilter: 0,
      preFilterEfficiency: 0,
      oxcParsingSuccessCount: 0,
      oxcParsingFailureCount: 0,
      swcParsingSuccessCount: 0,
      swcParsingFailureCount: 0,
      regexParsingCount: 0,
      oxcParsingDuration: 0,
      swcParsingDuration: 0,
      regexParsingDuration: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheHitRate: 0,
      averageTimePerFile: 0,
      astVsRegexSpeedRatio: 0,
    };
  }

  /**
   * Clear the entire file cache
   */
  public clearCache(): void {
    this.fileCache.clear();
    this.forceGarbageCollection();
  }

  /**
   * Clean up old cache entries based on age and usage
   */
  private cleanupCache(): void {
    const now = Date.now();
    const maxCacheSize = 1000; // Maximum number of cache entries
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    // Remove old entries
    for (const [filePath, entry] of this.fileCache.entries()) {
      if (now - entry.cachedAt > maxAge) {
        this.fileCache.delete(filePath);
      }
    }

    // If still too large, remove least recently used entries
    if (this.fileCache.size > maxCacheSize) {
      const entries = Array.from(this.fileCache.entries());
      entries.sort((a, b) => a[1].cachedAt - b[1].cachedAt); // Sort by age

      const entriesToRemove = entries.slice(0, this.fileCache.size - maxCacheSize);
      for (const [filePath] of entriesToRemove) {
        this.fileCache.delete(filePath);
      }

      if (process.env.DEBUG_PARSER) {
        console.log(`Cache cleanup: removed ${entriesToRemove.length} old entries, ${this.fileCache.size} entries remaining`);
      }
    }
  }

  /**
   * Clean up resources and stop memory monitoring
   */
  public cleanup(): void {
    if (this.memoryMonitoringInterval) {
      clearInterval(this.memoryMonitoringInterval);
      this.memoryMonitoringInterval = undefined;
    }
    this.forceGarbageCollection();
  }

  /**
   * Force garbage collection if available
   */
  private forceGarbageCollection(): void {
    if (global.gc) {
      global.gc();
    }
  }

  /**
   * Get current memory usage information
   */
  private getMemoryInfo(): { heapUsed: number; heapTotal: number; external: number; rss: number } {
    const memUsage = process.memoryUsage();
    return {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024) // MB
    };
  }

  /**
   * Remove a specific file from the cache
   * @param filePath Path to the file to remove from cache
   */
  public invalidateFile(filePath: string): void {
    this.fileCache.delete(filePath);
  }

  /**
   * Get cache statistics for monitoring
   * @returns Cache statistics
   */
  public getCacheStats(): { size: number; files: string[] } {
    return {
      size: this.fileCache.size,
      files: Array.from(this.fileCache.keys())
    };
  }

  /**
   * Enable or disable parallel processing
   * @param enabled Whether to enable parallel processing
   */
  public setParallelProcessing(enabled: boolean): void {
    this.enableParallelProcessing = enabled;
  }

  /**
   * Scan a single file for decorators (public method for worker threads)
   * @param filePath Path to the file to scan
   * @returns Container with found decorators
   */
  public async scanSingleFile(filePath: string): Promise<DecoratorContainer> {
    const container: DecoratorContainer = {
      methodCalls: [],
      imports: [],
      fields: [],
      contexts: [],
      others: {}
    };

    await this.scanFile(filePath, container);
    return container;
  }

  /**
   * Scan a codebase directory and return detailed performance metrics
   * @param codebaseDir Path to the codebase directory
   * @param patterns File patterns to scan (default: TypeScript files)
   * @returns Container with all found decorators and performance metrics
   */
  public async scanCodebaseWithMetrics(
    codebaseDir: string,
    patterns: string[] = ['**/*.ts', '**/*.tsx']
  ): Promise<{ container: DecoratorContainer; metrics: DecoratorParsingMetrics }> {
    // Use parallel processing for large codebases if enabled
    if (this.enableParallelProcessing) {
      try {
        const { ParallelDecoratorParser } = await import('./parallel-decorator-parser.js');
        const parallelParser = new ParallelDecoratorParser({
          maxWorkers: Math.max(1, require('os').cpus().length - 1),
          enableMonitoring: true
        });

        const result = await parallelParser.scanCodebase(codebaseDir, patterns);
        await parallelParser.shutdown();

        // Convert parallel metrics to standard metrics format
        const metrics: DecoratorParsingMetrics = {
          totalDuration: result.metrics.totalDuration,
          filesProcessed: result.metrics.filesProcessed,
          cacheHits: 0, // Parallel processing doesn't use the same cache system
          cacheMisses: result.metrics.filesProcessed,
          cacheHitRate: 0,
          decoratorsFound: {
            methodCalls: result.container.methodCalls.length,
            imports: result.container.imports.length,
            fields: result.container.fields.length,
            contexts: result.container.contexts.length
          },
          avgTimePerFile: result.metrics.averageTimePerFile
        };

        return { container: result.container, metrics };
      } catch (error) {
        if (process.env.DEBUG_PARSER) {
          console.warn('Parallel processing failed, falling back to single-threaded:', error);
        }
        // Fall through to single-threaded processing
      }
    }
    const startTime = Date.now();
    const initialMemory = process.memoryUsage();

    // Reset performance tracking for this scan
    this.resetPerformanceMetrics();

    const container = await this.scanCodebase(codebaseDir, patterns);
    const endTime = Date.now();
    const finalMemory = process.memoryUsage();

    const totalDuration = endTime - startTime;
    const filesProcessed = this.performanceMetrics.filesProcessed;
    const cacheHits = this.performanceMetrics.cacheHits || 0;
    const cacheMisses = this.performanceMetrics.cacheMisses || 0;

    const metrics: DecoratorParsingMetrics = {
      totalDuration,
      filesProcessed,
      cacheHits,
      cacheMisses,
      cacheHitRate: filesProcessed > 0 ? Math.round((cacheHits / filesProcessed) * 100) : 0,
      decoratorsFound: {
        methodCalls: container.methodCalls.length,
        imports: container.imports.length,
        fields: container.fields.length,
        contexts: container.contexts.length,
      },
      avgTimePerFile: filesProcessed > 0 ? totalDuration / filesProcessed : 0,
    };

    // Log comprehensive performance report
    this.logPerformanceReport(metrics, initialMemory, finalMemory);

    // Clean up resources to allow process to exit
    this.cleanup();

    return { container, metrics };
  }



  /**
   * Log comprehensive performance report
   */
  private logPerformanceReport(
    metrics: DecoratorParsingMetrics,
    initialMemory: NodeJS.MemoryUsage,
    finalMemory: NodeJS.MemoryUsage
  ): void {
    const memoryDelta = finalMemory.heapUsed - initialMemory.heapUsed;
    const memoryDeltaMB = Math.round(memoryDelta / 1024 / 1024);

    if (process.env.DEBUG_PARSER) {
      console.log('\n=== Enhanced Performance Report ===');
      console.log(`📊 Overall Performance:`);
      console.log(`   Duration: ${metrics.totalDuration}ms`);
      console.log(`   Throughput: ${(metrics.filesProcessed / (metrics.totalDuration / 1000)).toFixed(2)} files/sec`);
      console.log(`   Files Processed: ${metrics.filesProcessed}`);
      console.log(`   Avg Time/File: ${metrics.avgTimePerFile.toFixed(2)}ms`);

      console.log(`\n🎯 Cache Performance:`);
      console.log(`   Hit Rate: ${metrics.cacheHitRate}%`);
      console.log(`   Cache Hits: ${metrics.cacheHits}`);
      console.log(`   Cache Misses: ${metrics.cacheMisses}`);

      console.log(`\n🔍 Decorators Found:`);
      console.log(`   Method Calls: ${metrics.decoratorsFound.methodCalls}`);
      console.log(`   Imports: ${metrics.decoratorsFound.imports}`);
      console.log(`   Fields: ${metrics.decoratorsFound.fields}`);
      console.log(`   Contexts: ${metrics.decoratorsFound.contexts}`);
      console.log(`   Total: ${Object.values(metrics.decoratorsFound).reduce((a, b) => a + b, 0)}`);

      console.log(`\n💾 Memory Usage:`);
      console.log(`   Initial: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);
      console.log(`   Final: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
      console.log(`   Delta: ${memoryDeltaMB >= 0 ? '+' : ''}${memoryDeltaMB}MB`);
      console.log(`   RSS: ${Math.round(finalMemory.rss / 1024 / 1024)}MB`);

      // Performance efficiency rating
      const efficiency = this.calculateEfficiencyRating(metrics, memoryDeltaMB);
      console.log(`\n⭐ Performance Rating: ${efficiency.rating} (${efficiency.score}/100)`);
      console.log(`   ${efficiency.summary}`);
    }
  }

  /**
   * Calculate performance efficiency rating
   */
  private calculateEfficiencyRating(metrics: DecoratorParsingMetrics, memoryDeltaMB: number): {
    rating: string;
    score: number;
    summary: string;
  } {
    let score = 100;
    const issues: string[] = [];

    // Throughput scoring (files per second)
    const throughput = metrics.filesProcessed / (metrics.totalDuration / 1000);
    if (throughput < 500) {
      score -= 20;
      issues.push('low throughput');
    } else if (throughput < 1000) {
      score -= 10;
      issues.push('moderate throughput');
    }

    // Cache efficiency scoring
    if (metrics.cacheHitRate < 50) {
      score -= 15;
      issues.push('poor cache efficiency');
    } else if (metrics.cacheHitRate < 80) {
      score -= 5;
      issues.push('moderate cache efficiency');
    }

    // Memory efficiency scoring
    if (memoryDeltaMB > 100) {
      score -= 20;
      issues.push('high memory usage');
    } else if (memoryDeltaMB > 50) {
      score -= 10;
      issues.push('moderate memory usage');
    }

    // Average time per file scoring
    if (metrics.avgTimePerFile > 5) {
      score -= 15;
      issues.push('slow per-file processing');
    } else if (metrics.avgTimePerFile > 2) {
      score -= 5;
      issues.push('moderate per-file processing');
    }

    let rating: string;
    if (score >= 90) rating = 'Excellent ⭐⭐⭐⭐⭐';
    else if (score >= 80) rating = 'Very Good ⭐⭐⭐⭐';
    else if (score >= 70) rating = 'Good ⭐⭐⭐';
    else if (score >= 60) rating = 'Fair ⭐⭐';
    else rating = 'Needs Improvement ⭐';

    const summary = issues.length > 0
      ? `Areas for improvement: ${issues.join(', ')}`
      : 'Optimal performance achieved!';

    return { rating, score, summary };
  }

  /**
   * Scan a codebase directory for TypeScript decorators with enhanced filtering
   * @param codebaseDir Path to the codebase directory
   * @param patterns File patterns to scan (default: TypeScript files)
   * @returns Container with all found decorators
   */
  public async scanCodebase(
    codebaseDir: string,
    patterns: string[] = ['**/*.ts', '**/*.tsx']
  ): Promise<DecoratorContainer> {
    const startTime = Date.now();
    const container: DecoratorContainer = {
      methodCalls: [],
      imports: [],
      fields: [],
      contexts: [],
      others: {},
    };

    try {
      await fs.access(codebaseDir);
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Codebase directory not found: ${codebaseDir}`);
      }
      return container;
    }

    // Optimized file discovery using single glob pattern with better performance
    const files: string[] = [];

    // Use a single optimized glob pattern instead of multiple iterations
    const optimizedPattern = path.join(codebaseDir, '**/*.{ts,tsx}');
    const matchedFiles = await glob.glob(optimizedPattern, {
      windowsPathsNoEscape: true,
      // Enhanced ignore patterns for better performance
      ignore: [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/coverage/**',
        '**/.git/**',
        '**/.next/**',
        '**/.nuxt/**',
        '**/out/**',
        '**/public/**',
        '**/static/**',
        '**/*.d.ts',
        '**/*.generated.ts',
        '**/*.g.ts',
        '**/__tests__/**/*.{test,spec}.{ts,tsx}',
        '**/test/**/*.{test,spec}.{ts,tsx}',
        '**/tests/**/*.{test,spec}.{ts,tsx}',
        '**/*.{test,spec}.{ts,tsx}',
        '**/stories/**',
        '**/*.stories.{ts,tsx}',
        '**/storybook-static/**'
      ],
      // Performance optimizations
      dot: false,
      follow: false,
      absolute: true
    });
    files.push(...matchedFiles);

    if (process.env.DEBUG_PARSER) {
      console.log(`Found ${files.length} TypeScript files to scan for decorators`);
    }

    // Enhanced pre-filtering to skip files that definitely don't have decorators
    const preFilterStartTime = Date.now();
    const filteredFiles = await this.preFilterFiles(files);
    const preFilterDuration = Date.now() - preFilterStartTime;

    const skippedFiles = files.length - filteredFiles.length;
    if (process.env.DEBUG_PARSER) {
      console.log(`Pre-filtering completed in ${preFilterDuration}ms. Skipped ${skippedFiles} files (${Math.round((skippedFiles / files.length) * 100)}% reduction)`);
    }

    // Track cache performance and update metrics
    let cacheHits = 0;
    let cacheMisses = 0;

    // Process files in parallel chunks with adaptive processing
    const CHUNK_SIZE = this.getOptimalChunkSize(filteredFiles.length);
    const chunks = this.chunkArray(filteredFiles, CHUNK_SIZE);

    if (process.env.DEBUG_PARSER) {
      console.log(`Processing ${filteredFiles.length} files in ${chunks.length} chunks of size ${CHUNK_SIZE}`);
    }

    // Process chunks with memory management and error resilience
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkStartTime = Date.now();

      try {
        await Promise.all(
          chunk.map(async (filePath) => {
            try {
              const hadCachedResult = await this.getCachedFileResult(filePath);
              if (hadCachedResult) {
                cacheHits++;
                this.performanceMetrics.cacheHits++;
              } else {
                cacheMisses++;
                this.performanceMetrics.cacheMisses++;
              }
              await this.scanFile(filePath, container);
            } catch (error) {
              if (process.env.DEBUG_PARSER) {
                console.warn(`Error scanning file ${filePath}:`, error);
              }
            }
          })
        );

        // Memory management: adaptive garbage collection
        const shouldTriggerGC = (i + 1) % 5 === 0; // Every 5 chunks
        if (shouldTriggerGC) {
          const memBefore = process.memoryUsage().heapUsed;
          this.forceGarbageCollection();
          const memAfter = process.memoryUsage().heapUsed;
          const memFreed = Math.round((memBefore - memAfter) / 1024 / 1024);

          if (memFreed > 10 && process.env.DEBUG_PARSER) { // Only log if significant memory was freed
            console.log(`Memory cleanup: freed ${memFreed}MB after chunk ${i + 1}`);
          }
        }

        // Progress reporting for large codebases
        if (chunks.length > 10 && (i + 1) % Math.ceil(chunks.length / 10) === 0 && process.env.DEBUG_PARSER) {
          const progress = Math.round(((i + 1) / chunks.length) * 100);
          const chunkDuration = Date.now() - chunkStartTime;
          console.log(`Progress: ${progress}% (${i + 1}/${chunks.length} chunks, ${chunkDuration}ms)`);
        }
      } catch (error) {
        if (process.env.DEBUG_PARSER) {
          console.error(`Error processing chunk ${i + 1}/${chunks.length}:`, error);
        }
        // Continue with next chunk instead of failing completely
      }
    }

    // Update performance metrics
    const duration = Date.now() - startTime;
    const totalDecorators = container.methodCalls.length + container.imports.length +
                           container.fields.length + container.contexts.length;

    this.performanceMetrics.totalDuration = duration;
    this.performanceMetrics.filesProcessed = filteredFiles.length;
    this.performanceMetrics.decoratorsFound = totalDecorators;
    this.performanceMetrics.cacheHitRate = filteredFiles.length > 0 ? Math.round((cacheHits / filteredFiles.length) * 100) : 0;
    this.performanceMetrics.averageTimePerFile = filteredFiles.length > 0 ? duration / filteredFiles.length : 0;
    this.performanceMetrics.filesSkippedByPreFilter = skippedFiles;
    this.performanceMetrics.preFilterEfficiency = files.length > 0 ? Math.round((skippedFiles / files.length) * 100) : 0;
    this.performanceMetrics.cacheHits = cacheHits;
    this.performanceMetrics.cacheMisses = cacheMisses;
    this.performanceMetrics.cacheHitRate = filteredFiles.length > 0 ? Math.round((cacheHits / filteredFiles.length) * 100) : 0;
    this.performanceMetrics.averageTimePerFile = filteredFiles.length > 0 ? duration / filteredFiles.length : 0;

    // Calculate parsing speed ratios
    if (this.performanceMetrics.regexParsingDuration > 0 && this.performanceMetrics.oxcParsingDuration > 0) {
      const avgOxcTime = this.performanceMetrics.oxcParsingDuration / Math.max(1, this.performanceMetrics.oxcParsingSuccessCount + this.performanceMetrics.oxcParsingFailureCount);
      const avgRegexTime = this.performanceMetrics.regexParsingDuration / Math.max(1, this.performanceMetrics.regexParsingCount);
      this.performanceMetrics.astVsRegexSpeedRatio = avgRegexTime / avgOxcTime;
    }

    // Capture memory usage if available
    if (global.gc) {
      global.gc();
      this.performanceMetrics.memoryUsage = process.memoryUsage();
    }

    const cacheHitRate = filteredFiles.length > 0 ? Math.round((cacheHits / filteredFiles.length) * 100) : 0;

    // Only show essential completion info in debug mode or when decorators are found
    if (process.env.DEBUG_PARSER || totalDecorators > 0) {
      console.log(`Decorator scan complete in ${duration}ms. Found ${totalDecorators} decorators in ${filteredFiles.length} files.`);
    }

    // Show detailed breakdown only in debug mode
    if (process.env.DEBUG_PARSER) {
      console.log(`Cache: ${cacheHits} hits, ${cacheMisses} misses (${cacheHitRate}% hit rate). Found:
        - ${container.methodCalls.length} @GQLMethodCall decorators
        - ${container.imports.length} @GQLImport decorators
        - ${container.fields.length} @GQLField decorators
        - ${container.contexts.length} @GQLContext decorators`);
    }

    // Log performance summary if debug mode is enabled
    if (process.env.DEBUG_PARSER) {
      this.logPerformanceSummary();
    }

    // Cache cleanup and memory management
    this.cleanupCache();
    this.forceGarbageCollection();

    // Save persistent cache for next run
    await this.savePersistentCache();

    // Final memory cleanup and resource management
    this.forceGarbageCollection();

    // Log final memory usage for monitoring (debug only)
    if (process.env.DEBUG_PARSER) {
      const finalMemory = this.getMemoryInfo();
      console.log(`Final memory usage: ${finalMemory.heapUsed}MB heap, ${finalMemory.rss}MB RSS`);
    }

    // Clean up resources to allow process to exit
    this.cleanup();

    return container;
  }

  /**
   * Optimized pre-filter files to skip those that definitely don't contain decorators
   * @param files Array of file paths to filter
   * @returns Filtered array of file paths that likely contain decorators
   */
  private async preFilterFiles(files: string[]): Promise<string[]> {
    const filteredFiles: string[] = [];
    const BATCH_SIZE = 200; // Increased batch size for better performance

    // First pass: filter by filename patterns (no I/O)
    const nameFilteredFiles = files.filter(filePath => !this.shouldSkipFileByName(filePath));

    if (process.env.DEBUG_PARSER) {
      console.log(`Name-based filtering: ${files.length} -> ${nameFilteredFiles.length} files (${Math.round(((files.length - nameFilteredFiles.length) / files.length) * 100)}% reduction)`);
    }

    // Second pass: content-based filtering with optimized I/O
    for (let i = 0; i < nameFilteredFiles.length; i += BATCH_SIZE) {
      const batch = nameFilteredFiles.slice(i, i + BATCH_SIZE);

      // Use Promise.allSettled for better error handling and performance
      const batchResults = await Promise.allSettled(
        batch.map(async (filePath) => {
          const hasDecorators = await this.optimizedDecoratorCheck(filePath);
          return hasDecorators ? filePath : null;
        })
      );

      // Process results and handle any failures gracefully
      for (const result of batchResults) {
        if (result.status === 'fulfilled' && result.value) {
          filteredFiles.push(result.value);
        } else if (result.status === 'rejected') {
          // On error, include the file to be safe (get the original file path)
          const failedIndex = batchResults.indexOf(result);
          if (failedIndex >= 0 && failedIndex < batch.length) {
            filteredFiles.push(batch[failedIndex]);
          }
        }
      }
    }

    return filteredFiles;
  }

  /**
   * Determine if a file should be skipped based on its name/path patterns
   * @param filePath Path to the file
   * @returns True if the file should be skipped
   */
  private shouldSkipFileByName(filePath: string): boolean {
    const fileName = path.basename(filePath, path.extname(filePath));
    const relativePath = filePath.toLowerCase();
    const fullPath = filePath.toLowerCase();

    // Enhanced skip patterns with more comprehensive coverage
    const skipPatterns = [
      // Generated files
      /\.generated\./,
      /\.g\./,
      /\.d\.ts$/,
      /\.min\./,
      /\.bundle\./,

      // Configuration files
      /config\./,
      /\.config\./,
      /webpack\./,
      /rollup\./,
      /vite\./,
      /babel\./,
      /eslint/,
      /prettier/,
      /tsconfig/,
      /jest\./,
      /vitest\./,

      // Test files (comprehensive patterns)
      /\.test\./,
      /\.spec\./,
      /__tests__/,
      /__test__/,
      /\/tests?\//,
      /\/spec\//,
      /\.stories\./,
      /\.story\./,

      // Common utility files that rarely have decorators
      /utils?\./,
      /util\./,
      /helper/,
      /constant/,
      /enum/,
      /type/,
      /interface/,
      /model/,
      /schema/,
      /migration/,
      /seed/,

      // Build and tooling files
      /build/,
      /dist/,
      /coverage/,
      /\.git/,
      /node_modules/,
      /\.next/,
      /\.nuxt/,
      /out/,
      /public/,
      /static/,
      /assets/,

      // Documentation and meta files
      /readme/,
      /changelog/,
      /license/,
      /\.md$/,
      /\.txt$/,
      /\.json$/,
      /\.yml$/,
      /\.yaml$/,
      /\.xml$/,

      // Specific file name patterns that rarely contain decorators
      /^index\./,
      /^main\./,
      /^app\./,
      /^entry\./,
      /^bootstrap\./,
      /^setup\./,
      /^init\./,
    ];

    // Additional logic-based filtering
    if (this.isLikelyUtilityFile(fileName, relativePath)) {
      return true;
    }

    return skipPatterns.some(pattern => pattern.test(fullPath));
  }

  /**
   * Determine if a file is likely a utility file based on naming conventions
   * @param fileName Base file name without extension
   * @param relativePath Full relative path
   * @returns True if the file is likely a utility file
   */
  private isLikelyUtilityFile(fileName: string, relativePath: string): boolean {
    // Files with utility-like naming patterns
    const utilityPatterns = [
      /^utility-\d+$/,  // utility-1, utility-2, etc.
      /^util-\d+$/,     // util-1, util-2, etc.
      /^helper-\d+$/,   // helper-1, helper-2, etc.
      /^lib-\d+$/,      // lib-1, lib-2, etc.
      /^common-\d+$/,   // common-1, common-2, etc.
      /^shared-\d+$/,   // shared-1, shared-2, etc.
    ];

    return utilityPatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * Calculate optimal chunk size based on file count, system capabilities, and memory constraints
   * @param fileCount Number of files to process
   * @returns Optimal chunk size
   */
  private getOptimalChunkSize(fileCount: number): number {
    const os = require('os');
    const cpuCores = os.cpus().length;
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();

    // Calculate memory pressure factor (0-1, where 1 is high pressure)
    const memoryPressure = 1 - (freeMemory / totalMemory);

    // Base chunk size calculation considering CPU cores and memory
    let baseChunkSize = Math.ceil(fileCount / (cpuCores * 3)); // More conservative than before

    // Adjust for memory pressure
    if (memoryPressure > 0.8) {
      baseChunkSize = Math.max(5, Math.floor(baseChunkSize * 0.5)); // Reduce chunk size under memory pressure
    } else if (memoryPressure < 0.3) {
      baseChunkSize = Math.floor(baseChunkSize * 1.5); // Increase chunk size when memory is abundant
    }

    // Dynamic scaling based on file count
    if (fileCount < 20) return Math.min(fileCount, 5);
    if (fileCount < 100) return Math.min(fileCount, Math.max(10, baseChunkSize));
    if (fileCount < 500) return Math.min(50, Math.max(15, baseChunkSize));
    if (fileCount < 2000) return Math.min(75, Math.max(20, baseChunkSize));

    // For very large codebases, use adaptive chunking
    return Math.min(100, Math.max(25, baseChunkSize));
  }

  /**
   * Log detailed performance summary
   */
  private logPerformanceSummary(): void {
    const metrics = this.performanceMetrics;

    if (process.env.DEBUG_PARSER) {
      console.log('\n=== Decorator Parser Performance Summary ===');
      console.log(`Total Duration: ${metrics.totalDuration}ms`);
      console.log(`Files Processed: ${metrics.filesProcessed}`);
      console.log(`Decorators Found: ${metrics.decoratorsFound}`);
      console.log(`Average Time per File: ${metrics.averageTimePerFile.toFixed(2)}ms`);

      console.log('\n--- Pre-filtering Performance ---');
      console.log(`Pre-filter Duration: ${metrics.preFilterDuration}ms`);
      console.log(`Files Skipped: ${metrics.filesSkippedByPreFilter}`);
      console.log(`Pre-filter Efficiency: ${metrics.preFilterEfficiency}%`);

      console.log('\n--- Parsing Method Performance ---');
      console.log(`oxc-parser Success: ${metrics.oxcParsingSuccessCount}`);
      console.log(`oxc-parser Failures: ${metrics.oxcParsingFailureCount}`);
      console.log(`@swc/core Success: ${metrics.swcParsingSuccessCount}`);
      console.log(`@swc/core Failures: ${metrics.swcParsingFailureCount}`);
      console.log(`Regex Parsing Count: ${metrics.regexParsingCount}`);
      console.log(`oxc-parser Duration: ${metrics.oxcParsingDuration}ms`);
      console.log(`@swc/core Duration: ${metrics.swcParsingDuration}ms`);
      console.log(`Regex Parsing Duration: ${metrics.regexParsingDuration}ms`);
      // Calculate and display speed ratios
      if (metrics.oxcParsingDuration > 0 && metrics.swcParsingDuration > 0) {
        const avgOxcTime = metrics.oxcParsingDuration / Math.max(1, metrics.oxcParsingSuccessCount + metrics.oxcParsingFailureCount);
        const avgSwcTime = metrics.swcParsingDuration / Math.max(1, metrics.swcParsingSuccessCount + metrics.swcParsingFailureCount);
        const oxcVsSwcRatio = avgSwcTime / avgOxcTime;
        console.log(`oxc vs @swc/core Speed Ratio: ${oxcVsSwcRatio.toFixed(2)}x faster`);
      }

      if (metrics.oxcParsingDuration > 0 && metrics.regexParsingDuration > 0) {
        const avgOxcTime = metrics.oxcParsingDuration / Math.max(1, metrics.oxcParsingSuccessCount + metrics.oxcParsingFailureCount);
        const avgRegexTime = metrics.regexParsingDuration / Math.max(1, metrics.regexParsingCount);
        const oxcVsRegexRatio = avgRegexTime / avgOxcTime;
        console.log(`oxc vs Regex Speed Ratio: ${oxcVsRegexRatio.toFixed(2)}x`);
      }

      console.log('\n--- Cache Performance ---');
      console.log(`Cache Hits: ${metrics.cacheHits}`);
      console.log(`Cache Misses: ${metrics.cacheMisses}`);
      console.log(`Cache Hit Rate: ${metrics.cacheHitRate}%`);

      if (metrics.memoryUsage) {
        console.log('\n--- Memory Usage ---');
        console.log(`Heap Used: ${Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)}MB`);
        console.log(`Heap Total: ${Math.round(metrics.memoryUsage.heapTotal / 1024 / 1024)}MB`);
        console.log(`External: ${Math.round(metrics.memoryUsage.external / 1024 / 1024)}MB`);
      }

      console.log('==========================================\n');
    }
  }

  /**
   * Split an array into chunks of specified size
   * @param array Array to chunk
   * @param chunkSize Size of each chunk
   * @returns Array of chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Normalize file path for consistent cache key generation across platforms
   * @param filePath The file path to normalize
   * @returns Normalized file path
   */
  private normalizeFilePath(filePath: string): string {
    // Convert to absolute path and normalize separators for consistent cache keys
    return path.resolve(filePath).replace(/\\/g, '/');
  }

  /**
   * Get cached file result if the file hasn't been modified
   * @param filePath Path to the file
   * @returns Cached decorators if file is unchanged, null otherwise
   */
  private async getCachedFileResult(filePath: string): Promise<FileCacheEntry['decorators'] | null> {
    try {
      // Normalize file path for consistent cache lookup across platforms
      const normalizedPath = this.normalizeFilePath(filePath);
      const cached = this.fileCache.get(normalizedPath);
      if (!cached) {
        return null;
      }

      // Check if cache entry is too old (older than 1 hour)
      const cacheAge = Date.now() - cached.cachedAt;
      if (cacheAge > 60 * 60 * 1000) { // 1 hour
        this.fileCache.delete(filePath);
        return null;
      }

      const stats = await fs.stat(filePath);
      const currentMtime = stats.mtimeMs;
      const currentSize = stats.size;

      // Validate both modification time and file size for better accuracy
      if (cached.mtime === currentMtime && cached.size === currentSize) {
        return cached.decorators;
      }

      // File has changed, remove from cache
      this.fileCache.delete(normalizedPath);
      return null;
    } catch (error) {
      // If we can't stat the file, remove it from cache
      const normalizedPath = this.normalizeFilePath(filePath);
      this.fileCache.delete(normalizedPath);
      return null;
    }
  }

  /**
   * Cache the decorator results for a file
   * @param filePath Path to the file
   * @param decorators Parsed decorators for the file
   */
  private async cacheFileResult(filePath: string, decorators: FileCacheEntry['decorators']): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      // Use normalized path for consistent cache keys across platforms
      const normalizedPath = this.normalizeFilePath(filePath);
      this.fileCache.set(normalizedPath, {
        mtime: stats.mtimeMs,
        size: stats.size,
        decorators,
        cachedAt: Date.now()
      });
    } catch (error) {
      // If we can't get file stats, don't cache
    }
  }

  /**
   * Scan a single TypeScript file for decorators using hybrid AST/regex approach
   * @param filePath Path to the TypeScript file
   * @param container Container to add found decorators to
   */
  private async scanFile(filePath: string, container: DecoratorContainer): Promise<void> {
    // Check if we can use cached results
    const cachedResult = await this.getCachedFileResult(filePath);
    if (cachedResult) {
      // Add cached results to container
      container.methodCalls.push(...cachedResult.methodCalls);
      container.imports.push(...cachedResult.imports);
      container.fields.push(...cachedResult.fields);
      container.contexts.push(...cachedResult.contexts);
      return;
    }

    // First, do a quick check by reading only the first 1KB to see if decorators are likely present
    const hasDecorators = await this.quickDecoratorCheck(filePath);
    if (!hasDecorators) {
      // Cache empty result
      await this.cacheFileResult(filePath, {
        methodCalls: [],
        imports: [],
        fields: [],
        contexts: []
      });
      return;
    }

    // Read the full file content only if decorators are likely present
    // Use optimized reading with size checks for better performance
    const stats = await fs.stat(filePath);
    if (stats.size > 10 * 1024 * 1024) { // 10MB limit
      if (process.env.DEBUG_PARSER) {
        console.warn(`Skipping very large file: ${filePath} (${Math.round(stats.size / 1024 / 1024)}MB)`);
      }
      return;
    }

    // Use streaming for large files, direct read for small files
    const content = await this.readFileOptimized(filePath, stats.size);

    // Create temporary container for this file's decorators
    const fileContainer: DecoratorContainer = {
      methodCalls: [],
      imports: [],
      fields: [],
      contexts: [],
      others: {}
    };

    // Smart parser selection based on file characteristics
    const parserStrategy = this.selectOptimalParser(content, filePath);

    if (parserStrategy === 'oxc' || parserStrategy === 'auto') {
      // Try oxc-parser AST parsing first for maximum performance (10-20x faster than @swc/core)
      const oxcParsingStartTime = Date.now();
      try {
        const oxcDecorators = await this.extractDecoratorsWithOxc(content, filePath);
        const oxcParsingDuration = Date.now() - oxcParsingStartTime;

        if (oxcDecorators.length > 0) {
          // Update performance metrics
          this.performanceMetrics.oxcParsingSuccessCount++;
          this.performanceMetrics.oxcParsingDuration += oxcParsingDuration;

          // Process AST-extracted decorators
          for (const decorator of oxcDecorators) {
            this.processDecoratorFromAST(decorator, fileContainer);
          }

          // Cache and add results
          await this.cacheFileResult(filePath, fileContainer);
          container.methodCalls.push(...fileContainer.methodCalls);
          container.imports.push(...fileContainer.imports);
          container.fields.push(...fileContainer.fields);
          container.contexts.push(...fileContainer.contexts);

          if (process.env.DEBUG_PARSER) {
            console.log(`oxc-parser AST parsing successful for ${filePath} in ${oxcParsingDuration}ms`);
          }
          return;
        }
      } catch (oxcError) {
        const oxcParsingDuration = Date.now() - oxcParsingStartTime;
        this.performanceMetrics.oxcParsingFailureCount++;
        this.performanceMetrics.oxcParsingDuration += oxcParsingDuration;

        if (process.env.DEBUG_PARSER) {
          console.warn(`oxc-parser AST parsing failed for ${filePath}, falling back to @swc/core:`, oxcError);
        }
      }
    }

    if (parserStrategy === 'swc' || parserStrategy === 'auto') {
      // Fallback to @swc/core AST parsing for compatibility
      const swcParsingStartTime = Date.now();
      try {
        const swcDecorators = await this.extractDecoratorsWithSwc(content, filePath);
        const swcParsingDuration = Date.now() - swcParsingStartTime;

        if (swcDecorators.length > 0) {
          // Update performance metrics
          this.performanceMetrics.swcParsingSuccessCount++;
          this.performanceMetrics.swcParsingDuration += swcParsingDuration;

          // Process AST-extracted decorators
          for (const decorator of swcDecorators) {
            this.processDecoratorFromAST(decorator, fileContainer);
          }

          // Cache and add results
          await this.cacheFileResult(filePath, fileContainer);
          container.methodCalls.push(...fileContainer.methodCalls);
          container.imports.push(...fileContainer.imports);
          container.fields.push(...fileContainer.fields);
          container.contexts.push(...fileContainer.contexts);

          if (process.env.DEBUG_PARSER) {
            console.log(`@swc/core AST parsing successful for ${filePath} in ${swcParsingDuration}ms`);
          }
          return;
        }
      } catch (swcError) {
        const swcParsingDuration = Date.now() - swcParsingStartTime;
        this.performanceMetrics.swcParsingFailureCount++;
        this.performanceMetrics.swcParsingDuration += swcParsingDuration;

        if (process.env.DEBUG_PARSER) {
          console.warn(`@swc/core AST parsing failed for ${filePath}, falling back to regex:`, swcError);
        }
      }
    }

    // Use regex parsing if selected or as final fallback
    if (parserStrategy === 'regex' || parserStrategy === 'auto') {
      // Fallback to optimized regex parsing
      const regexParsingStartTime = Date.now();
      this.performanceMetrics.regexParsingCount++;

      // Use optimized regex pattern for better performance
      // This pattern is more efficient than the original [\s\S]*? approach
      const decoratorRegex = /@(GQL(?:MethodCall|Import|Field|Context))\s*\(\s*([^()]*(?:\([^()]*\)[^()]*)*)\s*\)/g;
      let match;

      while ((match = decoratorRegex.exec(content)) !== null) {
        try {
          const [fullMatch, decoratorName, args] = match;
          const lineNumber = content.substring(0, match.index).split('\n').length;

          // Clean and normalize multi-line arguments
          const cleanArgs = this.cleanMultilineArgs(args);

          // Create parsed decorator object
          const parsedDecorator: ParsedDecorator = {
            name: decoratorName,
            arguments: cleanArgs,
            raw: fullMatch,
            filePath,
            lineNumber,
            target: this.extractTargetFromContent(content, match.index + fullMatch.length)
          };

          this.processDecoratorFromRegex(parsedDecorator, fileContainer);
        } catch (error) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`Error processing decorator in ${filePath}:`, error);
          }
        }
      }

      const regexParsingDuration = Date.now() - regexParsingStartTime;
      this.performanceMetrics.regexParsingDuration += regexParsingDuration;

      if (process.env.DEBUG_PARSER) {
        console.log(`Regex parsing completed for ${filePath} in ${regexParsingDuration}ms`);
      }

      // Cache the results for this file
      await this.cacheFileResult(filePath, {
        methodCalls: fileContainer.methodCalls,
        imports: fileContainer.imports,
        fields: fileContainer.fields,
        contexts: fileContainer.contexts
      });

      // Add results to main container
      container.methodCalls.push(...fileContainer.methodCalls);
      container.imports.push(...fileContainer.imports);
      container.fields.push(...fileContainer.fields);
      container.contexts.push(...fileContainer.contexts);
    }
  }

  /**
   * Select optimal parser based on file characteristics
   * @param content File content to analyze
   * @param filePath File path for context
   * @returns Parser strategy: 'oxc', 'swc', 'regex', or 'auto'
   */
  private selectOptimalParser(content: string, filePath: string): 'oxc' | 'swc' | 'regex' | 'auto' {
    const fileSize = content.length;
    const isComplexFile = content.includes('namespace') || content.includes('declare') || content.includes('module');
    const hasJSX = filePath.endsWith('.tsx') || content.includes('<') && content.includes('>');
    const decoratorCount = (content.match(/@GQL/g) || []).length;

    // For very small files with few decorators, regex might be faster
    if (fileSize < 1000 && decoratorCount <= 2) {
      return 'regex';
    }

    // For complex TypeScript files, @swc/core might be more reliable
    if (isComplexFile || hasJSX) {
      return 'swc';
    }

    // IMPORTANT: oxc-parser has issues with TypeScript decorators, so we should be more conservative
    // and use 'auto' strategy to allow fallback to @swc/core and regex parsing
    // For large files with many decorators, use auto strategy instead of forcing oxc
    if (fileSize > 50000 || decoratorCount > 20) {
      return 'auto'; // Changed from 'oxc' to 'auto' for better fallback support
    }

    // Default to auto (try oxc first, then fallback to @swc/core and regex)
    return 'auto';
  }

  /**
   * Extract decorators using oxc-parser AST parsing for maximum performance (10-20x faster than @swc/core)
   * @param content File content to parse
   * @param filePath File path for error reporting
   * @returns Array of parsed decorators
   */
  private async extractDecoratorsWithOxc(content: string, filePath: string): Promise<ParsedDecorator[]> {
    try {
      const result = oxc.parseSync(filePath, content, {
        lang: filePath.endsWith('.tsx') ? 'tsx' : 'ts',
        sourceType: 'module',
        preserveParens: false, // Better performance
        showSemanticErrors: false, // Fast mode for better performance
      });

      if (result.errors && result.errors.length > 0) {
        // If there are parsing errors, let it fall back to @swc/core
        throw new Error(`oxc-parser errors: ${result.errors.map(e => e.message).join(', ')}`);
      }

      const decorators: ParsedDecorator[] = [];
      this.traverseOxcASTForDecorators(result.program, content, filePath, decorators);
      return decorators;
    } catch (error) {
      throw new Error(`oxc-parser parsing failed: ${error}`);
    }
  }

  /**
   * Extract decorators using @swc/core AST parsing for better performance and accuracy (fallback from oxc-parser)
   * @param content File content to parse
   * @param filePath File path for error reporting
   * @returns Array of parsed decorators
   */
  private async extractDecoratorsWithSwc(content: string, filePath: string): Promise<ParsedDecorator[]> {
    try {
      const ast = swcParseSync(content, {
        syntax: 'typescript',
        tsx: filePath.endsWith('.tsx'),
        decorators: true,
        dynamicImport: true,
      });

      const decorators: ParsedDecorator[] = [];
      this.traverseSwcASTForDecorators(ast, content, filePath, decorators);
      return decorators;
    } catch (error) {
      throw new Error(`@swc/core parsing failed: ${error}`);
    }
  }

  /**
   * Traverse oxc-parser AST to find GQL decorators (ESTree/TS-ESTree compatible)
   * @param program AST program node
   * @param content Original file content
   * @param filePath File path for context
   * @param decorators Array to collect found decorators
   */
  private traverseOxcASTForDecorators(program: any, content: string, filePath: string, decorators: ParsedDecorator[]): void {
    if (!program || !program.body) return;

    // Traverse all nodes in the program
    this.traverseOxcNode(program, content, filePath, decorators);
  }

  /**
   * Recursively traverse oxc-parser AST nodes to find decorators
   * @param node AST node to traverse
   * @param content Original file content
   * @param filePath File path for context
   * @param decorators Array to collect found decorators
   */
  private traverseOxcNode(node: any, content: string, filePath: string, decorators: ParsedDecorator[]): void {
    if (!node || typeof node !== 'object') return;

    // Check if this node has decorators (ESTree/TS-ESTree format)
    if (node.decorators && Array.isArray(node.decorators)) {
      for (const decorator of node.decorators) {
        if (decorator.type === 'Decorator' && decorator.expression) {
          const decoratorInfo = this.extractOxcDecoratorInfo(decorator, content, filePath);
          if (decoratorInfo && decoratorInfo.name.startsWith('GQL')) {
            decorators.push(decoratorInfo);
          }
        }
      }
    }

    // Recursively traverse child nodes
    for (const key in node) {
      if (key === 'range' || key === 'loc' || key === 'parent') continue; // Skip metadata
      const child = node[key];

      if (Array.isArray(child)) {
        for (const item of child) {
          this.traverseOxcNode(item, content, filePath, decorators);
        }
      } else if (child && typeof child === 'object') {
        this.traverseOxcNode(child, content, filePath, decorators);
      }
    }
  }

  /**
   * Extract decorator information from oxc-parser decorator node
   * @param decorator Decorator AST node
   * @param content Original file content
   * @param filePath File path for context
   * @returns Parsed decorator or null
   */
  private extractOxcDecoratorInfo(decorator: any, content: string, filePath: string): ParsedDecorator | null {
    try {
      if (!decorator.range) return null;

      const [start, end] = decorator.range;
      const decoratorText = content.substring(start, end);

      // Extract decorator name and arguments
      const match = decoratorText.match(/@(\w+)\s*\(\s*(.*)\s*\)$/s);
      if (!match) return null;

      const [fullMatch, decoratorName, args] = match;
      const lineNumber = content.substring(0, start).split('\n').length;

      // Find the target element (function, class, property, etc.)
      const target = this.extractTargetFromContent(content, end);

      return {
        name: decoratorName,
        arguments: args.trim(),
        raw: decoratorText,
        filePath,
        lineNumber,
        target
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error extracting oxc decorator info:`, error);
      }
      return null;
    }
  }

  /**
   * Traverse @swc/core AST to find GQL decorators
   * @param node AST node to traverse
   * @param content Original file content
   * @param filePath File path for context
   * @param decorators Array to collect found decorators
   */
  private traverseSwcASTForDecorators(node: any, content: string, filePath: string, decorators: ParsedDecorator[]): void {
    if (!node || typeof node !== 'object') return;

    // Check if this node has decorators
    if (node.decorators && Array.isArray(node.decorators)) {
      for (const decorator of node.decorators) {
        if (decorator.type === 'Decorator' && decorator.expression) {
          const decoratorInfo = this.extractDecoratorInfo(decorator, content, filePath);
          if (decoratorInfo && decoratorInfo.name.startsWith('GQL')) {
            decorators.push(decoratorInfo);
          }
        }
      }
    }

    // Recursively traverse child nodes
    for (const key in node) {
      if (key === 'span' || key === 'parent') continue; // Skip metadata
      const child = node[key];

      if (Array.isArray(child)) {
        for (const item of child) {
          this.traverseSwcASTForDecorators(item, content, filePath, decorators);
        }
      } else if (child && typeof child === 'object') {
        this.traverseSwcASTForDecorators(child, content, filePath, decorators);
      }
    }
  }

  /**
   * Extract decorator information from @swc/core decorator node
   * @param decorator Decorator AST node
   * @param content Original file content
   * @param filePath File path for context
   * @returns Parsed decorator or null
   */
  private extractDecoratorInfo(decorator: any, content: string, filePath: string): ParsedDecorator | null {
    try {
      const start = decorator.span.start;
      const end = decorator.span.end;
      const decoratorText = content.substring(start, end);

      // Extract decorator name and arguments
      const match = decoratorText.match(/@(\w+)\s*\(\s*(.*)\s*\)$/s);
      if (!match) return null;

      const [fullMatch, decoratorName, args] = match;
      const lineNumber = content.substring(0, start).split('\n').length;

      // Find the target element (function, class, property, etc.)
      const target = this.extractTargetFromContent(content, end);

      return {
        name: decoratorName,
        arguments: args.trim(),
        raw: decoratorText,
        filePath,
        lineNumber,
        target
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error extracting decorator info:`, error);
      }
      return null;
    }
  }

  /**
   * Process decorator extracted from AST
   * @param decorator Parsed decorator from AST
   * @param container Container to add processed decorator to
   */
  private processDecoratorFromAST(decorator: ParsedDecorator, container: DecoratorContainer): void {
    // Use the same processing logic as regex-based decorators
    this.processDecoratorFromRegex(decorator, container);
  }

  /**
   * Read file content using optimized strategy based on file size
   * @param filePath Path to the file to read
   * @param fileSize Size of the file in bytes
   * @returns File content as string
   */
  private async readFileOptimized(filePath: string, fileSize: number): Promise<string> {
    // For small files (< 100KB), use direct read
    if (fileSize < 100 * 1024) {
      return await fs.readFile(filePath, 'utf8');
    }

    // For larger files, use streaming with string concatenation
    return new Promise((resolve, reject) => {
      let content = '';
      const stream = fs.createReadStream(filePath, { encoding: 'utf8' });

      stream.on('data', (chunk: string | Buffer) => {
        content += chunk.toString();
      });

      stream.on('end', () => {
        resolve(content);
      });

      stream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Optimized decorator check using efficient file reading strategies
   * @param filePath Path to the file to check
   * @returns True if the file likely contains GQL decorators
   */
  private async optimizedDecoratorCheck(filePath: string): Promise<boolean> {
    try {
      // Use a simple approach with fs.readFile and size limit
      const stats = await fs.stat(filePath);

      // Skip very large files to avoid memory issues
      if (stats.size > 1024 * 1024) { // 1MB limit
        return true; // Include large files to be safe
      }

      // Read only first 2KB for performance
      const readSize = Math.min(stats.size, 2048);
      const buffer = Buffer.alloc(readSize);

      const fd = await fs.open(filePath, 'r');
      try {
        await fs.read(fd, buffer, 0, readSize, 0);
        const content = buffer.toString('utf8');

        // Fast string search for decorator patterns using optimized check
        return OPTIMIZED_DECORATOR_PATTERNS.hasGQLDecorator.test(content);
      } finally {
        await fs.close(fd);
      }
    } catch (error) {
      // If we can't read the file, include it to be safe
      return true;
    }
  }

  /**
   * Quick check to see if a file likely contains GQL decorators by reading only the first 2KB
   * @param filePath Path to the file to check
   * @returns True if the file likely contains GQL decorators
   */
  private async quickDecoratorCheck(filePath: string): Promise<boolean> {
    try {
      // Use optimized streaming approach for consistency
      const stream = fs.createReadStream(filePath, {
        encoding: 'utf8',
        start: 0,
        end: 2047 // Read first 2KB (0-2047 bytes) for better detection
      });

      let partialContent = '';

      return new Promise((resolve) => {
        stream.on('data', (chunk: string | Buffer) => {
          partialContent += chunk.toString();

          // Early termination if we find decorators using optimized pattern
          if (OPTIMIZED_DECORATOR_PATTERNS.hasGQLDecorator.test(partialContent)) {
            stream.destroy();
            resolve(true);
          }
        });

        stream.on('end', () => {
          // Fast string search for any GQL decorator pattern using optimized regex
          resolve(OPTIMIZED_DECORATOR_PATTERNS.hasGQLDecorator.test(partialContent));
        });

        stream.on('error', () => {
          // If we can't read the file partially, include it to be safe
          resolve(true);
        });
      });
    } catch (error) {
      // If we can't read the file partially, include it to be safe
      return true;
    }
  }

  /**
   * Clean and normalize multi-line decorator arguments
   * @param args Raw decorator arguments string
   * @returns Cleaned arguments string
   */
  private cleanMultilineArgs(args: string): string {
    return args
      .replace(/\s+/g, ' ')           // Normalize whitespace
      .replace(/,\s*([}\]])/g, '$1')  // Clean trailing commas
      .replace(/\n/g, ' ')            // Replace newlines with spaces
      .trim();
  }

  /**
   * Extract the target element (function, class, etc.) that follows a decorator
   * @param content File content
   * @param decoratorEndIndex Index where decorator ends
   * @returns Target element context for field name inference
   */
  private extractTargetFromContent(content: string, decoratorEndIndex: number): string {
    const remainingContent = content.substring(decoratorEndIndex);

    // Extract the next few lines to get the full declaration context
    const lines = remainingContent.split('\n').slice(0, 3); // Get up to 3 lines
    const targetContext = lines.join('\n').trim();

    // Look for export const/let/var patterns with assignment
    const exportConstMatch = targetContext.match(/^\s*export\s+(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/);
    if (exportConstMatch) {
      return `export const ${exportConstMatch[1]}`;
    }

    // Look for export function patterns
    const exportFunctionMatch = targetContext.match(/^\s*export\s+function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
    if (exportFunctionMatch) {
      return `export function ${exportFunctionMatch[1]}`;
    }

    // Look for export default patterns
    const exportDefaultMatch = targetContext.match(/^\s*export\s+default\s+(?:function\s+)?([a-zA-Z_$][a-zA-Z0-9_$]*)/);
    if (exportDefaultMatch) {
      return `export default ${exportDefaultMatch[1]}`;
    }

    // Look for const/let/var declarations with assignment
    const constMatch = targetContext.match(/^\s*(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/);
    if (constMatch) {
      return `const ${constMatch[1]}`;
    }

    // Look for function declarations
    const functionMatch = targetContext.match(/^\s*function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
    if (functionMatch) {
      return `function ${functionMatch[1]}`;
    }

    // Look for class declarations
    const classMatch = targetContext.match(/^\s*(?:export\s+)?class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
    if (classMatch) {
      return `class ${classMatch[1]}`;
    }

    // Return the first line as fallback context
    return lines[0] || 'unknown';
  }



  /**
   * Process a single decorator parsed from regex and add to container
   * @param parsedDecorator The parsed decorator object
   * @param container The container to add to
   */
  private processDecoratorFromRegex(
    parsedDecorator: ParsedDecorator,
    container: DecoratorContainer
  ): void {
    const { name, arguments: args } = parsedDecorator;

    // Process based on decorator type
    switch (name) {
      case 'GQLMethodCall':
        const methodCallData = this.parseGQLMethodCall(args, parsedDecorator);
        if (methodCallData) {
          container.methodCalls.push({ decorator: parsedDecorator, data: methodCallData });
        }
        break;

      case 'GQLImport':
        const importData = this.parseGQLImport(args);
        if (importData) {
          container.imports.push({ decorator: parsedDecorator, data: importData });
        }
        break;

      case 'GQLField':
        const fieldData = this.parseGQLFieldFromRegex(args);
        if (fieldData) {
          container.fields.push({ decorator: parsedDecorator, data: fieldData });
        }
        break;

      case 'GQLContext':
        const contextData = this.parseGQLContext(args);
        if (contextData) {
          container.contexts.push({ decorator: parsedDecorator, data: contextData });
        }
        break;

      default:
        // Store unknown decorators for future extensibility
        if (!container.others[name]) {
          container.others[name] = [];
        }
        container.others[name].push({ decorator: parsedDecorator, data: {} });
        break;
    }
  }



  /**
   * Parse @GQLMethodCall decorator arguments
   * @param args The decorator arguments string
   * @param decorator The parsed decorator object for field name inference
   * @returns Parsed method call data or null if invalid
   */
  private parseGQLMethodCall(args: string, decorator?: ParsedDecorator): GQLMethodCallData | null {
    try {
      // Remove outer braces and parse as object
      const cleanArgs = args.replace(/^\{|\}$/g, '').trim();
      const data = this.parseObjectLiteral(cleanArgs);

      if (!data.type) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Invalid @GQLMethodCall decorator: missing required 'type' parameter`);
        }
        return null;
      }

      // Field is optional - infer from function name if not provided
      let fieldName = data.field ? this.cleanStringValue(data.field) : undefined;

      if (!fieldName && decorator) {
        // Try to infer field name from function name
        fieldName = this.inferFieldNameFromTarget(decorator.target);
        if (!fieldName) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`Invalid @GQLMethodCall decorator: missing 'field' parameter and could not infer from function name`);
          }
          return null;
        }
      }

      // If no field name and no decorator provided, that's okay for testing purposes
      // In real usage, the decorator object should always be provided

      return {
        type: this.cleanStringValue(data.type),
        field: fieldName,
        call: data.call ? this.cleanStringValue(data.call) : undefined,
        schema: data.schema ? this.cleanStringValue(data.schema) : undefined,
        async: data.async === 'true',
        enableTypeCasting: data.enableTypeCasting === 'true',
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error parsing @GQLMethodCall decorator:`, error);
      }
      return null;
    }
  }

  /**
   * Infer field name from decorator target (function name)
   * @param target The target string from the decorator
   * @returns Inferred field name or undefined if cannot be determined
   */
  private inferFieldNameFromTarget(target: string): string | undefined {
    if (!target) return undefined;

    // Handle various export patterns:
    // export const functionName = ...
    // export function functionName(...) ...
    // const functionName = ...
    // function functionName(...) ...

    // Match export const/let/var patterns
    const exportConstMatch = target.match(/export\s+(?:const|let|var)\s+(\w+)/);
    if (exportConstMatch) {
      return exportConstMatch[1];
    }

    // Match export function patterns
    const exportFunctionMatch = target.match(/export\s+function\s+(\w+)/);
    if (exportFunctionMatch) {
      return exportFunctionMatch[1];
    }

    // Match const/let/var patterns (without export)
    const constMatch = target.match(/(?:const|let|var)\s+(\w+)/);
    if (constMatch) {
      return constMatch[1];
    }

    // Match function patterns (without export)
    const functionMatch = target.match(/function\s+(\w+)/);
    if (functionMatch) {
      return functionMatch[1];
    }

    // If no pattern matches, try to extract the first identifier
    const identifierMatch = target.match(/\b(\w+)\b/);
    if (identifierMatch) {
      return identifierMatch[1];
    }

    return undefined;
  }

  /**
   * Parse @GQLImport decorator arguments
   * @param args The decorator arguments string
   * @returns Parsed import data or null if invalid
   */
  private parseGQLImport(args: string): GQLImportData | null {
    try {
      // Handle simple string argument or object literal
      if (args.startsWith('"') || args.startsWith("'")) {
        return {
          importStatement: this.cleanStringValue(args),
        };
      }

      // Parse as object literal
      const cleanArgs = args.replace(/^\{|\}$/g, '').trim();
      const data = this.parseObjectLiteral(cleanArgs);

      if (!data.import && !data.importStatement && !data.statement) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Invalid @GQLImport decorator: missing import statement`);
        }
        return null;
      }

      return {
        importStatement: this.cleanStringValue(data.import || data.importStatement || data.statement),
        schema: data.schema ? this.cleanStringValue(data.schema) : undefined,
        conditional: data.conditional === 'true',
        condition: data.condition ? this.cleanStringValue(data.condition) : undefined,
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error parsing @GQLImport decorator:`, error);
      }
      return null;
    }
  }



  /**
   * Parse @GQLField decorator arguments from regex (without ts-morph dependency)
   * @param args The decorator arguments string
   * @returns Parsed field data or null if invalid
   */
  private parseGQLFieldFromRegex(args: string): GQLFieldData | null {
    try {
      const cleanArgs = args.replace(/^\{|\}$/g, '').trim();
      const data = this.parseObjectLiteral(cleanArgs);

      if (!data.ref || !data.name) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Invalid @GQLField decorator: missing required 'ref' or 'name' parameter`);
        }
        return null;
      }

      // For regex parsing, type is required since we can't auto-infer without AST
      if (!data.type) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Invalid @GQLField decorator: missing required 'type' parameter. Auto-inference is not available with regex parsing.`);
        }
        return null;
      }

      return {
        ref: this.cleanStringValue(data.ref),
        name: this.cleanStringValue(data.name),
        type: this.cleanStringValue(data.type),
        schema: data.schema ? this.cleanStringValue(data.schema) : undefined,
        optional: data.optional === 'true',
        importPath: data.importPath ? this.cleanStringValue(data.importPath) : undefined,
        hidden: data.hidden === 'true',
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error parsing @GQLField decorator:`, error);
      }
      return null;
    }
  }

  /**
   * Parse @GQLContext decorator arguments
   * @param args The decorator arguments string
   * @returns Parsed context data or null if invalid
   */
  private parseGQLContext(args: string): GQLContextData | null {
    try {
      const cleanArgs = args.replace(/^\{|\}$/g, '').trim();
      const data = this.parseObjectLiteral(cleanArgs);

      if (!data.path || !data.name) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Invalid @GQLContext decorator: missing required 'path' or 'name' parameter`);
        }
        return null;
      }

      return {
        path: this.cleanStringValue(data.path),
        name: this.cleanStringValue(data.name),
        schema: data.schema ? this.cleanStringValue(data.schema) : undefined,
      };
    } catch (error) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`Error parsing @GQLContext decorator:`, error);
      }
      return null;
    }
  }

  /**
   * Parse a simple object literal string into key-value pairs using regex
   * @param objectStr The object literal string
   * @returns Parsed object
   */
  private parseObjectLiteral(objectStr: string): Record<string, string> {
    // Use regex-based parsing directly for better performance
    return this.parseObjectLiteralRegex(objectStr);
  }

  /**
   * Fallback regex-based object literal parsing
   * @param objectStr The object literal string
   * @returns Parsed object
   */
  private parseObjectLiteralRegex(objectStr: string): Record<string, string> {
    const result: Record<string, string> = {};

    // Enhanced regex to handle more cases including unquoted values
    const propertyRegex = /(\w+)\s*:\s*(?:(['"`])((?:\\.|(?!\2)[^\\])*?)\2|([^,}]+))/g;

    let match;
    while ((match = propertyRegex.exec(objectStr)) !== null) {
      const [, key, , quotedValue, unquotedValue] = match;
      result[key] = quotedValue || unquotedValue?.trim() || '';
    }

    return result;
  }

  /**
   * Clean string values by removing quotes and trimming
   * @param value The string value to clean
   * @returns Cleaned string
   */
  private cleanStringValue(value: string): string {
    return value.replace(/^['"`]|['"`]$/g, '').trim();
  }

  /**
   * Validate decorator arguments for common issues
   * @param decoratorName The name of the decorator
   * @param data The parsed data
   * @returns Validation result with errors if any
   */
  public validateDecorator(
    decoratorName: string,
    data: any
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (decoratorName) {
      case 'GQLMethodCall':
        if (!data.type) errors.push('Missing required "type" parameter');
        if (data.type && typeof data.type !== 'string') {
          errors.push('"type" parameter must be a string');
        }
        if (data.call && typeof data.call !== 'string') {
          errors.push('"call" parameter must be a string');
        }
        break;

      case 'GQLImport':
        if (!data.importStatement) errors.push('Missing required import statement');
        if (data.importStatement && typeof data.importStatement !== 'string') {
          errors.push('Import statement must be a string');
        }
        break;

      case 'GQLField':
        if (!data.ref) errors.push('Missing required "ref" parameter');
        if (!data.name) errors.push('Missing required "name" parameter');
        // Note: 'type' parameter is now optional and can be auto-inferred
        if (data.type && typeof data.type !== 'string') {
          errors.push('"type" parameter must be a string');
        }
        break;

      case 'GQLContext':
        if (!data.path) errors.push('Missing required "path" parameter');
        if (!data.name) errors.push('Missing required "name" parameter');
        break;
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Get import path resolution for a decorator
   * @param decoratorFilePath The file containing the decorator
   * @param targetPath The target path to resolve
   * @returns Resolved import path
   */
  public resolveImportPath(decoratorFilePath: string, targetPath: string): string {
    // If it's already an absolute path or module name, return as-is
    if (path.isAbsolute(targetPath) || !targetPath.startsWith('.')) {
      return targetPath;
    }

    // Resolve relative path from decorator file location
    const decoratorDir = path.dirname(decoratorFilePath);
    const resolvedPath = path.resolve(decoratorDir, targetPath);

    // Convert back to relative path from project root if needed
    const projectRoot = process.cwd();
    const relativePath = path.relative(projectRoot, resolvedPath);

    return relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  }
}
