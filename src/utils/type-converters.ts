import * as path from 'path';
import * as fs from 'fs';
import * as glob from 'glob';

/**
 * Cache for scalar types extracted from schema
 */
let cachedSchemaScalarTypes: Set<string> | null = null;

/**
 * Extract all scalar type names from GraphQL schema files
 * @param schemaRoot The root directory containing schema files
 * @returns Set of scalar type names defined in the schema
 */
export function extractScalarTypesFromSchema(schemaRoot?: string): Set<string> {
    if (cachedSchemaScalarTypes) {
        return cachedSchemaScalarTypes;
    }

    const scalarTypes = new Set<string>();

    // Add built-in GraphQL scalar types
    scalarTypes.add('ID');
    scalarTypes.add('String');
    scalarTypes.add('Int');
    scalarTypes.add('Float');
    scalarTypes.add('Boolean');

    if (!schemaRoot) {
        cachedSchemaScalarTypes = scalarTypes;
        return scalarTypes;
    }

    try {
        // Find all GraphQL schema files
        const schemaFiles = glob.sync(path.join(schemaRoot, '**', '*.{gql,graphql}'), {
            windowsPathsNoEscape: true
        });

        // Process each schema file to find scalar definitions
        for (const schemaFile of schemaFiles) {
            if (fs.existsSync(schemaFile)) {
                const content = fs.readFileSync(schemaFile, 'utf8');
                const lines = content.split('\n');

                for (const line of lines) {
                    // Match scalar type definitions: "scalar TypeName"
                    const scalarMatch = line.trim().match(/^scalar\s+([A-Za-z][A-Za-z0-9_]*)/);
                    if (scalarMatch) {
                        scalarTypes.add(scalarMatch[1]);
                    }
                }
            }
        }
    } catch (error) {
        if (process.env.DEBUG_PARSER) {
          console.warn('Error extracting scalar types from schema:', error);
        }
    }

    cachedSchemaScalarTypes = scalarTypes;
    return scalarTypes;
}

/**
 * Generate scalar mappings with known types preserved and unknown scalars as 'unknown'
 * @param schemaRoot The root directory containing schema files
 * @returns Record mapping scalar types appropriately
 */
export function generateScalarMappings(schemaRoot?: string): Record<string, string> {
    const scalarTypes = extractScalarTypesFromSchema(schemaRoot);
    const mappings: Record<string, string> = {};

    // Built-in GraphQL scalar types and common scalars with their proper TypeScript types
    const knownScalars: Record<string, string> = {
        // Built-in GraphQL scalars
        ID: 'string | number',
        String: 'string',
        Int: 'number',
        Float: 'number',
        Boolean: 'boolean',

        // Common scalar types with proper TypeScript mappings
        // Note: Using globalThis.Date to avoid circular reference
        Date: 'globalThis.Date',
        DateTime: 'globalThis.Date',
        Time: 'string',
        Timestamp: 'number',
        JSON: 'Record<string, any>',
        JSONObject: 'Record<string, any>',
        URL: 'string',
        Email: 'string',
        ObjectID: 'string',
        Decimal: 'number',
        Upload: 'File',
    };

    for (const scalarType of scalarTypes) {
        if (knownScalars[scalarType]) {
            // Use proper TypeScript type for known scalars
            mappings[scalarType] = knownScalars[scalarType];
        } else {
            // Use 'unknown' for truly custom/undefined scalars
            mappings[scalarType] = 'unknown';
        }
    }

    return mappings;
}

/**
 * Get scalar type mappings (known scalars keep proper types, unknown scalars map to 'unknown')
 * @param schemaRoot The root directory containing schema files
 * @returns Record of scalar type mappings
 */
export function getScalarTypeMappings(schemaRoot?: string): Record<string, string> {
    return generateScalarMappings(schemaRoot);
}

/**
 * Clear cached scalar types (useful for testing or reloading)
 */
export function clearScalarMappingsCache(): void {
    cachedSchemaScalarTypes = null;
}

/**
 * Convert GraphQL type to TypeScript type
 * @param graphqlType GraphQL type string
 * @param schemaRoot Optional schema root for detecting scalar types
 * @param useCleanScalarTypes Whether to use clean scalar type names instead of Scalars['Type']['output']
 * @returns TypeScript type string
 */
export function convertGraphQLTypeToTypeScript(
    graphqlType: string,
    schemaRoot?: string,
    useCleanScalarTypes: boolean = true
): string {
    // Remove quotes if present
    graphqlType = graphqlType.replace(/^["']|["']$/g, '');

    // If it's already a TypeScript union type, return as is
    if (graphqlType.includes('|')) {
        return graphqlType;
    }

    // Handle non-null types
    const isNonNull = graphqlType.endsWith('!');
    const baseType = isNonNull ? graphqlType.slice(0, -1) : graphqlType;

    // Check if this is a scalar type
    const scalarTypes = extractScalarTypesFromSchema(schemaRoot);
    if (scalarTypes.has(baseType)) {
        // For scalar types, use 'unknown' type
        const tsBaseType = useCleanScalarTypes ? baseType : `Scalars['${baseType}']['output']`;
        return isNonNull ? tsBaseType : `${tsBaseType} | null | undefined`;
    }

    // For non-scalar types, use the type name as-is
    const tsBaseType = baseType;

    // Add nullability if needed
    return isNonNull ? tsBaseType : `${tsBaseType} | null | undefined`;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use getScalarTypeMappings() instead
 */
export const SCALAR_TYPE_MAPPINGS = new Proxy({} as Record<string, string>, {
    get(_target, prop) {
        const mappings = getScalarTypeMappings();
        return mappings[prop as string];
    },
    ownKeys() {
        const mappings = getScalarTypeMappings();
        return Object.keys(mappings);
    },
    has(_target, prop) {
        const mappings = getScalarTypeMappings();
        return prop in mappings;
    }
});

/**
 * Helper function to find the closing brace for a type definition
 * @param lines Array of file lines
 * @param openingLineIndex Index of the line with the opening brace
 * @returns Index of the line with the closing brace or -1 if not found
 */
export function findClosingBraceIndex(lines: string[], openingLineIndex: number): number {
    let braceCount = 0;
    let foundOpening = false;

    for (let i = openingLineIndex; i < lines.length; i++) {
        const line = lines[i];

        // Count opening braces
        if (line.includes('{')) {
            foundOpening = true;
            braceCount += (line.match(/\{/g) || []).length;
        }

        // Count closing braces
        if (line.includes('}')) {
            braceCount -= (line.match(/\}/g) || []).length;
        }

        // When brace count reaches 0 after finding opening, we've found the closing brace
        if (foundOpening && braceCount === 0) {
            return i;
        }
    }

    return -1; // Closing brace not found
}

/**
 * Generate scalars.ts file content with clean type aliases
 * @param schemaRoot The schema root directory for detecting scalar types
 * @returns Generated TypeScript content for scalars.ts file
 */
export function generateScalarsFileContent(schemaRoot: string = 'schema'): string {
    const scalarMappings = generateScalarMappings(schemaRoot);

    // Generate type aliases for all scalar types with appropriate mappings
    const typeAliases = Object.entries(scalarMappings)
        .map(([scalarName, tsType]) => {
            return `export type ${scalarName} = ${tsType};`;
        })
        .join('\n');

    // Generate the complete file content
    return `/**
 * Clean scalar type aliases for GraphQL scalar types
 * Known scalars (built-in + common) keep proper TypeScript types, unknown scalars use 'unknown'
 *
 * This file provides clean type aliases for GraphQL scalar types,
 * allowing you to use \`AddressLabel\` instead of \`Scalars['AddressLabel']['output']\`
 */

${typeAliases}
`;
}

/**
 * Check if a type name is a scalar type
 * @param typeName The type name to check
 * @param schemaRoot The schema root directory for detecting scalar types
 * @returns True if the type is a scalar type
 */
export function isScalarTypeName(typeName: string, schemaRoot?: string): boolean {
    const scalarTypes = extractScalarTypesFromSchema(schemaRoot);
    return scalarTypes.has(typeName);
}

/**
 * Interface for resolve type alias data
 */
export interface ResolveTypeAlias {
    typeName: string;
    typeKind: 'interface' | 'union' | 'object';
    possibleTypes: string[];
    aliasName: string;
}

/**
 * Generate resolve-types.ts file content with type aliases for __resolveType functions
 * @param resolveTypeAliases Array of resolve type alias data
 * @returns Generated TypeScript content for resolve-types.ts file
 */
export function generateResolveTypesFileContent(resolveTypeAliases: ResolveTypeAlias[]): string {
    if (resolveTypeAliases.length === 0) {
        return `/**
 * Type aliases for GraphQL __resolveType function return types
 * No type aliases needed - all interfaces and unions have fewer than 4 possible types
 */

// This file is intentionally empty
export {};
`;
    }

    // Generate type aliases for all resolve types with appropriate mappings
    const typeAliases = resolveTypeAliases
        .map(alias => {
            const unionType = alias.possibleTypes.map(type => `'${type}'`).join(' | ');
            return `export type ${alias.aliasName} = ${unionType};`;
        })
        .join('\n');

    // Generate the complete file content
    return `${typeAliases}`;
}

/**
 * Generate resolve-types.ts file with type aliases for __resolveType functions
 * @param outputDir The output directory where resolve-types.ts should be created
 * @param resolveTypeAliases Array of resolve type alias data
 * @param debug Enable debug logging
 */
export async function generateResolveTypesFile(
    outputDir: string,
    resolveTypeAliases: ResolveTypeAlias[],
    debug: boolean = false
): Promise<void> {
    const fs = await import('fs-extra');
    const path = await import('path');

    const resolveTypesPath = path.default.join(outputDir, 'resolve-types.ts');
    const content = generateResolveTypesFileContent(resolveTypeAliases);

    if (debug) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Generating resolve-types.ts with ${resolveTypeAliases.length} type aliases`);
        }
        resolveTypeAliases.forEach(alias => {
            if (process.env.DEBUG_PARSER) {
              console.log(`  - ${alias.aliasName}: ${alias.possibleTypes.length} possible types`);
            }
        });
    }

    fs.default.writeFileSync(resolveTypesPath, content);
    if (process.env.DEBUG_PARSER) {
      console.log(`Generated resolve types file at: ${resolveTypesPath}`);
    }
}