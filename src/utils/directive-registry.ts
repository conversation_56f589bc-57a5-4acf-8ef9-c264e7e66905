import type { ParsedDirective } from './directive-parser';

/**
 * Interface for directive handler implementations
 */
export interface DirectiveHandler<T = any> {
    /** The name of the directive this handler processes */
    directiveName: string;

    /** Process the directive and return processed data */
    process(directive: ParsedDirective): T | null;

    /** Validate directive content */
    validate?(directive: ParsedDirective): boolean;

    /** Priority for this handler (higher numbers run first) */
    priority?: number;
}

/**
 * Registry for directive handlers
 */
export class DirectiveRegistry {
    private static handlers: Map<string, DirectiveHandler[]> = new Map();

    /**
     * Register a directive handler
     * @param handler The directive handler to register
     */
    public static register(handler: DirectiveHandler): void {
        const { directiveName } = handler;

        if (!this.handlers.has(directiveName)) {
            this.handlers.set(directiveName, []);
        }

        const handlers = this.handlers.get(directiveName)!;
        handlers.push(handler);

        // Sort handlers by priority (higher priority first)
        handlers.sort((a, b) => (b.priority ?? 0) - (a.priority ?? 0));
    }

    /**
     * Get all registered handlers for a directive
     * @param directiveName The name of the directive
     * @returns Array of directive handlers
     */
    public static getHandlers(directiveName: string): DirectiveHandler[] {
        return this.handlers.get(directiveName) ?? [];
    }

    /**
     * Process a directive with the appropriate handler
     * @param directive The parsed directive
     * @returns Processed result or null if no handler found
     */
    public static process<T = any>(directive: ParsedDirective): T | null {
        const handlers = this.getHandlers(directive.name);

        for (const handler of handlers) {
            // Validate directive if handler has validation
            if (handler.validate && !handler.validate(directive)) {
                console.warn(`Directive validation failed: ${directive.raw}`);
                continue;
            }

            try {
                const result = handler.process(directive);
                if (result !== null) {
                    return result as T;
                }
            } catch (error) {
                console.error(`Error processing directive with handler ${handler.directiveName}:`, error);
            }
        }

        return null;
    }

    /**
     * Clear all registered handlers
     */
    public static clear(): void {
        this.handlers.clear();
    }
} 