import { Worker } from 'worker_threads';
import * as path from 'path';
import * as os from 'os';

/**
 * Task for worker thread processing
 */
export interface WorkerTask {
  id: string;
  filePath: string;
  type: 'decorator-scan' | 'ts-parse';
  data?: any;
}

/**
 * Result from worker thread processing
 */
export interface WorkerResult {
  taskId: string;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
}

/**
 * Worker thread pool configuration
 */
export interface WorkerPoolConfig {
  /** Maximum number of worker threads (default: CPU cores - 1) */
  maxWorkers?: number;
  /** Timeout for individual tasks in milliseconds (default: 30000) */
  taskTimeout?: number;
  /** Whether to enable performance monitoring (default: true) */
  enableMonitoring?: boolean;
}

/**
 * Performance metrics for worker thread pool
 */
export interface WorkerPoolMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageTaskDuration: number;
  totalDuration: number;
  workerUtilization: number;
}

/**
 * Worker thread pool for parallel file processing
 */
export class WorkerThreadPool {
  private workers: Worker[] = [];
  private availableWorkers: Worker[] = [];
  private taskQueue: WorkerTask[] = [];
  private activeTasks: Map<string, { worker: Worker; startTime: number; timeout: NodeJS.Timeout }> = new Map();
  private taskResolvers: Map<string, { resolve: (result: WorkerResult) => void; reject: (error: Error) => void }> = new Map();
  private config: Required<WorkerPoolConfig>;
  private metrics: WorkerPoolMetrics = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageTaskDuration: 0,
    totalDuration: 0,
    workerUtilization: 0
  };
  private isShuttingDown = false;

  constructor(config: WorkerPoolConfig = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || Math.max(1, os.cpus().length - 1),
      taskTimeout: config.taskTimeout || 30000,
      enableMonitoring: config.enableMonitoring ?? true
    };

    this.initializeWorkers();
  }

  /**
   * Initialize worker threads
   */
  private initializeWorkers(): void {
    // Use the compiled JavaScript file in production, TypeScript file in development
    const workerScript = path.join(__dirname, 'worker-thread-handler.js');
    
    for (let i = 0; i < this.config.maxWorkers; i++) {
      try {
        const worker = new Worker(workerScript);
        
        worker.on('message', (result: WorkerResult) => {
          this.handleWorkerMessage(worker, result);
        });

        worker.on('error', (error) => {
          this.handleWorkerError(worker, error);
        });

        worker.on('exit', (code) => {
          if (code !== 0 && !this.isShuttingDown) {
            console.warn(`Worker thread exited with code ${code}`);
            this.replaceWorker(worker);
          }
        });

        this.workers.push(worker);
        this.availableWorkers.push(worker);
      } catch (error) {
        console.warn(`Failed to create worker thread ${i}:`, error);
      }
    }

    if (this.workers.length === 0) {
      throw new Error('Failed to create any worker threads');
    }
  }

  /**
   * Submit a task for processing
   */
  public async submitTask(task: WorkerTask): Promise<WorkerResult> {
    if (this.isShuttingDown) {
      throw new Error('Worker pool is shutting down');
    }

    this.metrics.totalTasks++;

    return new Promise((resolve, reject) => {
      this.taskResolvers.set(task.id, { resolve, reject });
      this.taskQueue.push(task);
      this.processQueue();
    });
  }

  /**
   * Submit multiple tasks and wait for all to complete
   */
  public async submitTasks(tasks: WorkerTask[]): Promise<WorkerResult[]> {
    const promises = tasks.map(task => this.submitTask(task));
    return Promise.all(promises);
  }

  /**
   * Process the task queue
   */
  private processQueue(): void {
    while (this.taskQueue.length > 0 && this.availableWorkers.length > 0) {
      const task = this.taskQueue.shift()!;
      const worker = this.availableWorkers.shift()!;
      
      this.assignTaskToWorker(worker, task);
    }
  }

  /**
   * Assign a task to a worker
   */
  private assignTaskToWorker(worker: Worker, task: WorkerTask): void {
    const startTime = Date.now();
    
    // Set up timeout
    const timeout = setTimeout(() => {
      this.handleTaskTimeout(task.id);
    }, this.config.taskTimeout);

    this.activeTasks.set(task.id, { worker, startTime, timeout });

    // Send task to worker
    worker.postMessage(task);
  }

  /**
   * Handle message from worker
   */
  private handleWorkerMessage(worker: Worker, result: WorkerResult): void {
    const activeTask = this.activeTasks.get(result.taskId);
    if (!activeTask) {
      return; // Task might have timed out
    }

    // Clear timeout
    clearTimeout(activeTask.timeout);
    this.activeTasks.delete(result.taskId);

    // Return worker to available pool
    this.availableWorkers.push(worker);

    // Update metrics
    if (result.success) {
      this.metrics.completedTasks++;
    } else {
      this.metrics.failedTasks++;
    }

    const duration = Date.now() - activeTask.startTime;
    this.updateAverageTaskDuration(duration);

    // Resolve the task promise
    const resolver = this.taskResolvers.get(result.taskId);
    if (resolver) {
      this.taskResolvers.delete(result.taskId);
      resolver.resolve(result);
    }

    // Process next task in queue
    this.processQueue();
  }

  /**
   * Handle worker error
   */
  private handleWorkerError(worker: Worker, error: Error): void {
    console.error('Worker thread error:', error);
    
    // Find and fail any active tasks for this worker
    for (const [taskId, activeTask] of this.activeTasks.entries()) {
      if (activeTask.worker === worker) {
        clearTimeout(activeTask.timeout);
        this.activeTasks.delete(taskId);
        
        const resolver = this.taskResolvers.get(taskId);
        if (resolver) {
          this.taskResolvers.delete(taskId);
          resolver.reject(error);
        }
        
        this.metrics.failedTasks++;
      }
    }

    // Replace the failed worker
    this.replaceWorker(worker);
  }

  /**
   * Handle task timeout
   */
  private handleTaskTimeout(taskId: string): void {
    const activeTask = this.activeTasks.get(taskId);
    if (!activeTask) {
      return;
    }

    this.activeTasks.delete(taskId);
    
    const resolver = this.taskResolvers.get(taskId);
    if (resolver) {
      this.taskResolvers.delete(taskId);
      resolver.reject(new Error(`Task ${taskId} timed out after ${this.config.taskTimeout}ms`));
    }

    this.metrics.failedTasks++;

    // Return worker to available pool (it might still be working, but we'll let it finish)
    this.availableWorkers.push(activeTask.worker);
    
    // Process next task in queue
    this.processQueue();
  }

  /**
   * Replace a failed worker
   */
  private replaceWorker(failedWorker: Worker): void {
    // Remove from workers array
    const index = this.workers.indexOf(failedWorker);
    if (index !== -1) {
      this.workers.splice(index, 1);
    }

    // Remove from available workers if present
    const availableIndex = this.availableWorkers.indexOf(failedWorker);
    if (availableIndex !== -1) {
      this.availableWorkers.splice(availableIndex, 1);
    }

    // Terminate the failed worker
    try {
      failedWorker.terminate();
    } catch (error) {
      // Ignore termination errors
    }

    // Create a new worker if not shutting down
    if (!this.isShuttingDown) {
      try {
        const workerScript = path.join(__dirname, 'worker-thread-handler.js');
        const newWorker = new Worker(workerScript);
        
        newWorker.on('message', (result: WorkerResult) => {
          this.handleWorkerMessage(newWorker, result);
        });

        newWorker.on('error', (error) => {
          this.handleWorkerError(newWorker, error);
        });

        newWorker.on('exit', (code) => {
          if (code !== 0 && !this.isShuttingDown) {
            console.warn(`Replacement worker thread exited with code ${code}`);
            this.replaceWorker(newWorker);
          }
        });

        this.workers.push(newWorker);
        this.availableWorkers.push(newWorker);
      } catch (error) {
        console.error('Failed to create replacement worker:', error);
      }
    }
  }

  /**
   * Update average task duration
   */
  private updateAverageTaskDuration(duration: number): void {
    const totalCompleted = this.metrics.completedTasks + this.metrics.failedTasks;
    this.metrics.averageTaskDuration = 
      (this.metrics.averageTaskDuration * (totalCompleted - 1) + duration) / totalCompleted;
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): WorkerPoolMetrics {
    const activeTaskCount = this.activeTasks.size;
    const totalWorkers = this.workers.length;
    
    return {
      ...this.metrics,
      workerUtilization: totalWorkers > 0 ? (activeTaskCount / totalWorkers) * 100 : 0
    };
  }

  /**
   * Shutdown the worker pool
   */
  public async shutdown(timeout: number = 5000): Promise<void> {
    this.isShuttingDown = true;

    // Wait for active tasks to complete or timeout
    const startTime = Date.now();
    while (this.activeTasks.size > 0 && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Terminate all workers
    const terminationPromises = this.workers.map(worker => {
      return new Promise<void>((resolve) => {
        worker.terminate().then(() => resolve()).catch(() => resolve());
      });
    });

    await Promise.all(terminationPromises);
    
    this.workers = [];
    this.availableWorkers = [];
    this.activeTasks.clear();
    this.taskResolvers.clear();
  }
}
