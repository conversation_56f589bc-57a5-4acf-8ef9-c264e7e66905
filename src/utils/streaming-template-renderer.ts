import { Readable, Transform, Writable, pipeline } from 'stream';
import { promisify } from 'util';
import * as Handlebars from 'handlebars';
import { getGlobalTemplateCache } from './template-compilation-cache';
import { getGlobalMemoryMonitor, type MemoryMetrics as MonitorMemoryMetrics } from './memory-pressure-monitor';
import { EventEmitter } from 'events';

/**
 * Phase 2 Optimization: Streaming Template Renderer
 * Processes large schemas without loading entire files into memory
 */

const pipelineAsync = promisify(pipeline);

export interface StreamingRenderOptions {
  chunkSize?: number;
  maxMemoryUsage?: number; // in bytes
  enableBackpressure?: boolean;
  enableMemoryMonitoring?: boolean;
  debug?: boolean;
}

export interface TemplateChunk {
  templateName: string;
  data: any;
  outputPath: string;
  priority?: number;
}

export interface StreamingMemoryMetrics {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  arrayBuffers: number;
  memoryPressure: number; // 0-1 scale
  timestamp: number;
}

/**
 * Streaming template renderer with memory management and backpressure handling
 */
export class StreamingTemplateRenderer extends EventEmitter {
  private options: Required<StreamingRenderOptions>;
  private templateCache = getGlobalTemplateCache();
  private memoryMonitor = getGlobalMemoryMonitor();
  private activeStreams = new Set<NodeJS.ReadableStream>();
  private memoryMonitorInterval?: NodeJS.Timeout;
  private isMemoryConstrained = false;
  private processedChunks = 0;
  private totalChunks = 0;

  constructor(options: StreamingRenderOptions = {}) {
    super();
    
    this.options = {
      chunkSize: 10, // Process 10 templates at a time
      maxMemoryUsage: 500 * 1024 * 1024, // 500MB default
      enableBackpressure: true,
      enableMemoryMonitoring: true,
      debug: false,
      ...options
    };

    if (this.options.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }

    // Set up memory pressure event handlers
    this.memoryMonitor.on('pressure', (event) => {
      this.isMemoryConstrained = event.level !== 'normal';
      this.emit('memoryPressure', event);
    });
  }

  /**
   * Render templates in streaming fashion
   */
  async renderTemplatesStream(
    templateChunks: TemplateChunk[],
    outputStream: Writable
  ): Promise<void> {
    this.totalChunks = templateChunks.length;
    this.processedChunks = 0;

    if (this.options.debug) {
      console.log(`🌊 Starting streaming render of ${templateChunks.length} templates`);
    }

    // Create readable stream from template chunks
    const templateStream = this.createTemplateStream(templateChunks);
    
    // Create transform stream for rendering
    const renderStream = this.createRenderTransform();
    
    // Create memory management stream
    const memoryStream = this.createMemoryManagementTransform();

    try {
      // Pipeline: Template Source → Render → Memory Management → Output
      await pipelineAsync(
        templateStream,
        renderStream,
        memoryStream,
        outputStream
      );

      if (this.options.debug) {
        console.log(`✅ Streaming render completed: ${this.processedChunks}/${this.totalChunks} templates`);
      }

      this.emit('completed', { processedChunks: this.processedChunks, totalChunks: this.totalChunks });
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Create readable stream from template chunks
   */
  private createTemplateStream(templateChunks: TemplateChunk[]): Readable {
    let index = 0;
    
    const stream = new Readable({
      objectMode: true,
      read() {
        if (index < templateChunks.length) {
          this.push(templateChunks[index++]);
        } else {
          this.push(null); // End of stream
        }
      }
    });

    this.activeStreams.add(stream);
    stream.on('close', () => this.activeStreams.delete(stream));
    
    return stream;
  }

  /**
   * Create transform stream for template rendering
   */
  private createRenderTransform(): Transform {
    const batchBuffer: TemplateChunk[] = [];
    
    const stream = new Transform({
      objectMode: true,
      transform: async (chunk: TemplateChunk, encoding, callback) => {
        try {
          batchBuffer.push(chunk);
          
          // Process batch when it reaches chunk size or when memory constrained
          if (batchBuffer.length >= this.options.chunkSize || this.isMemoryConstrained) {
            const renderedBatch = await this.renderBatch(batchBuffer.splice(0));
            callback(null, renderedBatch);
          } else {
            callback();
          }
        } catch (error) {
          callback(error as Error);
        }
      },
      flush: async (callback) => {
        try {
          // Process remaining chunks
          if (batchBuffer.length > 0) {
            const renderedBatch = await this.renderBatch(batchBuffer);
            callback(null, renderedBatch);
          } else {
            callback();
          }
        } catch (error) {
          callback(error as Error);
        }
      }
    });

    this.activeStreams.add(stream);
    stream.on('close', () => this.activeStreams.delete(stream));
    
    return stream;
  }

  /**
   * Create memory management transform stream
   */
  private createMemoryManagementTransform(): Transform {
    const stream = new Transform({
      objectMode: true,
      transform: async (renderedBatch: any[], encoding, callback) => {
        try {
          // Check memory pressure before processing
          if (this.options.enableBackpressure && this.isMemoryConstrained) {
            await this.waitForMemoryRecovery();
          }

          // Process the rendered batch
          for (const rendered of renderedBatch) {
            this.processedChunks++;
            this.emit('progress', {
              processed: this.processedChunks,
              total: this.totalChunks,
              percentage: Math.round((this.processedChunks / this.totalChunks) * 100)
            });
          }

          callback(null, renderedBatch);
        } catch (error) {
          callback(error as Error);
        }
      }
    });

    this.activeStreams.add(stream);
    stream.on('close', () => this.activeStreams.delete(stream));
    
    return stream;
  }

  /**
   * Render a batch of template chunks
   */
  private async renderBatch(chunks: TemplateChunk[]): Promise<any[]> {
    const rendered: any[] = [];
    
    for (const chunk of chunks) {
      try {
        const template = this.templateCache.compile(chunk.templateName);
        const result = template(chunk.data);
        
        rendered.push({
          outputPath: chunk.outputPath,
          content: result,
          templateName: chunk.templateName
        });
      } catch (error) {
        this.emit('renderError', { chunk, error });
        // Continue with other chunks
      }
    }
    
    return rendered;
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = setInterval(() => {
      const metrics = this.getMemoryMetrics();
      
      // Update memory constraint status
      this.isMemoryConstrained = metrics.memoryPressure > 0.8;
      
      if (this.options.debug && this.isMemoryConstrained) {
        console.warn(`⚠️  Memory pressure detected: ${Math.round(metrics.memoryPressure * 100)}%`);
      }
      
      this.emit('memoryMetrics', metrics);
      
      // Force garbage collection if available and memory pressure is high
      if (metrics.memoryPressure > 0.9 && global.gc) {
        global.gc();
      }
    }, 1000); // Check every second
  }

  /**
   * Get current memory metrics
   */
  private getMemoryMetrics(): StreamingMemoryMetrics {
    const memUsage = process.memoryUsage();
    const memoryPressure = Math.min(1, memUsage.heapUsed / this.options.maxMemoryUsage);
    
    return {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      arrayBuffers: memUsage.arrayBuffers || 0,
      memoryPressure,
      timestamp: Date.now()
    };
  }

  /**
   * Wait for memory recovery using enhanced memory monitor
   */
  private async waitForMemoryRecovery(): Promise<void> {
    const recovered = await this.memoryMonitor.waitForMemoryRecovery(0.7, 10000);

    if (!recovered && this.options.debug) {
      console.warn('⚠️  Memory recovery timeout - continuing with constrained processing');
    }
  }

  /**
   * Stop memory monitoring and cleanup
   */
  destroy(): void {
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
      this.memoryMonitorInterval = undefined;
    }
    
    // Close all active streams
    for (const stream of this.activeStreams) {
      if ('destroy' in stream && typeof stream.destroy === 'function') {
        stream.destroy();
      }
    }
    
    this.activeStreams.clear();
    this.removeAllListeners();
  }

  /**
   * Get current streaming statistics
   */
  getStats() {
    return {
      processedChunks: this.processedChunks,
      totalChunks: this.totalChunks,
      activeStreams: this.activeStreams.size,
      isMemoryConstrained: this.isMemoryConstrained,
      memoryMetrics: this.getMemoryMetrics()
    };
  }
}

/**
 * Global streaming renderer instance
 */
let globalStreamingRenderer: StreamingTemplateRenderer | null = null;

/**
 * Get or create global streaming template renderer
 */
export function getGlobalStreamingRenderer(options?: StreamingRenderOptions): StreamingTemplateRenderer {
  if (!globalStreamingRenderer) {
    globalStreamingRenderer = new StreamingTemplateRenderer(options);
  }
  return globalStreamingRenderer;
}

/**
 * Cleanup global streaming renderer
 */
export function cleanupGlobalStreamingRenderer(): void {
  if (globalStreamingRenderer) {
    globalStreamingRenderer.destroy();
    globalStreamingRenderer = null;
  }
}
