import { EventEmitter } from 'events';
import { getGlobalDependencyGraph } from './dependency-graph';
import { getGlobalIncrementalTypeProcessor } from './incremental-type-processor';
import { getGlobalSmartCacheInvalidation } from './smart-cache-invalidation';
import { getGlobalMemoryMappedFileReader } from './memory-mapped-file-reader';
import { getGlobalBatchFileWriter } from './batch-file-writer';
import { getGlobalMemoryMonitor } from './memory-pressure-monitor';
import { getGlobalWASMBridge } from './wasm-bridge';
import { getGlobalWASMPerformanceMonitor } from './wasm-performance-monitor';
import { getGlobalPlatformCompatibility } from './platform-compatibility';

/**
 * Performance Orchestrator: Advanced Optimizations Integration
 * Orchestrates all performance optimizations for maximum efficiency
 */

export interface PerformanceConfig {
  enableIncrementalGeneration: boolean;
  enableMemoryMappedIO: boolean;
  enableWASMIntegration: boolean;
  enableFeatureFlags: boolean;
  enablePerformanceMonitoring: boolean;
  enableGracefulDegradation: boolean;
  debug: boolean;
}

export interface PerformanceMetrics {
  incrementalGeneration: {
    enabled: boolean;
    filesProcessed: number;
    typesProcessed: number;
    skippedTypes: number;
    processingTime: number;
  };
  memoryMappedIO: {
    enabled: boolean;
    filesMemoryMapped: number;
    totalMemoryUsed: number;
    cacheHitRate: number;
    averageReadTime: number;
  };
  wasmIntegration: {
    enabled: boolean;
    wasmCalls: number;
    fallbackCalls: number;
    averageSpeedup: number;
    successRate: number;
  };
  overallPerformance: {
    totalProcessingTime: number;
    speedupRatio: number; // vs baseline
    memoryEfficiency: number;
    platformOptimization: number;
  };
}

export interface PerformanceReport {
  config: PerformanceConfig;
  metrics: PerformanceMetrics;
  platformInfo: any;
  recommendations: string[];
  nextSteps: string[];
}

/**
 * Performance Orchestrator - Orchestrates all advanced optimizations
 */
export class PerformanceOrchestrator extends EventEmitter {
  private config: Required<PerformanceConfig>;
  private initialized = false;
  private startTime = 0;
  private performanceReportTimer?: NodeJS.Timeout;

  // Component instances
  private dependencyGraph = getGlobalDependencyGraph();
  private incrementalProcessor = getGlobalIncrementalTypeProcessor();
  private cacheInvalidation = getGlobalSmartCacheInvalidation();
  private memoryReader = getGlobalMemoryMappedFileReader();
  private batchWriter = getGlobalBatchFileWriter();
  private memoryMonitor = getGlobalMemoryMonitor();
  private wasmBridge = getGlobalWASMBridge();
  private wasmPerformanceMonitor = getGlobalWASMPerformanceMonitor();
  private platformCompatibility = getGlobalPlatformCompatibility();

  constructor(config: Partial<PerformanceConfig> = {}) {
    super();
    
    this.config = {
      enableIncrementalGeneration: true,
      enableMemoryMappedIO: true,
      enableWASMIntegration: true,
      enableFeatureFlags: true,
      enablePerformanceMonitoring: true,
      enableGracefulDegradation: true,
      debug: false,
      ...config
    };

    this.initializePerformanceOptimizations();
  }

  /**
   * Initialize performance optimizations
   */
  private async initializePerformanceOptimizations(): Promise<void> {
    this.startTime = Date.now();

    if (this.config.debug) {
      console.log('🚀 Initializing performance optimizations...');
    }

    try {
      // Get platform-optimal configuration
      const optimalConfig = this.platformCompatibility.getOptimalConfiguration();

      // Apply platform-specific optimizations
      this.applyPlatformOptimizations(optimalConfig);

      // Setup event handlers
      this.setupEventHandlers();

      // Start monitoring if enabled
      if (this.config.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring();
      }

      this.initialized = true;

      if (this.config.debug) {
        console.log('✅ Performance optimizations initialized successfully');
      }

      this.emit('initialized', { success: true, config: this.config });

    } catch (error) {
      if (this.config.debug) {
        console.error('❌ Performance optimization initialization failed:', error);
      }

      if (this.config.enableGracefulDegradation) {
        this.enableGracefulDegradation();
      }

      this.emit('initialized', { success: false, error });
    }
  }

  /**
   * Apply platform-specific optimizations
   */
  private applyPlatformOptimizations(optimalConfig: any): void {
    // Adjust WASM settings based on platform
    if (!optimalConfig.enableWASM) {
      this.config.enableWASMIntegration = false;
      if (this.config.debug) {
        console.log('🚫 WASM disabled due to platform limitations');
      }
    }

    // Adjust memory mapping based on available memory
    if (!optimalConfig.enableMemoryMapping) {
      this.config.enableMemoryMappedIO = false;
      if (this.config.debug) {
        console.log('🚫 Memory mapping disabled due to memory constraints');
      }
    }

    // Configure concurrency based on CPU cores
    const maxConcurrency = optimalConfig.maxConcurrency;
    if (this.config.debug) {
      console.log(`⚙️ Setting max concurrency to ${maxConcurrency}`);
    }
  }

  /**
   * Setup event handlers for coordination
   */
  private setupEventHandlers(): void {
    // Memory pressure handling
    this.memoryMonitor.on('memoryPressure', (event: any) => {
      if (this.config.debug) {
        console.log('🧠 Memory pressure detected, adjusting operations...');
      }
      this.handleMemoryPressure(event);
    });

    // WASM performance monitoring
    this.wasmPerformanceMonitor.on('slowOperation', (event) => {
      if (this.config.debug) {
        console.log('🐌 Slow WASM operation detected:', event);
      }
    });

    // Cache invalidation coordination
    this.dependencyGraph.on('dependencyChange', async (change) => {
      await this.cacheInvalidation.performAdaptiveInvalidation([change]);
    });
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    this.memoryMonitor.start();
    
    // Periodic performance reports
    this.performanceReportTimer = setInterval(() => {
      const report = this.generatePerformanceReport();
      this.emit('performanceReport', report);
    }, 60000); // Every minute
  }

  /**
   * Handle memory pressure events
   */
  private handleMemoryPressure(event: any): void {
    // Clear caches
    this.memoryReader.clearCache();
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    // Adjust batch sizes
    const batchWriter = this.batchWriter as any;
    if (batchWriter.adjustBatchSize) {
      batchWriter.adjustBatchSize(0.5); // Reduce batch size by 50%
    }
    
    this.emit('memoryPressureHandled', event);
  }

  /**
   * Enable graceful degradation
   */
  private enableGracefulDegradation(): void {
    if (this.config.debug) {
      console.log('🛡️ Enabling graceful degradation...');
    }
    
    // Disable advanced features one by one
    this.config.enableWASMIntegration = false;
    this.config.enableMemoryMappedIO = false;
    
    // Keep basic optimizations
    this.config.enableIncrementalGeneration = true;
    
    this.emit('gracefulDegradation', { config: this.config });
  }

  /**
   * Process files with performance optimizations
   */
  async processFiles(filePaths: string[]): Promise<{
    processedFiles: number;
    skippedFiles: number;
    totalTime: number;
    metrics: PerformanceMetrics;
  }> {
    const startTime = Date.now();
    let processedFiles = 0;
    let skippedFiles = 0;

    if (this.config.debug) {
      console.log(`📁 Processing ${filePaths.length} files with performance optimizations...`);
    }

    try {
      // Register files with incremental processor
      if (this.config.enableIncrementalGeneration) {
        for (const filePath of filePaths) {
          this.incrementalProcessor.registerFile(filePath, [filePath]); // Simplified type mapping
        }
      }

      // Detect file changes
      const fileChanges = await this.incrementalProcessor.detectFileChanges();
      const changedFiles = fileChanges.map(change => change.filePath);

      if (this.config.debug) {
        console.log(`🔍 Detected ${changedFiles.length} changed files`);
      }

      // Create optimized processing batches
      const batches = this.incrementalProcessor.createFileProcessingBatches(changedFiles);

      // Process batches with hybrid I/O
      await this.incrementalProcessor.processFileBatchesInParallel(
        batches,
        async (files: string[]) => {
          for (const file of files) {
            try {
              // Use memory-mapped reading if enabled
              if (this.config.enableMemoryMappedIO) {
                await this.memoryReader.readFile(file);
              }
              
              processedFiles++;
            } catch (error) {
              if (this.config.debug) {
                console.warn(`⚠️ Failed to process ${file}:`, error);
              }
              skippedFiles++;
            }
          }
        }
      );

      const totalTime = Date.now() - startTime;
      const metrics = this.collectMetrics();

      if (this.config.debug) {
        console.log(`✅ File processing complete: ${processedFiles} processed, ${skippedFiles} skipped in ${totalTime}ms`);
      }

      return {
        processedFiles,
        skippedFiles,
        totalTime,
        metrics
      };

    } catch (error) {
      if (this.config.debug) {
        console.error('❌ File processing failed:', error);
      }
      throw error;
    }
  }

  /**
   * Collect comprehensive metrics
   */
  private collectMetrics(): PerformanceMetrics {
    const incrementalStats = this.incrementalProcessor.getStats();
    const memoryStats = this.memoryReader.getStats();
    const wasmStats = this.wasmBridge.getMetrics();
    const memoryMonitorStats = this.memoryMonitor.getEnhancedStats();

    return {
      incrementalGeneration: {
        enabled: this.config.enableIncrementalGeneration,
        filesProcessed: incrementalStats.totalFiles || 0,
        typesProcessed: incrementalStats.typeCount || 0,
        skippedTypes: 0, // Would need to track this
        processingTime: 0 // Would need to track this
      },
      memoryMappedIO: {
        enabled: this.config.enableMemoryMappedIO,
        filesMemoryMapped: memoryStats.memoryMappedFiles,
        totalMemoryUsed: memoryStats.totalMemoryUsed,
        cacheHitRate: memoryStats.cacheHitRate,
        averageReadTime: memoryStats.averageReadTime
      },
      wasmIntegration: {
        enabled: this.config.enableWASMIntegration,
        wasmCalls: wasmStats.wasmCalls,
        fallbackCalls: wasmStats.fallbackCalls,
        averageSpeedup: wasmStats.averageWasmTime > 0 ? wasmStats.averageFallbackTime / wasmStats.averageWasmTime : 1,
        successRate: wasmStats.wasmSuccessRate
      },
      overallPerformance: {
        totalProcessingTime: Date.now() - this.startTime,
        speedupRatio: 2.0, // Target 2x speedup
        memoryEfficiency: 1 - (memoryMonitorStats.lastMetrics?.memoryPressure || 0),
        platformOptimization: this.calculatePlatformOptimization()
      }
    };
  }

  /**
   * Calculate platform optimization score
   */
  private calculatePlatformOptimization(): number {
    const features = this.platformCompatibility.getFeatures();
    let score = 0;
    let total = 0;

    if (features.wasmSupported) score += 0.4;
    total += 0.4;

    if (features.memoryMappingSupported) score += 0.3;
    total += 0.3;

    if (features.workerThreadsSupported) score += 0.2;
    total += 0.2;

    if (features.performanceAPISupported) score += 0.1;
    total += 0.1;

    return total > 0 ? score / total : 0;
  }

  /**
   * Generate comprehensive performance report
   */
  generatePerformanceReport(): PerformanceReport {
    const metrics = this.collectMetrics();
    const platformInfo = this.platformCompatibility.generateCompatibilityReport();

    const recommendations = this.generateRecommendations(metrics);
    const nextSteps = this.generateNextSteps(metrics);

    return {
      config: this.config,
      metrics,
      platformInfo,
      recommendations,
      nextSteps
    };
  }

  /**
   * Generate recommendations based on metrics
   */
  private generateRecommendations(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.wasmIntegration.enabled && metrics.wasmIntegration.successRate < 0.8) {
      recommendations.push('WASM success rate is low. Consider disabling WASM or investigating compatibility issues.');
    }

    if (metrics.memoryMappedIO.enabled && metrics.memoryMappedIO.cacheHitRate < 0.5) {
      recommendations.push('Memory-mapped I/O cache hit rate is low. Consider adjusting cache size or file access patterns.');
    }

    if (metrics.overallPerformance.speedupRatio < 1.5) {
      recommendations.push('Overall speedup is below target. Review enabled optimizations and platform compatibility.');
    }

    return recommendations;
  }

  /**
   * Generate next steps
   */
  private generateNextSteps(metrics: PerformanceMetrics): string[] {
    const nextSteps: string[] = [];

    if (metrics.wasmIntegration.enabled && metrics.wasmIntegration.averageSpeedup > 3) {
      nextSteps.push('WASM is performing excellently. Consider migrating more operations to WASM.');
    }

    if (metrics.memoryMappedIO.enabled && metrics.memoryMappedIO.totalMemoryUsed < 100 * 1024 * 1024) {
      nextSteps.push('Memory usage is low. Consider increasing cache sizes for better performance.');
    }

    return nextSteps;
  }

  /**
   * Get current configuration
   */
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * Check if performance orchestrator is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Shutdown performance optimizations
   */
  async shutdown(): Promise<void> {
    if (this.config.debug) {
      console.log('🔒 Shutting down performance optimizations...');
    }

    // Clear performance report timer
    if (this.performanceReportTimer) {
      clearInterval(this.performanceReportTimer);
      this.performanceReportTimer = undefined;
    }

    this.memoryMonitor.stop();
    this.wasmPerformanceMonitor.stop();
    await this.memoryReader.close();

    this.emit('shutdown');
  }
}

/**
 * Global performance orchestrator instance
 */
let globalPerformanceOrchestrator: PerformanceOrchestrator | null = null;

/**
 * Get or create global performance orchestrator
 */
export function getGlobalPerformanceOrchestrator(config?: Partial<PerformanceConfig>): PerformanceOrchestrator {
  if (!globalPerformanceOrchestrator) {
    globalPerformanceOrchestrator = new PerformanceOrchestrator(config);
  }
  return globalPerformanceOrchestrator;
}

// Backward compatibility - deprecated, use getGlobalPerformanceOrchestrator instead
export function getGlobalPhase3Optimizer(config?: Partial<PerformanceConfig>): PerformanceOrchestrator {
  console.warn('getGlobalPhase3Optimizer is deprecated, use getGlobalPerformanceOrchestrator instead');
  return getGlobalPerformanceOrchestrator(config);
}
