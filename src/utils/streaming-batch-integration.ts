import { Writable } from 'stream';
import { BatchFileWriter } from './batch-file-writer';
import { StreamingTemplateRenderer, type TemplateChunk } from './streaming-template-renderer';
import { getGlobalMemoryMonitor } from './memory-pressure-monitor';
import { EventEmitter } from 'events';

/**
 * Phase 2 Optimization: Streaming Batch Integration
 * Connects streaming template renderer with existing BatchFileWriter for optimized I/O
 */

export interface StreamingBatchConfig {
  batchSize?: number;
  maxMemoryUsage?: number;
  enableAdaptiveBatching?: boolean;
  enableMemoryMonitoring?: boolean;
  debug?: boolean;
}

export interface StreamingBatchMetrics {
  templatesProcessed: number;
  filesWritten: number;
  batchesCompleted: number;
  averageBatchSize: number;
  memoryPressure: number;
  processingTime: number;
}

/**
 * Writable stream that integrates with BatchFileWriter
 */
class BatchFileWriterStream extends Writable {
  private batchWriter: BatchFileWriter;
  private filesWritten = 0;
  private debug: boolean;

  constructor(batchWriter: BatchFileWriter, debug = false) {
    super({ objectMode: true });
    this.batchWriter = batchWriter;
    this.debug = debug;
  }

  _write(chunk: any[], encoding: string, callback: (error?: Error | null) => void): void {
    try {
      // Process rendered templates and queue for batch writing
      for (const rendered of chunk) {
        this.batchWriter.queueWrite(rendered.outputPath, rendered.content);
        this.filesWritten++;
      }

      if (this.debug && chunk.length > 0) {
        console.log(`📝 Queued ${chunk.length} files for batch writing (total: ${this.filesWritten})`);
      }

      callback();
    } catch (error) {
      callback(error as Error);
    }
  }

  _final(callback: (error?: Error | null) => void): void {
    // Flush all pending writes when stream ends
    this.batchWriter.flushAll()
      .then(() => {
        if (this.debug) {
          console.log(`✅ Batch writing completed: ${this.filesWritten} files written`);
        }
        callback();
      })
      .catch(callback);
  }

  getFilesWritten(): number {
    return this.filesWritten;
  }
}

/**
 * Integrated streaming and batch processing system
 */
export class StreamingBatchProcessor extends EventEmitter {
  private config: Required<StreamingBatchConfig>;
  private batchWriter: BatchFileWriter;
  private streamingRenderer: StreamingTemplateRenderer;
  private memoryMonitor = getGlobalMemoryMonitor();
  private metrics: StreamingBatchMetrics;

  constructor(config: StreamingBatchConfig = {}) {
    super();

    this.config = {
      batchSize: 20,
      maxMemoryUsage: 500 * 1024 * 1024, // 500MB
      enableAdaptiveBatching: true,
      enableMemoryMonitoring: true,
      debug: false,
      ...config
    };

    // Initialize batch writer with adaptive configuration
    this.batchWriter = new BatchFileWriter({
      batchSize: this.config.batchSize,
      maxMemoryUsage: this.config.maxMemoryUsage,
      autoFlushInterval: 5000, // 5 seconds
      enableLogging: this.config.debug
    });

    // Initialize streaming renderer
    this.streamingRenderer = new StreamingTemplateRenderer({
      chunkSize: Math.max(5, Math.floor(this.config.batchSize / 2)),
      maxMemoryUsage: this.config.maxMemoryUsage,
      enableBackpressure: true,
      enableMemoryMonitoring: this.config.enableMemoryMonitoring,
      debug: this.config.debug
    });

    this.metrics = {
      templatesProcessed: 0,
      filesWritten: 0,
      batchesCompleted: 0,
      averageBatchSize: 0,
      memoryPressure: 0,
      processingTime: 0
    };

    this.setupEventHandlers();
  }

  /**
   * Process templates using integrated streaming and batch writing
   */
  async processTemplates(templateChunks: TemplateChunk[]): Promise<StreamingBatchMetrics> {
    const startTime = Date.now();

    if (this.config.debug) {
      console.log(`🚀 Starting integrated streaming batch processing of ${templateChunks.length} templates`);
    }

    // Start memory monitoring if enabled
    if (this.config.enableMemoryMonitoring) {
      this.memoryMonitor.start();
    }

    try {
      // Create batch writer stream
      const batchStream = new BatchFileWriterStream(this.batchWriter, this.config.debug);

      // Process templates through streaming renderer
      await this.streamingRenderer.renderTemplatesStream(templateChunks, batchStream);

      // Update metrics
      this.metrics.templatesProcessed = templateChunks.length;
      this.metrics.filesWritten = batchStream.getFilesWritten();
      this.metrics.processingTime = Date.now() - startTime;
      this.metrics.memoryPressure = this.memoryMonitor.getCurrentMetrics().memoryPressure;

      // Calculate batch metrics
      const batchMetrics = this.batchWriter.getMetrics();
      this.metrics.batchesCompleted = batchMetrics.totalBatches;
      this.metrics.averageBatchSize = batchMetrics.averageBatchSize;

      if (this.config.debug) {
        console.log(`✅ Integrated processing completed in ${this.metrics.processingTime}ms`);
        console.log(`📊 Metrics: ${this.metrics.templatesProcessed} templates → ${this.metrics.filesWritten} files in ${this.metrics.batchesCompleted} batches`);
      }

      this.emit('completed', this.metrics);
      return { ...this.metrics };

    } catch (error) {
      this.emit('error', error);
      throw error;
    } finally {
      // Stop memory monitoring
      if (this.config.enableMemoryMonitoring) {
        this.memoryMonitor.stop();
      }
    }
  }

  /**
   * Process templates with adaptive configuration based on system resources
   */
  async processTemplatesAdaptive(templateChunks: TemplateChunk[]): Promise<StreamingBatchMetrics> {
    // Analyze system resources and adjust configuration
    const memoryMetrics = this.memoryMonitor.getCurrentMetrics();
    const recommendations = this.memoryMonitor.getRecommendations();

    if (memoryMetrics.memoryPressure > 0.7) {
      // Reduce batch sizes under memory pressure
      const reducedBatchSize = Math.max(5, Math.floor(this.config.batchSize * 0.5));
      
      if (this.config.debug) {
        console.log(`⚠️  High memory pressure detected, reducing batch size to ${reducedBatchSize}`);
      }

      // Create new batch writer with reduced configuration
      this.batchWriter = new BatchFileWriter({
        batchSize: reducedBatchSize,
        maxMemoryUsage: this.config.maxMemoryUsage,
        autoFlushInterval: 5000,
        enableLogging: this.config.debug
      });

      // Update streaming renderer chunk size
      this.streamingRenderer = new StreamingTemplateRenderer({
        chunkSize: Math.max(3, Math.floor(reducedBatchSize / 2)),
        maxMemoryUsage: this.config.maxMemoryUsage,
        enableBackpressure: true,
        enableMemoryMonitoring: true,
        debug: this.config.debug
      });
    }

    return this.processTemplates(templateChunks);
  }

  /**
   * Setup event handlers for monitoring and coordination
   */
  private setupEventHandlers(): void {
    // Streaming renderer events
    this.streamingRenderer.on('progress', (progress) => {
      this.emit('streamingProgress', progress);
    });

    this.streamingRenderer.on('memoryPressure', (event) => {
      this.emit('memoryPressure', event);
      
      // Adaptive response to memory pressure
      if (event.level === 'critical' && this.config.enableAdaptiveBatching) {
        this.batchWriter.flushAll().catch(error => {
          this.emit('error', error);
        });
      }
    });

    // Note: BatchFileWriter doesn't emit events in current implementation
    // This is a placeholder for future enhancement

    // Memory monitor events
    this.memoryMonitor.on('pressure', (event) => {
      if (event.level === 'critical') {
        this.emit('criticalMemoryPressure', event);
      }
    });
  }

  /**
   * Get current processing metrics
   */
  getMetrics(): StreamingBatchMetrics {
    return { ...this.metrics };
  }

  /**
   * Get detailed system status
   */
  getSystemStatus() {
    return {
      streaming: this.streamingRenderer.getStats(),
      batching: this.batchWriter.getMetrics(),
      memory: this.memoryMonitor.getStats(),
      integrated: this.metrics
    };
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    try {
      // Flush any pending writes
      await this.batchWriter.flushAll();
      
      // Shutdown components
      await this.batchWriter.shutdown();
      this.streamingRenderer.destroy();
      
      this.removeAllListeners();
      
      if (this.config.debug) {
        console.log('🔄 Streaming batch processor shutdown completed');
      }
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
}

/**
 * Global streaming batch processor instance
 */
let globalStreamingBatchProcessor: StreamingBatchProcessor | null = null;

/**
 * Get or create global streaming batch processor
 */
export function getGlobalStreamingBatchProcessor(config?: StreamingBatchConfig): StreamingBatchProcessor {
  if (!globalStreamingBatchProcessor) {
    globalStreamingBatchProcessor = new StreamingBatchProcessor(config);
  }
  return globalStreamingBatchProcessor;
}

/**
 * Cleanup global streaming batch processor
 */
export function cleanupGlobalStreamingBatchProcessor(): Promise<void> {
  if (globalStreamingBatchProcessor) {
    const shutdownPromise = globalStreamingBatchProcessor.shutdown();
    globalStreamingBatchProcessor = null;
    return shutdownPromise;
  }
  return Promise.resolve();
}
