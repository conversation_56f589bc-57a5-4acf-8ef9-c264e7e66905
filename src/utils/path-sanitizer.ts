import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * Configuration options for path sanitization
 */
export interface PathSanitizerOptions {
  /** Base directory that all paths must be within */
  baseDirectory?: string;
  /** Whether to allow absolute paths */
  allowAbsolute?: boolean;
  /** Whether to resolve symlinks */
  resolveSymlinks?: boolean;
  /** Maximum path length allowed */
  maxLength?: number;
  /** Allowed file extensions (if specified, only these extensions are allowed) */
  allowedExtensions?: string[];
  /** Forbidden path segments */
  forbiddenSegments?: string[];
  /** Whether to allow directories without file extensions */
  allowDirectories?: boolean;
}

/**
 * Result of path sanitization
 */
export interface SanitizationResult {
  /** Whether the path is valid and safe */
  isValid: boolean;
  /** The sanitized path (if valid) */
  sanitizedPath?: string;
  /** Error message if invalid */
  error?: string;
  /** Warning messages */
  warnings?: string[];
}

/**
 * Utility class for sanitizing and validating file paths to prevent security vulnerabilities
 */
export class PathSanitizer {
  private static readonly DEFAULT_OPTIONS: Required<PathSanitizerOptions> = {
    baseDirectory: process.cwd(),
    allowAbsolute: false,
    resolveSymlinks: true,
    maxLength: 4096,
    allowedExtensions: [],
    forbiddenSegments: ['..', '.', 'node_modules', '.git', '.env'],
    allowDirectories: false,
  };

  /**
   * Sanitize and validate a file path
   * @param inputPath The path to sanitize
   * @param options Sanitization options
   * @returns Sanitization result
   */
  public static sanitizePath(
    inputPath: string,
    options: PathSanitizerOptions = {}
  ): SanitizationResult {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const warnings: string[] = [];

    try {
      // Basic validation
      if (!inputPath || typeof inputPath !== 'string') {
        return {
          isValid: false,
          error: 'Path must be a non-empty string',
        };
      }

      // Check path length
      if (inputPath.length > opts.maxLength) {
        return {
          isValid: false,
          error: `Path exceeds maximum length of ${opts.maxLength} characters`,
        };
      }

      // Check for null bytes and other dangerous characters
      if (inputPath.includes('\0') || inputPath.includes('\x00')) {
        return {
          isValid: false,
          error: 'Path contains null bytes',
        };
      }

      // Normalize the path to handle different separators
      let normalizedPath = path.normalize(inputPath);

      // Check if absolute path is allowed
      if (path.isAbsolute(normalizedPath) && !opts.allowAbsolute) {
        return {
          isValid: false,
          error: 'Absolute paths are not allowed',
        };
      }

      // Resolve relative to base directory if not absolute
      if (!path.isAbsolute(normalizedPath)) {
        normalizedPath = path.resolve(opts.baseDirectory, normalizedPath);
      }

      // Check for path traversal attempts
      const relativePath = path.relative(opts.baseDirectory, normalizedPath);
      if (relativePath.startsWith('..') || path.isAbsolute(relativePath)) {
        return {
          isValid: false,
          error: 'Path attempts to escape base directory',
        };
      }

      // Check for forbidden path segments
      const pathSegments = normalizedPath.split(path.sep);
      for (const segment of pathSegments) {
        if (opts.forbiddenSegments.includes(segment)) {
          return {
            isValid: false,
            error: `Path contains forbidden segment: ${segment}`,
          };
        }
      }

      // Check file extension if restrictions are specified
      if (opts.allowedExtensions.length > 0) {
        const ext = path.extname(normalizedPath).toLowerCase();

        // If allowDirectories is enabled and the path is a directory, skip extension check
        if (opts.allowDirectories && fs.existsSync(normalizedPath) && fs.statSync(normalizedPath).isDirectory()) {
          // Directory paths are allowed, skip extension validation
        } else if (!opts.allowedExtensions.includes(ext)) {
          return {
            isValid: false,
            error: `File extension '${ext}' is not allowed`,
          };
        }
      }

      // Resolve symlinks if requested
      let finalPath = normalizedPath;
      if (opts.resolveSymlinks) {
        try {
          if (fs.existsSync(normalizedPath)) {
            const realPath = fs.realpathSync(normalizedPath);
            
            // Ensure the resolved path is still within base directory
            const resolvedRelative = path.relative(opts.baseDirectory, realPath);
            if (resolvedRelative.startsWith('..') || path.isAbsolute(resolvedRelative)) {
              return {
                isValid: false,
                error: 'Symlink resolves outside base directory',
              };
            }
            
            finalPath = realPath;
            if (realPath !== normalizedPath) {
              warnings.push('Path was resolved through symlink');
            }
          }
        } catch (error) {
          warnings.push('Could not resolve symlinks');
        }
      }

      return {
        isValid: true,
        sanitizedPath: finalPath,
        warnings: warnings.length > 0 ? warnings : undefined,
      };

    } catch (error) {
      return {
        isValid: false,
        error: `Path sanitization failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Sanitize a path and throw an error if invalid
   * @param inputPath The path to sanitize
   * @param options Sanitization options
   * @returns The sanitized path
   * @throws Error if path is invalid
   */
  public static sanitizePathOrThrow(
    inputPath: string,
    options: PathSanitizerOptions = {}
  ): string {
    const result = this.sanitizePath(inputPath, options);
    
    if (!result.isValid) {
      throw new Error(`Invalid path: ${result.error}`);
    }
    
    return result.sanitizedPath!;
  }

  /**
   * Sanitize multiple paths
   * @param paths Array of paths to sanitize
   * @param options Sanitization options
   * @returns Array of sanitization results
   */
  public static sanitizePaths(
    paths: string[],
    options: PathSanitizerOptions = {}
  ): SanitizationResult[] {
    return paths.map(path => this.sanitizePath(path, options));
  }

  /**
   * Check if a path is safe without full sanitization
   * @param inputPath The path to check
   * @param options Sanitization options
   * @returns Whether the path appears safe
   */
  public static isPathSafe(
    inputPath: string,
    options: PathSanitizerOptions = {}
  ): boolean {
    const result = this.sanitizePath(inputPath, options);
    return result.isValid;
  }

  /**
   * Create a sanitizer function with pre-configured options
   * @param options Default options for the sanitizer
   * @returns A sanitizer function
   */
  public static createSanitizer(options: PathSanitizerOptions) {
    return (inputPath: string, overrideOptions?: PathSanitizerOptions) => {
      const mergedOptions = { ...options, ...overrideOptions };
      return this.sanitizePath(inputPath, mergedOptions);
    };
  }

  /**
   * Sanitize a glob pattern to ensure it's safe
   * @param pattern The glob pattern to sanitize
   * @param options Sanitization options
   * @returns Sanitized glob pattern or error
   */
  public static sanitizeGlobPattern(
    pattern: string,
    options: PathSanitizerOptions = {}
  ): SanitizationResult {
    // Remove glob-specific characters for path validation
    const pathPart = pattern.replace(/[*?[\]{}]/g, '');
    
    // If the pattern has no path component, it's just wildcards
    if (!pathPart.trim()) {
      return {
        isValid: true,
        sanitizedPath: pattern,
      };
    }

    // Validate the path component
    const pathResult = this.sanitizePath(pathPart, options);
    
    if (!pathResult.isValid) {
      return pathResult;
    }

    return {
      isValid: true,
      sanitizedPath: pattern,
      warnings: pathResult.warnings,
    };
  }
}

/**
 * Pre-configured sanitizers for common use cases
 */
export const CommonSanitizers = {
  /** Sanitizer for schema files */
  schemaFiles: PathSanitizer.createSanitizer({
    allowedExtensions: ['.gql', '.graphql', '.schema'],
    forbiddenSegments: ['..', 'node_modules', '.git'],
    allowDirectories: true,
  }),

  /** Sanitizer for TypeScript files */
  typescriptFiles: PathSanitizer.createSanitizer({
    allowedExtensions: ['.ts', '.tsx', '.js', '.jsx'],
    forbiddenSegments: ['..', 'node_modules', '.git', 'dist', 'build'],
  }),

  /** Sanitizer for output directories */
  outputDirectories: PathSanitizer.createSanitizer({
    forbiddenSegments: ['..', 'node_modules', '.git', 'src'],
  }),

  /** Sanitizer for configuration files */
  configFiles: PathSanitizer.createSanitizer({
    allowedExtensions: ['.js', '.json', '.ts', '.yaml', '.yml'],
    forbiddenSegments: ['..', 'node_modules', '.git'],
  }),
};
