import { DirectiveParser } from './directive-parser';
import { getGlobalOptimizedDirectiveParser } from './optimized-directive-parser';
import { getGlobalPerformanceMonitor } from './comment-directive-performance-monitor';
import { LargeCodebaseTestGenerator, TestCodebasePresets, TestCodebaseStats } from './large-codebase-test-generator';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

/**
 * Benchmark configuration
 */
export interface BenchmarkConfig {
  testSizes?: Array<keyof typeof TestCodebasePresets>;
  iterations?: number;
  warmupIterations?: number;
  enableDetailedLogging?: boolean;
  outputDir?: string;
  testLegacyParser?: boolean;
  testOptimizedParser?: boolean;
}

/**
 * Benchmark results for a single test
 */
export interface BenchmarkResult {
  testName: string;
  codebaseStats: TestCodebaseStats;
  legacyResults?: ParserBenchmarkResult;
  optimizedResults?: ParserBenchmarkResult;
  performanceImprovement?: {
    speedupFactor: number;
    timeReduction: number;
    percentageImprovement: number;
  };
}

/**
 * Parser-specific benchmark results
 */
export interface ParserBenchmarkResult {
  parserType: 'legacy' | 'optimized';
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  iterations: number;
  cacheHitRate?: number;
  memoryUsage: NodeJS.MemoryUsage;
  errors: number;
}

/**
 * Comprehensive benchmark report
 */
export interface BenchmarkReport {
  summary: {
    totalTests: number;
    averageSpeedup: number;
    bestSpeedup: number;
    worstSpeedup: number;
    recommendedConfiguration: string;
  };
  results: BenchmarkResult[];
  systemInfo: {
    platform: string;
    nodeVersion: string;
    cpuCount: number;
    totalMemory: number;
    timestamp: number;
  };
  recommendations: string[];
}

/**
 * Comprehensive benchmarking tool for comment directive parsing performance
 */
export class CommentDirectiveBenchmark {
  private config: Required<BenchmarkConfig>;
  private performanceMonitor = getGlobalPerformanceMonitor();
  private testGenerator: LargeCodebaseTestGenerator;

  constructor(config: BenchmarkConfig = {}) {
    this.config = {
      testSizes: ['small', 'medium', 'large'],
      iterations: 10,
      warmupIterations: 3,
      enableDetailedLogging: true,
      outputDir: path.join(os.tmpdir(), 'gql-benchmark-results'),
      testLegacyParser: true,
      testOptimizedParser: true,
      ...config
    };

    this.testGenerator = new LargeCodebaseTestGenerator({
      enableLogging: this.config.enableDetailedLogging
    });
  }

  /**
   * Run comprehensive benchmark suite
   */
  async runBenchmarkSuite(): Promise<BenchmarkReport> {
    if (this.config.enableDetailedLogging) {
      console.log('🚀 Starting Comment Directive Performance Benchmark Suite...\n');
    }

    await fs.ensureDir(this.config.outputDir);
    
    const results: BenchmarkResult[] = [];
    const speedups: number[] = [];

    try {
      // Run benchmarks for each test size
      for (const testSize of this.config.testSizes) {
        if (this.config.enableDetailedLogging) {
          console.log(`📊 Running benchmark for ${testSize} codebase...`);
        }

        const result = await this.runSingleBenchmark(testSize);
        results.push(result);

        if (result.performanceImprovement) {
          speedups.push(result.performanceImprovement.speedupFactor);
        }

        if (this.config.enableDetailedLogging) {
          this.logBenchmarkResult(result);
        }
      }

      // Generate comprehensive report
      const report = this.generateBenchmarkReport(results, speedups);
      
      // Export report
      const reportPath = await this.exportBenchmarkReport(report);
      
      if (this.config.enableDetailedLogging) {
        console.log(`\n✅ Benchmark suite completed. Report saved to: ${reportPath}`);
        this.logBenchmarkSummary(report);
      }

      return report;

    } catch (error) {
      console.error('❌ Error running benchmark suite:', error);
      throw error;
    }
  }

  /**
   * Run benchmark for a single test size
   */
  private async runSingleBenchmark(testSize: keyof typeof TestCodebasePresets): Promise<BenchmarkResult> {
    // Generate test codebase
    this.testGenerator.updateConfig({
      ...TestCodebasePresets[testSize],
      outputDir: path.join(this.config.outputDir, `test-${testSize}`)
    });

    const codebaseStats = await this.testGenerator.generateTestCodebase();
    const schemaFiles = await this.getSchemaFiles(this.testGenerator.getOutputDir());

    let legacyResults: ParserBenchmarkResult | undefined;
    let optimizedResults: ParserBenchmarkResult | undefined;

    try {
      // Test legacy parser
      if (this.config.testLegacyParser) {
        legacyResults = await this.benchmarkLegacyParser(schemaFiles);
      }

      // Test optimized parser
      if (this.config.testOptimizedParser) {
        optimizedResults = await this.benchmarkOptimizedParser(schemaFiles);
      }

      // Calculate performance improvement
      let performanceImprovement;
      if (legacyResults && optimizedResults) {
        const speedupFactor = legacyResults.averageTime / optimizedResults.averageTime;
        const timeReduction = legacyResults.averageTime - optimizedResults.averageTime;
        const percentageImprovement = ((timeReduction / legacyResults.averageTime) * 100);

        performanceImprovement = {
          speedupFactor,
          timeReduction,
          percentageImprovement
        };
      }

      return {
        testName: testSize,
        codebaseStats,
        legacyResults,
        optimizedResults,
        performanceImprovement
      };

    } finally {
      // Cleanup test codebase
      await this.testGenerator.cleanup();
    }
  }

  /**
   * Benchmark legacy DirectiveParser
   */
  private async benchmarkLegacyParser(schemaFiles: string[]): Promise<ParserBenchmarkResult> {
    const times: number[] = [];
    let errors = 0;
    
    // Disable optimized parser for legacy testing
    process.env.ENABLE_DIRECTIVE_OPTIMIZATION = 'false';

    try {
      // Warmup iterations
      for (let i = 0; i < this.config.warmupIterations; i++) {
        await this.runParserIteration(schemaFiles, 'legacy');
      }

      // Actual benchmark iterations
      for (let i = 0; i < this.config.iterations; i++) {
        const startTime = Date.now();
        
        try {
          await this.runParserIteration(schemaFiles, 'legacy');
          times.push(Date.now() - startTime);
        } catch (error) {
          errors++;
          if (this.config.enableDetailedLogging) {
            console.error(`❌ Legacy parser error in iteration ${i}:`, error);
          }
        }
      }

      const totalTime = times.reduce((sum, time) => sum + time, 0);
      const averageTime = totalTime / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      const memoryUsage = process.memoryUsage();

      return {
        parserType: 'legacy',
        totalTime,
        averageTime,
        minTime,
        maxTime,
        iterations: times.length,
        memoryUsage,
        errors
      };

    } finally {
      // Re-enable optimized parser
      delete process.env.ENABLE_DIRECTIVE_OPTIMIZATION;
    }
  }

  /**
   * Benchmark optimized DirectiveParser
   */
  private async benchmarkOptimizedParser(schemaFiles: string[]): Promise<ParserBenchmarkResult> {
    const times: number[] = [];
    let errors = 0;
    const optimizedParser = getGlobalOptimizedDirectiveParser();

    // Clear caches before benchmarking
    optimizedParser.clearCaches();
    optimizedParser.resetMetrics();

    try {
      // Warmup iterations
      for (let i = 0; i < this.config.warmupIterations; i++) {
        await this.runParserIteration(schemaFiles, 'optimized');
      }

      // Reset metrics after warmup
      optimizedParser.resetMetrics();

      // Actual benchmark iterations
      for (let i = 0; i < this.config.iterations; i++) {
        const startTime = Date.now();
        
        try {
          await this.runParserIteration(schemaFiles, 'optimized');
          times.push(Date.now() - startTime);
        } catch (error) {
          errors++;
          if (this.config.enableDetailedLogging) {
            console.error(`❌ Optimized parser error in iteration ${i}:`, error);
          }
        }
      }

      const totalTime = times.reduce((sum, time) => sum + time, 0);
      const averageTime = totalTime / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      const memoryUsage = process.memoryUsage();
      
      // Get cache hit rate from metrics
      const metrics = optimizedParser.getMetrics();
      const cacheHitRate = metrics.totalCalls > 0 
        ? (metrics.cacheHits / metrics.totalCalls) * 100 
        : 0;

      return {
        parserType: 'optimized',
        totalTime,
        averageTime,
        minTime,
        maxTime,
        iterations: times.length,
        cacheHitRate,
        memoryUsage,
        errors
      };

    } finally {
      // Clean up
      optimizedParser.clearCaches();
    }
  }

  /**
   * Run a single parser iteration
   */
  private async runParserIteration(schemaFiles: string[], parserType: 'legacy' | 'optimized'): Promise<void> {
    const extractionPromises: Promise<any>[] = [];

    for (const schemaFile of schemaFiles) {
      // Extract directives for multiple types/fields to simulate real usage
      for (let typeIndex = 0; typeIndex < 3; typeIndex++) {
        const typeName = `Type0_${typeIndex}`;
        
        // Extract type-level directives
        extractionPromises.push(
          DirectiveParser.extractDirectivesFromSchema(schemaFile, typeName)
        );
        
        // Extract field-level directives
        for (let fieldIndex = 0; fieldIndex < 2; fieldIndex++) {
          const fieldName = `field${fieldIndex}`;
          extractionPromises.push(
            DirectiveParser.extractDirectivesFromSchema(schemaFile, typeName, fieldName)
          );
        }
      }
    }

    await Promise.all(extractionPromises);
  }

  /**
   * Get list of schema files from directory
   */
  private async getSchemaFiles(dir: string): Promise<string[]> {
    const files = await fs.readdir(dir);
    return files
      .filter(file => file.endsWith('.gql') || file.endsWith('.graphql'))
      .map(file => path.join(dir, file));
  }

  /**
   * Generate comprehensive benchmark report
   */
  private generateBenchmarkReport(results: BenchmarkResult[], speedups: number[]): BenchmarkReport {
    const averageSpeedup = speedups.length > 0 
      ? speedups.reduce((sum, speedup) => sum + speedup, 0) / speedups.length 
      : 0;
    
    const bestSpeedup = speedups.length > 0 ? Math.max(...speedups) : 0;
    const worstSpeedup = speedups.length > 0 ? Math.min(...speedups) : 0;

    // Generate recommendations
    const recommendations = this.generateRecommendations(results, averageSpeedup);

    // Determine recommended configuration
    let recommendedConfiguration = 'optimized';
    if (averageSpeedup < 1.5) {
      recommendedConfiguration = 'legacy (optimizations provide minimal benefit)';
    } else if (averageSpeedup > 5) {
      recommendedConfiguration = 'optimized with aggressive caching';
    }

    return {
      summary: {
        totalTests: results.length,
        averageSpeedup: Math.round(averageSpeedup * 100) / 100,
        bestSpeedup: Math.round(bestSpeedup * 100) / 100,
        worstSpeedup: Math.round(worstSpeedup * 100) / 100,
        recommendedConfiguration
      },
      results,
      systemInfo: {
        platform: os.platform(),
        nodeVersion: process.version,
        cpuCount: os.cpus().length,
        totalMemory: os.totalmem(),
        timestamp: Date.now()
      },
      recommendations
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(results: BenchmarkResult[], averageSpeedup: number): string[] {
    const recommendations: string[] = [];

    if (averageSpeedup > 3) {
      recommendations.push('Excellent performance improvement! Enable optimized directive parsing in production.');
    } else if (averageSpeedup > 1.5) {
      recommendations.push('Good performance improvement. Consider enabling optimizations for large schemas.');
    } else {
      recommendations.push('Minimal performance improvement. Optimizations may not be necessary for your use case.');
    }

    // Check for memory usage patterns
    const hasHighMemoryUsage = results.some(result => 
      result.optimizedResults && result.optimizedResults.memoryUsage.heapUsed > 100 * 1024 * 1024
    );

    if (hasHighMemoryUsage) {
      recommendations.push('High memory usage detected. Consider reducing cache sizes or using streaming for very large schemas.');
    }

    // Check for error patterns
    const hasErrors = results.some(result => 
      (result.legacyResults && result.legacyResults.errors > 0) || 
      (result.optimizedResults && result.optimizedResults.errors > 0)
    );

    if (hasErrors) {
      recommendations.push('Errors detected during benchmarking. Review error logs and consider fallback mechanisms.');
    }

    return recommendations;
  }

  /**
   * Log benchmark result
   */
  private logBenchmarkResult(result: BenchmarkResult): void {
    console.log(`\n📊 ${result.testName.toUpperCase()} Codebase Results:`);
    console.log(`   Files: ${result.codebaseStats.totalFiles}, Types: ${result.codebaseStats.totalTypes}, Fields: ${result.codebaseStats.totalFields}`);
    
    if (result.legacyResults) {
      console.log(`   Legacy Parser: ${Math.round(result.legacyResults.averageTime)}ms avg (${result.legacyResults.errors} errors)`);
    }
    
    if (result.optimizedResults) {
      console.log(`   Optimized Parser: ${Math.round(result.optimizedResults.averageTime)}ms avg (${Math.round(result.optimizedResults.cacheHitRate || 0)}% cache hit, ${result.optimizedResults.errors} errors)`);
    }
    
    if (result.performanceImprovement) {
      console.log(`   Performance Improvement: ${Math.round(result.performanceImprovement.speedupFactor * 100) / 100}x faster (${Math.round(result.performanceImprovement.percentageImprovement)}% improvement)`);
    }
  }

  /**
   * Log benchmark summary
   */
  private logBenchmarkSummary(report: BenchmarkReport): void {
    console.log('\n🎯 BENCHMARK SUMMARY:');
    console.log(`   Average Speedup: ${report.summary.averageSpeedup}x`);
    console.log(`   Best Speedup: ${report.summary.bestSpeedup}x`);
    console.log(`   Recommended: ${report.summary.recommendedConfiguration}`);
    console.log('\n💡 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => console.log(`   • ${rec}`));
  }

  /**
   * Export benchmark report to file
   */
  private async exportBenchmarkReport(report: BenchmarkReport): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(this.config.outputDir, `benchmark-report-${timestamp}.json`);
    
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    return reportPath;
  }
}

/**
 * Quick benchmark runner for common scenarios
 */
export async function runQuickBenchmark(
  testSize: keyof typeof TestCodebasePresets = 'medium',
  iterations: number = 5
): Promise<BenchmarkResult> {
  const benchmark = new CommentDirectiveBenchmark({
    testSizes: [testSize],
    iterations,
    enableDetailedLogging: true
  });

  const report = await benchmark.runBenchmarkSuite();
  return report.results[0];
}
