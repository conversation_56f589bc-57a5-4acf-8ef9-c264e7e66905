/**
 * Dynamic chalk loader utility to handle ES Module compatibility
 *
 * This utility provides a way to load chalk (ES Module) in a CommonJS environment
 * using dynamic imports with proper fallback mechanisms.
 *
 * IMPORTANT: This solves the ERR_REQUIRE_ESM error on Windows when chalk v5+ is used.
 *
 * Background:
 * - Chalk v5+ is an ES module and cannot be imported using require()
 * - TypeScript compiles `import chalk from 'chalk'` to `require("chalk")` in CommonJS mode
 * - This causes ERR_REQUIRE_ESM errors on Windows (and other platforms)
 *
 * Solution:
 * - Use dynamic import() to load chalk at runtime
 * - Provide fallback implementation if chalk loading fails
 * - Cache the loaded instance for performance
 *
 * Usage:
 * Instead of: import chalk from 'chalk'
 * Use: import { chalkLoader } from './chalk-loader'
 * Then: const chalk = chalkLoader.getChalkSync()
 */

// Type definitions for chalk functionality we use
interface ChalkColorFunction {
  (text: string): string;
  bold: (text: string) => string;
}

interface ChalkInstance {
  green: ChalkColorFunction;
  red: ChalkColorFunction;
  blue: ChalkColorFunction;
  yellow: ChalkColorFunction;
  cyan: (text: string) => string;
  gray: (text: string) => string;
  bold: (text: string) => string;
}

// Helper function to create a color function with bold support
function createColorFunction(colorFn: (text: string) => string): ChalkColorFunction {
  const fn = colorFn as ChalkColorFunction;
  fn.bold = (text: string) => text;
  return fn;
}

// Fallback implementation that returns plain text
const fallbackChalk: ChalkInstance = {
  green: createColorFunction((text: string) => text),
  red: createColorFunction((text: string) => text),
  blue: createColorFunction((text: string) => text),
  yellow: createColorFunction((text: string) => text),
  cyan: (text: string) => text,
  gray: (text: string) => text,
  bold: (text: string) => text,
};

class ChalkLoader {
  private chalkInstance: ChalkInstance | null = null;
  private loadingPromise: Promise<ChalkInstance> | null = null;
  private loadAttempted = false;

  /**
   * Get chalk instance, loading it dynamically if needed
   */
  async getChalk(): Promise<ChalkInstance> {
    // Return cached instance if available
    if (this.chalkInstance) {
      return this.chalkInstance;
    }

    // Return existing loading promise if already in progress
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    // Start loading chalk
    this.loadingPromise = this.loadChalk();
    return this.loadingPromise;
  }

  /**
   * Get chalk instance synchronously, using fallback if not loaded
   */
  getChalkSync(): ChalkInstance {
    if (this.chalkInstance) {
      return this.chalkInstance;
    }

    // If we haven't attempted to load yet, try to load asynchronously
    if (!this.loadAttempted) {
      this.loadChalk().catch(() => {
        // Ignore errors in background loading
      });
    }

    // Return fallback for immediate use
    return fallbackChalk;
  }

  /**
   * Load chalk using dynamic import with error handling
   */
  private async loadChalk(): Promise<ChalkInstance> {
    this.loadAttempted = true;

    try {
      // Use dynamic import to load chalk ES module
      // This should work on Windows, Linux, and macOS with Node.js 12+
      const chalkModule = await import('chalk');

      // Handle both default and named exports
      const chalk = chalkModule.default || chalkModule;
      
      // Verify chalk has the methods we need
      if (typeof chalk.green === 'function' &&
          typeof chalk.red === 'function' &&
          typeof chalk.blue === 'function' &&
          typeof chalk.bold === 'function') {
        
        this.chalkInstance = chalk as ChalkInstance;
        return this.chalkInstance;
      } else {
        throw new Error('Loaded chalk module does not have expected methods');
      }
    } catch (error) {
      // Log error for debugging but don't throw
      // Include platform info for cross-platform debugging
      const platform = typeof process !== 'undefined' ? process.platform : 'unknown';
      console.warn(`Failed to load chalk module on ${platform}, using fallback:`, error instanceof Error ? error.message : String(error));

      // Use fallback
      this.chalkInstance = fallbackChalk;
      return this.chalkInstance;
    }
  }

  /**
   * Check if chalk is loaded and available
   */
  isChalkLoaded(): boolean {
    return this.chalkInstance !== null && this.chalkInstance !== fallbackChalk;
  }

  /**
   * Reset the loader (useful for testing)
   */
  reset(): void {
    this.chalkInstance = null;
    this.loadingPromise = null;
    this.loadAttempted = false;
  }
}

// Export singleton instance
export const chalkLoader = new ChalkLoader();

// Export types for external use
export type { ChalkInstance };
