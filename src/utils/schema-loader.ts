import { loadFilesSync } from '@graphql-tools/load-files';
import { mergeTypeDefs } from '@graphql-tools/merge';
import { makeExecutableSchema } from '@graphql-tools/schema';
import type { GraphQLSchema, DocumentNode } from 'graphql';
import { print } from 'graphql';
import fs from 'fs-extra';
import path from 'path';
import _ from 'lodash';
import * as glob from 'glob';
import { SchemaFilter } from './schema-filter';
import { SchemaErrorEnhancer } from './schema-error-enhancer';
import { WatchedFileWriter } from './watched-file-writer';
import { getGlobalMemoryMappedFileReader, type MemoryMappedConfig } from './memory-mapped-file-reader';
import { getGlobalWASMBridge } from './wasm-bridge';

/**
 * Custom error class for schema loading and processing errors
 */
export class SchemaLoadingError extends Error {
  public readonly enhancedError: any;
  public readonly originalError: Error;
  public readonly formattedMessage: string;

  constructor(originalError: Error, enhancedError: any, formattedMessage: string) {
    super(originalError.message);
    this.name = 'SchemaLoadingError';
    this.originalError = originalError;
    this.enhancedError = enhancedError;
    this.formattedMessage = formattedMessage;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SchemaLoadingError);
    }
  }

  /**
   * Get the formatted error message for display
   */
  getFormattedMessage(): string {
    return this.formattedMessage;
  }

  /**
   * Get the enhanced error details
   */
  getEnhancedError(): any {
    return this.enhancedError;
  }
}

/**
 * Normalize a path to use forward slashes consistently across platforms
 * @param inputPath Path to normalize
 * @returns Normalized path with forward slashes
 */
export function normalizePath(inputPath: string): string {
  return inputPath.replace(/\\/g, '/');
}

/**
 * Debug logger function
 * @param debug Whether debug mode is enabled
 * @param message The message to log
 * @param data Additional data to log
 */
const debugLog = (debug: boolean, message: string, ...data: any[]) => {
  if (debug) {
    if (process.env.DEBUG_PARSER) {
      console.log(`[DEBUG] ${message}`, ...data);
    }
  }
};

/**
 * List all GraphQL schema files (.gql or .graphql) in the specified directory and its subdirectories
 * @param schemaDirPath - Path to the directory containing schema files
 * @returns Array of schema file paths
 */
export function listSchemaFiles(schemaDirPath: string): string[] {
  try {
    // Check if it's a direct file path
    if (fs.existsSync(schemaDirPath) && fs.statSync(schemaDirPath).isFile()) {
      const extension = path.extname(schemaDirPath);
      if (extension === '.graphql' || extension === '.gql') {
        return [schemaDirPath];
      }
      return [];
    }

    // If it's a glob pattern or directory, use glob to find all .gql and .graphql files
    const normalizedPath = normalizePath(schemaDirPath);
    let pattern: string;

    if (normalizedPath.includes('*')) {
      // It's already a glob pattern
      pattern = normalizedPath;
    } else if (fs.existsSync(normalizedPath) && fs.statSync(normalizedPath).isDirectory()) {
      // It's a directory, create a glob pattern for it
      pattern = `${normalizedPath}/**/*.{gql,graphql}`.replace(/\\/g, '/');
    } else {
      // Not a valid path
      console.error(`Schema path does not exist: ${normalizedPath}`);
      throw new Error(`Invalid schema path: ${normalizedPath}. Please provide a valid path to your GraphQL schema files.`);
    }

    // Find all schema files
    const files = glob.sync(pattern, { windowsPathsNoEscape: true });

    // If no files found in a valid directory, throw an error
    if (files.length === 0 && fs.existsSync(normalizedPath) && fs.statSync(normalizedPath).isDirectory()) {
      console.error(`No GraphQL schema files (.gql or .graphql) found in directory: ${normalizedPath}`);
      throw new Error(`No GraphQL schema files (.gql or .graphql) found in directory: ${normalizedPath}`);
    }

    return files;
  } catch (error) {
    console.error('Error listing schema files:', error);
    throw error; // Rethrow the error instead of returning an empty array
  }
}

/**
 * Load all GraphQL schema files (.gql or .graphql) and merge them into a single schema
 * @param schemaDirPath - Path to the directory containing schema files
 * @param debug - Enable debug logging
 * @returns Object containing the executable schema and schema string
 */
export function loadAndMergeSchemas(schemaDirPath: string, debug: boolean = false): {
  executableSchema: GraphQLSchema;
  schemaString: string;
} {
  const errorEnhancer = new SchemaErrorEnhancer();

  // Debug logger function
  const debugLog = (debug: boolean, message: string, ...data: any[]) => {
    if (debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`[DEBUG] ${message}`, ...data);
      }
    }
  };

  try {
    if (process.env.DEBUG_PARSER) {
      console.log(`Loading schema files from: ${schemaDirPath}`);
    }

    // When in DEBUG mode, show the list of schema files
    if (debug) {
      const schemaFiles = listSchemaFiles(schemaDirPath);
      if (process.env.DEBUG_PARSER) {
        console.log('\n===== GRAPHQL SCHEMA FILES LIST =====');
      }
      if (schemaFiles.length === 0) {
        if (process.env.DEBUG_PARSER) {
          console.log('No GraphQL schema files found.');
        }
      } else {
        if (process.env.DEBUG_PARSER) {
          console.log(`Found ${schemaFiles.length} GraphQL schema files:`);
        }
        schemaFiles.forEach((file, index) => {
          if (process.env.DEBUG_PARSER) {
            console.log(`${index + 1}. ${file}`);
          }
        });
      }
      if (process.env.DEBUG_PARSER) {
        console.log('===== END GRAPHQL SCHEMA FILES LIST =====\n');
      }
    }

    // First, check if the path is directly to a graphql file
    if (fs.existsSync(schemaDirPath) && fs.statSync(schemaDirPath).isFile()) {
      const extension = path.extname(schemaDirPath);
      if (extension === '.graphql' || extension === '.gql') {
        if (process.env.DEBUG_PARSER) {
          console.log(`Loading single schema file: ${schemaDirPath}`);
        }
        const schemaContent = fs.readFileSync(schemaDirPath, 'utf8');

        debugLog(debug, `Schema file content length: ${schemaContent.length} characters`);
        debugLog(debug, `Schema file line count: ${schemaContent.split('\n').length} lines`);

        if (debug) {
          if (process.env.DEBUG_PARSER) {
            console.log(`\n===== SCHEMA FILE CONTENT (${path.basename(schemaDirPath)}) =====`);
          }
          if (process.env.DEBUG_PARSER) {
            console.log(schemaContent);
          }
          if (process.env.DEBUG_PARSER) {
            console.log(`===== END SCHEMA FILE CONTENT =====\n`);
          }
        }

        try {
          // Create executable schema directly from the content
          const executableSchema = makeExecutableSchema({ typeDefs: schemaContent });

          return {
            executableSchema,
            schemaString: schemaContent
          };
        } catch (error) {
          // Enhance single file errors
          const schemaFiles = [{
            filePath: schemaDirPath,
            content: schemaContent,
            document: null as any, // Will be set if needed
            lineCount: schemaContent.split('\n').length
          }];

          errorEnhancer.initialize(schemaFiles, schemaContent);
          const enhancedError = errorEnhancer.enhanceError(error as Error);
          const formattedMessage = SchemaErrorEnhancer.formatError(enhancedError);

          console.error(formattedMessage);
          throw new SchemaLoadingError(error as Error, enhancedError, formattedMessage);
        }
      }
    }

    // Load all .gql and .graphql files from the specified directory and its subdirectories
    const typesArray = loadFilesSync(schemaDirPath, {
      extensions: ['.gql', '.graphql']
    });

    if (typesArray.length === 0) {
      throw new Error(`No GraphQL schema files (.gql or .graphql) found at ${schemaDirPath}`);
    }

    if (process.env.DEBUG_PARSER) {
      console.log(`Found ${typesArray.length} schema files`);
    }

    if (debug) {
      // Log each schema file individually with detailed information
      if (process.env.DEBUG_PARSER) {
        console.log(`\n===== SCHEMA FILES DETAILS =====`);
      }
      typesArray.forEach((typeDoc, index) => {
        const filePath = typeof typeDoc.loc?.source?.name === 'string'
          ? typeDoc.loc?.source?.name
          : `Schema document ${index + 1}`;

        const content = typeDoc.loc?.source?.body ?? '';

        if (process.env.DEBUG_PARSER) {
          console.log(`\n----- Schema File ${index + 1}: ${filePath} -----`);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(`Content length: ${content.length} characters`);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(`Line count: ${content.split('\n').length} lines`);
        }

        // Print the actual schema content
        if (process.env.DEBUG_PARSER) {
          console.log(`Content:\n${content}`);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(`----- End Schema File ${index + 1} -----\n`);
        }
      });
      if (process.env.DEBUG_PARSER) {
        console.log(`===== END SCHEMA FILES DETAILS =====\n`);
      }
    } else {
      // In non-debug mode, just log the file names
      debugLog(debug, `Schema files found:`, typesArray.map(doc =>
        typeof doc.loc?.source?.name === 'string' ? doc.loc.source.name : 'Unknown location'));
    }

    // Get actual file paths for better error reporting
    const actualSchemaFiles = listSchemaFiles(schemaDirPath);

    // Prepare schema file information for error enhancement
    const schemaFiles = typesArray.map((typeDoc, index) => {
      let filePath = typeof typeDoc.loc?.source?.name === 'string'
        ? typeDoc.loc.source.name
        : `Schema document ${index + 1}`;

      const content = typeDoc.loc?.source?.body ?? '';

      // Try to match with actual file paths if the source name is generic
      if (filePath === 'GraphQL request' || filePath.startsWith('Schema document')) {
        // Try to match content with actual files to get correct file path
        for (const actualFile of actualSchemaFiles) {
          try {
            const actualContent = fs.readFileSync(actualFile, 'utf8');
            if (actualContent.trim() === content.trim()) {
              filePath = actualFile;
              break;
            }
          } catch (error) {
            // Continue if file can't be read
          }
        }

        // Fallback to index-based mapping if content matching fails
        if ((filePath === 'GraphQL request' || filePath.startsWith('Schema document')) && actualSchemaFiles[index]) {
          filePath = actualSchemaFiles[index];
        }
      }

      return {
        filePath,
        content,
        document: typeDoc,
        lineCount: content.split('\n').length
      };
    });

    let mergedTypeDefs;
    let schemaString;

    try {
      // Merge all schema type definitions
      // Using lodash concat to better handle the array and mergeTypeDefs to properly merge types
      mergedTypeDefs = mergeTypeDefs(_.concat(typesArray));

      // Convert merged schema to string
      schemaString = print(mergedTypeDefs);

      debugLog(debug, `Merged schema string length: ${schemaString.length} characters`);
      debugLog(debug, `Merged schema line count: ${schemaString.split('\n').length} lines`);

      if (debug) {
        if (process.env.DEBUG_PARSER) {
          console.log(`\n===== MERGED SCHEMA =====`);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(schemaString);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(`===== END MERGED SCHEMA =====\n`);
        }
      }
    } catch (error) {
      // Enhance merge errors
      errorEnhancer.initialize(schemaFiles, '');
      const enhancedError = errorEnhancer.enhanceError(error as Error);
      const formattedMessage = SchemaErrorEnhancer.formatError(enhancedError);

      console.error(formattedMessage);
      throw new SchemaLoadingError(error as Error, enhancedError, formattedMessage);
    }

    try {
      // Create executable schema
      const executableSchema = makeExecutableSchema({ typeDefs: mergedTypeDefs });

      return {
        executableSchema,
        schemaString
      };
    } catch (error) {
      // Enhance schema creation errors
      errorEnhancer.initialize(schemaFiles, schemaString);
      const enhancedError = errorEnhancer.enhanceError(error as Error);
      const formattedMessage = SchemaErrorEnhancer.formatError(enhancedError);

      console.error(formattedMessage);
      throw new SchemaLoadingError(error as Error, enhancedError, formattedMessage);
    }
  } catch (error) {
    // If we reach here, it's likely a file system or other non-GraphQL error
    console.error('Error loading schema files:', error);
    throw error;
  }
}

/**
 * Save the merged schema to a file, filtering out @field fields
 * @param mergedSchema - The merged schema content
 * @param outputPath - Path where to save the schema file
 * @param schemaSourcePath - Optional path to original schema files for @field filtering
 * @param debug - Enable debug logging
 */
export async function saveMergedSchema(
  mergedSchema: string,
  outputPath: string,
  schemaSourcePath?: string,
  debug: boolean = false
): Promise<void> {
  // Debug logger function
  const debugLog = (debug: boolean, message: string, ...data: any[]) => {
    if (debug) {
      if (process.env.DEBUG_PARSER) {
        console.log(`[DEBUG] ${message}`, ...data);
      }
    }
  };

  try {
    // Ensure the output directory exists
    const outputDir = path.dirname(outputPath);
    fs.ensureDirSync(outputDir);

    let finalSchema = mergedSchema;

    // Filter @field fields from the schema if source path is provided
    if (schemaSourcePath) {
      try {
        debugLog(debug, `Filtering @field fields from schema using source path: ${schemaSourcePath}`);
        finalSchema = await SchemaFilter.filterFieldDirectiveFields(mergedSchema, schemaSourcePath, debug);
      } catch (filterError) {
        if (process.env.DEBUG_PARSER) {
          console.warn('Warning: Failed to filter @field fields from schema:', filterError);
        }
        // Continue with unfiltered schema if filtering fails
      }
    }

    // Write the schema to the output file with automatic formatting
    WatchedFileWriter.writeFileSync(outputPath, finalSchema);
    debugLog(debug, `Schema saved to: ${outputPath}`);
  } catch (error) {
    console.error('Error saving merged schema:', error);
    throw error;
  }
}

/**
 * Load and merge schemas, then save the result
 * @param schemaDirPath - Path to the directory containing schema files
 * @param outputPath - Path to save the merged schema
 * @param debug - Enable debug logging
 * @returns Object containing the executable schema and schema string
 */
export async function processSchemas(schemaDirPath: string, outputPath: string, debug: boolean = false): Promise<{
  executableSchema: GraphQLSchema;
  schemaString: string;
}> {
  const result = loadAndMergeSchemas(schemaDirPath, debug);
  await saveMergedSchema(result.schemaString, outputPath, schemaDirPath, debug);
  return result;
}

// Phase 3: Memory-Mapped Schema Processing

/**
 * Load and merge schemas using memory-mapped I/O for large files
 * Phase 3: Optimized for large schema processing with streaming and memory management
 */
export async function loadAndMergeSchemasOptimized(
  schemaDirPath: string,
  debug: boolean = false,
  memoryMappedConfig?: Partial<MemoryMappedConfig>
): Promise<{
  executableSchema: GraphQLSchema;
  schemaString: string;
  processingMetrics: {
    totalFiles: number;
    memoryMappedFiles: number;
    totalProcessingTime: number;
    memoryUsage: number;
  };
}> {
  const startTime = Date.now();

  if (debug) {
    console.log('🚀 Loading schemas with Phase 3 memory-mapped optimization...');
  }

  try {
    // Initialize memory-mapped file reader
    const memoryReader = getGlobalMemoryMappedFileReader({
      enableMemoryMapping: true,
      maxFileSize: 50 * 1024 * 1024, // 50MB max for schema files
      enableCaching: true,
      cacheSize: 100, // Cache more schema files
      enablePerformanceMonitoring: true,
      debug,
      ...memoryMappedConfig
    });

    // Initialize WASM bridge for performance-critical operations
    const wasmBridge = getGlobalWASMBridge({
      enableWASM: true,
      enableFallback: true,
      enablePerformanceMonitoring: true,
      debug
    });

    // Find all schema files
    const schemaFiles = await findSchemaFilesOptimized(schemaDirPath);

    if (debug) {
      console.log(`📁 Found ${schemaFiles.length} schema files`);
    }

    // Process files in batches for memory efficiency
    const batchSize = 20; // Process 20 files at a time
    const typeDefs: DocumentNode[] = [];
    let memoryMappedCount = 0;

    for (let i = 0; i < schemaFiles.length; i += batchSize) {
      const batch = schemaFiles.slice(i, i + batchSize);

      if (debug) {
        console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(schemaFiles.length / batchSize)} (${batch.length} files)`);
      }

      // Process batch in parallel
      const batchResults = await Promise.all(
        batch.map(async (filePath) => {
          try {
            const result = await memoryReader.readFile(filePath);

            if (result.isFromCache || result.readTime < 10) {
              memoryMappedCount++;
            }

            const content = result.data.toString('utf8');

            // Parse GraphQL content
            const { loadSchemaSync } = require('@graphql-tools/load');
            const typeDef = loadSchemaSync(filePath, {
              loaders: [
                {
                  loaderId: () => 'memory-mapped-loader',
                  canLoad: () => true,
                  load: () => Promise.resolve({ document: content })
                }
              ]
            });

            return typeDef;
          } catch (error) {
            if (debug) {
              console.warn(`⚠️ Failed to process ${filePath}:`, error);
            }
            // Fallback to standard loading
            return loadFilesSync(filePath)[0];
          }
        })
      );

      typeDefs.push(...batchResults.filter(Boolean));

      // Memory pressure check - clear cache if needed
      const stats = memoryReader.getStats();
      if (stats.totalMemoryUsed > 200 * 1024 * 1024) { // 200MB threshold
        if (debug) {
          console.log('🧠 Memory pressure detected, clearing cache...');
        }
        memoryReader.clearCache();
      }
    }

    // Merge type definitions
    const mergedTypeDefs = mergeTypeDefs(typeDefs);
    const schemaString = print(mergedTypeDefs);

    // Create executable schema
    const executableSchema = makeExecutableSchema({
      typeDefs: mergedTypeDefs,
    });

    const totalProcessingTime = Date.now() - startTime;
    const finalStats = memoryReader.getStats();

    if (debug) {
      console.log(`✅ Schema processing complete in ${totalProcessingTime}ms`);
      console.log(`📊 Memory-mapped files: ${memoryMappedCount}/${schemaFiles.length}`);
      console.log(`💾 Memory usage: ${Math.round(finalStats.totalMemoryUsed / 1024 / 1024)}MB`);
      console.log(`⚡ Cache hit rate: ${Math.round(finalStats.cacheHitRate * 100)}%`);
    }

    return {
      executableSchema,
      schemaString,
      processingMetrics: {
        totalFiles: schemaFiles.length,
        memoryMappedFiles: memoryMappedCount,
        totalProcessingTime,
        memoryUsage: finalStats.totalMemoryUsed
      }
    };

  } catch (error) {
    const enhancer = new SchemaErrorEnhancer();
    const enhancedError = enhancer.enhanceError(error as Error);
    const formattedMessage = SchemaErrorEnhancer.formatError(enhancedError);

    console.error(formattedMessage);
    throw new SchemaLoadingError(error as Error, enhancedError, formattedMessage);
  }
}

/**
 * Find schema files with optimized directory traversal
 */
async function findSchemaFilesOptimized(schemaDirPath: string): Promise<string[]> {
  const extensions = ['.gql', '.graphql', '.schema'];
  const files: string[] = [];

  const traverseDirectory = async (dirPath: string): Promise<void> => {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      // Process files and directories in parallel
      await Promise.all(
        entries.map(async (entry) => {
          const fullPath = path.join(dirPath, entry.name);

          if (entry.isDirectory()) {
            await traverseDirectory(fullPath);
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (extensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        })
      );
    } catch (error) {
      // Skip directories that can't be read
      console.warn(`⚠️ Could not read directory ${dirPath}:`, error);
    }
  };

  await traverseDirectory(schemaDirPath);
  return files.sort(); // Sort for consistent processing order
}

/**
 * Process schemas with streaming for very large files
 */
export async function processLargeSchemaWithStreaming(
  filePath: string,
  chunkSize: number = 64 * 1024
): Promise<string> {
  const memoryReader = getGlobalMemoryMappedFileReader();

  try {
    // Check file size first
    const stats = await fs.stat(filePath);

    if (stats.size < chunkSize * 2) {
      // Small file, read normally
      const result = await memoryReader.readFile(filePath);
      return result.data.toString('utf8');
    }

    // Large file, process in chunks
    let content = '';
    let offset = 0;

    while (offset < stats.size) {
      const remainingBytes = stats.size - offset;
      const readSize = Math.min(chunkSize, remainingBytes);

      const result = await memoryReader.readFile(filePath, offset, readSize);
      content += result.data.toString('utf8');
      offset += result.bytesRead;
    }

    return content;
  } catch (error) {
    // Fallback to standard file reading
    return fs.readFile(filePath, 'utf8');
  }
}