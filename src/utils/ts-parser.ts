import { parseSync as swcParseSync, Module } from '@swc/core';
import * as oxc from 'oxc-parser';
import * as fs from 'fs-extra';
import * as ts from 'typescript';

/**
 * TypeScript code structure representing parsed file components
 */
export interface TypeScriptCodeStructure {
  imports: string[];
  functionSignature: string;
  functionBody: string;
  exports: string[];
  isDefault: boolean;
  hasCustomImplementation: boolean;
  fileContent?: string;
}

// Enhanced performance tracking for parser optimization with oxc-parser
interface ParserPerformanceMetrics {
  oxcSuccessCount: number;
  oxcFailureCount: number;
  swcSuccessCount: number;
  swcFailureCount: number;
  fallbackCount: number;
  totalParseTime: number;
  oxcParseTime: number;
  swcParseTime: number;
  fallbackParseTime: number;
  oxcVsSwcSpeedRatio: number;
  oxcVsFallbackSpeedRatio: number;
}

const parserMetrics: ParserPerformanceMetrics = {
  oxcSuccessCount: 0,
  oxcFailureCount: 0,
  swcSuccessCount: 0,
  swcFailureCount: 0,
  fallbackCount: 0,
  totalParseTime: 0,
  oxcParseTime: 0,
  swcParseTime: 0,
  fallbackParseTime: 0,
  oxcVsSwcSpeedRatio: 0,
  oxcVsFallbackSpeedRatio: 0
};

/**
 * Get current parser performance metrics
 */
export function getParserPerformanceMetrics(): ParserPerformanceMetrics {
  return { ...parserMetrics };
}

/**
 * Reset parser performance metrics
 */
export function resetParserPerformanceMetrics(): void {
  parserMetrics.oxcSuccessCount = 0;
  parserMetrics.oxcFailureCount = 0;
  parserMetrics.swcSuccessCount = 0;
  parserMetrics.swcFailureCount = 0;
  parserMetrics.fallbackCount = 0;
  parserMetrics.totalParseTime = 0;
  parserMetrics.oxcParseTime = 0;
  parserMetrics.swcParseTime = 0;
  parserMetrics.fallbackParseTime = 0;
  parserMetrics.oxcVsSwcSpeedRatio = 0;
  parserMetrics.oxcVsFallbackSpeedRatio = 0;
}

/**
 * Parse a TypeScript file and extract its structure with performance tracking
 * @param filePath - Path to the TypeScript file to parse
 * @returns The parsed code structure or null if parsing failed
 */
export async function parseTypeScriptFile(filePath: string): Promise<TypeScriptCodeStructure | null> {
  const startTime = Date.now();

  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }

    const content = fs.readFileSync(filePath, 'utf8');

    // Try oxc-parser first for maximum performance (10-20x faster than @swc/core)
    const oxcStartTime = Date.now();
    try {
      const oxcResult = parseWithOxc(content, filePath);
      const oxcEndTime = Date.now();

      if (oxcResult) {
        parserMetrics.oxcSuccessCount++;
        parserMetrics.oxcParseTime += (oxcEndTime - oxcStartTime);
        parserMetrics.totalParseTime += (oxcEndTime - startTime);
        return oxcResult;
      }
    } catch (oxcError) {
      const oxcEndTime = Date.now();
      parserMetrics.oxcFailureCount++;
      parserMetrics.oxcParseTime += (oxcEndTime - oxcStartTime);

      // Only log warnings in debug mode to reduce noise
      if (process.env.DEBUG_PARSER) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`oxc-parser parsing failed for ${filePath}, falling back to @swc/core:`, oxcError);
        }
      }
    }

    // Fallback to @swc/core for compatibility
    const swcStartTime = Date.now();
    try {
      const swcResult = parseWithSwc(content, filePath);
      const swcEndTime = Date.now();

      if (swcResult) {
        parserMetrics.swcSuccessCount++;
        parserMetrics.swcParseTime += (swcEndTime - swcStartTime);
        parserMetrics.totalParseTime += (swcEndTime - startTime);
        return swcResult;
      }
    } catch (swcError) {
      const swcEndTime = Date.now();
      parserMetrics.swcFailureCount++;
      parserMetrics.swcParseTime += (swcEndTime - swcStartTime);

      // Only log warnings in debug mode to reduce noise
      if (process.env.DEBUG_PARSER) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`@swc/core parsing failed for ${filePath}, falling back to TypeScript API:`, swcError);
        }
      }
    }

    // Fallback to TypeScript compiler API for complex cases
    const fallbackStartTime = Date.now();
    const astResult = findExportedFunctionWithAST(content);
    const fallbackEndTime = Date.now();

    parserMetrics.fallbackCount++;
    parserMetrics.fallbackParseTime += (fallbackEndTime - fallbackStartTime);
    parserMetrics.totalParseTime += (fallbackEndTime - startTime);

    // Calculate performance ratios
    if (parserMetrics.oxcParseTime > 0 && parserMetrics.swcParseTime > 0) {
      const avgOxcTime = parserMetrics.oxcParseTime / Math.max(1, parserMetrics.oxcSuccessCount + parserMetrics.oxcFailureCount);
      const avgSwcTime = parserMetrics.swcParseTime / Math.max(1, parserMetrics.swcSuccessCount + parserMetrics.swcFailureCount);
      parserMetrics.oxcVsSwcSpeedRatio = avgSwcTime / avgOxcTime;
    }

    if (parserMetrics.oxcParseTime > 0 && parserMetrics.fallbackParseTime > 0) {
      const avgOxcTime = parserMetrics.oxcParseTime / Math.max(1, parserMetrics.oxcSuccessCount + parserMetrics.oxcFailureCount);
      const avgFallbackTime = parserMetrics.fallbackParseTime / Math.max(1, parserMetrics.fallbackCount);
      parserMetrics.oxcVsFallbackSpeedRatio = avgFallbackTime / avgOxcTime;
    }

    if (!astResult) {
      if (process.env.DEBUG_PARSER) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Could not find exported function in ${filePath}`);
        }
      }
      return null;
    }

    // Extract imports using regex (fast and reliable for most cases)
    const imports = extractImportsFromContent(content);

    // Extract exports using regex
    const exports = extractExportsFromContent(content);

    return {
      imports,
      functionSignature: astResult.signature,
      functionBody: astResult.body,
      exports,
      isDefault: astResult.isDefault,
      hasCustomImplementation: astResult.hasCustomImplementation,
      fileContent: content
    };
  } catch (_error) {
    // Log error but continue execution (only in debug mode)
    if (process.env.DEBUG_PARSER) {
      console.error(`Error parsing TypeScript file ${filePath}:`, _error);
    }
    parserMetrics.totalParseTime += (Date.now() - startTime);
    return null;
  }
}

/**
 * Parse TypeScript file using oxc-parser for maximum performance (10-20x faster than @swc/core)
 * @param content - The file content to parse
 * @param filePath - The file path for error reporting
 * @returns Parsed structure or null if parsing failed
 */
function parseWithOxc(content: string, filePath: string): TypeScriptCodeStructure | null {
  try {
    const result = oxc.parseSync(filePath, content, {
      lang: filePath.endsWith('.tsx') ? 'tsx' : 'ts',
      sourceType: 'module',
      preserveParens: false, // Better performance
      showSemanticErrors: false, // Fast mode for better performance
    });

    if (result.errors && result.errors.length > 0) {
      // If there are parsing errors, let it fall back to @swc/core
      throw new Error(`oxc-parser errors: ${result.errors.map(e => e.message).join(', ')}`);
    }

    // Extract imports from oxc AST
    const imports = extractImportsFromOxcAST(result.program, content);

    // Extract exports from oxc AST
    const exports = extractExportsFromOxcAST(result.program, content);

    // Find exported function from oxc AST
    const exportedFunction = findExportedFunctionFromOxcAST(result.program, content);

    if (!exportedFunction) {
      return null;
    }

    return {
      imports,
      functionSignature: exportedFunction.signature,
      functionBody: exportedFunction.body,
      exports,
      isDefault: exportedFunction.isDefault,
      hasCustomImplementation: exportedFunction.hasCustomImplementation,
      fileContent: content
    };
  } catch (error) {
    // Let the caller handle the fallback
    throw error;
  }
}

/**
 * Parse TypeScript file using @swc/core for better performance (fallback from oxc-parser)
 * @param content - The file content to parse
 * @param filePath - The file path for error reporting
 * @returns Parsed structure or null if parsing failed
 */
function parseWithSwc(content: string, filePath: string): TypeScriptCodeStructure | null {
  try {
    const ast = swcParseSync(content, {
      syntax: 'typescript',
      tsx: filePath.endsWith('.tsx'),
      decorators: true,
      dynamicImport: true,
    });

    // Extract imports
    const imports = extractImportsFromSwcAst(ast, content);

    // Extract exports
    const exports = extractExportsFromSwcAst(ast, content);

    // Find exported function
    const exportedFunction = findExportedFunctionFromSwcAst(ast, content);

    if (!exportedFunction) {
      return null;
    }

    return {
      imports,
      functionSignature: exportedFunction.signature,
      functionBody: exportedFunction.body,
      exports,
      isDefault: exportedFunction.isDefault,
      hasCustomImplementation: exportedFunction.hasCustomImplementation,
      fileContent: content
    };
  } catch (error) {
    // Let the caller handle the fallback
    throw error;
  }
}

/**
 * Extract imports from oxc-parser AST (ESTree/TS-ESTree compatible)
 */
function extractImportsFromOxcAST(program: any, content: string): string[] {
  const imports: string[] = [];

  if (program.body) {
    for (const node of program.body) {
      if (node.type === 'ImportDeclaration') {
        // oxc-parser provides range information
        if (node.range) {
          const [start, end] = node.range;
          const importText = content.substring(start, end);
          imports.push(importText);
        } else {
          // Fallback: reconstruct import statement
          const importText = reconstructImportStatement(node);
          if (importText) imports.push(importText);
        }
      }
    }
  }

  return imports;
}

/**
 * Extract exports from oxc-parser AST (ESTree/TS-ESTree compatible)
 */
function extractExportsFromOxcAST(program: any, content: string): string[] {
  const exports: string[] = [];

  if (program.body) {
    for (const node of program.body) {
      if (node.type === 'ExportNamedDeclaration' || node.type === 'ExportDefaultDeclaration') {
        // oxc-parser provides range information
        if (node.range) {
          const [start, end] = node.range;
          const exportText = content.substring(start, end);
          // Remove the 'export' keyword for consistency
          exports.push(exportText.replace(/^export\s+(default\s+)?/, ''));
        }
      }
    }
  }

  return exports;
}

/**
 * Find exported function from oxc-parser AST (ESTree/TS-ESTree compatible)
 */
function findExportedFunctionFromOxcAST(program: any, content: string): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  if (!program.body) return null;

  for (const node of program.body) {
    // Handle function declarations
    if (node.type === 'FunctionDeclaration' && node.range) {
      const [start, end] = node.range;
      const functionText = content.substring(start, end);

      if (functionText.includes('export')) {
        const bodyMatch = functionText.match(/\{([\s\S]*)\}$/);
        const body = bodyMatch ? bodyMatch[1].trim() : '';
        const signature = functionText.replace(/\{[\s\S]*\}$/, '').trim();

        return {
          signature,
          body,
          isDefault: functionText.includes('export default'),
          hasCustomImplementation: isCustomImplementation(body, content)
        };
      }
    }

    // Handle variable declarations (const arrow functions)
    if (node.type === 'VariableDeclaration' && node.range) {
      const [start, end] = node.range;
      const varText = content.substring(start, end);

      if (varText.includes('export')) {
        const arrowMatch = varText.match(/const\s+(\w+)\s*=\s*([^=]*?=>\s*\{[\s\S]*\})/);

        if (arrowMatch) {
          const [, functionName, arrowFunction] = arrowMatch;
          const bodyMatch = arrowFunction.match(/\{([\s\S]*)\}$/);
          const body = bodyMatch ? bodyMatch[1].trim() : '';
          const signature = `const ${functionName} = ${arrowFunction.replace(/\{[\s\S]*\}$/, '').trim()} =>`;

          return {
            signature,
            body,
            isDefault: content.includes(`export default ${functionName}`),
            hasCustomImplementation: isCustomImplementation(body, content)
          };
        }
      }
    }
  }

  return null;
}

/**
 * Reconstruct import statement from oxc-parser AST node (fallback)
 */
function reconstructImportStatement(node: any): string | null {
  try {
    if (!node.source || !node.source.value) return null;

    const source = node.source.value;
    let importClause = '';

    if (node.specifiers && node.specifiers.length > 0) {
      const specifierParts: string[] = [];

      for (const spec of node.specifiers) {
        if (spec.type === 'ImportDefaultSpecifier') {
          specifierParts.push(spec.local.name);
        } else if (spec.type === 'ImportNamespaceSpecifier') {
          specifierParts.push(`* as ${spec.local.name}`);
        } else if (spec.type === 'ImportSpecifier') {
          if (spec.imported.name === spec.local.name) {
            specifierParts.push(spec.local.name);
          } else {
            specifierParts.push(`${spec.imported.name} as ${spec.local.name}`);
          }
        }
      }

      if (specifierParts.length > 0) {
        importClause = ` { ${specifierParts.join(', ')} } from`;
      }
    }

    return `import${importClause} '${source}';`;
  } catch (error) {
    return null;
  }
}

/**
 * Extract imports from @swc/core AST
 */
function extractImportsFromSwcAst(ast: Module, content: string): string[] {
  const imports: string[] = [];

  for (const item of ast.body) {
    if (item.type === 'ImportDeclaration') {
      const start = item.span.start;
      const end = item.span.end;
      const importText = content.substring(start, end);
      imports.push(importText);
    }
  }

  return imports;
}

/**
 * Extract exports from @swc/core AST with enhanced detection
 */
function extractExportsFromSwcAst(ast: Module, content: string): string[] {
  const exports: string[] = [];

  for (const item of ast.body) {
    // Handle direct export declarations
    if (item.type === 'ExportDefaultDeclaration' || item.type === 'ExportNamedDeclaration') {
      const start = item.span.start;
      const end = item.span.end;
      const exportText = content.substring(start, end);
      // Remove the 'export' keyword for consistency
      exports.push(exportText.replace(/^export\s+(default\s+)?/, ''));
    }

    // Handle legacy export detection for backward compatibility
    else if (item.type === 'ExportDeclaration' ||
        (item.type === 'VariableDeclaration' && content.substring(item.span.start, item.span.end).includes('export'))) {
      const start = item.span.start;
      const end = item.span.end;
      const exportText = content.substring(start, end);
      // Remove the 'export' keyword for consistency
      exports.push(exportText.replace(/^export\s+(default\s+)?/, ''));
    }
  }

  // Also scan for export statements that might not be captured by AST
  const exportRegexPatterns = [
    /export\s*\{\s*[^}]+\s*\}/g,  // export { name1, name2 }
    /export\s+default\s+\w+/g,     // export default functionName
  ];

  for (const pattern of exportRegexPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      for (const match of matches) {
        const cleanExport = match.replace(/^export\s+(default\s+)?/, '');
        if (!exports.includes(cleanExport)) {
          exports.push(cleanExport);
        }
      }
    }
  }

  return exports;
}

/**
 * Find exported function from @swc/core AST with enhanced pattern detection
 */
function findExportedFunctionFromSwcAst(ast: Module, content: string): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  // Track all export statements for better detection
  const exportStatements = new Set<string>();
  const functionDeclarations = new Map<string, any>();

  // First pass: collect all exports and function declarations
  for (const item of ast.body) {
    if (item.type === 'ExportNamedDeclaration' || item.type === 'ExportDefaultDeclaration') {
      const exportText = content.substring(item.span.start, item.span.end);
      exportStatements.add(exportText);
    }

    if (item.type === 'VariableDeclaration') {
      const varText = content.substring(item.span.start, item.span.end);
      // Extract function names from variable declarations
      const constMatch = varText.match(/const\s+(\w+)\s*=/);
      if (constMatch) {
        functionDeclarations.set(constMatch[1], item);
      }
    }
  }

  // Second pass: find exported functions with enhanced detection
  for (const item of ast.body) {
    // Handle direct function declarations with export
    if (item.type === 'FunctionDeclaration') {
      const functionText = content.substring(item.span.start, item.span.end);
      if (functionText.includes('export')) {
        const result = extractFunctionDetails(functionText, content);
        if (result) return result;
      }
    }

    // Handle export declarations
    if (item.type === 'ExportDefaultDeclaration' || item.type === 'ExportNamedDeclaration') {
      const exportText = content.substring(item.span.start, item.span.end);

      // Handle direct function export: export default function name() {}
      if (exportText.includes('function')) {
        const result = extractFunctionDetails(exportText, content);
        if (result) return result;
      }

      // Handle variable export: export { functionName } or export default functionName
      const varExportMatch = exportText.match(/export\s+(?:default\s+)?(?:\{\s*)?(\w+)(?:\s*\})?/);
      if (varExportMatch) {
        const functionName = varExportMatch[1];
        const varDeclaration = functionDeclarations.get(functionName);

        if (varDeclaration) {
          const varText = content.substring(varDeclaration.span.start, varDeclaration.span.end);
          const result = extractVariableFunctionDetails(varText, functionName, content, exportText.includes('default'));
          if (result) return result;
        }
      }
    }

    // Handle variable declarations that might be exported elsewhere
    if (item.type === 'VariableDeclaration') {
      const varText = content.substring(item.span.start, item.span.end);

      // Check if this variable is exported elsewhere in the file
      const constMatch = varText.match(/const\s+(\w+)\s*=/);
      if (constMatch) {
        const functionName = constMatch[1];
        const isExported = Array.from(exportStatements).some(exp =>
          exp.includes(functionName) || content.includes(`export { ${functionName}`) ||
          content.includes(`export default ${functionName}`)
        );

        if (isExported) {
          const isDefault = Array.from(exportStatements).some(exp =>
            exp.includes(`default ${functionName}`) || content.includes(`export default ${functionName}`)
          );
          const result = extractVariableFunctionDetails(varText, functionName, content, isDefault);
          if (result) return result;
        }
      }
    }
  }

  return null;
}

/**
 * Extract function details from function declaration text
 */
function extractFunctionDetails(functionText: string, content: string): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  // Handle __resolveType functions specifically
  if (functionText.includes('__resolveType')) {
    const bodyMatch = functionText.match(/\{([\s\S]*)\}$/);
    const body = bodyMatch ? bodyMatch[1].trim() : '';
    const signature = functionText.replace(/\{[\s\S]*\}$/, '').trim();

    return {
      signature,
      body,
      isDefault: functionText.includes('export default'),
      hasCustomImplementation: isCustomImplementation(body, content)
    };
  }

  // Handle regular function declarations
  const bodyMatch = functionText.match(/\{([\s\S]*)\}$/);
  if (bodyMatch) {
    const body = bodyMatch[1].trim();
    const signature = functionText.replace(/\{[\s\S]*\}$/, '').trim();

    return {
      signature,
      body,
      isDefault: functionText.includes('export default'),
      hasCustomImplementation: isCustomImplementation(body, content)
    };
  }

  return null;
}

/**
 * Extract function details from variable declaration (arrow functions)
 */
function extractVariableFunctionDetails(
  varText: string,
  functionName: string,
  content: string,
  isDefault: boolean
): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  // Enhanced arrow function detection with multiple patterns
  const patterns = [
    // async arrow function: const name = async (params) => { body }
    /const\s+(\w+)\s*=\s*(async\s+)?\(([^)]*)\)\s*(?::\s*[^=]+)?\s*=>\s*\{([\s\S]*)\}/,
    // arrow function with type: const name: Type = (params) => { body }
    /const\s+(\w+)\s*:\s*[^=]+\s*=\s*(async\s+)?\(([^)]*)\)\s*(?::\s*[^=]+)?\s*=>\s*\{([\s\S]*)\}/,
    // simple arrow function: const name = (params) => { body }
    /const\s+(\w+)\s*=\s*\(([^)]*)\)\s*(?::\s*[^=]+)?\s*=>\s*\{([\s\S]*)\}/,
    // single param arrow function: const name = param => { body }
    /const\s+(\w+)\s*=\s*(async\s+)?(\w+)\s*=>\s*\{([\s\S]*)\}/
  ];

  for (const pattern of patterns) {
    const match = varText.match(pattern);
    if (match) {
      const [, matchedName, asyncKeyword = '', params, body] = match;

      if (matchedName === functionName) {
        const cleanBody = body.trim();
        const signature = `const ${functionName} = ${asyncKeyword}(${params})${asyncKeyword ? ' ' : ''} =>`;

        return {
          signature,
          body: cleanBody,
          isDefault,
          hasCustomImplementation: isCustomImplementation(cleanBody, content)
        };
      }
    }
  }

  return null;
}

/**
 * Extract imports from content using regex (fallback method)
 */
function extractImportsFromContent(content: string): string[] {
  const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"][^'"]*['"];?/g;
  const matches = content.match(importRegex);
  return matches || [];
}

/**
 * Extract exports from content using regex (fallback method)
 */
function extractExportsFromContent(content: string): string[] {
  const exportRegex = /export\s+(?:default\s+)?(?:const|let|var|function|class|interface|type)\s+[^;{]+(?:\{[^}]*\}|[^;]*);?/g;
  const matches = content.match(exportRegex);
  return matches ? matches.map(exp => exp.replace(/^export\s+(default\s+)?/, '')) : [];
}

/**
 * Generate a new TypeScript file content while preserving the implementation body
 * @param template - The template content to use as a base
 * @param existingStructure - The structure parsed from an existing file
 * @returns The merged content
 */
export function generatePreservedImplementation(
  newContent: string,
  existingStructure: TypeScriptCodeStructure
): string {
  // We should preserve all custom content even if there's no custom implementation
  // in the function body - this handles cases where users add only imports

  const { imports: existingImports, functionBody, fileContent } = existingStructure;

  try {
    // Extract imports from the new content
    const newImportsMatch = newContent.match(/import.*?;(\s*import.*?;)*/gs);
    const _newImports = newImportsMatch ? newImportsMatch[0] : '';

    // Extract individual import statements from new content
    const newImportStatements = newContent.match(/import\s+.*?from\s+['"].*?['"]\s*;/gs) ?? [];

    // Create a map of module paths to their import statements for both new and existing imports
    const moduleToNewImports = new Map<string, string>();
    const moduleToExistingImports = new Map<string, string>();

    // Parse new imports
    newImportStatements.forEach(importStmt => {
      const moduleMatch = importStmt.match(/from\s+['"]([^'"]+)['"]/);
      if (moduleMatch && moduleMatch[1]) {
        const _modulePath = moduleMatch[1];
        moduleToNewImports.set(_modulePath, importStmt);
      }
    });

    // Parse existing imports
    existingImports.forEach(importStmt => {
      const moduleMatch = importStmt.match(/from\s+['"]([^'"]+)['"]/);
      if (moduleMatch && moduleMatch[1]) {
        const _modulePath = moduleMatch[1];
        moduleToExistingImports.set(_modulePath, importStmt);
      }
    });

    // Special handling for named imports that might have conflicts
    const mergedImports: string[] = [];

    // Add all new imports first
    newImportStatements.forEach(importStmt => {
      const moduleMatch = importStmt.match(/from\s+['"]([^'"]+)['"]/);
      if (moduleMatch && moduleMatch[1]) {
        const _modulePath = moduleMatch[1];
        // If this is a standard import and there's no custom import from the same module,
        // add it to the merged imports
        mergedImports.push(importStmt);
      }
    });

    // Process all existing imports - preserve them with filtering for resolve-types
    existingImports.forEach(importStmt => {
      const moduleMatch = importStmt.match(/from\s+['"]([^'"]+)['"]/);
      if (!moduleMatch || !moduleMatch[1]) {
        // If we can't extract the module path, keep it to be safe
        if (!mergedImports.includes(importStmt)) {
          mergedImports.push(importStmt);
        }
        return;
      }

      const _modulePath = moduleMatch[1];
      const newImportStatement = moduleToNewImports.get(_modulePath);

      // Special filtering: Don't preserve resolve-types imports for regular field resolvers
      // Only __resolveType functions should import from resolve-types.ts
      if (_modulePath.includes('resolve-types') && !fileContent?.includes('__resolveType')) {
        if (process.env.DEBUG_PARSER) {
          console.log(`[DEBUG] Filtering out resolve-types import from regular field resolver: ${importStmt}`);
        }
        if (process.env.DEBUG_PARSER) {
          console.log(`[DEBUG] Module path: ${_modulePath}, File contains __resolveType: ${fileContent?.includes('__resolveType')}`);
        }
        return; // Skip this import - it shouldn't be in a regular field resolver
      }

      // Additional check: Skip imports of type aliases that end with 'ResolveType' for regular field resolvers
      if (importStmt.includes('ResolveType') && !fileContent?.includes('__resolveType')) {
        if (process.env.DEBUG_PARSER) {
          console.log(`[DEBUG] Filtering out ResolveType import from regular field resolver: ${importStmt}`);
        }
        return; // Skip this import - type aliases are only for __resolveType functions
      }

      // If this module isn't in the new imports, it's a custom import so keep it
      if (!newImportStatement) {
        if (!mergedImports.includes(importStmt)) {
          mergedImports.push(importStmt);
        }
        return;
      }

      // This is where the conflict handling happens
      // The module exists in both new and existing imports
      // We need to merge the named imports if possible

      // Check if both are named imports
      const existingNamedMatch = importStmt.match(/import\s+{([^}]+)}\s+from/);
      const newNamedMatch = newImportStatement.match(/import\s+{([^}]+)}\s+from/);

      if (existingNamedMatch && newNamedMatch) {
        // Both are named imports, try to merge them
        const existingNames = existingNamedMatch[1].split(',').map(n => n.trim());
        const newNames = newNamedMatch[1].split(',').map(n => n.trim());

        // Find names in existing that aren't in new imports
        const uniqueExistingNames = existingNames.filter(name => {
          // Remove any 'as' aliases for comparison
          const baseName = name.split(' as ')[0].trim();
          return !newNames.some(newName => {
            const baseNewName = newName.split(' as ')[0].trim();
            return baseName === baseNewName;
          });
        });

        // If there are unique names, create a merged import
        if (uniqueExistingNames.length > 0) {
          const allNames = [...newNames, ...uniqueExistingNames];
          const mergedImport = `import { ${allNames.join(', ')} } from '${_modulePath}';`;

          // Find the index of the newImportStatement in mergedImports
          const index = mergedImports.findIndex(imp => imp === newImportStatement);
          if (index !== -1) {
            // Replace it with our merged one
            mergedImports[index] = mergedImport;
          } else {
            // Otherwise just add it (shouldn't typically happen)
            mergedImports.push(mergedImport);
          }
          return; // Skip adding the original import below
        }
      }

      // For default imports or other cases, check if we already have this import
      if (!mergedImports.includes(importStmt) && !mergedImports.includes(newImportStatement)) {
        mergedImports.push(importStmt);
      }
    });

    // Build a new imports section - ensure no duplicates
    const uniqueImports = Array.from(new Set(mergedImports));
    const mergedImportsText = uniqueImports.join('\n');

    // Replace the imports section in the new content
    let resultContent = newContent;
    if (newImportsMatch && newImportsMatch[0]) {
      resultContent = resultContent.replace(newImportsMatch[0], mergedImportsText);
    } else {
      // If there are no imports in the new content, add them at the beginning
      resultContent = mergedImportsText + '\n\n' + resultContent;
    }

    // Check for custom comments after imports but before the function
    if (fileContent) {
      // Find the function in the original content
      const functionMatch = fileContent.match(/export\s+const\s+\w+(Resolver|Mutator).*?}\s*;/s);
      if (functionMatch) {
        const functionStart = fileContent.indexOf(functionMatch[0]);

        // Get content before the function
        const contentBeforeFunction = fileContent.substring(0, functionStart).trim();

        // Find the last import statement
        const lastImportMatch = contentBeforeFunction.match(/import\s+.*?from\s+['"].*?['"]\s*;/gs);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const lastImportEndPos = contentBeforeFunction.lastIndexOf(lastImport) + lastImport.length;

          // Get content between imports and function
          const contentBetween = contentBeforeFunction.substring(lastImportEndPos).trim();

          // Remove JSDoc comments to avoid duplication
          const jsdocRegex = /\/\*\*[\s\S]*?\*\//g;
          const tempContentWithoutJSDoc = contentBetween.replace(jsdocRegex, '').trim();

          // If there's custom content, add it after imports in the result
          if (tempContentWithoutJSDoc.length > 0) {
            if (process.env.DEBUG_PARSER) {
              console.log('Found custom content between imports and function, preserving it.');
            }
            // Find where to insert the custom content in the result
            const resultLastImportMatch = resultContent.match(/import\s+.*?from\s+['"].*?['"]\s*;/gs);
            if (resultLastImportMatch) {
              const resultLastImport = resultLastImportMatch[resultLastImportMatch.length - 1];
              const resultLastImportEndPos = resultContent.lastIndexOf(resultLastImport) + resultLastImport.length;

              // Extract the JSDoc comment from the new content to know what to replace
              const newJSDocMatch = resultContent.substring(resultLastImportEndPos).match(/\s*\/\*\*[\s\S]*?\*\//);
              const insertPos = resultLastImportEndPos;

              if (newJSDocMatch) {
                // Insert content before the JSDoc
                // Maintain a double newline for readability
                resultContent =
                  resultContent.substring(0, insertPos) +
                  '\n\n' + tempContentWithoutJSDoc + '\n\n' +
                  resultContent.substring(insertPos);
              } else {
                // If no JSDoc found, just insert after the last import
                resultContent =
                  resultContent.substring(0, insertPos) +
                  '\n\n' + tempContentWithoutJSDoc +
                  (resultContent.substring(insertPos).startsWith('\n') ? '' : '\n') +
                  resultContent.substring(insertPos);
              }
            }
          }
        }

        // Check for content after the function
        const functionEnd = functionStart + functionMatch[0].length;
        const contentAfterFunction = fileContent.substring(functionEnd).trim();

        if (contentAfterFunction.length > 0) {
          if (process.env.DEBUG_PARSER) {
            console.log('Found content after the function, preserving it.');
          }
          // Find the function in the result content
          const resultFunctionMatch = resultContent.match(/export\s+const\s+\w+(Resolver|Mutator).*?}\s*;/s);
          if (resultFunctionMatch) {
            const resultFunctionEnd = resultContent.indexOf(resultFunctionMatch[0]) + resultFunctionMatch[0].length;

            // Add the content after the function
            resultContent =
              resultContent.substring(0, resultFunctionEnd) +
              '\n\n' + contentAfterFunction +
              (resultContent.substring(resultFunctionEnd).trim().length > 0 ? '\n' + resultContent.substring(resultFunctionEnd).trim() : '');
          } else {
            // If we can't find the function in the result, just append the content
            resultContent += '\n\n' + contentAfterFunction;
          }
        }
      }
    }

    // Now replace the function body to preserve implementation
    // Extract function body from new content
    const functionBodyMatch = resultContent.match(/try\s*{([^}]*?)}\s*catch/s);

    if (functionBodyMatch && functionBodyMatch[1] && functionBody) {
      // Replace only the content inside the try block
      resultContent = resultContent.replace(
        /try\s*{([^}]*?)}\s*catch/s,
        `try {\n    ${functionBody.trim()}\n  } catch`
      );
    }

    return resultContent;
  } catch (_error) {
    // Return the original new content if there's an error
    if (process.env.DEBUG_PARSER) {
      console.warn('Error generating preserved implementation:', _error);
    }
    return newContent;
  }
}

/**
 * Determine if a function body contains custom implementation
 * @param body The function body
 * @returns Whether the body contains custom implementation
 */
function isCustomImplementation(body: string, fullContent?: string): boolean {
  // Check if the function is a __resolveType implementation (for interfaces and unions)
  if (fullContent && fullContent.includes('__resolveType') && fullContent.includes('obj.__typename')) {
    // Special handling for __resolveType functions
    const hasCustomCode = body.split('\n').some(line => {
      const trimmedLine = line.trim();
      // Check if there are actual implementation lines that are uncommented
      // Skip standard template patterns like __typename check and error throwing
      if (
        trimmedLine &&
        !trimmedLine.startsWith('//') &&
        !trimmedLine.startsWith('/*') &&
        !trimmedLine.endsWith('*/') &&
        !trimmedLine.includes('if (obj.__typename)') &&
        !trimmedLine.includes('return obj.__typename') &&
        !trimmedLine.includes('throw new Error') &&
        !trimmedLine.includes('Unable to resolve type') &&
        // Lines that have been uncommented from the example implementations
        trimmedLine.includes('in obj') &&
        trimmedLine.includes('return')
      ) {
        return true;
      }
      return false;
    });

    // Also check if any of the commented examples have been uncommented
    const examplePattern = /\/\/\s*if \('([^']+)' in obj\)/;
    const activePattern = /if \('([^']+)' in obj\)/;

    // If we find uncommented examples, it's a custom implementation
    const hasUncommentedExamples =
      (fullContent.match(activePattern) ?? []).length >
      (fullContent.match(examplePattern) ?? []).length;

    return hasCustomCode || hasUncommentedExamples;
  }

  // Regular resolver function check (non __resolveType)
  // Check if there's custom implementation (non-TODO/non-throw lines)
  const hasImplementation = body.split('\n').some(line => {
    const trimmedLine = line.trim();
    // Skip empty lines, comments, and standard template code
    if (
      !trimmedLine ||
      trimmedLine.startsWith('//') ||
      trimmedLine.startsWith('/*') ||
      trimmedLine.endsWith('*/') ||
      trimmedLine.includes('TODO:') ||
      trimmedLine.includes('throw new Error') ||
      trimmedLine.includes('try {') ||
      trimmedLine.includes('} catch') ||
      trimmedLine.includes('console.error')
    ) {
      return false;
    }

    // For modern resolver format, check if it's a simple return statement with actual implementation
    if (trimmedLine.startsWith('return ')) {
      // Skip default template returns that are just placeholders
      if (
        trimmedLine.includes('TODO') ||
        trimmedLine.includes('throw new Error') ||
        trimmedLine === 'return null;' ||
        trimmedLine === 'return undefined;' ||
        trimmedLine === 'return {};' ||
        trimmedLine === 'return [];'
      ) {
        return false;
      }
      // This is a real return statement with implementation
      return true;
    }

    return true;
  });

  // Also check if the error message is customized (not the default template one)
  const defaultErrorPattern = /throw new Error\(['"].*?not implemented['"]\)/;
  const hasCustomErrorMessage = body.includes('throw new Error') && !defaultErrorPattern.test(body);

  return hasImplementation || hasCustomErrorMessage;
}



/**
 * AST-based function detection for modern resolver patterns
 * @param content - The TypeScript file content
 * @returns The function details or null if not found
 */
function findExportedFunctionWithAST(content: string): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  try {
    // Create TypeScript source file
    const sourceFile = ts.createSourceFile(
      'temp.ts',
      content,
      ts.ScriptTarget.Latest,
      true
    );

    let result: {
      signature: string;
      body: string;
      isDefault: boolean;
      hasCustomImplementation: boolean;
    } | null = null;

    // Traverse the AST to find exported functions
    function visit(node: ts.Node): void {
      // Handle __resolveType functions (interface/union resolvers)
      if (ts.isFunctionDeclaration(node) &&
          node.name?.getText(sourceFile) === '__resolveType' &&
          node.modifiers?.some(m => m.kind === ts.SyntaxKind.ExportKeyword || m.kind === ts.SyntaxKind.DefaultKeyword)) {

        const body = node.body?.getText(sourceFile) || '';
        const signature = node.getText(sourceFile).replace(body, '').trim();
        const hasCustomImpl = analyzeCustomImplementationAST(node.body, sourceFile, content);

        result = {
          signature,
          body: body.replace(/^\{|\}$/g, '').trim(), // Remove outer braces
          isDefault: node.modifiers?.some(m => m.kind === ts.SyntaxKind.DefaultKeyword) || false,
          hasCustomImplementation: hasCustomImpl
        };
        return;
      }

      // Handle modern const arrow function declarations (may be exported separately)
      if (ts.isVariableStatement(node)) {
        // Check if this is an exported variable statement
        const isDirectlyExported = node.modifiers?.some(m => m.kind === ts.SyntaxKind.ExportKeyword);

        if (node.declarationList.declarations.length > 0) {
          const declaration = node.declarationList.declarations[0];

          if (ts.isVariableDeclaration(declaration) &&
              declaration.initializer &&
              ts.isArrowFunction(declaration.initializer)) {

            const arrowFunc = declaration.initializer;
            const functionName = declaration.name.getText(sourceFile);

            // Check if this function is exported elsewhere in the file
            const isExportedElsewhere = content.includes(`export { ${functionName} }`) ||
                                       content.includes(`export default ${functionName}`) ||
                                       isDirectlyExported;

            if (isExportedElsewhere) {
              // Extract function body
              let body = '';
              let hasCustomImpl = false;

              if (ts.isBlock(arrowFunc.body)) {
                body = arrowFunc.body.getText(sourceFile);
                body = body.replace(/^\{|\}$/g, '').trim(); // Remove outer braces
                hasCustomImpl = analyzeCustomImplementationAST(arrowFunc.body, sourceFile, content);
              } else {
                // Expression body (e.g., const fn = async () => someExpression)
                body = `return ${arrowFunc.body.getText(sourceFile)};`;
                hasCustomImpl = analyzeExpressionImplementation(arrowFunc.body, sourceFile);
              }

              // Build signature
              const params = arrowFunc.parameters.map(p => p.getText(sourceFile)).join(', ');
              const returnType = arrowFunc.type ? `: ${arrowFunc.type.getText(sourceFile)}` : '';
              const asyncKeyword = arrowFunc.modifiers?.some(m => m.kind === ts.SyntaxKind.AsyncKeyword) ? 'async ' : '';

              const signature = `const ${functionName} = ${asyncKeyword}(${params})${returnType} =>`;

              result = {
                signature,
                body,
                isDefault: content.includes(`export default ${functionName}`),
                hasCustomImplementation: hasCustomImpl
              };
              return;
            }
          }
        }
      }

      // Continue traversing
      ts.forEachChild(node, visit);
    }

    visit(sourceFile);
    return result;

  } catch (error) {
    if (process.env.DEBUG_PARSER) {
      console.warn('AST parsing failed, falling back to regex:', error);
    }
    return findExportedFunctionWithRegex(content);
  }
}

/**
 * Analyze if a function block contains custom implementation using AST
 * @param block - The function block node
 * @param sourceFile - The source file
 * @param fullContent - The full file content for additional context
 * @returns Whether the implementation is custom
 */
function analyzeCustomImplementationAST(
  block: ts.Block | undefined,
  sourceFile: ts.SourceFile,
  fullContent: string
): boolean {
  if (!block) return false;

  // Special handling for __resolveType functions
  if (fullContent.includes('__resolveType')) {
    return analyzeResolveTypeImplementation(block, sourceFile, fullContent);
  }

  // Regular resolver analysis
  for (const statement of block.statements) {
    if (ts.isReturnStatement(statement) && statement.expression) {
      return analyzeReturnStatementImplementation(statement.expression, sourceFile);
    }

    // Check for other custom statements (variable declarations, if statements, etc.)
    if (!isTemplateStatement(statement, sourceFile)) {
      return true;
    }
  }

  return false;
}

/**
 * Analyze expression-based arrow function implementations
 * @param expression - The expression node
 * @param sourceFile - The source file
 * @returns Whether the implementation is custom
 */
function analyzeExpressionImplementation(expression: ts.Expression, sourceFile: ts.SourceFile): boolean {
  const expressionText = expression.getText(sourceFile).trim();

  // Skip placeholder expressions
  if (expressionText === 'null' ||
      expressionText === 'undefined' ||
      expressionText === '{}' ||
      expressionText === '[]' ||
      expressionText.includes('TODO') ||
      expressionText.includes('throw new Error')) {
    return false;
  }

  // Check for actual function calls or property access
  if (ts.isCallExpression(expression) ||
      ts.isAwaitExpression(expression) ||
      ts.isPropertyAccessExpression(expression)) {
    return true;
  }

  return false;
}

/**
 * Analyze __resolveType function implementations
 * @param block - The function block
 * @param sourceFile - The source file
 * @param fullContent - The full file content
 * @returns Whether the implementation is custom
 */
function analyzeResolveTypeImplementation(
  block: ts.Block,
  sourceFile: ts.SourceFile,
  fullContent: string
): boolean {
  // Check for uncommented example implementations
  const examplePattern = /\/\/\s*if \('([^']+)' in obj\)/g;
  const activePattern = /if \('([^']+)' in obj\)/g;

  const commentedExamples = (fullContent.match(examplePattern) || []).length;
  const activeExamples = (fullContent.match(activePattern) || []).length;

  if (activeExamples > commentedExamples) {
    return true; // Has uncommented examples
  }

  // Check for custom implementation in the block
  for (const statement of block.statements) {
    if (ts.isIfStatement(statement)) {
      // Check if it's a custom type check
      const condition = statement.expression.getText(sourceFile);
      if (condition.includes('in obj') && !condition.includes('__typename')) {
        return true;
      }
    }

    if (ts.isReturnStatement(statement) && statement.expression) {
      const returnText = statement.expression.getText(sourceFile);
      if (!returnText.includes('obj.__typename') && !returnText.includes('throw new Error')) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Analyze return statement implementations
 * @param expression - The return expression
 * @param sourceFile - The source file
 * @returns Whether the implementation is custom
 */
function analyzeReturnStatementImplementation(expression: ts.Expression, sourceFile: ts.SourceFile): boolean {
  const returnText = expression.getText(sourceFile).trim();

  // Skip placeholder returns
  if (returnText === 'null' ||
      returnText === 'undefined' ||
      returnText === '{}' ||
      returnText === '[]' ||
      returnText.includes('TODO') ||
      returnText.includes('throw new Error')) {
    return false;
  }

  // Check for actual implementation patterns
  if (ts.isAwaitExpression(expression)) {
    return true; // await calls are real implementations
  }

  if (ts.isCallExpression(expression)) {
    return true; // function calls are real implementations
  }

  if (ts.isPropertyAccessExpression(expression)) {
    // Property access like obj.someProperty or service.method()
    return true;
  }

  return false;
}

/**
 * Check if a statement is a template/placeholder statement
 * @param statement - The AST statement node
 * @param sourceFile - The source file
 * @returns Whether the statement is a template placeholder
 */
function isTemplateStatement(statement: ts.Statement, sourceFile: ts.SourceFile): boolean {
  const text = statement.getText(sourceFile);

  // Check for common template patterns
  if (text.includes('TODO') ||
      text.includes('throw new Error') ||
      text.includes('console.error') ||
      text.trim().startsWith('//') ||
      text.trim().startsWith('/*')) {
    return true;
  }

  return false;
}

/**
 * Fallback regex-based function detection for when AST parsing fails
 * @param content - The file content
 * @returns Function details or null
 */
function findExportedFunctionWithRegex(content: string): {
  signature: string;
  body: string;
  isDefault: boolean;
  hasCustomImplementation: boolean;
} | null {
  // Check for __resolveType function (interface and union resolvers)
  if (content.includes('export default function __resolveType')) {
    const resolveTypeMatch = content.match(/export default function __resolveType\([^)]*\)[^{]*{([\s\S]*?)return obj\.__typename[\s\S]*?}\s*$/);

    if (resolveTypeMatch) {
      const body = resolveTypeMatch[1].trim();
      const hasCustomImpl = isCustomImplementation(body, content);

      return {
        signature: 'export default function __resolveType',
        body,
        isDefault: true,
        hasCustomImplementation: hasCustomImpl
      };
    }
  }

  // Handle modern resolver format - const arrow functions that are exported
  const modernResolverPattern = /const\s+([a-zA-Z0-9_]+)\s*=\s*async\s*\([^)]*\)[^{]*=>\s*{([\s\S]*?)}\s*[\s\S]*?export\s*{[^}]*\1[^}]*}/;
  const modernResolverMatch = content.match(modernResolverPattern);
  if (modernResolverMatch) {
    const functionName = modernResolverMatch[1];
    const body = modernResolverMatch[2].trim();
    const hasCustomImpl = isCustomImplementation(body, content);

    const signaturePattern = new RegExp(`const\\s+${functionName}\\s*=\\s*async\\s*\\([^)]*\\)[^{]*(?=\\s*=>\\s*{)`);
    const signatureMatch = content.match(signaturePattern);
    const signature = signatureMatch ? signatureMatch[0].trim() : '';

    return {
      signature,
      body,
      isDefault: false,
      hasCustomImplementation: hasCustomImpl
    };
  }

  // Try pattern for old-style try-catch resolvers
  const exportedFunctionMatch = content.match(/export\s+const\s+([a-zA-Z0-9_]+)[\s\S]*?=\s*async[\s\S]*?try\s*{([\s\S]*?)}\s*catch/);
  if (exportedFunctionMatch) {
    const body = exportedFunctionMatch[2].trim();
    const hasCustomImpl = isCustomImplementation(body, content);

    const signatureMatch = content.match(/export\s+const[\s\S]*?=\s*async[\s\S]*?(?=\{\s*try)/s);
    const signature = signatureMatch ? signatureMatch[0].trim() : '';

    return {
      signature,
      body,
      isDefault: false,
      hasCustomImplementation: hasCustomImpl
    };
  }

  return null;
} 