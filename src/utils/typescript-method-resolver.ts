import * as fs from 'fs-extra';
import * as path from 'path';
import { parseSync as swcParseSync } from '@swc/core';
import { UI } from './ui';

/**
 * Information about a resolved TypeScript method
 */
export interface ResolvedMethod {
  /** File path where the method was found */
  filePath: string;
  /** Method/function name */
  methodName: string;
  /** Line number where the method is defined */
  lineNumber: number;
  /** Whether it's an exported function */
  isExported: boolean;
  /** Whether it's a default export */
  isDefaultExport: boolean;
  /** Whether it's a class method */
  isClassMethod: boolean;
  /** Class name if it's a class method */
  className?: string;
  /** Function signature */
  signature: string;
  /** Confidence score (0-1) for the match */
  confidence: number;
}

/**
 * Options for method resolution
 */
export interface MethodResolutionOptions {
  /** Enable verbose logging */
  verbose?: boolean;
  /** Minimum confidence score to consider a match */
  minConfidence?: number;
  /** Maximum number of results to return */
  maxResults?: number;
  /** Enable fuzzy matching */
  enableFuzzyMatching?: boolean;
}

/**
 * TypeScript method resolver for finding function/method definitions in codebase
 */
export class TypeScriptMethodResolver {
  private options: MethodResolutionOptions;

  constructor(options: MethodResolutionOptions = {}) {
    this.options = {
      verbose: false,
      minConfidence: 0.7,
      maxResults: 10,
      enableFuzzyMatching: true,
      ...options
    };
  }

  /**
   * Resolve a method call expression to find the actual method definition
   * @param callExpression The method call expression (e.g., "UserService.findById(args.id)")
   * @param typescriptFiles List of TypeScript files to search
   * @returns Array of resolved methods sorted by confidence
   */
  public async resolveMethodCall(
    callExpression: string,
    typescriptFiles: string[]
  ): Promise<ResolvedMethod[]> {
    const methodInfo = this.parseMethodCall(callExpression);
    if (!methodInfo) {
      return [];
    }

    if (this.options.verbose) {
      UI.info(`🔍 Resolving method call: ${callExpression}`);
      if (methodInfo.isClassMethod) {
        UI.info(`   → Class: ${methodInfo.className}, Method: ${methodInfo.methodName}`);
      } else {
        UI.info(`   → Function: ${methodInfo.methodName}`);
      }
    }

    const results: ResolvedMethod[] = [];

    // Search for the method in all TypeScript files
    for (const filePath of typescriptFiles) {
      try {
        const fileResults = await this.searchMethodInFile(methodInfo, filePath, callExpression);
        results.push(...fileResults);
      } catch (error) {
        if (this.options.verbose) {
          UI.warning(`⚠️ Error searching in ${filePath}: ${error}`);
        }
      }
    }

    // Sort by confidence and limit results
    const sortedResults = results
      .filter(result => result.confidence >= this.options.minConfidence!)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, this.options.maxResults);

    if (this.options.verbose && sortedResults.length > 0) {
      UI.info(`✅ Found ${sortedResults.length} potential matches for ${methodInfo.methodName}`);
      sortedResults.forEach((result, index) => {
        const classInfo = result.className ? ` in ${result.className}` : '';
        UI.info(`   ${index + 1}. ${path.relative(process.cwd(), result.filePath)}:${result.lineNumber}${classInfo} (confidence: ${Math.round(result.confidence * 100)}%)`);
      });
    }

    return sortedResults;
  }

  /**
   * Extract method name and class context from a call expression
   */
  private extractMethodName(callExpression: string): string | null {
    const methodInfo = this.parseMethodCall(callExpression);
    return methodInfo ? methodInfo.methodName : null;
  }

  /**
   * Parse method call expression to extract class and method information
   */
  private parseMethodCall(callExpression: string): {
    className?: string;
    methodName: string;
    isClassMethod: boolean;
    fullCall: string;
  } | null {
    // Remove common patterns and extract the actual method call
    const cleanExpression = callExpression
      .replace(/\s+/g, ' ')
      .replace(/await\s+/, '')
      .replace(/\s*as\s+\w+.*$/, '') // Remove type casting
      .trim();

    // Handle different call patterns:
    // 1. Static method call: ClassName.methodName(args)
    // 2. Simple function call: methodName(args)
    // 3. Object method call: obj.methodName(args)
    // 4. Chained method call: obj.prop.methodName(args)

    // Pattern for class/object method calls: SomeClass.methodName(...)
    const classMethodPattern = /^([A-Za-z_$][A-Za-z0-9_$]*)\.([A-Za-z_$][A-Za-z0-9_$]*)\s*\(/;
    const classMethodMatch = cleanExpression.match(classMethodPattern);

    if (classMethodMatch) {
      return {
        className: classMethodMatch[1],
        methodName: classMethodMatch[2],
        isClassMethod: true,
        fullCall: cleanExpression
      };
    }

    // Pattern for simple function calls: methodName(...)
    const simpleFunctionPattern = /^([A-Za-z_$][A-Za-z0-9_$]*)\s*\(/;
    const simpleFunctionMatch = cleanExpression.match(simpleFunctionPattern);

    if (simpleFunctionMatch) {
      return {
        methodName: simpleFunctionMatch[1],
        isClassMethod: false,
        fullCall: cleanExpression
      };
    }

    return null;
  }

  /**
   * Search for a method in a specific TypeScript file
   */
  private async searchMethodInFile(
    methodInfo: { className?: string; methodName: string; isClassMethod: boolean; fullCall: string },
    filePath: string,
    originalExpression: string
  ): Promise<ResolvedMethod[]> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const results: ResolvedMethod[] = [];

      // Try AST parsing first for better accuracy
      try {
        const astResults = await this.searchWithAST(methodInfo, filePath, content, originalExpression);
        results.push(...astResults);
      } catch (astError) {
        if (this.options.verbose) {
          UI.warning(`AST parsing failed for ${filePath}, falling back to regex`);
        }
      }

      // Fallback to regex-based search if AST parsing fails or finds nothing
      if (results.length === 0) {
        const regexResults = this.searchWithRegex(methodInfo, filePath, content, originalExpression);
        results.push(...regexResults);
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to search in ${filePath}: ${error}`);
    }
  }

  /**
   * Search using AST parsing for better accuracy
   */
  private async searchWithAST(
    methodInfo: { className?: string; methodName: string; isClassMethod: boolean; fullCall: string },
    filePath: string,
    content: string,
    originalExpression: string
  ): Promise<ResolvedMethod[]> {
    const results: ResolvedMethod[] = [];

    try {
      const ast = swcParseSync(content, {
        syntax: 'typescript',
        tsx: filePath.endsWith('.tsx'),
        decorators: true,
        dynamicImport: true,
      });

      this.traverseASTForMethods(ast, content, filePath, methodInfo, originalExpression, results);
    } catch (error) {
      throw new Error(`AST parsing failed: ${error}`);
    }

    return results;
  }

  /**
   * Traverse AST to find method definitions
   */
  private traverseASTForMethods(
    node: any,
    content: string,
    filePath: string,
    methodInfo: { className?: string; methodName: string; isClassMethod: boolean; fullCall: string },
    originalExpression: string,
    results: ResolvedMethod[]
  ): void {
    if (!node || typeof node !== 'object') return;

    // Check for function declarations (standalone functions)
    if (node.type === 'FunctionDeclaration' && node.identifier?.value === methodInfo.methodName) {
      // Only match if we're looking for a standalone function (not a class method)
      if (!methodInfo.isClassMethod) {
        const lineNumber = this.getLineNumber(content, node.span.start);
        const confidence = this.calculateConfidence(
          methodInfo.methodName,
          methodInfo.methodName,
          originalExpression,
          false
        );

        results.push({
          filePath,
          methodName: node.identifier.value,
          lineNumber,
          isExported: this.isNodeExported(node),
          isDefaultExport: false,
          isClassMethod: false,
          signature: this.extractFunctionSignature(content, node.span.start, node.span.end),
          confidence
        });
      }
    }

    // Check for variable declarations with arrow functions (standalone functions)
    if (node.type === 'VariableDeclaration' && !methodInfo.isClassMethod) {
      for (const declarator of node.declarations || []) {
        if (declarator.id?.value === methodInfo.methodName &&
            (declarator.init?.type === 'ArrowFunctionExpression' ||
             declarator.init?.type === 'FunctionExpression')) {
          const lineNumber = this.getLineNumber(content, declarator.span.start);
          const confidence = this.calculateConfidence(
            methodInfo.methodName,
            methodInfo.methodName,
            originalExpression,
            false
          );

          results.push({
            filePath,
            methodName: declarator.id.value,
            lineNumber,
            isExported: this.isNodeExported(node),
            isDefaultExport: false,
            isClassMethod: false,
            signature: this.extractFunctionSignature(content, declarator.span.start, declarator.span.end),
            confidence
          });
        }
      }
    }

    // Check for class methods
    if (node.type === 'ClassDeclaration') {
      const className = node.identifier?.value;

      // If we're looking for a class method, check if the class name matches
      if (methodInfo.isClassMethod && methodInfo.className && className !== methodInfo.className) {
        // Skip this class if it doesn't match the expected class name
      } else {
        for (const member of node.body || []) {
          if (member.type === 'MethodDefinition' && member.key?.value === methodInfo.methodName) {
            const lineNumber = this.getLineNumber(content, member.span.start);

            // Calculate confidence based on class name match
            let confidence = this.calculateConfidence(
              methodInfo.methodName,
              methodInfo.methodName,
              originalExpression,
              true,
              className
            );

            // Boost confidence significantly if class name matches exactly
            if (methodInfo.isClassMethod && methodInfo.className === className) {
              confidence = Math.min(confidence + 0.4, 1.0); // High confidence for exact class match
            }

            results.push({
              filePath,
              methodName: member.key.value,
              lineNumber,
              isExported: this.isNodeExported(node),
              isDefaultExport: false,
              isClassMethod: true,
              className,
              signature: this.extractFunctionSignature(content, member.span.start, member.span.end),
              confidence
            });
          }
        }
      }
    }

    // Recursively traverse child nodes
    for (const key in node) {
      if (key !== 'parent' && node[key] && typeof node[key] === 'object') {
        if (Array.isArray(node[key])) {
          for (const child of node[key]) {
            this.traverseASTForMethods(child, content, filePath, methodInfo, originalExpression, results);
          }
        } else {
          this.traverseASTForMethods(node[key], content, filePath, methodInfo, originalExpression, results);
        }
      }
    }
  }

  /**
   * Search using regex patterns as fallback
   */
  private searchWithRegex(
    methodInfo: { className?: string; methodName: string; isClassMethod: boolean; fullCall: string },
    filePath: string,
    content: string,
    originalExpression: string
  ): ResolvedMethod[] {
    const results: ResolvedMethod[] = [];
    const lines = content.split('\n');

    if (methodInfo.isClassMethod && methodInfo.className) {
      // Search for class methods with specific class name
      return this.searchClassMethodWithRegex(
        { ...methodInfo, className: methodInfo.className },
        filePath,
        content,
        lines,
        originalExpression
      );
    } else {
      // Search for standalone functions
      return this.searchStandaloneFunctionWithRegex(methodInfo, filePath, content, lines, originalExpression);
    }
  }

  /**
   * Search for class methods using regex
   */
  private searchClassMethodWithRegex(
    methodInfo: { className: string; methodName: string; isClassMethod: boolean; fullCall: string },
    filePath: string,
    content: string,
    lines: string[],
    originalExpression: string
  ): ResolvedMethod[] {
    const results: ResolvedMethod[] = [];

    // Find the class declaration first
    const classPattern = new RegExp(`^\\s*(?:export\\s+)?class\\s+${methodInfo.className}\\b`, 'm');
    const classMatch = content.match(classPattern);

    if (!classMatch) {
      return results; // Class not found
    }

    // Find method within the class
    const methodPattern = new RegExp(
      `^\\s*(?:public|private|protected|static)?\\s*${methodInfo.methodName}\\s*\\(`,
      'gm'
    );

    const matches = content.matchAll(methodPattern);
    for (const match of matches) {
      const matchIndex = match.index!;
      const lineNumber = content.substring(0, matchIndex).split('\n').length;
      const line = lines[lineNumber - 1];

      // Check if this method is within the correct class by looking at context
      const beforeMatch = content.substring(0, matchIndex);
      const lastClassMatch = beforeMatch.match(new RegExp(`class\\s+(\\w+)`, 'g'));
      const currentClass = lastClassMatch ? lastClassMatch[lastClassMatch.length - 1].split(' ')[1] : null;

      if (currentClass === methodInfo.className) {
        const confidence = this.calculateConfidence(
          methodInfo.methodName,
          methodInfo.methodName,
          originalExpression,
          true,
          methodInfo.className
        ) + 0.3; // Boost for exact class match

        results.push({
          filePath,
          methodName: methodInfo.methodName,
          lineNumber,
          isExported: line.includes('export'),
          isDefaultExport: line.includes('export default'),
          isClassMethod: true,
          className: methodInfo.className,
          signature: line.trim(),
          confidence: Math.min(confidence, 1.0)
        });
      }
    }

    return results;
  }

  /**
   * Search for standalone functions using regex
   */
  private searchStandaloneFunctionWithRegex(
    methodInfo: { methodName: string; isClassMethod: boolean; fullCall: string },
    filePath: string,
    content: string,
    lines: string[],
    originalExpression: string
  ): ResolvedMethod[] {
    const results: ResolvedMethod[] = [];

    // Regex patterns for different function/method declarations
    const patterns = [
      // Function declarations: function methodName(...) or export function methodName(...)
      new RegExp(`^\\s*(?:export\\s+)?function\\s+${methodInfo.methodName}\\s*\\(`, 'm'),
      // Arrow function assignments: const methodName = (...) => or export const methodName = (...) =>
      new RegExp(`^\\s*(?:export\\s+)?const\\s+${methodInfo.methodName}\\s*=\\s*(?:async\\s+)?\\([^)]*\\)\\s*=>`, 'm'),
      // Object method shorthand: methodName(...) {
      new RegExp(`^\\s*${methodInfo.methodName}\\s*\\([^)]*\\)\\s*{`, 'm'),
    ];

    for (const pattern of patterns) {
      const matches = content.matchAll(new RegExp(pattern.source, 'gm'));

      for (const match of matches) {
        const matchIndex = match.index!;
        const lineNumber = content.substring(0, matchIndex).split('\n').length;
        const line = lines[lineNumber - 1];

        const confidence = this.calculateConfidence(
          methodInfo.methodName,
          methodInfo.methodName,
          originalExpression,
          false
        );

        results.push({
          filePath,
          methodName: methodInfo.methodName,
          lineNumber,
          isExported: line.includes('export'),
          isDefaultExport: line.includes('export default'),
          isClassMethod: false,
          signature: line.trim(),
          confidence
        });
      }
    }

    return results;
  }

  /**
   * Calculate confidence score for a method match
   */
  private calculateConfidence(
    foundMethodName: string,
    searchMethodName: string,
    originalExpression: string,
    isClassMethod: boolean,
    className?: string
  ): number {
    let confidence = 0;

    // Exact name match
    if (foundMethodName === searchMethodName) {
      confidence += 0.5;
    } else if (this.options.enableFuzzyMatching) {
      // Fuzzy matching for similar names
      const similarity = this.calculateStringSimilarity(foundMethodName, searchMethodName);
      confidence += similarity * 0.3;
    }

    // Bonus for class method if original expression suggests it
    if (isClassMethod && originalExpression.includes('.')) {
      confidence += 0.2;
      
      // Extra bonus if class name matches
      if (className && originalExpression.includes(className)) {
        confidence += 0.2;
      }
    }

    // Bonus for exported functions (more likely to be used externally)
    confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    const distance = matrix[str2.length][str1.length];
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - distance) / maxLength;
  }

  /**
   * Get line number from character position
   */
  private getLineNumber(content: string, position: number): number {
    return content.substring(0, position).split('\n').length;
  }

  /**
   * Check if a node is exported
   */
  private isNodeExported(node: any): boolean {
    return node.decorators?.some((decorator: any) => 
      decorator.type === 'Decorator' && decorator.expression?.value === 'export'
    ) || false;
  }

  /**
   * Extract function signature from content
   */
  private extractFunctionSignature(content: string, start: number, end: number): string {
    const signature = content.substring(start, end);
    const lines = signature.split('\n');
    return lines[0].trim();
  }
}
