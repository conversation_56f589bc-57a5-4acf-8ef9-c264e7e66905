import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * Configuration options for backup naming
 */
export interface BackupNamingOptions {
  /** Base directory for all backups (default: .backup) */
  baseBackupDir?: string;
  /** Maximum length for extracted names (default: 50) */
  maxNameLength?: number;
  /** Whether to include timestamp in directory name (default: true) */
  includeTimestamp?: boolean;
  /** Custom separator for name components (default: _) */
  separator?: string;
  /** Whether to create subdirectories for organization (default: true) */
  useSubdirectories?: boolean;
  /** Custom naming template (default: {timestamp}_{codebase}_{schema}) */
  namingTemplate?: string;
  /** Whether to compress old backups (default: false) */
  compressOldBackups?: boolean;
  /** Maximum number of backups to keep (default: unlimited) */
  maxBackupsToKeep?: number;
  /** Whether to allow Unicode characters in names (default: false) */
  allowUnicodeNames?: boolean;
  /** Custom prefix for backup directories (default: none) */
  customPrefix?: string;
  /** Whether to create backup metadata files (default: true) */
  createMetadata?: boolean;
  /** Session ID for grouping related backups (default: none) */
  sessionId?: string;
  /** Whether to include session ID in directory name (default: true if sessionId provided) */
  includeSessionInName?: boolean;
}

/**
 * Backup metadata information
 */
export interface BackupMetadata {
  /** When the backup was created */
  createdAt: string;
  /** Version of the backup system */
  version: string;
  /** Original paths that were backed up */
  originalPaths: {
    codebasePath: string;
    schemaPath: string;
  };
  /** Migration options used */
  migrationOptions?: any;
  /** File count and size statistics */
  statistics: {
    fileCount: number;
    totalSize: number;
    schemaFiles: number;
    codebaseFiles: number;
  };
}

/**
 * Information about a backup directory structure
 */
export interface BackupDirectoryInfo {
  /** Full path to the backup directory */
  backupDir: string;
  /** Extracted codebase name */
  codebaseName: string;
  /** Extracted schema name */
  schemaName: string;
  /** Timestamp string used in naming */
  timestamp: string;
  /** Whether this directory already exists */
  exists: boolean;
}

/**
 * Utility class for generating consistent backup directory names and structures
 */
export class BackupNamingUtils {
  private static readonly DEFAULT_OPTIONS: Required<BackupNamingOptions> = {
    baseBackupDir: '.backup',
    maxNameLength: 50,
    includeTimestamp: true,
    separator: '_',
    useSubdirectories: true,
    namingTemplate: '{timestamp}_{codebase}_{schema}',
    compressOldBackups: false,
    maxBackupsToKeep: 0, // 0 means unlimited
    allowUnicodeNames: false,
    customPrefix: '',
    createMetadata: true,
    sessionId: '',
    includeSessionInName: true
  };

  /**
   * Extract a clean name from a file path
   * @param filePath Path to extract name from
   * @returns Sanitized name suitable for directory naming
   */
  static extractNameFromPath(filePath: string): string {
    // Get the base name of the path (last directory or file name)
    const baseName = path.basename(filePath);
    
    // Remove common extensions and sanitize
    const cleanName = baseName
      .replace(/\.(ts|tsx|js|jsx|gql|graphql)$/, '') // Remove file extensions
      .replace(/[^a-zA-Z0-9\-_]/g, '-') // Replace special chars with hyphens
      .replace(/-+/g, '-') // Collapse multiple hyphens
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .toLowerCase();

    return cleanName || 'unnamed';
  }

  /**
   * Sanitize a name for use in file/directory paths
   * @param name Name to sanitize
   * @param maxLength Maximum length (default: 50)
   * @returns Sanitized name
   */
  static sanitizeName(name: string, maxLength: number = 50): string {
    const sanitized = name
      .replace(/[^a-zA-Z0-9\-_]/g, '-') // Replace special chars
      .replace(/-+/g, '-') // Collapse hyphens
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .toLowerCase();

    // Truncate if too long, but try to keep meaningful parts
    if (sanitized.length > maxLength) {
      return sanitized.substring(0, maxLength).replace(/-$/, '');
    }

    return sanitized || 'unnamed';
  }

  /**
   * Generate a timestamp string suitable for directory naming
   * @returns Timestamp string in format YYYY-MM-DD_HH-MM-SS
   */
  static generateTimestamp(): string {
    const now = new Date();
    return now.toISOString()
      .replace(/T/, '_')
      .replace(/:/g, '-')
      .replace(/\..+/, ''); // Remove milliseconds and timezone
  }

  /**
   * Create a backup directory structure with organized naming
   * @param codebasePath Path to the codebase directory
   * @param schemaPath Path to the schema directory
   * @param options Backup naming options
   * @returns Information about the created backup directory
   */
  static async createBackupDirectory(
    codebasePath: string,
    schemaPath: string,
    options: BackupNamingOptions = {}
  ): Promise<BackupDirectoryInfo> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    // Extract and sanitize names
    const codebaseName = this.sanitizeName(
      this.extractNameFromPath(codebasePath),
      opts.maxNameLength
    );
    const schemaName = this.sanitizeName(
      this.extractNameFromPath(schemaPath),
      opts.maxNameLength
    );
    
    // Generate timestamp
    const timestamp = this.generateTimestamp();
    
    // Build directory name using template
    const templateVariables = {
      timestamp: opts.includeTimestamp ? timestamp : '',
      codebase: codebaseName,
      schema: schemaName,
      prefix: opts.customPrefix || '',
      session: (opts.sessionId && opts.includeSessionInName) ? opts.sessionId : ''
    };

    const dirName = this.processNamingTemplate(opts.namingTemplate, templateVariables);
    
    // Create full backup directory path
    const backupDir = path.resolve(process.cwd(), opts.baseBackupDir, dirName);
    
    // Check if directory already exists
    const exists = await fs.pathExists(backupDir);
    
    // Create the directory structure
    await fs.ensureDir(backupDir);
    
    if (opts.useSubdirectories) {
      // Create organized subdirectories
      await fs.ensureDir(path.join(backupDir, 'schema'));
      await fs.ensureDir(path.join(backupDir, 'codebase'));

      // Create additional organizational directories
      await fs.ensureDir(path.join(backupDir, 'metadata'));
      await fs.ensureDir(path.join(backupDir, 'logs'));
    }
    
    return {
      backupDir,
      codebaseName,
      schemaName,
      timestamp,
      exists
    };
  }

  /**
   * Handle naming collisions by appending a counter
   * @param basePath Base path that might have collisions
   * @param maxAttempts Maximum number of attempts to find unique name
   * @returns Unique path
   */
  static async resolveNamingCollision(
    basePath: string,
    maxAttempts: number = 100
  ): Promise<string> {
    let attempt = 0;
    let currentPath = basePath;

    while (await fs.pathExists(currentPath) && attempt < maxAttempts) {
      attempt++;
      const dir = path.dirname(basePath);
      const base = path.basename(basePath);
      currentPath = path.join(dir, `${base}-${attempt}`);
    }

    if (attempt >= maxAttempts) {
      throw new Error(`Could not resolve naming collision for ${basePath} after ${maxAttempts} attempts`);
    }

    return currentPath;
  }

  /**
   * Advanced path sanitization with platform-specific considerations
   * @param inputPath Path to sanitize
   * @param options Sanitization options
   * @returns Sanitized path safe for all platforms
   */
  static sanitizePath(
    inputPath: string,
    options: {
      maxLength?: number;
      preserveExtension?: boolean;
      allowUnicode?: boolean;
    } = {}
  ): string {
    const { maxLength = 255, preserveExtension = true, allowUnicode = false } = options;

    // Reserved names on Windows
    const reservedNames = new Set([
      'CON', 'PRN', 'AUX', 'NUL',
      'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
      'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]);

    let sanitized = inputPath;

    // Extract extension if preserving
    let extension = '';
    if (preserveExtension) {
      const extMatch = sanitized.match(/(\.[^.]+)$/);
      if (extMatch) {
        extension = extMatch[1];
        sanitized = sanitized.replace(/\.[^.]+$/, '');
      }
    }

    // Remove or replace problematic characters
    if (allowUnicode) {
      // Only replace filesystem-unsafe characters
      sanitized = sanitized.replace(/[<>:"/\\|?*\x00-\x1f]/g, '-');
    } else {
      // Replace all non-ASCII characters
      sanitized = sanitized.replace(/[^a-zA-Z0-9\-_.]/g, '-');
    }

    // Collapse multiple separators
    sanitized = sanitized.replace(/[-_.]+/g, '-');

    // Remove leading/trailing separators
    sanitized = sanitized.replace(/^[-_.]+|[-_.]+$/g, '');

    // Handle reserved names
    const upperSanitized = sanitized.toUpperCase();
    if (reservedNames.has(upperSanitized)) {
      sanitized = `${sanitized}_safe`;
    }

    // Ensure not empty
    if (!sanitized) {
      sanitized = 'unnamed';
    }

    // Handle length limits
    const maxContentLength = maxLength - extension.length;
    if (sanitized.length > maxContentLength) {
      sanitized = sanitized.substring(0, maxContentLength).replace(/-$/, '');
    }

    return sanitized + extension;
  }

  /**
   * Detect potential naming conflicts before they occur
   * @param targetDir Directory to check for conflicts
   * @param proposedName Proposed name to check
   * @returns Conflict information
   */
  static async detectNamingConflicts(
    targetDir: string,
    proposedName: string
  ): Promise<{
    hasConflict: boolean;
    conflictingPaths: string[];
    suggestedAlternatives: string[];
  }> {
    const conflictingPaths: string[] = [];
    const suggestedAlternatives: string[] = [];

    if (!await fs.pathExists(targetDir)) {
      return { hasConflict: false, conflictingPaths, suggestedAlternatives };
    }

    const entries = await fs.readdir(targetDir);
    const proposedLower = proposedName.toLowerCase();

    // Check for exact and case-insensitive conflicts
    for (const entry of entries) {
      if (entry.toLowerCase() === proposedLower) {
        conflictingPaths.push(path.join(targetDir, entry));
      }
    }

    // Generate alternatives if conflicts exist
    if (conflictingPaths.length > 0) {
      for (let i = 1; i <= 5; i++) {
        const alternative = `${proposedName}-${i}`;
        if (!entries.some(entry => entry.toLowerCase() === alternative.toLowerCase())) {
          suggestedAlternatives.push(alternative);
        }
      }

      // Add timestamp-based alternative
      const timestamp = this.generateTimestamp().split('_')[1]; // Just time part
      const timestampAlternative = `${proposedName}-${timestamp}`;
      if (!entries.some(entry => entry.toLowerCase() === timestampAlternative.toLowerCase())) {
        suggestedAlternatives.push(timestampAlternative);
      }
    }

    return {
      hasConflict: conflictingPaths.length > 0,
      conflictingPaths,
      suggestedAlternatives
    };
  }

  /**
   * Generate a backup file name for individual files
   * @param originalPath Original file path
   * @param backupDir Backup directory
   * @param options Naming options
   * @returns Full path for backup file
   */
  static generateBackupFilePath(
    originalPath: string,
    backupDir: string,
    options: BackupNamingOptions = {}
  ): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    // Get relative path from cwd to maintain structure
    const relativePath = path.relative(process.cwd(), originalPath);
    const safeRelativePath = this.sanitizePath(relativePath, {
      maxLength: 200,
      preserveExtension: false,
      allowUnicode: opts.allowUnicodeNames
    });

    // Generate timestamp for this specific file
    const timestamp = this.generateTimestamp();

    // Use template for file naming if provided
    const fileTemplate = opts.namingTemplate || '{timestamp}_{file}';
    const templateVariables = {
      timestamp,
      file: safeRelativePath.replace(/[/\\]/g, opts.separator),
      prefix: opts.customPrefix || ''
    };

    const backupFileName = this.processNamingTemplate(fileTemplate, templateVariables) + '.backup';

    return path.join(backupDir, backupFileName);
  }

  /**
   * Generate a structured backup path with better organization
   * @param originalPath Original file path
   * @param backupDir Backup directory
   * @param options Advanced naming options
   * @returns Organized backup file path
   */
  static generateStructuredBackupPath(
    originalPath: string,
    backupDir: string,
    options: BackupNamingOptions & {
      organizeByDate?: boolean;
      organizeByType?: boolean;
      preserveStructure?: boolean;
    } = {}
  ): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    let targetDir = backupDir;

    // Organize by date if requested
    if (options.organizeByDate) {
      const now = new Date();
      const dateDir = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      targetDir = path.join(targetDir, 'by-date', dateDir);
    }

    // Organize by file type if requested
    if (options.organizeByType) {
      const ext = path.extname(originalPath).toLowerCase();
      let typeDir = 'other';

      if (['.ts', '.tsx'].includes(ext)) {
        typeDir = 'typescript';
      } else if (['.js', '.jsx'].includes(ext)) {
        typeDir = 'javascript';
      } else if (['.gql', '.graphql'].includes(ext)) {
        typeDir = 'graphql';
      } else if (['.json', '.yaml', '.yml'].includes(ext)) {
        typeDir = 'config';
      }

      targetDir = path.join(targetDir, 'by-type', typeDir);
    }

    // Preserve directory structure if requested
    if (options.preserveStructure) {
      const relativePath = path.relative(process.cwd(), originalPath);
      const dirPath = path.dirname(relativePath);
      if (dirPath && dirPath !== '.') {
        targetDir = path.join(targetDir, 'structure', dirPath);
      }
    }

    // Generate the final file name
    const fileName = path.basename(originalPath);
    const timestamp = this.generateTimestamp();
    const backupFileName = `${fileName}.${timestamp}.backup`;

    return path.join(targetDir, backupFileName);
  }

  /**
   * List all backup directories in the base backup directory
   * @param baseBackupDir Base backup directory (default: .backup)
   * @returns Array of backup directory information
   */
  static async listBackupDirectories(
    baseBackupDir: string = '.backup'
  ): Promise<BackupDirectoryInfo[]> {
    const backupBasePath = path.resolve(process.cwd(), baseBackupDir);
    
    if (!await fs.pathExists(backupBasePath)) {
      return [];
    }
    
    const entries = await fs.readdir(backupBasePath, { withFileTypes: true });
    const backupDirs: BackupDirectoryInfo[] = [];
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const dirPath = path.join(backupBasePath, entry.name);
        
        // Try to parse the directory name to extract components
        const parts = entry.name.split('_');
        if (parts.length >= 3) {
          const [timestamp, codebaseName, schemaName] = parts;
          
          backupDirs.push({
            backupDir: dirPath,
            codebaseName,
            schemaName,
            timestamp,
            exists: true
          });
        }
      }
    }
    
    // Sort by timestamp (newest first)
    return backupDirs.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
  }

  /**
   * Process naming template with variables
   * @param template Template string with placeholders
   * @param variables Variables to substitute
   * @returns Processed template string
   */
  static processNamingTemplate(
    template: string,
    variables: {
      timestamp?: string;
      codebase?: string;
      schema?: string;
      prefix?: string;
    }
  ): string {
    let processed = template;

    // Replace template variables
    Object.entries(variables).forEach(([key, value]) => {
      if (value) {
        const placeholder = `{${key}}`;
        processed = processed.replace(new RegExp(placeholder, 'g'), value);
      }
    });

    // Clean up any remaining placeholders
    processed = processed.replace(/\{[^}]+\}/g, '');

    // Clean up multiple separators
    processed = processed.replace(/[_-]+/g, '_');
    processed = processed.replace(/^[_-]+|[_-]+$/g, '');

    return processed || 'backup';
  }

  /**
   * Create backup metadata file
   * @param backupDir Backup directory
   * @param metadata Metadata to save
   */
  static async createBackupMetadata(
    backupDir: string,
    metadata: BackupMetadata
  ): Promise<void> {
    const metadataPath = path.join(backupDir, 'backup-metadata.json');
    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
  }

  /**
   * Read backup metadata file
   * @param backupDir Backup directory
   * @returns Metadata or null if not found
   */
  static async readBackupMetadata(
    backupDir: string
  ): Promise<BackupMetadata | null> {
    const metadataPath = path.join(backupDir, 'backup-metadata.json');

    if (!await fs.pathExists(metadataPath)) {
      return null;
    }

    try {
      const content = await fs.readFile(metadataPath, 'utf8');
      return JSON.parse(content) as BackupMetadata;
    } catch (error) {
      console.warn(`Failed to read backup metadata from ${metadataPath}:`, error);
      return null;
    }
  }

  /**
   * Clean up old backups based on configuration
   * @param options Backup naming options
   */
  static async cleanupOldBackups(options: BackupNamingOptions = {}): Promise<void> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    if (opts.maxBackupsToKeep <= 0) {
      return; // No cleanup needed
    }

    const backupDirs = await this.listBackupDirectories(opts.baseBackupDir);

    if (backupDirs.length <= opts.maxBackupsToKeep) {
      return; // Not enough backups to clean up
    }

    // Sort by timestamp (oldest first for deletion)
    const sortedDirs = backupDirs.sort((a, b) => a.timestamp.localeCompare(b.timestamp));
    const dirsToDelete = sortedDirs.slice(0, sortedDirs.length - opts.maxBackupsToKeep);

    for (const dirInfo of dirsToDelete) {
      try {
        if (opts.compressOldBackups) {
          // TODO: Implement compression before deletion
          console.log(`Compressing backup: ${dirInfo.backupDir}`);
        }

        await fs.remove(dirInfo.backupDir);
        console.log(`Cleaned up old backup: ${dirInfo.backupDir}`);
      } catch (error) {
        console.warn(`Failed to clean up backup ${dirInfo.backupDir}:`, error);
      }
    }
  }

  /**
   * Validate backup naming configuration
   * @param options Options to validate
   * @returns Validation result
   */
  static validateConfiguration(options: BackupNamingOptions): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate base backup directory
    if (options.baseBackupDir) {
      if (path.isAbsolute(options.baseBackupDir)) {
        warnings.push('Using absolute path for backup directory may cause portability issues');
      }

      if (options.baseBackupDir.includes('..')) {
        errors.push('Backup directory cannot contain parent directory references (..)');
      }
    }

    // Validate max name length
    if (options.maxNameLength !== undefined) {
      if (options.maxNameLength < 10) {
        errors.push('Maximum name length must be at least 10 characters');
      }
      if (options.maxNameLength > 255) {
        warnings.push('Very long name lengths may cause filesystem issues');
      }
    }

    // Validate separator
    if (options.separator) {
      const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
      if (invalidChars.test(options.separator)) {
        errors.push('Separator contains invalid filesystem characters');
      }
    }

    // Validate naming template
    if (options.namingTemplate) {
      const requiredPlaceholders = ['{timestamp}', '{codebase}', '{schema}'];
      const hasRequired = requiredPlaceholders.some(placeholder =>
        options.namingTemplate!.includes(placeholder)
      );

      if (!hasRequired) {
        warnings.push('Naming template should include at least one of: timestamp, codebase, or schema');
      }
    }

    // Validate max backups to keep
    if (options.maxBackupsToKeep !== undefined && options.maxBackupsToKeep < 0) {
      errors.push('Maximum backups to keep cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Create an advanced backup directory structure with detailed organization
   * @param backupDir Base backup directory
   * @param options Organization options
   */
  static async createAdvancedDirectoryStructure(
    backupDir: string,
    options: {
      includeMetadata?: boolean;
      includeLogs?: boolean;
      createDateStructure?: boolean;
      separateByFileType?: boolean;
    } = {}
  ): Promise<void> {
    const {
      includeMetadata = true,
      includeLogs = true,
      createDateStructure = false,
      separateByFileType = false
    } = options;

    // Create base structure
    await fs.ensureDir(backupDir);

    // Create main content directories
    await fs.ensureDir(path.join(backupDir, 'schema'));
    await fs.ensureDir(path.join(backupDir, 'codebase'));

    if (includeMetadata) {
      await fs.ensureDir(path.join(backupDir, 'metadata'));
    }

    if (includeLogs) {
      await fs.ensureDir(path.join(backupDir, 'logs'));
    }

    if (createDateStructure) {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      const dateDir = path.join(backupDir, 'by-date', `${year}`, `${month}`, `${day}`);
      await fs.ensureDir(dateDir);
    }

    if (separateByFileType) {
      // Create directories for different file types
      const fileTypeDirs = [
        'graphql-files',
        'typescript-files',
        'javascript-files',
        'config-files',
        'other-files'
      ];

      for (const typeDir of fileTypeDirs) {
        await fs.ensureDir(path.join(backupDir, 'by-type', typeDir));
      }
    }

    // Create a README file explaining the structure
    const readmeContent = `# Backup Directory Structure

This backup was created on ${new Date().toISOString()}

## Directory Structure:
- \`schema/\` - GraphQL schema files
- \`codebase/\` - TypeScript/JavaScript source files
${includeMetadata ? '- `metadata/` - Backup metadata and information\n' : ''}${includeLogs ? '- `logs/` - Migration and backup logs\n' : ''}${createDateStructure ? '- `by-date/` - Files organized by creation date\n' : ''}${separateByFileType ? '- `by-type/` - Files organized by file type\n' : ''}
## Restoration:
To restore from this backup, use the gql-generator restore command or manually copy files back to their original locations.

## Metadata:
Check the metadata.json file for detailed information about this backup.
`;

    await fs.writeFile(path.join(backupDir, 'README.md'), readmeContent);
  }

  /**
   * Get backup directory size and file count
   * @param backupDir Backup directory path
   * @returns Size and count information
   */
  static async getBackupDirectoryStats(backupDir: string): Promise<{
    totalSize: number;
    fileCount: number;
    directoryCount: number;
    largestFile: { path: string; size: number } | null;
  }> {
    let totalSize = 0;
    let fileCount = 0;
    let directoryCount = 0;
    let largestFile: { path: string; size: number } | null = null;

    if (!await fs.pathExists(backupDir)) {
      return { totalSize: 0, fileCount: 0, directoryCount: 0, largestFile: null };
    }

    const walkDir = async (dir: string): Promise<void> => {
      const entries = await fs.readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          directoryCount++;
          await walkDir(fullPath);
        } else if (entry.isFile()) {
          fileCount++;
          try {
            const stats = await fs.stat(fullPath);
            totalSize += stats.size;

            if (!largestFile || stats.size > largestFile.size) {
              largestFile = { path: fullPath, size: stats.size };
            }
          } catch (error) {
            // Ignore files that can't be read
          }
        }
      }
    };

    await walkDir(backupDir);

    return { totalSize, fileCount, directoryCount, largestFile };
  }

  /**
   * Generate a unique session ID for grouping related backups
   * @param prefix Optional prefix for the session ID
   * @returns Unique session ID
   */
  static generateSessionId(prefix?: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    const sessionId = `${timestamp}_${random}`;

    return prefix ? `${prefix}_${sessionId}` : sessionId;
  }

  /**
   * Create a session-aware backup directory
   * @param codebasePath Path to the codebase directory
   * @param schemaPath Path to the schema directory
   * @param sessionId Session ID for grouping
   * @param options Additional backup naming options
   * @returns Information about the created backup directory
   */
  static async createSessionBackupDirectory(
    codebasePath: string,
    schemaPath: string,
    sessionId: string,
    options: BackupNamingOptions = {}
  ): Promise<BackupDirectoryInfo> {
    const sessionOptions: BackupNamingOptions = {
      ...options,
      sessionId,
      includeSessionInName: true,
      namingTemplate: options.namingTemplate || '{timestamp}_{codebase}_{schema}_{session}'
    };

    return this.createBackupDirectory(codebasePath, schemaPath, sessionOptions);
  }

  /**
   * List all backups for a specific session
   * @param sessionId Session ID to search for
   * @param baseBackupDir Base backup directory (default: .backup)
   * @returns Array of backup directories for the session
   */
  static async listSessionBackups(
    sessionId: string,
    baseBackupDir: string = '.backup'
  ): Promise<BackupDirectoryInfo[]> {
    const allBackups = await this.listBackupDirectories(baseBackupDir);

    // Filter backups that contain the session ID
    return allBackups.filter(backup =>
      backup.backupDir.includes(sessionId) ||
      backup.timestamp.includes(sessionId)
    );
  }

  /**
   * Clean up all backups for a specific session
   * @param sessionId Session ID to clean up
   * @param baseBackupDir Base backup directory (default: .backup)
   * @returns Number of directories cleaned up
   */
  static async cleanupSessionBackups(
    sessionId: string,
    baseBackupDir: string = '.backup'
  ): Promise<number> {
    const sessionBackups = await this.listSessionBackups(sessionId, baseBackupDir);
    let cleanedCount = 0;

    for (const backup of sessionBackups) {
      try {
        await fs.remove(backup.backupDir);
        cleanedCount++;
        console.log(`Cleaned up session backup: ${backup.backupDir}`);
      } catch (error) {
        console.warn(`Failed to clean up session backup ${backup.backupDir}:`, error);
      }
    }

    return cleanedCount;
  }

  /**
   * Get session statistics
   * @param sessionId Session ID to analyze
   * @param baseBackupDir Base backup directory (default: .backup)
   * @returns Session statistics
   */
  static async getSessionStatistics(
    sessionId: string,
    baseBackupDir: string = '.backup'
  ): Promise<{
    backupCount: number;
    totalSize: number;
    oldestBackup: Date | null;
    newestBackup: Date | null;
  }> {
    const sessionBackups = await this.listSessionBackups(sessionId, baseBackupDir);

    if (sessionBackups.length === 0) {
      return {
        backupCount: 0,
        totalSize: 0,
        oldestBackup: null,
        newestBackup: null
      };
    }

    let totalSize = 0;
    const timestamps: Date[] = [];

    for (const backup of sessionBackups) {
      try {
        const stats = await this.getBackupDirectoryStats(backup.backupDir);
        totalSize += stats.totalSize;

        // Parse timestamp from backup directory name or use current time as fallback
        const timestampMatch = backup.timestamp.match(/(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})/);
        if (timestampMatch) {
          const date = new Date(timestampMatch[1].replace(/_/, 'T').replace(/-/g, ':'));
          timestamps.push(date);
        }
      } catch (error) {
        console.warn(`Failed to get stats for backup ${backup.backupDir}:`, error);
      }
    }

    timestamps.sort((a, b) => a.getTime() - b.getTime());

    return {
      backupCount: sessionBackups.length,
      totalSize,
      oldestBackup: timestamps.length > 0 ? timestamps[0] : null,
      newestBackup: timestamps.length > 0 ? timestamps[timestamps.length - 1] : null
    };
  }
}
