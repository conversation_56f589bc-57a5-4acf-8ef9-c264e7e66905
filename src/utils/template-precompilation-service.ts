import { TemplateCompilationCache, getGlobalTemplateCache } from './template-compilation-cache';
import { getGlobalWASMBridge } from './wasm-bridge';

/**
 * Template registry entry for pre-compilation
 */
export interface TemplateRegistryEntry {
  /** Unique identifier for the template */
  id: string;
  /** Template content string */
  content: string;
  /** Template description for logging */
  description?: string;
  /** Module path where the template is defined */
  modulePath?: string;
  /** Priority for pre-compilation (higher = compiled first) */
  priority?: number;
}

/**
 * Pre-compilation result for a single template
 */
export interface PrecompilationResult {
  /** Template ID */
  id: string;
  /** Whether pre-compilation was successful */
  success: boolean;
  /** Compilation time in milliseconds */
  compilationTime: number;
  /** Error message if compilation failed */
  error?: string;
  /** Template size in bytes */
  templateSize: number;
}

/**
 * Summary of pre-compilation process
 */
export interface PrecompilationSummary {
  /** Total number of templates processed */
  totalTemplates: number;
  /** Number of successfully compiled templates */
  successfulCompilations: number;
  /** Number of failed compilations */
  failedCompilations: number;
  /** Total pre-compilation time in milliseconds */
  totalTime: number;
  /** Average compilation time per template */
  averageCompilationTime: number;
  /** Individual template results */
  results: PrecompilationResult[];
  /** Any errors encountered during the process */
  errors: string[];
}

/**
 * Configuration options for template pre-compilation
 */
export interface PrecompilationOptions {
  /** Whether to enable detailed logging (default: false) */
  enableLogging?: boolean;
  /** Whether to continue on compilation errors (default: true) */
  continueOnError?: boolean;
  /** Maximum time to spend on pre-compilation in milliseconds (default: 30 seconds) */
  maxPrecompilationTime?: number;
  /** Whether to validate templates before compilation (default: true) */
  validateTemplates?: boolean;
}

/**
 * Service for pre-compiling templates during application startup
 * Warms the template cache with commonly used templates to eliminate runtime compilation overhead
 */
export class TemplatePrecompilationService {
  private templateRegistry: Map<string, TemplateRegistryEntry> = new Map();
  private cache: TemplateCompilationCache;
  private readonly options: Required<PrecompilationOptions>;
  private wasmBridge = getGlobalWASMBridge(); // Phase 3: WASM integration

  constructor(
    cache?: TemplateCompilationCache,
    options: PrecompilationOptions = {}
  ) {
    this.cache = cache || getGlobalTemplateCache();
    this.options = {
      enableLogging: false,
      continueOnError: true,
      maxPrecompilationTime: 30000, // 30 seconds
      validateTemplates: true,
      ...options,
    };

    if (this.options.enableLogging) {
      if (process.env.DEBUG_PARSER) {
        console.log('🚀 TemplatePrecompilationService initialized');
      }
    }
  }

  /**
   * Register a template for pre-compilation
   * @param entry Template registry entry
   */
  public registerTemplate(entry: TemplateRegistryEntry): void {
    if (this.templateRegistry.has(entry.id) && this.options.enableLogging) {
      if (process.env.DEBUG_PARSER) {
        console.warn(`⚠️  Template '${entry.id}' already registered, overwriting`);
      }
    }

    this.templateRegistry.set(entry.id, {
      priority: 0,
      ...entry,
    });

    // Only log in debug mode for individual registrations
    if (this.options.enableLogging && process.env.NODE_ENV === 'development') {
      if (process.env.DEBUG_PARSER) {
        console.log(`📝 Registered template '${entry.id}'`);
      }
    }
  }

  /**
   * Register multiple templates for pre-compilation
   * @param entries Array of template registry entries
   */
  public registerTemplates(entries: TemplateRegistryEntry[]): void {
    for (const entry of entries) {
      this.registerTemplate(entry);
    }
  }

  /**
   * Validate a template content
   * @param content Template content to validate
   * @returns Validation result
   */
  private validateTemplate(content: string): { isValid: boolean; error?: string } {
    if (!content || typeof content !== 'string') {
      return { isValid: false, error: 'Template content must be a non-empty string' };
    }

    if (content.trim().length === 0) {
      return { isValid: false, error: 'Template content cannot be empty or whitespace only' };
    }

    // Basic Handlebars syntax validation
    const openBraces = (content.match(/\{\{/g) || []).length;
    const closeBraces = (content.match(/\}\}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      return { 
        isValid: false, 
        error: `Mismatched Handlebars braces: ${openBraces} opening, ${closeBraces} closing` 
      };
    }

    return { isValid: true };
  }

  /**
   * Pre-compile a single template
   * @param entry Template registry entry
   * @returns Pre-compilation result
   */
  private async precompileTemplate(entry: TemplateRegistryEntry): Promise<PrecompilationResult> {
    const startTime = Date.now();
    
    try {
      // Validate template if enabled
      if (this.options.validateTemplates) {
        const validation = this.validateTemplate(entry.content);
        if (!validation.isValid) {
          return {
            id: entry.id,
            success: false,
            compilationTime: Date.now() - startTime,
            error: `Template validation failed: ${validation.error}`,
            templateSize: Buffer.byteLength(entry.content, 'utf8'),
          };
        }
      }

      // Compile the template using WASM if available, otherwise use cache
      if (this.wasmBridge.isWASMAvailable()) {
        try {
          const wasmResult = await this.wasmBridge.compileTemplateFast(entry.content, entry.id);
          // Store the WASM-compiled result in cache (using the cache's internal storage)
          this.cache.compile(entry.content); // This will cache the original, then we override
          // Note: In a real implementation, we'd need to extend the cache to support direct setting
        } catch (wasmError) {
          // Fallback to regular compilation
          this.cache.compile(entry.content);
        }
      } else {
        // Regular compilation
        this.cache.compile(entry.content);
      }
      
      const compilationTime = Date.now() - startTime;

      // Only log individual compilations in debug mode to avoid performance impact
      if (this.options.enableLogging && process.env.NODE_ENV === 'development') {
        if (process.env.DEBUG_PARSER) {
          console.log(`✅ Pre-compiled '${entry.id}' (${compilationTime}ms)`);
        }
      }

      return {
        id: entry.id,
        success: true,
        compilationTime,
        templateSize: Buffer.byteLength(entry.content, 'utf8'),
      };
    } catch (error) {
      const compilationTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Always log errors, but keep them concise
      if (this.options.enableLogging) {
        console.error(`❌ Failed to pre-compile '${entry.id}': ${errorMessage}`);
      }

      return {
        id: entry.id,
        success: false,
        compilationTime,
        error: errorMessage,
        templateSize: Buffer.byteLength(entry.content, 'utf8'),
      };
    }
  }

  /**
   * Pre-compile all registered templates
   * @returns Pre-compilation summary
   */
  public async precompileAll(): Promise<PrecompilationSummary> {
    const startTime = Date.now();
    const results: PrecompilationResult[] = [];
    const errors: string[] = [];

    // Only log summary information to avoid performance impact
    if (this.options.enableLogging) {
      if (process.env.DEBUG_PARSER) {
        console.log(`🔄 Pre-compiling ${this.templateRegistry.size} templates...`);
      }
    }

    // Sort templates by priority (higher priority first)
    const sortedTemplates = Array.from(this.templateRegistry.values())
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));

    let processedCount = 0;
    
    for (const entry of sortedTemplates) {
      // Check if we've exceeded the maximum pre-compilation time
      if (Date.now() - startTime > this.options.maxPrecompilationTime) {
        const timeoutError = `Pre-compilation timeout exceeded (${this.options.maxPrecompilationTime}ms)`;
        errors.push(timeoutError);
        
        if (this.options.enableLogging) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`⏰ ${timeoutError}`);
          }
        }
        break;
      }

      try {
        const result = await this.precompileTemplate(entry);
        results.push(result);
        
        if (!result.success && !this.options.continueOnError) {
          errors.push(`Pre-compilation stopped due to error in template '${entry.id}': ${result.error}`);
          break;
        }
        
        processedCount++;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`Unexpected error pre-compiling template '${entry.id}': ${errorMessage}`);
        
        if (!this.options.continueOnError) {
          break;
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const successfulCompilations = results.filter(r => r.success).length;
    const failedCompilations = results.filter(r => !r.success).length;

    const summary: PrecompilationSummary = {
      totalTemplates: this.templateRegistry.size,
      successfulCompilations,
      failedCompilations,
      totalTime,
      averageCompilationTime: results.length > 0 
        ? results.reduce((sum, r) => sum + r.compilationTime, 0) / results.length 
        : 0,
      results,
      errors,
    };

    // Only log summary to avoid performance impact
    if (this.options.enableLogging) {
      console.log(`🎉 Pre-compilation: ${successfulCompilations}/${this.templateRegistry.size} templates (${totalTime}ms)`);

      if (errors.length > 0) {
        if (process.env.DEBUG_PARSER) {
          console.log(`   ⚠️  ${errors.length} errors encountered`);
        }
      }
    }

    return summary;
  }

  /**
   * Get the list of registered templates
   * @returns Array of registered template entries
   */
  public getRegisteredTemplates(): TemplateRegistryEntry[] {
    return Array.from(this.templateRegistry.values());
  }

  /**
   * Clear all registered templates
   */
  public clearRegistry(): void {
    this.templateRegistry.clear();
    
    if (this.options.enableLogging) {
      if (process.env.DEBUG_PARSER) {
        console.log('🧹 Template registry cleared');
      }
    }
  }

  /**
   * Get the number of registered templates
   * @returns Number of registered templates
   */
  public getRegistrySize(): number {
    return this.templateRegistry.size;
  }

  /**
   * Check if a template is registered
   * @param id Template ID to check
   * @returns True if the template is registered
   */
  public hasTemplate(id: string): boolean {
    return this.templateRegistry.has(id);
  }

  /**
   * Remove a template from the registry
   * @param id Template ID to remove
   * @returns True if the template was removed
   */
  public unregisterTemplate(id: string): boolean {
    const removed = this.templateRegistry.delete(id);
    
    // Skip verbose logging for individual operations
    if (removed && this.options.enableLogging && process.env.NODE_ENV === 'development') {
      if (process.env.DEBUG_PARSER) {
        console.log(`🗑️  Unregistered '${id}'`);
      }
    }
    
    return removed;
  }
}

// Global singleton instance
let globalPrecompilationService: TemplatePrecompilationService | null = null;

/**
 * Get the global template pre-compilation service instance
 * @param options Optional service configuration (only used on first call)
 * @returns Global service instance
 */
export function getGlobalPrecompilationService(
  options?: PrecompilationOptions
): TemplatePrecompilationService {
  if (!globalPrecompilationService) {
    globalPrecompilationService = new TemplatePrecompilationService(undefined, options);
  }
  return globalPrecompilationService;
}

/**
 * Reset the global template pre-compilation service
 * Useful for testing or when configuration changes
 */
export function resetGlobalPrecompilationService(): void {
  globalPrecompilationService = null;
}
