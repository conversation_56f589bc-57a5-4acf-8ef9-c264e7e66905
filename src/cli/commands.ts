import { Command } from 'commander';
import { generateAll } from '../core/generator-orchestrator';
import { WatchService } from '../core/watch-service';
import { EnhancedWatchService } from '../core/enhanced-watch-service';
import * as path from 'path';
import * as fs from 'fs';
import { UI } from '../utils/ui';
import { CommentDirectiveBenchmark, runQuickBenchmark } from '../utils/comment-directive-benchmark';
import { getGlobalPerformanceMonitor } from '../utils/comment-directive-performance-monitor';
import { LargeCodebaseTestGenerator, TestCodebasePresets } from '../utils/large-codebase-test-generator';
import { generateEnterpriseSchema } from '../utils/enterprise-schema-generator';
import { MigrationService, type MigrationOptions } from '../utils/migration-service';
import { AliasConfigUtils } from '../utils/alias-config';

// Default paths
const DEFAULT_SCHEMA_GLOB = './schema/**/*.gql';
const DEFAULT_OUTPUT_DIR = './src/generated';

/**
 * Collect multiple values for repeatable options
 */
function collectValues(value: string, previous: string[] = []): string[] {
    return previous.concat([value]);
}

/**
 * Validate alias format to ensure it follows safe patterns
 * @param alias The alias to validate
 * @returns True if valid, false otherwise
 */
function validateAliasFormat(alias: string): boolean {
    // Alias must start with @ and contain only alphanumeric characters, underscores, hyphens, and forward slashes
    const aliasPattern = /^@[a-zA-Z0-9_\-\/]+$/;

    // Check basic pattern
    if (!aliasPattern.test(alias)) {
        return false;
    }

    // Prevent path traversal attempts
    if (alias.includes('..') || alias.includes('./')) {
        return false;
    }

    // Prevent common security issues
    if (alias.includes('//') || alias.endsWith('/')) {
        return false;
    }

    return true;
}

/**
 * Validate that a codebase directory path exists
 * @param codebasePath The codebase directory path to validate
 * @returns True if valid, false otherwise
 */
function validateCodebaseDir(codebasePath: string): boolean {
    if (!codebasePath) {
        return true; // Allow empty/undefined paths
    }

    const resolvedPath = path.isAbsolute(codebasePath)
        ? codebasePath
        : path.resolve(process.cwd(), codebasePath);

    if (!fs.existsSync(resolvedPath)) {
        console.error('❌ Error: Codebase directory does not exist');
        console.error(`   Path: ${resolvedPath}`);
        console.error('   Please ensure the directory exists before running the command');
        return false;
    }

    if (!fs.statSync(resolvedPath).isDirectory()) {
        console.error('❌ Error: Codebase path is not a directory');
        console.error(`   Path: ${resolvedPath}`);
        console.error('   Please provide a valid directory path');
        return false;
    }

    return true;
}

/**
 * Process collected values, removing defaults if user provided values
 */
function processCollectedValues(values: string[], defaultValue: string): string[] {
    // If we have more than one value and the first is the default, remove it
    if (values.length > 1 && values[0] === defaultValue) {
        return values.slice(1);
    }
    return values;
}

/**
 * Resolve a path relative to the current working directory
 * @param inputPath The path to resolve (can be relative or absolute)
 * @returns Absolute path with canonical case
 */

/**
 * Validate that a path is safe and accessible
 * @param targetPath The path to validate
 * @returns True if the path is valid and accessible
 */
function validatePath(targetPath: string): boolean {
    const fs = require('fs');

    try {
        // Check if the path exists or if its parent directory exists
        if (fs.existsSync(targetPath)) {
            return true;
        }

        // If the path doesn't exist, check if we can create it
        const parentDir = path.dirname(targetPath);
        if (fs.existsSync(parentDir)) {
            // Check if we have write permissions to the parent directory
            try {
                fs.accessSync(parentDir, fs.constants.W_OK);
                return true;
            } catch (_error) {
                return false;
            }
        }

        // Recursively check parent directories
        return validatePath(parentDir);
    } catch (_error) {
        return false;
    }
}
/**
 * Get the canonical case of a path on case-insensitive filesystems
 * @param targetPath The path to canonicalize
 * @returns The path with correct case
 */
function getCanonicalCase(targetPath: string): string {
    const fs = require('fs');

    try {
        // Normalize the path first to handle different separators
        const normalizedPath = path.normalize(targetPath);

        // Parse the path to handle Windows drive letters properly
        const parsedPath = path.parse(normalizedPath);

        // For Windows, handle drive letters specially
        if (process.platform === 'win32' && parsedPath.root) {
            // Start with the root (e.g., "C:\")
            let canonicalPath = parsedPath.root;

            // Get the directory path without the root
            const relativePath = path.relative(parsedPath.root, normalizedPath);

            if (relativePath && relativePath !== '.') {
                // Split the relative path into components
                const pathComponents = relativePath.split(path.sep);

                // Build the canonical path component by component
                for (const component of pathComponents) {
                    if (!component) continue; // Skip empty components

                    try {
                        // Read the directory to get the actual case of the component
                        const entries = fs.readdirSync(canonicalPath);

                        // Find the entry that matches case-insensitively
                        const actualComponent = entries.find((entry: string) =>
                            entry.toLowerCase() === component.toLowerCase()
                        );

                        if (actualComponent) {
                            canonicalPath = path.join(canonicalPath, actualComponent);
                        } else {
                            // Component doesn't exist, use original case
                            canonicalPath = path.join(canonicalPath, component);
                        }
                    } catch (_error) {
                        // If we can't read the directory, use original case
                        canonicalPath = path.join(canonicalPath, component);
                    }
                }
            }

            return canonicalPath;
        } else {
            // For non-Windows platforms, use the original logic
            const pathComponents = normalizedPath.split(path.sep);
            let canonicalPath = '';

            // For absolute paths, start with the root
            if (path.isAbsolute(normalizedPath)) {
                canonicalPath = pathComponents[0] + path.sep;
                pathComponents.shift(); // Remove the root component
            }

            // Build the canonical path component by component
            for (const component of pathComponents) {
                if (!component) continue; // Skip empty components

                try {
                    // Read the directory to get the actual case of the component
                    const parentDir = canonicalPath || '.';
                    const entries = fs.readdirSync(parentDir);

                    // Find the entry that matches case-insensitively
                    const actualComponent = entries.find((entry: string) =>
                        entry.toLowerCase() === component.toLowerCase()
                    );

                    if (actualComponent) {
                        canonicalPath = path.join(canonicalPath, actualComponent);
                    } else {
                        // Component doesn't exist, use original case
                        canonicalPath = path.join(canonicalPath, component);
                    }
                } catch (_error) {
                    // If we can't read the directory, use original case
                    canonicalPath = path.join(canonicalPath, component);
                }
            }

            return canonicalPath;
        }
    } catch (_error) {
        // If anything fails, return the original path
        return targetPath;
    }
}

function resolvePath(inputPath: string): string {
    const fs = require('fs');

    try {
        // Normalize the input path first to handle different separators
        const normalizedInput = path.normalize(inputPath);

        let resolvedPath: string;
        if (path.isAbsolute(normalizedInput)) {
            resolvedPath = normalizedInput;
        } else {
            resolvedPath = path.resolve(process.cwd(), normalizedInput);
        }

        // Use fs.realpathSync to resolve symlinks, then canonicalize case
        try {
            const realPath = fs.realpathSync(resolvedPath);
            const canonicalPath = getCanonicalCase(realPath);
            return canonicalPath;
        } catch (realpathError) {
            // If the path doesn't exist, try to canonicalize what we have
            // This is common when the output directory doesn't exist yet
            const canonicalPath = getCanonicalCase(resolvedPath);
            return canonicalPath;
        }
    } catch (error) {
        // If all else fails, return the normalized input path
        // This ensures we don't break the CLI even with problematic paths
        console.warn(`Warning: Path resolution failed for "${inputPath}": ${error instanceof Error ? error.message : String(error)}`);
        return path.normalize(inputPath);
    }
}

// Create the CLI program
export const createProgram = (): Command => {
    const program = new Command();

    program
        .name('gql-generator')
        .description('GraphQL TypeScript Code Generator with TypeScript Decorator Support')
        .version('0.1.0')
        .addHelpText('after', `
Examples:
  $ gql-generator generate --schema ./schema --output ./output
  $ gql-generator generate --schema ./schema --output ./output --codebase-dir ./src
  $ gql-generator watch --schema ./schema --output ./output --codebase-dir ./src
  $ gql-generator init --directory ./my-project

TypeScript Decorator System:
  Use --codebase-dir to automatically enable scanning of your TypeScript codebase for:
  • @GQLMethodCall - Implement GraphQL field resolvers
  • @GQLImport - Add custom imports to generated files
  • @GQLField - Add TypeScript-only fields to types
  • @GQLContext - Configure context types for schemas

For more information, visit: https://github.com/your-org/gql-generator
        `);

    program
        .command('init')
        .description('Initialize the GraphQL schema and generator configuration')
        .option('-d, --directory <dir>', 'Target directory for initialization')
        .action(async (options) => {
            // Use require instead of import to avoid issues with ESM/CJS compatibility
            const initModule = require('../init');
            initModule.default(options);
        });

    program
        .command('generate')
        .description('Generate TypeScript code from GraphQL schema with optional decorator support')
        .option('-s, --schema <path>', 'Path to GraphQL schema files (can be used multiple times)', collectValues, [DEFAULT_SCHEMA_GLOB])
        .option('-o, --output <path>', 'Output directory for generated files (can be used multiple times)', collectValues, [DEFAULT_OUTPUT_DIR])
        .option('-f, --force', 'Force overwrite of existing files')
        .option('--skip-codegen', 'Skip running GraphQL codegen')
        .option('-c, --context <path>', 'Path to custom Context interface')
        .option('--context-name <n>', 'Name of the Context interface', 'Context')
        .option('--test', 'Generate test stubs for mutations')
        .option('--test-output <dir>', 'Output directory for test files', '{{output}}/__test__')
        .option('--test-style <style>', 'Test file style (grouped|individual)')
        .option('--debug', 'Enable debug mode for verbose logging and schema files listing')
        .option('--apply-aliases', 'Force regeneration of test files with alias directives', true)
        .option('--codebase-dir <path>', 'Path to TypeScript codebase directory for decorator scanning (automatically enables decorators)')
        .option('--schema-id <identifier>', 'Schema identifier for multi-schema support (generates namespaced type enums)')
        .option('--alias-codebase <alias>', 'Import alias for codebase directory (e.g., "@user_codebase") to replace long relative paths in generated imports')
        .option('--format', 'Enable post-generation formatting (default: true)')
        .option('--no-format', 'Disable post-generation formatting')
        .option('--formatter <type>', 'Formatter to use: biome, prettier, swc, none (default: auto-detect, prefers biome)', 'auto')
        .action(async (options) => {
            if (options.debug) {
                console.log('\n===== DEBUG MODE ENABLED =====');
                console.log('Verbose logging and schema files listing will be displayed');
                console.log('===== DEBUG MODE ENABLED =====\n');
            }

            // Handle multiple schema-output pairs
            const rawSchemaPaths = Array.isArray(options.schema) ? options.schema : [options.schema];
            const rawOutputPaths = Array.isArray(options.output) ? options.output : [options.output];

            // Process collected values to remove defaults if user provided values
            const schemaPaths = processCollectedValues(rawSchemaPaths, DEFAULT_SCHEMA_GLOB);
            const outputPaths = processCollectedValues(rawOutputPaths, DEFAULT_OUTPUT_DIR);

            // Validate that schema and output counts match
            if (schemaPaths.length !== outputPaths.length) {
                console.error('❌ Error: Number of --schema and --output options must match');
                console.error(`   Found ${schemaPaths.length} schema path(s) and ${outputPaths.length} output path(s)`);
                console.error('   Usage: --schema "path1" --output "out1" --schema "path2" --output "out2"');
                process.exit(1);
            }

            // Validate alias format if provided
            if (options.aliasCodebase && !validateAliasFormat(options.aliasCodebase)) {
                console.error('❌ Error: Invalid alias format');
                console.error(`   Alias "${options.aliasCodebase}" must start with @ and contain only alphanumeric characters, underscores, hyphens, and forward slashes`);
                console.error('   Examples: "@src", "@user_codebase", "@my-app/core"');
                process.exit(1);
            }

            // Validate codebase directory if provided
            if (options.codebaseDir && !validateCodebaseDir(options.codebaseDir)) {
                process.exit(1);
            }

            // Process each schema-output pair
            for (let i = 0; i < schemaPaths.length; i++) {
                const schemaPath = schemaPaths[i];
                const outputPath = outputPaths[i];

                console.log(`\n📦 Processing schema-output pair ${i + 1}/${schemaPaths.length}:`);
                console.log(`   Schema: ${schemaPath}`);
                console.log(`   Output: ${outputPath}`);

                // Resolve paths relative to the current working directory
                const resolvedOptions = {
                    ...options,
                    schema: resolvePath(schemaPath),
                    output: resolvePath(outputPath),
                    // Resolve context path if provided
                    context: options.context ? resolvePath(options.context) : options.context,
                    // Resolve test output path if provided and contains {{output}} placeholder
                    testOutput: options.testOutput ?
                        (options.testOutput.includes('{{output}}') ?
                            options.testOutput.replace('{{output}}', resolvePath(outputPath)) :
                            resolvePath(options.testOutput)) :
                        options.testOutput,
                    // Resolve codebase directory if provided
                    codebaseDir: options.codebaseDir ? resolvePath(options.codebaseDir) : options.codebaseDir,
                    // Auto-enable decorators when codebase directory is provided
                    enableDecorators: !!options.codebaseDir,
                    // Pass through schema identifier
                    schemaId: options.schemaId,
                    // Pass through alias configuration
                    aliasCodebase: options.aliasCodebase,
                    // Formatting options
                    enableFormatting: options.format !== false && !options.noFormat && options.formatter !== 'none',
                    formatter: options.formatter === 'auto' ? undefined : options.formatter as any
                };

                if (options.debug) {
                    console.log('Resolved paths:');
                    console.log(`  Schema: ${resolvedOptions.schema}`);
                    console.log(`  Output: ${resolvedOptions.output}`);
                    if (resolvedOptions.context) {
                        console.log(`  Context: ${resolvedOptions.context}`);
                    }
                    if (resolvedOptions.testOutput) {
                        console.log(`  Test Output: ${resolvedOptions.testOutput}`);
                    }
                    if (resolvedOptions.codebaseDir) {
                        console.log(`  Codebase Directory: ${resolvedOptions.codebaseDir}`);
                    }
                    if (resolvedOptions.enableDecorators) {
                        console.log(`  Decorator Scanning: enabled`);
                    }
                    if (resolvedOptions.aliasCodebase) {
                        console.log(`  Codebase Alias: ${resolvedOptions.aliasCodebase}`);
                    }
                }

                await generateAll(resolvedOptions);
            }

            console.log(`\n✅ Successfully processed ${schemaPaths.length} schema-output pair(s)`);

            // Phase 3: Cleanup optimizations to ensure CLI exits properly
            try {
                // Cleanup individual Phase 3 components that might have been initialized
                const { getGlobalMemoryMappedFileReader } = require('../utils/memory-mapped-file-reader');
                const { getGlobalWASMPerformanceMonitor } = require('../utils/wasm-performance-monitor');
                const { getGlobalMemoryMonitor } = require('../utils/memory-pressure-monitor');

                // Check if components were initialized and clean them up
                const memoryReader = getGlobalMemoryMappedFileReader();
                if (memoryReader) {
                    await memoryReader.close();
                }

                const wasmPerfMonitor = getGlobalWASMPerformanceMonitor();
                if (wasmPerfMonitor) {
                    wasmPerfMonitor.stop();
                }

                const memoryMonitor = getGlobalMemoryMonitor();
                if (memoryMonitor) {
                    memoryMonitor.stop();
                }

                // Finally cleanup the performance orchestrator if it was initialized
                try {
                    const { getGlobalPerformanceOrchestrator } = require('../utils/performance-orchestrator');
                    const performanceOrchestrator = getGlobalPerformanceOrchestrator();
                    await performanceOrchestrator.shutdown();
                } catch (optimizerError) {
                    // Performance orchestrator might not have been initialized
                }

                if (options.debug) {
                    console.log('✅ Phase 3 cleanup completed successfully');
                }
            } catch (error) {
                // Ignore cleanup errors to avoid breaking existing functionality
                if (options.debug) {
                    console.log('⚠️ Phase 3 cleanup completed with warnings:', error);
                }
            }

            // Force exit after a short delay to ensure all cleanup is complete
            setTimeout(() => {
                process.exit(0);
            }, 100);
        })
        .addHelpText('after', `
Examples:
  Basic generation:
    $ gql-generator generate --schema ./schema --output ./output

  With TypeScript decorators:
    $ gql-generator generate --schema ./schema --output ./output --codebase-dir ./src

  Multiple schemas:
    $ gql-generator generate --schema ./schema/public --output ./output/public --schema ./schema/admin --output ./output/admin

  With custom context:
    $ gql-generator generate --schema ./schema --output ./output --context ./src/types/context.ts

  With import aliases:
    $ gql-generator generate --schema ./schema --output ./output --codebase-dir ./src --alias-codebase "@src"

TypeScript Decorator Usage:
  When --codebase-dir is provided, the generator automatically scans your codebase for:

  @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
  export const getUser = (id: string) => ({ id, name: "User" });

  @GQLImport("import { UserService } from '../services/user-service'")
  @GQLMethodCall({ type: "Query", field: "users", call: "UserService.findAll()" })
  export const getUsers = () => null;

  Decorators take precedence over comment-based directives in schema files.
        `);

    program
        .command('clean')
        .description('Clean generated files')
        .option('-o, --output <path>', 'Output directory for generated files', DEFAULT_OUTPUT_DIR)
        .action(async (options) => {
            // Resolve output path relative to the current working directory
            const resolvedOptions = {
                ...options,
                output: resolvePath(options.output)
            };

            // Use require instead of import to avoid issues with ESM/CJS compatibility
            const cleanModule = require('../core/clean');
            cleanModule.cleanGeneratedFiles(resolvedOptions);
        });

    program
        .command('watch')
        .description('Watch schema files and TypeScript codebase, automatically regenerate code on changes')
        .option('-s, --schema <path>', 'Path to GraphQL schema files (can be used multiple times)', collectValues, [DEFAULT_SCHEMA_GLOB])
        .option('-o, --output <path>', 'Output directory for generated files (can be used multiple times)', collectValues, [DEFAULT_OUTPUT_DIR])
        .option('-f, --force', 'Force overwrite of existing files')
        .option('--skip-codegen', 'Skip running GraphQL codegen')
        .option('-c, --context <path>', 'Path to custom Context interface')
        .option('--context-name <n>', 'Name of the Context interface', 'Context')
        .option('--test', 'Generate test stubs for mutations')
        .option('--test-output <dir>', 'Output directory for test files', '{{output}}/__test__')
        .option('--test-style <style>', 'Test file style (grouped|individual)')
        .option('--debug', 'Enable debug mode for verbose logging and schema files listing')
        .option('--apply-aliases', 'Force regeneration of test files with alias directives', true)
        .option('--debounce <ms>', 'Debounce delay in milliseconds', '300')
        .option('--no-initial', 'Skip initial generation on startup')
        .option('--watch-paths <paths>', 'Additional paths to watch (comma-separated)')
        .option('--format', 'Enable post-generation formatting (default: true)')
        .option('--no-format', 'Disable post-generation formatting')
        .option('--formatter <type>', 'Formatter to use: biome, prettier, swc, none (default: auto-detect, prefers biome)', 'auto')
        // Enhanced watch options
        .option('--bidirectional', 'Enable bidirectional sync between schema and generated code')
        .option('--sync-debounce <ms>', 'Debounce delay for sync operations in milliseconds', '500')
        .option('--no-await-patterns', 'Disable detection of await patterns in return statements')
        .option('--no-cast-patterns', 'Disable detection of cast patterns in return statements')
        .option('--no-import-patterns', 'Disable detection of import patterns')
        .option('--no-backup', 'Disable backup of schema files before modification')
        .option('--max-sync-attempts <n>', 'Maximum sync attempts before giving up', '3')
        .option('--codebase-dir <path>', 'Path to TypeScript codebase directory for decorator scanning (automatically enables decorators)')
        .option('--schema-id <identifier>', 'Schema identifier for multi-schema support (generates namespaced type enums)')
        .option('--alias-codebase <alias>', 'Import alias for codebase directory (e.g., "@user_codebase") to replace long relative paths in generated imports')
        .action(async (options) => {
            if (options.debug) {
                console.log('\n===== DEBUG MODE ENABLED =====');
                console.log('Verbose logging and schema files listing will be displayed');
                console.log('===== DEBUG MODE ENABLED =====\n');
            }

            // Handle multiple schema-output pairs
            const rawSchemaPaths = Array.isArray(options.schema) ? options.schema : [options.schema];
            const rawOutputPaths = Array.isArray(options.output) ? options.output : [options.output];

            // Process collected values to remove defaults if user provided values
            const schemaPaths = processCollectedValues(rawSchemaPaths, DEFAULT_SCHEMA_GLOB);
            const outputPaths = processCollectedValues(rawOutputPaths, DEFAULT_OUTPUT_DIR);

            // Validate that schema and output counts match
            if (schemaPaths.length !== outputPaths.length) {
                UI.error('Number of --schema and --output options must match');
                UI.error(`Found ${schemaPaths.length} schema path(s) and ${outputPaths.length} output path(s)`);
                UI.error('Usage: --schema "path1" --output "out1" --schema "path2" --output "out2"');
                process.exit(1);
            }

            // Validate alias format if provided
            if (options.aliasCodebase && !validateAliasFormat(options.aliasCodebase)) {
                UI.error('Invalid alias format');
                UI.error(`Alias "${options.aliasCodebase}" must start with @ and contain only alphanumeric characters, underscores, hyphens, and forward slashes`);
                UI.error('Examples: "@src", "@user_codebase", "@my-app/core"');
                process.exit(1);
            }

            // Validate codebase directory if provided
            if (options.codebaseDir && !validateCodebaseDir(options.codebaseDir)) {
                process.exit(1);
            }

            // Parse watch-specific options
            const debounce = parseInt(options.debounce, 10);
            const syncDebounce = parseInt(options.syncDebounce, 10);
            const maxSyncAttempts = parseInt(options.maxSyncAttempts, 10);
            const watchPaths = options.watchPaths ?
                options.watchPaths.split(',').map((p: string) => p.trim()) :
                undefined;

            // Use compact startup message
            UI.startup(schemaPaths, outputPaths, options.bidirectional || false);

            // Create watch services for each schema-output pair
            const watchServices: Array<WatchService | EnhancedWatchService> = [];

            for (let i = 0; i < schemaPaths.length; i++) {
                const schemaPath = schemaPaths[i];
                const outputPath = outputPaths[i];

                if (options.debug) {
                    UI.info(`${i + 1}. Schema: ${schemaPath} → Output: ${outputPath}`);
                }

                // Resolve paths relative to the current working directory
                const resolvedOptions = {
                    ...options,
                    schema: resolvePath(schemaPath),
                    output: resolvePath(outputPath),
                    // Resolve context path if provided
                    context: options.context ? resolvePath(options.context) : options.context,
                    // Resolve test output path if provided and contains {{output}} placeholder
                    testOutput: options.testOutput ?
                        (options.testOutput.includes('{{output}}') ?
                            options.testOutput.replace('{{output}}', resolvePath(outputPath)) :
                            resolvePath(options.testOutput)) :
                        options.testOutput,
                    // Watch-specific options
                    debounce: isNaN(debounce) ? 300 : debounce,
                    initial: options.initial !== false, // Default to true unless --no-initial
                    watchPaths: watchPaths ? watchPaths.map(resolvePath) : undefined,
                    // Enhanced watch options
                    enableBidirectionalSync: options.bidirectional || false,
                    syncDebounce: isNaN(syncDebounce) ? 500 : syncDebounce,
                    detectAwaitPatterns: options.awaitPatterns !== false,
                    detectCastPatterns: options.castPatterns !== false,
                    detectImportPatterns: options.importPatterns !== false,
                    backupSchemaFiles: options.backup !== false,
                    maxSyncAttempts: isNaN(maxSyncAttempts) ? 3 : maxSyncAttempts,
                    // Decorator options
                    codebaseDir: options.codebaseDir ? resolvePath(options.codebaseDir) : options.codebaseDir,
                    enableDecorators: !!options.codebaseDir,
                    // Schema identifier
                    schemaId: options.schemaId,
                    // Alias configuration
                    aliasCodebase: options.aliasCodebase,
                    // Formatting options
                    enableFormatting: options.format !== false && !options.noFormat && options.formatter !== 'none',
                    formatter: options.formatter === 'auto' ? undefined : options.formatter as any
                };

                // Create appropriate watch service for this pair
                if (resolvedOptions.enableBidirectionalSync) {
                    watchServices.push(new EnhancedWatchService(resolvedOptions));
                } else {
                    watchServices.push(new WatchService(resolvedOptions));
                }
            }

            if (options.debug) {
                console.log('\nDebug information for all watch services:');
                watchServices.forEach((_, index) => {
                    const schemaPath = schemaPaths[index];
                    const outputPath = outputPaths[index];
                    console.log(`\n  Watch Service ${index + 1}:`);
                    console.log(`    Schema: ${resolvePath(schemaPath)}`);
                    console.log(`    Output: ${resolvePath(outputPath)}`);
                    if (options.context) {
                        console.log(`    Context: ${resolvePath(options.context)}`);
                    }
                    if (watchPaths) {
                        console.log(`    Watch Paths: ${watchPaths.map(resolvePath).join(', ')}`);
                    }
                    if (options.codebaseDir) {
                        console.log(`    Codebase Directory: ${resolvePath(options.codebaseDir)}`);
                    }
                    if (options.aliasCodebase) {
                        console.log(`    Codebase Alias: ${options.aliasCodebase}`);
                    }
                });
                console.log(`\n  Global Settings:`);
                console.log(`    Debounce: ${isNaN(debounce) ? 300 : debounce}ms`);
                console.log(`    Initial Generation: ${options.initial !== false}`);
                if (options.enableDecorators) {
                    console.log(`    Decorator Scanning: enabled`);
                }
                if (options.bidirectional) {
                    console.log(`    Bidirectional Sync: enabled`);
                    console.log(`    Sync Debounce: ${isNaN(syncDebounce) ? 500 : syncDebounce}ms`);
                    console.log(`    Max Sync Attempts: ${isNaN(maxSyncAttempts) ? 3 : maxSyncAttempts}`);
                }
            }

            // Start all watch services concurrently
            await Promise.all(watchServices.map(async (service, index) => {
                try {
                    if (options.debug) {
                        UI.info(`Starting watch service ${index + 1}/${watchServices.length}...`);
                    }
                    await service.start();
                } catch (error) {
                    UI.error(`Failed to start watch service ${index + 1}: ${error}`);
                    throw error;
                }
            }));

            // Set up graceful shutdown for all services
            const shutdown = async () => {
                UI.clearLine();
                UI.newLine();
                UI.info('Shutting down watch services...');
                await Promise.all(watchServices.map(async (service, index) => {
                    try {
                        if ('stop' in service && typeof service.stop === 'function') {
                            await service.stop();
                            if (options.debug) {
                                UI.info(`Watch service ${index + 1} stopped`);
                            }
                        }
                    } catch (error) {
                        UI.error(`Error stopping watch service ${index + 1}: ${error}`);
                    }
                }));

                // Phase 3: Cleanup optimizations
                try {
                    // Cleanup individual Phase 3 components that might have been initialized
                    const { getGlobalMemoryMappedFileReader } = require('../utils/memory-mapped-file-reader');
                    const { getGlobalWASMPerformanceMonitor } = require('../utils/wasm-performance-monitor');
                    const { getGlobalMemoryMonitor } = require('../utils/memory-pressure-monitor');

                    // Check if components were initialized and clean them up
                    const memoryReader = getGlobalMemoryMappedFileReader();
                    if (memoryReader) {
                        await memoryReader.close();
                    }

                    const wasmPerfMonitor = getGlobalWASMPerformanceMonitor();
                    if (wasmPerfMonitor) {
                        wasmPerfMonitor.stop();
                    }

                    const memoryMonitor = getGlobalMemoryMonitor();
                    if (memoryMonitor) {
                        memoryMonitor.stop();
                    }

                    // Finally cleanup the performance orchestrator if it was initialized
                    try {
                        const { getGlobalPerformanceOrchestrator } = require('../utils/performance-orchestrator');
                        const performanceOrchestrator = getGlobalPerformanceOrchestrator();
                        await performanceOrchestrator.shutdown();
                    } catch (optimizerError) {
                        // Performance orchestrator might not have been initialized
                    }

                    if (options.debug) {
                        UI.info('Phase 3 optimizations cleaned up');
                    }
                } catch (error) {
                    // Ignore cleanup errors to avoid breaking existing functionality
                    if (options.debug) {
                        UI.info('Phase 3 cleanup completed with warnings');
                    }
                }

                UI.success('All watch services stopped');
                process.exit(0);
            };

            process.on('SIGINT', shutdown);
            process.on('SIGTERM', shutdown);
        })
        .addHelpText('after', `
Examples:
  Basic watch mode:
    $ gql-generator watch --schema ./schema --output ./output

  Watch with TypeScript decorators:
    $ gql-generator watch --schema ./schema --output ./output --codebase-dir ./src

  Watch with bidirectional sync:
    $ gql-generator watch --schema ./schema --output ./output --bidirectional --codebase-dir ./src

  Watch multiple schemas:
    $ gql-generator watch --schema ./schema/public --output ./output/public --schema ./schema/admin --output ./output/admin

  Watch with import aliases:
    $ gql-generator watch --schema ./schema --output ./output --codebase-dir ./src --alias-codebase "@src"

Watch Mode Features:
  • Automatically regenerates code when schema files change
  • Watches TypeScript codebase for decorator changes (when --codebase-dir is provided)
  • Bidirectional sync updates schema when generated code changes (with --bidirectional)
  • Debounced file watching to prevent excessive regeneration
  • Graceful shutdown with Ctrl+C

TypeScript Decorator Integration:
  Watch mode monitors your TypeScript files for decorator changes and automatically
  regenerates GraphQL resolvers when decorators are added, modified, or removed.
        `);

    // Performance testing commands
    program
        .command('benchmark')
        .description('Run performance benchmarks for comment directive parsing')
        .option('--test-size <size>', 'Test size: small, medium, large, xlarge', 'medium')
        .option('--iterations <number>', 'Number of benchmark iterations', '10')
        .option('--output-dir <path>', 'Output directory for benchmark results')
        .option('--legacy-only', 'Test only legacy parser')
        .option('--optimized-only', 'Test only optimized parser')
        .action(async (options) => {
            try {
                UI.info('🚀 Running comment directive performance benchmark...');

                const testSize = options.testSize as keyof typeof TestCodebasePresets;
                const iterations = parseInt(options.iterations);

                if (!TestCodebasePresets[testSize]) {
                    UI.error(`Invalid test size: ${testSize}. Valid options: ${Object.keys(TestCodebasePresets).join(', ')}`);
                    process.exit(1);
                }

                const benchmark = new CommentDirectiveBenchmark({
                    testSizes: [testSize],
                    iterations,
                    outputDir: options.outputDir,
                    testLegacyParser: !options.optimizedOnly,
                    testOptimizedParser: !options.legacyOnly,
                    enableDetailedLogging: true
                });

                const report = await benchmark.runBenchmarkSuite();

                UI.success(`✅ Benchmark completed! Average speedup: ${report.summary.averageSpeedup}x`);
                UI.info(`📄 Full report available in benchmark results directory`);

            } catch (error) {
                UI.error(`❌ Benchmark failed: ${error}`);
                process.exit(1);
            }
        });

    program
        .command('quick-benchmark')
        .description('Run a quick performance benchmark')
        .option('--test-size <size>', 'Test size: small, medium, large', 'small')
        .action(async (options) => {
            try {
                UI.info('⚡ Running quick performance benchmark...');

                const result = await runQuickBenchmark(options.testSize, 3);

                if (result.performanceImprovement) {
                    UI.success(`✅ Performance improvement: ${Math.round(result.performanceImprovement.speedupFactor * 100) / 100}x faster`);
                } else {
                    UI.info('📊 Benchmark completed (no comparison data available)');
                }

            } catch (error) {
                UI.error(`❌ Quick benchmark failed: ${error}`);
                process.exit(1);
            }
        });

    program
        .command('performance-monitor')
        .description('Start performance monitoring for comment directive parsing')
        .option('--interval <ms>', 'Monitoring interval in milliseconds', '30000')
        .option('--log-file <path>', 'Log file path for performance metrics')
        .action(async (options) => {
            try {
                UI.info('📊 Starting performance monitoring...');

                const monitor = getGlobalPerformanceMonitor({
                    enableRealTimeMonitoring: true,
                    enableLogging: true,
                    reportingIntervalMs: parseInt(options.interval),
                    logFilePath: options.logFile
                });

                await monitor.startMonitoring();

                UI.success('✅ Performance monitoring started. Press Ctrl+C to stop and generate report.');

                // Handle graceful shutdown
                process.on('SIGINT', async () => {
                    UI.info('\n🛑 Stopping performance monitoring...');
                    monitor.stopMonitoring();

                    const reportPath = await monitor.exportReport();
                    UI.success(`📄 Performance report exported to: ${reportPath}`);

                    process.exit(0);
                });

                // Keep process alive
                await new Promise(() => {});

            } catch (error) {
                UI.error(`❌ Performance monitoring failed: ${error}`);
                process.exit(1);
            }
        });

    program
        .command('generate-test-schema')
        .description('Generate large test schema for performance testing')
        .option('--size <size>', 'Schema size: small, medium, large, xlarge, massive, enterprise', 'medium')
        .option('--output-dir <path>', 'Output directory for test schema', './test-schema')
        .action(async (options) => {
            try {
                UI.info(`🏗️ Generating ${options.size} test schema...`);

                const generator = new LargeCodebaseTestGenerator({
                    ...TestCodebasePresets[options.size as keyof typeof TestCodebasePresets],
                    outputDir: options.outputDir,
                    enableLogging: true
                });

                const stats = await generator.generateTestCodebase();

                UI.success(`✅ Generated test schema:`);
                UI.info(`   📁 Files: ${stats.totalFiles}`);
                UI.info(`   🏷️  Types: ${stats.totalTypes}`);
                UI.info(`   📝 Fields: ${stats.totalFields}`);
                UI.info(`   🎯 Directives: ${stats.totalDirectives}`);
                UI.info(`   📊 Size: ${Math.round(stats.estimatedSize / 1024)}KB`);
                UI.info(`   ⏱️  Generated in: ${stats.generationTime}ms`);
                UI.info(`   📂 Location: ${options.outputDir}`);

            } catch (error) {
                UI.error(`❌ Test schema generation failed: ${error}`);
                process.exit(1);
            }
        });

    program
        .command('generate-enterprise-schema')
        .description('Generate enterprise-scale schema with hundreds of files (role-based organization)')
        .option('--output-dir <path>', 'Output directory for enterprise schema', './enterprise-schemas')
        .action(async (options) => {
            try {
                UI.info(`🏗️ Generating enterprise schema with hundreds of files...`);

                const stats = await generateEnterpriseSchema(options.outputDir);

                UI.success(`✅ Generated enterprise schema:`);
                UI.info(`   📁 Files: ${stats.totalFiles}`);
                UI.info(`   🏷️  Types: ${stats.totalTypes}`);
                UI.info(`   📝 Fields: ${stats.totalFields}`);
                UI.info(`   🎯 Directives: ${stats.totalDirectives}`);
                UI.info(`   📊 Size: ${Math.round(stats.estimatedSize / 1024)}KB`);
                UI.info(`   ⏱️  Generated in: ${stats.generationTime}ms`);
                UI.info(`   📂 Location: ${options.outputDir}`);

            } catch (error) {
                UI.error(`❌ Enterprise schema generation failed: ${error}`);
                process.exit(1);
            }
        });

    program
        .command('migrate')
        .description('Migrate comment-based directives in GraphQL files to TypeScript decorators')
        .option('-s, --schema <path>', 'Path to GraphQL schema directory (can be used multiple times)', collectValues, ['./schema'])
        .option('-c, --codebase <path>', 'Path to TypeScript codebase directory', './src')
        .option('--schema-id <identifier>', 'Schema identifier for multi-schema support (can be used multiple times)', collectValues, [])
        .option('--dry-run', 'Preview migration without making changes')
        .option('--verbose', 'Enable verbose logging')
        .option('--backup', 'Create backups before migration')
        .option('--include <patterns>', 'TypeScript file patterns to include (comma-separated)', '**/*.ts,**/*.tsx')
        .option('--exclude <patterns>', 'TypeScript file patterns to exclude (comma-separated)', '**/node_modules/**,**/dist/**,**/*.d.ts,**/__tests__/**,**/*.test.ts,**/*.spec.ts')
        .option('--alias-codebase <alias>', 'Alias for codebase imports (e.g., "@src")')
        .action(async (options) => {
            try {
                UI.info('🚀 Starting GraphQL directive to TypeScript decorator migration...');

                // Handle multiple schema paths and schema IDs
                const rawSchemaPaths = Array.isArray(options.schema) ? options.schema : [options.schema];
                const rawSchemaIds = Array.isArray(options.schemaId) ? options.schemaId : (options.schemaId ? [options.schemaId] : []);

                // Process collected values to remove defaults if user provided values
                const schemaPaths = processCollectedValues(rawSchemaPaths, './schema');

                // Validate that schema and schema-id counts match (if schema IDs are provided)
                if (rawSchemaIds.length > 0 && schemaPaths.length !== rawSchemaIds.length) {
                    UI.error('Number of --schema and --schema-id options must match');
                    UI.error(`Found ${schemaPaths.length} schema path(s) and ${rawSchemaIds.length} schema ID(s)`);
                    UI.error('Usage: --schema "path1" --schema-id "id1" --schema "path2" --schema-id "id2"');
                    process.exit(1);
                }

                // Validate required paths
                for (const schemaPath of schemaPaths) {
                    if (!fs.existsSync(schemaPath)) {
                        UI.error(`Schema directory does not exist: ${schemaPath}`);
                        process.exit(1);
                    }
                }

                if (!fs.existsSync(options.codebase)) {
                    UI.error(`Codebase directory does not exist: ${options.codebase}`);
                    process.exit(1);
                }

                // Parse include/exclude patterns
                const includePatterns = options.include.split(',').map((p: string) => p.trim());
                const excludePatterns = options.exclude.split(',').map((p: string) => p.trim());

                // Create alias configuration if provided
                const aliasConfig = options.aliasCodebase
                    ? AliasConfigUtils.createAliasConfig(resolvePath(options.codebase), options.aliasCodebase)
                    : undefined;

                // Execute migration for each schema
                const allResults = [];
                let totalDirectivesMigrated = 0;
                let totalGraphQLFilesProcessed = 0;
                let totalTypeScriptFilesModified = 0;
                let allErrors: string[] = [];
                let allWarnings: string[] = [];

                for (let i = 0; i < schemaPaths.length; i++) {
                    const schemaPath = schemaPaths[i];
                    const schemaId = rawSchemaIds.length > 0 ? rawSchemaIds[i] : undefined;

                    if (schemaPaths.length > 1) {
                        UI.info(`🚀 Processing schema: ${schemaId || 'default'}`);
                    }

                    // Create migration options for this schema
                    const migrationOptions: MigrationOptions = {
                        schemaPath: resolvePath(schemaPath),
                        codebasePath: resolvePath(options.codebase),
                        schemaId: schemaId,
                        dryRun: options.dryRun || false,
                        verbose: options.verbose || false,
                        createBackups: options.backup || false,
                        includePatterns,
                        excludePatterns,
                        aliasConfig: aliasConfig || undefined
                    };

                    if (options.verbose) {
                        UI.info(`Migration configuration for schema ${i + 1}/${schemaPaths.length}:`);
                        UI.info(`  Schema path: ${migrationOptions.schemaPath}`);
                        UI.info(`  Codebase path: ${migrationOptions.codebasePath}`);
                        if (migrationOptions.schemaId) {
                            UI.info(`  Schema ID: ${migrationOptions.schemaId}`);
                        }
                        if (migrationOptions.aliasConfig) {
                            UI.info(`  Alias: ${migrationOptions.aliasConfig.codebaseAlias}`);
                        }
                        UI.info(`  Dry run: ${migrationOptions.dryRun}`);
                        UI.info(`  Create backups: ${migrationOptions.createBackups}`);
                        UI.info(`  Include patterns: ${includePatterns.join(', ')}`);
                        UI.info(`  Exclude patterns: ${excludePatterns.join(', ')}`);
                    }

                    // Execute migration for this schema
                    const migrationService = new MigrationService(migrationOptions);
                    const result = await migrationService.migrate();

                    allResults.push(result);
                    totalDirectivesMigrated += result.directivesMigrated;
                    totalGraphQLFilesProcessed += result.graphqlFilesProcessed;
                    totalTypeScriptFilesModified += result.typescriptFilesModified;
                    allErrors.push(...result.errors);
                    allWarnings.push(...result.warnings);
                }

                // Display aggregated results
                const overallSuccess = allResults.every(result => result.success);

                if (overallSuccess) {
                    UI.success('✅ Migration completed successfully!');
                } else {
                    UI.error('❌ Migration completed with errors');
                    allErrors.forEach(error => UI.error(`   ${error}`));
                }

                if (allWarnings.length > 0) {
                    UI.warning('Migration warnings:');
                    allWarnings.forEach(warning => UI.warning(`   ${warning}`));
                }

                // Display summary for multi-schema migrations
                if (schemaPaths.length > 1) {
                    UI.info('\n📊 Migration Summary:');
                    UI.info(`   Schemas processed: ${schemaPaths.length}`);
                    UI.info(`   Total directives migrated: ${totalDirectivesMigrated}`);
                    UI.info(`   GraphQL files processed: ${totalGraphQLFilesProcessed}`);
                    UI.info(`   TypeScript files modified: ${totalTypeScriptFilesModified}`);

                    if (options.verbose) {
                        UI.info('\n📋 Per-schema results:');
                        allResults.forEach((result, index) => {
                            const schemaId = rawSchemaIds.length > 0 ? rawSchemaIds[index] : `schema-${index + 1}`;
                            UI.info(`   ${schemaId}: ${result.directivesMigrated} directives, ${result.graphqlFilesProcessed} GraphQL files, ${result.typescriptFilesModified} TS files`);
                        });
                    }
                }

                // Exit with appropriate code
                process.exit(overallSuccess ? 0 : 1);

            } catch (error) {
                UI.error(`❌ Migration failed: ${error}`);
                process.exit(1);
            }
        })
        .addHelpText('after', `
Examples:
  Basic migration:
    $ gql-generator migrate --schema ./schema --codebase ./src

  Multi-schema migration (single command):
    $ gql-generator migrate --schema ./schema/public --schema-id public --schema ./schema/admin --schema-id admin --codebase ./src

  Multi-schema migration (separate commands):
    $ gql-generator migrate --schema ./schema/public --codebase ./src --schema-id public
    $ gql-generator migrate --schema ./schema/admin --codebase ./src --schema-id admin

  Dry run (preview only):
    $ gql-generator migrate --schema ./schema --codebase ./src --dry-run

  Custom patterns:
    $ gql-generator migrate --schema ./schema --codebase ./src --include "**/*.ts" --exclude "**/test/**"

  With alias support:
    $ gql-generator migrate --schema ./schema --codebase ./src --alias-codebase "@src"

Migration Process:
  1. Scans GraphQL files for comment-based directives (#@methodCall, #@import, #@field)
  2. Locates referenced methods in TypeScript files
  3. Adds appropriate decorators (@GQLMethodCall, @GQLImport, @GQLField) to TypeScript functions
  4. Removes comment-based directives from GraphQL files
  5. Creates backups and provides rollback capabilities

The migration tool supports multi-schema environments and handles edge cases like
duplicate decorators when running multiple times on the same codebase.
        `);

    return program;
};