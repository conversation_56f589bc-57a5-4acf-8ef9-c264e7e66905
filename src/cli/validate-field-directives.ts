#!/usr/bin/env node

import { validateFieldDirectives } from '@utils/field-directive-validator';

/**
 * <PERSON><PERSON><PERSON> script to validate @field directives in GraphQL schema files
 * 
 * Usage:
 *   npx validate-field-directives [schemaPath] [options]
 * 
 * Options:
 *   --verbose          Show detailed processing information
 *   --strict           Treat warnings as errors
 *   --check-imports    Validate that imported types exist
 *   --import-root=DIR  Root directory for type imports (default: ./src)
 * 
 * Examples:
 *   npx validate-field-directives ./schema
 *   npx validate-field-directives ./schema --strict --check-imports --import-root=./src/types
 */

// Run the validator with command line arguments
validateFieldDirectives(process.argv.slice(2))
    .catch((error: Error) => {
        console.error('Validation failed:', error);
        process.exit(1);
    }); 