import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DirectiveManager } from '../utils/directive-manager';
import { WatchService } from '../core/watch-service';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 2: Complete Integration Test', () => {
  let tempDir: string;
  let codebaseDir: string;
  let outputDir: string;
  let schemaDir: string;

  beforeEach(async () => {
    // Create temporary directories
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase2-integration-test-'));
    codebaseDir = path.join(tempDir, 'codebase');
    outputDir = path.join(tempDir, 'output');
    schemaDir = path.join(tempDir, 'schema');
    
    await fs.ensureDir(codebaseDir);
    await fs.ensureDir(outputDir);
    await fs.ensureDir(schemaDir);
  });

  afterEach(async () => {
    if (tempDir) {
      await fs.remove(tempDir);
    }
  });

  describe('End-to-End Integration', () => {
    it('should demonstrate complete Phase 2 functionality', async () => {
      // Step 1: Create test schema
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
          email: String!
        }
        
        type Query {
          user(id: ID!): User
          users: [User!]!
        }
      `);

      await fs.writeFile(
        path.join(schemaDir, 'user.gql'),
        `
          type User {
            id: ID!
            name: String!
            email: String!
          }
          
          type Query {
            user(id: ID!): User
            users: [User!]!
          }
        `
      );

      // Step 2: Create codebase with decorators
      const resolverContent = `
        // User resolvers with decorators
        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ 
          id, 
          name: "Test User", 
          email: "<EMAIL>" 
        });

        @GQLMethodCall({ type: "Query", field: "users", call: "getAllUsers()" })
        export const getAllUsers = () => [
          { id: "1", name: "User 1", email: "<EMAIL>" }
        ];

        @GQLImport("import { UserService } from '../services/user'")
        @GQLMethodCall({ type: "User", field: "email", call: "UserService.getEmail(obj.id)" })
        export const getUserEmail = () => null;
      `;

      await fs.writeFile(path.join(codebaseDir, 'user-resolvers.ts'), resolverContent);

      // Step 3: Test SchemaBasedGenerator integration
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        debug: false, // Reduce noise in tests
      });

      // Should not throw
      await expect(generator.generate()).resolves.not.toThrow();

      // Step 4: Test DirectiveManager integration
      const directiveManager = DirectiveManager.getInstance();
      
      // Create mock decorator metadata
      const decoratorMetadata = {
        methodCalls: [
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '',
              raw: '@GQLMethodCall()',
              filePath: '/test/file.ts',
              lineNumber: 1,
              target: 'function test',
            },
            data: {
              type: 'User',
              field: 'name',
              call: 'getUserName(obj.id)',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const directives = directiveManager.getDecoratorDirectivesForType(
        decoratorMetadata,
        'User'
      );

      expect(directives).toBeDefined();
      expect(directives.methodCalls).toBeDefined();

      // Step 5: Test WatchService integration
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false, // Don't run initial generation
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      // Should include both schema and codebase directories
      expect(watchPaths.length).toBeGreaterThanOrEqual(2);
      expect(watchPaths).toContain(path.resolve(codebaseDir));

      // Test file filtering
      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // TypeScript files should be included when decorators are enabled
      expect(ignoredPatterns('/test/file.ts', { isFile: () => true })).toBe(false);
      expect(ignoredPatterns('/test/file.tsx', { isFile: () => true })).toBe(false);
      
      // GraphQL files should always be included
      expect(ignoredPatterns('/test/schema.gql', { isFile: () => true })).toBe(false);
    });

    it('should handle decorator and comment directive conflicts', () => {
      const directiveManager = DirectiveManager.getInstance();
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const decoratorDirectives = {
        imports: [{ name: 'import', content: 'decorator import', raw: '' }],
        methodCalls: [{ name: 'methodCall', content: 'decorator call', raw: '' }],
        fieldFields: [],
        others: {},
      };

      const commentDirectives = {
        imports: [{ name: 'import', content: 'comment import', raw: '' }],
        methodCalls: [{ name: 'methodCall', content: 'comment call', raw: '' }],
        fieldFields: [],
        others: {},
      };

      const merged = directiveManager.mergeDirectives(
        decoratorDirectives,
        commentDirectives,
        { typeName: 'User', fieldName: 'name' }
      );

      // Should detect conflicts
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Conflict detected at User.name')
      );

      // Should merge with decorator precedence
      expect(merged.imports).toHaveLength(2);
      expect(merged.methodCalls).toHaveLength(2);

      consoleSpy.mockRestore();
    });

    it('should demonstrate backward compatibility', async () => {
      // Test that existing functionality still works when decorators are disabled
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false, // Disabled
      });

      // Should work without issues
      await expect(generator.generate()).resolves.not.toThrow();

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: false, // Disabled
        initial: false,
      });

      const watchPaths = (watchService as any).getWatchPaths();
      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // Should not include codebase directory
      expect(watchPaths).not.toContain(path.resolve(codebaseDir));
      
      // Should ignore TypeScript files when decorators are disabled
      expect(ignoredPatterns('/test/file.ts', { isFile: () => true })).toBe(true);
    });
  });
});
