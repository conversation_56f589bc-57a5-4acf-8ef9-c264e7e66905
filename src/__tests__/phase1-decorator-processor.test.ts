import { DecoratorProcessor } from '../utils/decorator-processor';
import { DecoratorContainer, ParsedDecorator } from '../utils/decorator-parser';
import { DirectiveContainer } from '../utils/directive-parser';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';

describe('DecoratorProcessor', () => {
  let processor: DecoratorProcessor;

  beforeEach(() => {
    processor = new DecoratorProcessor(DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES);
  });

  const createMockDecorator = (name: string, filePath = '/test/file.ts'): ParsedDecorator => ({
    name,
    arguments: '',
    raw: `@${name}()`,
    filePath,
    lineNumber: 1,
    target: 'function test',
  });

  describe('convertToDirectiveContainer', () => {
    it('should convert empty decorator container', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result).toBeDefined();
      expect(result.methodCalls).toHaveLength(0);
      expect(result.imports).toHaveLength(0);
      expect(result.fieldFields).toHaveLength(0);
    });

    it('should convert @GQLMethodCall decorators to methodCall directives', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              type: 'User',
              field: 'name',
              call: 'getUserName(obj.id)',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].name).toBe('methodCall');
      expect(result.methodCalls[0].content).toBe('getUserName(obj.id)');
    });

    it('should convert @GQLImport decorators to import directives', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [],
        imports: [
          {
            decorator: createMockDecorator('GQLImport'),
            data: {
              importStatement: 'import { User } from "./types"',
            },
          },
        ],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.imports).toHaveLength(1);
      expect(result.imports[0].name).toBe('import');
      expect(result.imports[0].content).toBe('import { User } from "./types"');
    });

    it('should convert @GQLField decorators to field directives', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [],
        imports: [],
        fields: [
          {
            decorator: createMockDecorator('GQLField'),
            data: {
              ref: 'User',
              name: 'metadata',
              type: 'UserMetadata',
              optional: true,
            },
          },
        ],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.fieldFields).toHaveLength(1);
      expect(result.fieldFields[0].name).toBe('metadata');
      expect(result.fieldFields[0].type).toBe('UserMetadata');
      expect(result.fieldFields[0].optional).toBe(true);
    });

    it('should convert @GQLContext decorators to context directives', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [],
        imports: [],
        fields: [],
        contexts: [
          {
            decorator: createMockDecorator('GQLContext'),
            data: {
              path: './types/Context',
              name: 'AppContext',
            },
          },
        ],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.others.context).toBeDefined();
      expect(result.others.context).toHaveLength(1);
      expect(result.others.context[0].name).toBe('context');
      expect(result.others.context[0].content).toBe('{path: "./types/Context", name: "AppContext"}');
    });

    it('should filter decorators by schema', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              type: 'User',
              call: 'getUser()',
              schema: 'default',
            },
          },
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              type: 'Admin',
              call: 'getAdmin()',
              schema: 'admin',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const defaultResult = processor.convertToDirectiveContainer(decoratorContainer, 'default');
      const adminResult = processor.convertToDirectiveContainer(decoratorContainer, 'admin');

      expect(defaultResult.methodCalls).toHaveLength(1);
      expect(defaultResult.methodCalls[0].content).toBe('getUser()');

      expect(adminResult.methodCalls).toHaveLength(1);
      expect(adminResult.methodCalls[0].content).toBe('getAdmin()');
    });

    it('should handle invalid decorator data gracefully', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              // Missing required fields
              type: '',
              call: '',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      // Should not include invalid decorators
      expect(result.methodCalls).toHaveLength(0);
    });
  });

  describe('mergeDirectives', () => {
    it('should merge decorator and comment directives with decorator precedence', () => {
      const decoratorDirectives: DirectiveContainer = {
        imports: [{ name: 'import', content: 'decorator import', raw: '' }],
        methodCalls: [{ name: 'methodCall', content: 'decorator call', raw: '' }],
        fieldFields: [],
        others: {},
      };

      const commentDirectives: DirectiveContainer = {
        imports: [{ name: 'import', content: 'comment import', raw: '' }],
        methodCalls: [{ name: 'methodCall', content: 'comment call', raw: '' }],
        fieldFields: [],
        others: {},
      };

      const result = processor.mergeDirectives(decoratorDirectives, commentDirectives);

      // With decorator precedence, decorator directives should come last (higher precedence)
      expect(result.imports).toHaveLength(2);
      expect(result.imports[0].content).toBe('comment import');
      expect(result.imports[1].content).toBe('decorator import');

      expect(result.methodCalls).toHaveLength(2);
      expect(result.methodCalls[0].content).toBe('comment call');
      expect(result.methodCalls[1].content).toBe('decorator call');
    });

    it('should merge other directives', () => {
      const decoratorDirectives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {
          context: [{ name: 'context', content: 'decorator context', raw: '' }],
        },
      };

      const commentDirectives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {
          context: [{ name: 'context', content: 'comment context', raw: '' }],
          alias: [{ name: 'alias', content: 'comment alias', raw: '' }],
        },
      };

      const result = processor.mergeDirectives(decoratorDirectives, commentDirectives);

      expect(result.others.context).toHaveLength(2);
      expect(result.others.alias).toHaveLength(1);
    });
  });

  describe('splitBySchema', () => {
    it('should split decorators by schema', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: { type: 'User', call: 'getUser()' }, // No schema = default
          },
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: { type: 'Admin', call: 'getAdmin()', schema: 'admin' },
          },
        ],
        imports: [
          {
            decorator: createMockDecorator('GQLImport'),
            data: { importStatement: 'import default', schema: 'default' },
          },
          {
            decorator: createMockDecorator('GQLImport'),
            data: { importStatement: 'import admin', schema: 'admin' },
          },
        ],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.splitBySchema(decoratorContainer);

      expect(result.default.methodCalls).toHaveLength(1);
      expect(result.default.methodCalls[0].data.call).toBe('getUser()');

      expect(result.schemas.admin).toBeDefined();
      expect(result.schemas.admin.methodCalls).toHaveLength(1);
      expect(result.schemas.admin.methodCalls[0].data.call).toBe('getAdmin()');

      expect(result.default.imports).toHaveLength(1);
      expect(result.default.imports[0].data.importStatement).toBe('import default');

      expect(result.schemas.admin.imports).toHaveLength(1);
      expect(result.schemas.admin.imports[0].data.importStatement).toBe('import admin');
    });
  });

  describe('generateSmartImport', () => {
    it('should generate import statement with relative path', () => {
      const decoratorFilePath = '/project/src/resolvers/user.ts';
      const targetOutputPath = '/project/output/user/name.ts';
      const functionName = 'getUserName';

      const result = processor.generateSmartImport(decoratorFilePath, targetOutputPath, functionName);

      expect(result).toContain('import { getUserName }');
      expect(result).toContain('from');
      expect(result).toMatch(/['"].*resolvers.*user['"];?$/);
    });

    it('should handle different file extensions', () => {
      const decoratorFilePath = '/project/src/resolvers/user.tsx';
      const targetOutputPath = '/project/output/user/name.ts';
      const functionName = 'getUserName';

      const result = processor.generateSmartImport(decoratorFilePath, targetOutputPath, functionName);

      expect(result).toContain('import { getUserName }');
      // Should remove the .tsx extension
      expect(result).not.toContain('.tsx');
    });
  });

  describe('async operations', () => {
    it('should handle async method calls', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              type: 'User',
              call: 'getUser()',
              async: true,
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].content).toBe('await getUser()');
    });

    it('should not add await if already present', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: createMockDecorator('GQLMethodCall'),
            data: {
              type: 'User',
              call: 'await getUser()',
              async: true,
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].content).toBe('await getUser()');
    });
  });

  describe('conditional imports', () => {
    it('should handle conditional imports', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [],
        imports: [
          {
            decorator: createMockDecorator('GQLImport'),
            data: {
              importStatement: 'import { User } from "./types"',
              conditional: true,
              condition: 'process.env.NODE_ENV === "development"',
            },
          },
        ],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = processor.convertToDirectiveContainer(decoratorContainer);

      expect(result.imports).toHaveLength(1);
      expect(result.imports[0].content).toContain('Conditional:');
      expect(result.imports[0].content).toContain('process.env.NODE_ENV === "development"');
    });
  });
});
