import { DecoratorParser } from '../utils/decorator-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('DecoratorParser', () => {
  let tempDir: string;
  let parser: DecoratorParser;

  beforeEach(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'decorator-parser-test-'));
    parser = new DecoratorParser();
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('scanCodebase', () => {
    it('should scan empty directory without errors', async () => {
      const result = await parser.scanCodebase(tempDir);
      
      expect(result).toBeDefined();
      expect(result.methodCalls).toHaveLength(0);
      expect(result.imports).toHaveLength(0);
      expect(result.fields).toHaveLength(0);
      expect(result.contexts).toHaveLength(0);
    });

    it('should handle non-existent directory gracefully', async () => {
      const nonExistentDir = path.join(tempDir, 'does-not-exist');
      const result = await parser.scanCodebase(nonExistentDir);
      
      expect(result).toBeDefined();
      expect(result.methodCalls).toHaveLength(0);
    });

    it('should find @GQLMethodCall decorators', async () => {
      const testFile = path.join(tempDir, 'user-resolver.ts');
      await fs.writeFile(testFile, `
import { User } from './types';

@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
export const getUserName = (id: string) => \`User \${id}\`;

@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => ({ id, name: "Test User" });
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.methodCalls).toHaveLength(2);
      
      const userNameDecorator = result.methodCalls.find(mc => mc.data.field === 'name');
      expect(userNameDecorator).toBeDefined();
      expect(userNameDecorator!.data.type).toBe('User');
      expect(userNameDecorator!.data.call).toBe('getUserName(obj.id)');
      
      const userQueryDecorator = result.methodCalls.find(mc => mc.data.field === 'user');
      expect(userQueryDecorator).toBeDefined();
      expect(userQueryDecorator!.data.type).toBe('Query');
      expect(userQueryDecorator!.data.call).toBe('getUser(args.id)');
    });

    it('should find @GQLImport decorators', async () => {
      const testFile = path.join(tempDir, 'imports.ts');
      await fs.writeFile(testFile, `
@GQLImport("import { UserService } from '../services/user-service'")
@GQLMethodCall({ type: "Query", field: "users", call: "UserService.getAll()" })
export const getAllUsers = () => [];

@GQLImport({ importStatement: "import { Logger } from '@/utils/logger'", schema: "admin" })
export const loggerImport = null;
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.imports).toHaveLength(2);
      
      const userServiceImport = result.imports.find(imp => 
        imp.data.importStatement.includes('UserService')
      );
      expect(userServiceImport).toBeDefined();
      expect(userServiceImport!.data.importStatement).toBe("import { UserService } from '../services/user-service'");
      
      const loggerImport = result.imports.find(imp => 
        imp.data.importStatement.includes('Logger')
      );
      expect(loggerImport).toBeDefined();
      expect(loggerImport!.data.schema).toBe('admin');
    });

    it('should find @GQLField decorators', async () => {
      const testFile = path.join(tempDir, 'fields.ts');
      await fs.writeFile(testFile, `
@GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
export interface UserMetadata {
  createdAt: Date;
  updatedAt: Date;
}

@GQLField({ ref: "Product", name: "internalId", type: "string", optional: true })
export type ProductInternalId = string;
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.fields).toHaveLength(2);
      
      const metadataField = result.fields.find(f => f.data.name === 'metadata');
      expect(metadataField).toBeDefined();
      expect(metadataField!.data.ref).toBe('User');
      expect(metadataField!.data.type).toBe('UserMetadata');
      
      const internalIdField = result.fields.find(f => f.data.name === 'internalId');
      expect(internalIdField).toBeDefined();
      expect(internalIdField!.data.ref).toBe('Product');
      // Note: optional property may not be set if not explicitly provided
      expect(internalIdField!.data.optional).toBe(true);
    });

    it('should find @GQLContext decorators', async () => {
      const testFile = path.join(tempDir, 'context.ts');
      await fs.writeFile(testFile, `
@GQLContext({ path: "./types/AppContext", name: "AppContext" })
export class ContextConfig {}

@GQLContext({ path: "./types/AdminContext", name: "AdminContext", schema: "admin" })
export class AdminContextConfig {}
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.contexts).toHaveLength(2);
      
      const appContext = result.contexts.find(c => c.data.name === 'AppContext');
      expect(appContext).toBeDefined();
      expect(appContext!.data.path).toBe('./types/AppContext');
      
      const adminContext = result.contexts.find(c => c.data.name === 'AdminContext');
      expect(adminContext).toBeDefined();
      expect(adminContext!.data.schema).toBe('admin');
    });

    it('should handle multiple files', async () => {
      // Create multiple test files
      await fs.writeFile(path.join(tempDir, 'file1.ts'), `
@GQLMethodCall({ type: "User", call: "getUser1()" })
export const getUser1 = () => null;
      `);

      await fs.writeFile(path.join(tempDir, 'file2.ts'), `
@GQLMethodCall({ type: "User", call: "getUser2()" })
export const getUser2 = () => null;
      `);

      // Ensure subdirectory exists
      await fs.ensureDir(path.join(tempDir, 'subdir'));
      await fs.writeFile(path.join(tempDir, 'subdir', 'file3.ts'), `
@GQLMethodCall({ type: "User", call: "getUser3()" })
export const getUser3 = () => null;
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.methodCalls).toHaveLength(3);
      expect(result.methodCalls.map(mc => mc.data.call)).toEqual(
        expect.arrayContaining(['getUser1()', 'getUser2()', 'getUser3()'])
      );
    });

    it('should ignore non-TypeScript files', async () => {
      await fs.writeFile(path.join(tempDir, 'test.js'), `
@GQLMethodCall({ type: "User", call: "getUser()" })
export const getUser = () => null;
      `);

      await fs.writeFile(path.join(tempDir, 'test.txt'), `
@GQLMethodCall({ type: "User", call: "getUser()" })
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.methodCalls).toHaveLength(0);
    });

    it('should handle files with syntax errors gracefully', async () => {
      await fs.writeFile(path.join(tempDir, 'invalid.ts'), `
@GQLMethodCall({ type: "User", call: "getUser()" })
export const getUser = () => {
  // Missing closing brace
      `);

      const result = await parser.scanCodebase(tempDir);
      
      // Should not throw and should return empty results
      expect(result).toBeDefined();
    });
  });

  describe('parseGQLMethodCall', () => {
    it('should parse valid method call decorator', () => {
      const parser = new DecoratorParser();
      const args = '{ type: "User", field: "name", call: "getUserName(obj.id)" }';
      
      // Access private method for testing
      const result = (parser as any).parseGQLMethodCall(args);
      
      expect(result).toBeDefined();
      expect(result.type).toBe('User');
      expect(result.field).toBe('name');
      expect(result.call).toBe('getUserName(obj.id)');
    });

    it('should handle missing required fields', () => {
      const parser = new DecoratorParser();
      const args = '{ field: "name" }'; // Missing type and call
      
      const result = (parser as any).parseGQLMethodCall(args);
      
      expect(result).toBeNull();
    });

    it('should handle schema parameter', () => {
      const parser = new DecoratorParser();
      const args = '{ type: "User", call: "getUser()", schema: "admin" }';

      const result = (parser as any).parseGQLMethodCall(args);

      expect(result).toBeDefined();
      expect(result.schema).toBe('admin');
    });

    it('should infer field name from function name when field is not provided', async () => {
      const testFile = path.join(tempDir, 'field-inference.ts');
      await fs.writeFile(testFile, `
@GQLMethodCall({ type: "RootQuery", call: "users()" })
export const users = () => [];

@GQLMethodCall({ type: "User", call: "getUserPosts(obj.id)" })
export const getUserPosts = (userId: string) => [];
      `);

      const result = await parser.scanCodebase(tempDir);

      expect(result.methodCalls).toHaveLength(2);

      const usersDecorator = result.methodCalls.find(mc => mc.data.call === 'users()');
      expect(usersDecorator).toBeDefined();
      expect(usersDecorator!.data.type).toBe('RootQuery');
      expect(usersDecorator!.data.field).toBe('users');

      const postsDecorator = result.methodCalls.find(mc => mc.data.call === 'getUserPosts(obj.id)');
      expect(postsDecorator).toBeDefined();
      expect(postsDecorator!.data.type).toBe('User');
      expect(postsDecorator!.data.field).toBe('getUserPosts');
    });
  });

  describe('parseGQLImport', () => {
    it('should parse string import', () => {
      const parser = new DecoratorParser();
      const args = '"import { User } from \'./types\'"';
      
      const result = (parser as any).parseGQLImport(args);
      
      expect(result).toBeDefined();
      expect(result.importStatement).toBe('import { User } from \'./types\'');
    });

    it('should parse object import', () => {
      const parser = new DecoratorParser();
      const args = '{ importStatement: "import { User } from \'./types\'", schema: "default" }';
      
      const result = (parser as any).parseGQLImport(args);
      
      expect(result).toBeDefined();
      expect(result.importStatement).toBe("import { User } from './types");
      expect(result.schema).toBe('default');
    });
  });

  describe('validation', () => {
    it('should validate decorator parameters', () => {
      const validData = { type: 'User', call: 'getUser()' };
      const result = parser.validateDecorator('GQLMethodCall', validData);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid decorator parameters', () => {
      const invalidData = { type: '', call: '' };
      const result = parser.validateDecorator('GQLMethodCall', invalidData);
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('import path resolution', () => {
    it('should resolve relative import paths', () => {
      const decoratorFilePath = '/project/src/resolvers/user.ts';
      const targetPath = './types/User';
      
      const result = parser.resolveImportPath(decoratorFilePath, targetPath);
      
      expect(result).toContain('types/User');
    });

    it('should leave absolute paths unchanged', () => {
      const decoratorFilePath = '/project/src/resolvers/user.ts';
      const targetPath = '@/types/User';
      
      const result = parser.resolveImportPath(decoratorFilePath, targetPath);
      
      expect(result).toBe('@/types/User');
    });
  });
});
