import { buildSchema } from 'graphql';
import { InterfaceInheritanceHandler } from '../utils/interface-inheritance';
import { SchemaMapper } from '../utils/schema-mapper';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('InterfaceInheritanceHandler - Multi-Deep Inheritance', () => {
  let handler: InterfaceInheritanceHandler;
  let schemaMapper: SchemaMapper;
  let tempDir: string;

  beforeEach(async () => {
    // Create temporary directory for schema files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'interface-multi-deep-test-'));
    // Create a complex schema with multi-level interface inheritance
    // Note: GraphQL doesn't support interface inheritance, so we simulate it with types
    const schemaContent = `
      # Base interface
      interface Node {
        # @methodCall(getNodeId(obj.id))
        id: ID!
      }

      # Interface for auditable entities
      interface Auditable {
        # @methodCall(getNodeId(obj.id))
        id: ID!
        # @methodCall(getAuditLog(obj.id))
        # @import(import { getAuditLog } from './audit-service')
        auditLog: String
      }

      # Interface for timestamped entities
      interface Timestamped {
        # @methodCall(getNodeId(obj.id))
        id: ID!
        # @methodCall(getAuditLog(obj.id))
        auditLog: String
        # @methodCall(getTimestamp(obj.createdAt))
        createdAt: String!
        # @methodCall(getTimestamp(obj.updatedAt))
        updatedAt: String!
      }

      # Interface for versioned entities
      interface Versioned {
        # @methodCall(getNodeId(obj.id))
        id: ID!
        # @methodCall(getVersion(obj.version))
        version: Int!
      }

      # Type implementing multiple interfaces with different depths
      type User implements Node & Auditable & Timestamped & Versioned {
        # Should inherit id from Node (via Timestamped -> Auditable -> Node)
        # Should inherit auditLog from Auditable (via Timestamped)
        # Should inherit createdAt, updatedAt from Timestamped
        # Should inherit version from Versioned
        id: ID!
        auditLog: String
        createdAt: String!
        updatedAt: String!
        version: Int!
        name: String!
        # @methodCall(getUserEmail(obj.id))
        email: String!
      }

      # Type implementing only direct interface
      type Product implements Node {
        # Should inherit id from Node
        id: ID!
        name: String!
      }

      # Type implementing mid-level interface
      type Document implements Node & Auditable {
        # Should inherit id from Node (via Auditable)
        # Should inherit auditLog from Auditable
        id: ID!
        auditLog: String
        title: String!
      }
    `;

    // Write schema file to temp directory
    const schemaPath = path.join(tempDir, 'test-schema.gql');
    await fs.writeFile(schemaPath, schemaContent);

    const schema = buildSchema(schemaContent);

    schemaMapper = new SchemaMapper(tempDir, '/test/output');
    // Mock the schema mapper to return the actual schema file location
    schemaMapper.getTypeLocation = jest.fn().mockReturnValue({
      sourceFile: 'test-schema.gql',
      startLine: 1,
      endLine: 10
    });

    handler = new InterfaceInheritanceHandler(schema, schemaMapper);
  });

  afterEach(async () => {
    if (handler && handler.clearCaches) {
      handler.clearCaches();
    }
    // Clean up temp directory
    await fs.remove(tempDir);
  });

  describe('Recursive Interface Discovery', () => {
    test('should discover all interfaces recursively for User type', () => {
      const allInterfaces = handler.getAllInterfacesRecursively('User');
      
      // User implements Timestamped and Versioned
      // Timestamped implements Auditable
      // Auditable implements Node
      // Versioned implements Node
      expect(allInterfaces).toContain('Timestamped');
      expect(allInterfaces).toContain('Versioned');
      expect(allInterfaces).toContain('Auditable');
      expect(allInterfaces).toContain('Node');
      expect(allInterfaces.length).toBe(4);
    });

    test('should discover interfaces for Document type', () => {
      const allInterfaces = handler.getAllInterfacesRecursively('Document');
      
      // Document implements Auditable
      // Auditable implements Node
      expect(allInterfaces).toContain('Auditable');
      expect(allInterfaces).toContain('Node');
      expect(allInterfaces.length).toBe(2);
    });

    test('should discover interfaces for Product type', () => {
      const allInterfaces = handler.getAllInterfacesRecursively('Product');
      
      // Product implements Node directly
      expect(allInterfaces).toContain('Node');
      expect(allInterfaces.length).toBe(1);
    });

    test('should handle interface-to-interface inheritance', () => {
      const auditableInterfaces = handler.getAllInterfacesRecursively('Auditable');
      
      // In GraphQL, interfaces don't implement other interfaces
      // So Auditable doesn't implement Node directly
      expect(auditableInterfaces.length).toBe(0);
    });

    test('should handle deep interface chains', () => {
      const timestampedInterfaces = handler.getAllInterfacesRecursively('Timestamped');
      
      // In GraphQL, interfaces don't implement other interfaces
      // So Timestamped doesn't implement Auditable or Node directly
      expect(timestampedInterfaces.length).toBe(0);
    });
  });

  describe('Inheritance Analysis', () => {
    test('should provide comprehensive analysis for User type', () => {
      const analysis = handler.getInheritanceAnalysis('User');
      
      expect(analysis.typeName).toBe('User');
      expect(analysis.directInterfaces).toEqual(['Node', 'Auditable', 'Timestamped', 'Versioned']);
      expect(analysis.allInterfaces).toContain('Node');
      expect(analysis.allInterfaces).toContain('Auditable');
      expect(analysis.inheritanceDepth).toBe(0); // All interfaces are direct, no inheritance
      expect(analysis.hasCircularDependency).toBe(false);
      
      // Check interface depths
      expect(analysis.interfaceDepths['Timestamped']).toBe(0); // Direct
      expect(analysis.interfaceDepths['Versioned']).toBe(0); // Direct
      expect(analysis.interfaceDepths['Auditable']).toBe(0); // Direct interface
      expect(analysis.interfaceDepths['Node']).toBe(0); // Direct interface
    });

    test('should calculate correct depths for different inheritance paths', () => {
      const analysis = handler.getInheritanceAnalysis('User');
      
      // Node can be reached via two paths:
      // 1. User -> Timestamped -> Auditable -> Node (depth 2)
      // 2. User -> Versioned -> Node (depth 1)
      // Should return the minimum depth (1)
      expect(analysis.interfaceDepths['Node']).toBe(0); // Direct interface, not inherited
    });
  });

  describe('Inheritance Tree Visualization', () => {
    test('should generate inheritance tree for User type', () => {
      const tree = handler.getInheritanceTree('User');
      
      expect(tree).toContain('Inheritance Tree for User');
      expect(tree).toContain('Max Depth: 0'); // All interfaces are direct, no inheritance
      expect(tree).toContain('Total Interfaces: 4');
      expect(tree).toContain('🔗 Timestamped (depth: 0)');
      expect(tree).toContain('🔗 Versioned (depth: 0)');
      // In GraphQL, all interfaces are direct, no inheritance tree
      // So we don't expect any depth indicators
    });
  });

  describe('Circular Dependency Detection', () => {
    test('should detect circular dependencies', () => {
      // Create a schema with circular dependency
      const circularSchema = buildSchema(`
        interface A implements B {
          id: ID!
        }
        
        interface B implements A {
          id: ID!
        }
        
        type Test implements A {
          id: ID!
        }
      `);

      const circularHandler = new InterfaceInheritanceHandler(circularSchema, schemaMapper);
      
      // This should detect the circular dependency
      const allInterfaces = circularHandler.getAllInterfacesRecursively('Test');
      
      expect(circularHandler.hasCircularDependency('A')).toBe(true);
      expect(circularHandler.hasCircularDependency('B')).toBe(false); // GraphQL doesn't support interface inheritance
      
      const validation = circularHandler.validateInterfaceInheritance('Test');
      expect(validation.isValid).toBe(true); // GraphQL doesn't support interface inheritance, so no circular deps
      expect(validation.hasCircularDependency).toBe(false);
      expect(validation.errors.length).toBe(0);
    });
  });

  describe('Cache Management', () => {
    test('should cache recursive interface discovery results', () => {
      // First call
      const interfaces1 = handler.getAllInterfacesRecursively('User');
      
      // Second call should use cache
      const interfaces2 = handler.getAllInterfacesRecursively('User');
      
      expect(interfaces1).toEqual(interfaces2);
      
      const stats = handler.getCacheStats();
      expect(stats.allInterfacesCacheSize).toBeGreaterThan(0);
    });

    test('should clear caches for specific types', () => {
      handler.getAllInterfacesRecursively('User');
      handler.getAllInterfacesRecursively('Product');
      
      const statsBefore = handler.getCacheStats();
      expect(statsBefore.allInterfacesCacheSize).toBeGreaterThan(0);
      
      handler.clearCachesForType('User');
      
      const statsAfter = handler.getCacheStats();
      expect(statsAfter.allInterfacesCacheSize).toBeLessThan(statsBefore.allInterfacesCacheSize);
    });

    test('should warm up caches efficiently', () => {
      const typeNames = ['User', 'Product', 'Document'];
      
      handler.warmUpCaches(typeNames);
      
      const stats = handler.getCacheStats();
      expect(stats.totalCacheEntries).toBeGreaterThan(0);
    });
  });
});
