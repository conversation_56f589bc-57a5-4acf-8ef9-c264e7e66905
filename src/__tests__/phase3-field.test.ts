import { DecoratorParser } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 3: @GQLField Processing', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase3-field-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Field directive processing', () => {
    it('should process @GQLField decorators', async () => {
      const fieldContent = `
        @GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
        export interface UserMetadata {
          createdAt: Date;
          updatedAt: Date;
          lastLogin?: Date;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'field-definitions.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.ref).toBe('User');
      expect(fieldData.name).toBe('metadata');
      expect(fieldData.type).toBe('UserMetadata');
    });

    it('should handle optional fields', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "debugInfo", 
          type: "DebugInfo", 
          optional: true
        })
        export interface DebugInfo {
          queries: number;
          mutations: number;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'optional-field.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.optional).toBe(true);
    });

    it('should handle hidden fields', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "internalId", 
          type: "string", 
          hidden: true
        })
        export type UserInternalId = string;
      `;

      await fs.writeFile(path.join(tempDir, 'hidden-field.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.hidden).toBe(true);
    });

    it('should handle import paths for custom types', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "Product", 
          name: "pricing", 
          type: "PricingInfo",
          importPath: "../types/pricing"
        })
        export interface PricingInfo {
          basePrice: number;
          currency: string;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'import-field.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.importPath).toBe('../types/pricing');
    });
  });

  describe('Field directive conversion', () => {
    it('should convert @GQLField to FieldDirectiveField', async () => {
      const fieldContent = `
        @GQLField({ ref: "User", name: "permissions", type: "UserPermissions[]" })
        export interface UserPermissions {
          resource: string;
          actions: string[];
        }
      `;

      await fs.writeFile(path.join(tempDir, 'permissions.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.fieldFields).toHaveLength(1);

      const fieldDirective = directives.fieldFields[0];
      expect(fieldDirective.name).toBe('permissions');
      expect(fieldDirective.type).toBe('UserPermissions[]');
      expect(fieldDirective.raw).toBe('permissions: UserPermissions[]');
    });

    it('should handle optional field syntax', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "avatar", 
          type: "string", 
          optional: true
        })
        export type UserAvatar = string;
      `;

      await fs.writeFile(path.join(tempDir, 'optional.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.fieldFields).toHaveLength(1);

      const fieldDirective = directives.fieldFields[0];
      expect(fieldDirective.optional).toBe(true);
      expect(fieldDirective.raw).toBe('avatar?: string');
    });

    it('should preserve hidden field information', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "secret", 
          type: "string", 
          hidden: true
        })
        export type UserSecret = string;
      `;

      await fs.writeFile(path.join(tempDir, 'hidden.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.fieldFields).toHaveLength(1);

      const fieldDirective = directives.fieldFields[0];
      expect(fieldDirective.hidden).toBe(true);
    });
  });

  describe('Field filtering by type', () => {
    it('should filter field directives by ref type', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
        }
        type Product {
          id: ID!
          title: String!
        }
      `);

      const fieldContent = `
        @GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
        export interface UserMetadata {
          createdAt: Date;
        }

        @GQLField({ ref: "Product", name: "pricing", type: "ProductPricing" })
        export interface ProductPricing {
          price: number;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'multi-fields.ts'), fieldContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Wait for initialization to complete by calling generate
      // This ensures decorator scanning is complete
      await generator.generate();

      // Test filtering for User type
      const userDirectives = generator.getDecoratorDirectives('User');
      expect(userDirectives.fieldFields).toHaveLength(1);
      expect(userDirectives.fieldFields[0].name).toBe('metadata');

      // Test filtering for Product type
      const productDirectives = generator.getDecoratorDirectives('Product');
      expect(productDirectives.fieldFields).toHaveLength(1);
      expect(productDirectives.fieldFields[0].name).toBe('pricing');
    });
  });

  describe('Hidden field handling', () => {
    it('should skip resolver generation for hidden fields', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
          secret: String!
        }
      `);

      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "secret", 
          type: "string", 
          hidden: true
        })
        export type UserSecret = string;
      `;

      await fs.writeFile(path.join(tempDir, 'hidden-field.ts'), fieldContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize to scan decorators
      await generator.generate();

      // Check that hidden field directive is available but marked as hidden
      const userDirectives = generator.getDecoratorDirectives('User', 'secret');
      expect(userDirectives.fieldFields).toHaveLength(1);
      expect(userDirectives.fieldFields[0].hidden).toBe(true);

      // Verify that no resolver file was generated for the hidden field
      const secretResolverPath = path.join(tempDir, 'output', 'user', 'secret.ts');
      expect(fs.existsSync(secretResolverPath)).toBe(false);
    });
  });

  describe('Schema-specific fields', () => {
    it('should handle schema-specific field decorators', async () => {
      const fieldContent = `
        @GQLField({ 
          ref: "User", 
          name: "adminData", 
          type: "AdminData",
          schema: "admin"
        })
        export interface AdminData {
          permissions: string[];
          lastAccess: Date;
        }

        @GQLField({ 
          ref: "User", 
          name: "publicData", 
          type: "PublicData",
          schema: "public"
        })
        export interface PublicData {
          displayName: string;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'schema-fields.ts'), fieldContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Test admin schema
      const adminDirectives = processor.convertToDirectiveContainer(result, 'admin');
      expect(adminDirectives.fieldFields).toHaveLength(1);
      expect(adminDirectives.fieldFields[0].name).toBe('adminData');

      // Test public schema
      const publicDirectives = processor.convertToDirectiveContainer(result, 'public');
      expect(publicDirectives.fieldFields).toHaveLength(1);
      expect(publicDirectives.fieldFields[0].name).toBe('publicData');

      // Test default schema (should be empty)
      const defaultDirectives = processor.convertToDirectiveContainer(result, 'default');
      expect(defaultDirectives.fieldFields).toHaveLength(0);
    });
  });

  describe('Integration with existing field directives', () => {
    it('should work alongside comment-based field directives', async () => {
      const schema = buildSchema(`
        type UserMetadata {
          createdAt: String
          updatedAt: String
        }

        type User {
          id: ID!
          name: String!
          metadata: UserMetadata
        }
      `);

      // Create a schema file with comment-based field directive
      const schemaContent = `
        type User {
          id: ID!
          name: String!
          # @field(metadata: UserMetadata, path: "./types/user-metadata")
          metadata: UserMetadata
        }
      `;

      await fs.writeFile(path.join(tempDir, 'user.gql'), schemaContent);

      // Create decorator-based field
      const fieldContent = `
        @GQLField({ ref: "User", name: "profile", type: "UserProfile" })
        export interface UserProfile {
          bio: string;
          avatar: string;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'decorator-fields.ts'), fieldContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Wait for initialization to complete by calling generate
      await generator.generate();

      // Both comment-based and decorator-based fields should be available
      const userDirectives = generator.getDecoratorDirectives('User');
      expect(userDirectives.fieldFields).toHaveLength(1); // Only decorator field
      expect(userDirectives.fieldFields[0].name).toBe('profile');
    });
  });
});
