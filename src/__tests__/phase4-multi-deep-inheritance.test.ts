import { GraphQLSchema, buildSchema } from 'graphql';
import { InterfaceInheritanceHandler } from '../utils/interface-inheritance';
import { SchemaMapper } from '../utils/schema-mapper';
import { DecoratorContainer } from '../utils/decorator-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 4: Multi-Deep Inheritance with Decorators', () => {
  let tempDir: string;
  let schemaMapper: SchemaMapper;
  let inheritanceHandler: InterfaceInheritanceHandler;
  let schema: GraphQLSchema;

  beforeEach(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'gql-generator-test-'));

    // Create test schema with multi-level inheritance
    const schemaContent = `
      # Level 1 - Base interface
      interface Node {
        id: ID!
      }

      # Level 2 - Extends Node
      interface Auditable implements Node {
        id: ID!
        createdAt: String!
        updatedAt: String!
      }

      # Level 3 - Extends Auditable (which extends Node)
      interface Versioned implements Auditable {
        id: ID!
        createdAt: String!
        updatedAt: String!
        version: Int!
      }

      # Level 4 - Concrete type implementing Versioned
      type Document implements Versioned {
        id: ID!
        createdAt: String!
        updatedAt: String!
        version: Int!
        title: String!
        content: String!
      }

      # Another branch - Level 2
      interface Timestamped implements Node {
        id: ID!
        timestamp: String!
      }

      # Level 3 - Multiple inheritance
      type Event implements Node & Timestamped {
        id: ID!
        timestamp: String!
        name: String!
        description: String!
      }

      type Query {
        document(id: ID!): Document
        event(id: ID!): Event
      }
    `;

    const schemaPath = path.join(tempDir, 'schema.gql');
    await fs.writeFile(schemaPath, schemaContent);

    // Build GraphQL schema
    schema = buildSchema(schemaContent);

    // Create schema mapper
    schemaMapper = new SchemaMapper(tempDir, '/test/output');
    schemaMapper.analyzeSchema(schema);

    // Create inheritance handler
    inheritanceHandler = new InterfaceInheritanceHandler(schema, schemaMapper);
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('Multi-Level Interface Inheritance', () => {
    it('should identify all interfaces in deep inheritance chains', () => {
      // Test Document type (implements Versioned -> Auditable -> Node)
      const documentInterfaces = inheritanceHandler.getAllInterfacesRecursively('Document');
      expect(documentInterfaces).toContain('Versioned');
      expect(documentInterfaces).toContain('Auditable');
      expect(documentInterfaces).toContain('Node');

      // Test Event type (implements Node & Timestamped)
      const eventInterfaces = inheritanceHandler.getAllInterfacesRecursively('Event');
      expect(eventInterfaces).toContain('Node');
      expect(eventInterfaces).toContain('Timestamped');
    });

    it('should maintain proper precedence in multi-level inheritance', () => {
      // Direct interfaces should come first
      const documentDirectInterfaces = inheritanceHandler.getImplementedInterfaces('Document');
      expect(documentDirectInterfaces).toEqual(['Versioned']);

      // All interfaces should include the full chain
      const allDocumentInterfaces = inheritanceHandler.getAllInterfacesRecursively('Document');
      expect(allDocumentInterfaces.length).toBeGreaterThan(1);
    });

    it('should validate complex inheritance chains', () => {
      const validation = inheritanceHandler.validateInterfaceInheritance('Document');
      expect(validation.isValid).toBe(true);
      expect(validation.hasCircularDependency).toBe(false);
      expect(validation.allInterfaces).toContain('Node');
      expect(validation.allInterfaces).toContain('Auditable');
      expect(validation.allInterfaces).toContain('Versioned');
    });
  });

  describe('Decorator Integration with Multi-Level Inheritance', () => {
    it('should handle decorators at different inheritance levels', async () => {
      // Create decorator metadata for different levels of the inheritance chain
      const decoratorMetadata: DecoratorContainer = {
        methodCalls: [
          // Level 1 - Node interface
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "Node", field: "id", call: "getNodeId(obj)" }',
              raw: '@GQLMethodCall({ type: "Node", field: "id", call: "getNodeId(obj)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 1,
              target: 'function getNodeId',
            },
            data: {
              type: 'Node',
              field: 'id',
              call: 'getNodeId(obj)',
            },
          },
          // Level 2 - Auditable interface
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "Auditable", field: "createdAt", call: "getCreatedAt(obj.id)" }',
              raw: '@GQLMethodCall({ type: "Auditable", field: "createdAt", call: "getCreatedAt(obj.id)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 5,
              target: 'function getCreatedAt',
            },
            data: {
              type: 'Auditable',
              field: 'createdAt',
              call: 'getCreatedAt(obj.id)',
            },
          },
          // Level 3 - Versioned interface
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "Versioned", field: "version", call: "getVersion(obj.id)" }',
              raw: '@GQLMethodCall({ type: "Versioned", field: "version", call: "getVersion(obj.id)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 9,
              target: 'function getVersion',
            },
            data: {
              type: 'Versioned',
              field: 'version',
              call: 'getVersion(obj.id)',
            },
          },
        ],
        imports: [
          {
            decorator: {
              name: 'GQLImport',
              arguments: '"import { NodeService } from \'../services/node\'"',
              raw: '@GQLImport("import { NodeService } from \'../services/node\'")',
              filePath: '/test/decorators.ts',
              lineNumber: 13,
              target: 'function getNodeId',
            },
            data: {
              importStatement: 'import { NodeService } from \'../services/node\'',
            },
          },
        ],
        fields: [],
        contexts: [],
        others: {},
      };

      inheritanceHandler.setDecoratorMetadata(decoratorMetadata);

      // Test that decorator metadata is properly set
      expect(inheritanceHandler['decoratorMetadata']).toBe(decoratorMetadata);
      expect(inheritanceHandler['decoratorProcessor']).toBeDefined();
    });

    it('should find inherited directives from multiple levels', async () => {
      const decoratorMetadata: DecoratorContainer = {
        methodCalls: [
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "Node", field: "id", call: "getNodeId(obj)" }',
              raw: '@GQLMethodCall({ type: "Node", field: "id", call: "getNodeId(obj)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 1,
              target: 'function getNodeId',
            },
            data: {
              type: 'Node',
              field: 'id',
              call: 'getNodeId(obj)',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      inheritanceHandler.setDecoratorMetadata(decoratorMetadata);

      // Test finding inherited directives for Document type (which inherits from Node through multiple levels)
      const inheritedDirectives = await inheritanceHandler.findInheritedDirectives('Document', 'id');

      // The directive should be found even though it's defined on a base interface several levels up
      expect(inheritedDirectives).toBeDefined();
    });

    it('should handle complex inheritance analysis with decorators', async () => {
      const decoratorMetadata: DecoratorContainer = {
        methodCalls: [
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "Auditable", field: "createdAt", call: "getAuditableCreatedAt(obj.id)" }',
              raw: '@GQLMethodCall({ type: "Auditable", field: "createdAt", call: "getAuditableCreatedAt(obj.id)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 1,
              target: 'function getAuditableCreatedAt',
            },
            data: {
              type: 'Auditable',
              field: 'createdAt',
              call: 'getAuditableCreatedAt(obj.id)',
            },
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      inheritanceHandler.setDecoratorMetadata(decoratorMetadata);

      // Analyze inheritance conflicts for a field that exists in multiple levels
      const analysis = await inheritanceHandler.analyzeFieldInheritanceConflicts('Document', 'createdAt');

      expect(analysis.fieldName).toBe('createdAt');
      expect(analysis.interfacesWithDirectives).toBeDefined();
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle deep inheritance chains efficiently', () => {
      // Test performance with deep inheritance
      const start = Date.now();
      
      for (let i = 0; i < 100; i++) {
        inheritanceHandler.getAllInterfacesRecursively('Document');
      }
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
    });

    it('should detect circular dependencies in complex scenarios', () => {
      // The current schema doesn't have circular dependencies
      expect(inheritanceHandler.hasCircularDependency('Document')).toBe(false);
      expect(inheritanceHandler.hasCircularDependency('Event')).toBe(false);
    });

    it('should provide detailed inheritance information', () => {
      const details = inheritanceHandler.getInheritanceAnalysis('Document');
      
      expect(details.typeName).toBe('Document');
      expect(details.directInterfaces).toContain('Versioned');
      expect(details.allInterfaces).toContain('Node');
      expect(details.allInterfaces).toContain('Auditable');
      expect(details.allInterfaces).toContain('Versioned');
      expect(details.inheritanceDepth).toBeGreaterThan(0);
    });
  });
});
