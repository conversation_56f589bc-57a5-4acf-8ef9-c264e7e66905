import { DirectiveParser } from '../utils/directive-parser';
import { getGlobalOptimizedDirectiveParser, resetGlobalOptimizedDirectiveParser } from '../utils/optimized-directive-parser';
import { getGlobalCommentDirectiveCache, resetGlobalCommentDirectiveCache } from '../utils/comment-directive-cache';
import { LargeCodebaseTestGenerator, TestCodebasePresets } from '../utils/large-codebase-test-generator';
import { CommentDirectiveBenchmark } from '../utils/comment-directive-benchmark';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Comment Directive Performance Optimizations', () => {
  let tempDir: string;
  let testGenerator: LargeCodebaseTestGenerator;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'comment-directive-perf-test-'));
    testGenerator = new LargeCodebaseTestGenerator({
      outputDir: tempDir,
      enableLogging: false
    });
    
    // Reset global instances
    resetGlobalOptimizedDirectiveParser();
    resetGlobalCommentDirectiveCache();
  });

  afterEach(async () => {
    await fs.remove(tempDir);
    resetGlobalOptimizedDirectiveParser();
    resetGlobalCommentDirectiveCache();
  });

  describe('Cache System', () => {
    it('should cache file content and avoid repeated I/O', async () => {
      const cache = getGlobalCommentDirectiveCache();
      
      // Generate a test schema
      testGenerator.updateConfig(TestCodebasePresets.small);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFile = path.join(tempDir, schemaFiles.find(f => f.endsWith('.gql'))!);
      
      // First read should be a cache miss
      const content1 = await cache.getFileContent(testFile);
      expect(content1).toBeTruthy();
      
      const stats1 = cache.getStatistics();
      expect(stats1.fileContentCache.misses).toBe(1);
      expect(stats1.fileContentCache.hits).toBe(0);
      
      // Second read should be a cache hit
      const content2 = await cache.getFileContent(testFile);
      expect(content2).toBe(content1);
      
      const stats2 = cache.getStatistics();
      expect(stats2.fileContentCache.hits).toBe(1);
      expect(stats2.fileContentCache.misses).toBe(1);
    });

    it('should invalidate cache when file is modified', async () => {
      const cache = getGlobalCommentDirectiveCache();
      
      // Create a test file
      const testFile = path.join(tempDir, 'test.gql');
      await fs.writeFile(testFile, 'type Test { id: ID! }');
      
      // Read file (cache miss)
      const content1 = await cache.getFileContent(testFile);
      expect(content1).toContain('type Test');
      
      // Modify file
      await new Promise(resolve => setTimeout(resolve, 10)); // Ensure different mtime
      await fs.writeFile(testFile, 'type Test { id: ID!, name: String! }');
      
      // Read file again (should be cache miss due to mtime change)
      const content2 = await cache.getFileContent(testFile);
      expect(content2).toContain('name: String!');
      expect(content2).not.toBe(content1);
    });

    it('should respect cache size limits', async () => {
      const cache = getGlobalCommentDirectiveCache({
        maxFileContentEntries: 2,
        maxMemoryUsage: 1024 // Very small limit
      });
      
      // Create multiple test files
      const files = [];
      for (let i = 0; i < 5; i++) {
        const filePath = path.join(tempDir, `test${i}.gql`);
        await fs.writeFile(filePath, `type Test${i} { id: ID! }`);
        files.push(filePath);
      }
      
      // Read all files
      for (const file of files) {
        await cache.getFileContent(file);
      }
      
      const stats = cache.getStatistics();
      expect(stats.fileContentCache.entryCount).toBeLessThanOrEqual(2);
    });
  });

  describe('Optimized Parser', () => {
    it('should provide same results as legacy parser', async () => {
      // Generate test schema
      testGenerator.updateConfig(TestCodebasePresets.small);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFile = path.join(tempDir, schemaFiles.find(f => f.endsWith('.gql'))!);
      
      // Test with legacy parser
      process.env.ENABLE_DIRECTIVE_OPTIMIZATION = 'false';
      const legacyResult = await DirectiveParser.extractDirectivesFromSchema(testFile, 'Type0_0');
      
      // Test with optimized parser
      delete process.env.ENABLE_DIRECTIVE_OPTIMIZATION;
      const optimizedResult = await DirectiveParser.extractDirectivesFromSchema(testFile, 'Type0_0');
      
      // Results should be equivalent
      expect(optimizedResult.imports.length).toBe(legacyResult.imports.length);
      expect(optimizedResult.methodCalls.length).toBe(legacyResult.methodCalls.length);
      expect(optimizedResult.fieldFields.length).toBe(legacyResult.fieldFields.length);
    });

    it('should show performance improvement over legacy parser', async () => {
      // Generate medium-sized test schema
      testGenerator.updateConfig(TestCodebasePresets.medium);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFiles = schemaFiles
        .filter(f => f.endsWith('.gql'))
        .map(f => path.join(tempDir, f));
      
      // Benchmark legacy parser
      process.env.ENABLE_DIRECTIVE_OPTIMIZATION = 'false';
      const legacyStart = Date.now();
      
      for (const file of testFiles.slice(0, 3)) { // Test subset for speed
        for (let i = 0; i < 3; i++) {
          await DirectiveParser.extractDirectivesFromSchema(file, `Type0_${i}`);
          await DirectiveParser.extractDirectivesFromSchema(file, `Type0_${i}`, 'field0');
        }
      }
      
      const legacyTime = Date.now() - legacyStart;
      
      // Benchmark optimized parser
      delete process.env.ENABLE_DIRECTIVE_OPTIMIZATION;
      const optimizedParser = getGlobalOptimizedDirectiveParser();
      optimizedParser.clearCaches(); // Start fresh
      
      const optimizedStart = Date.now();
      
      for (const file of testFiles.slice(0, 3)) {
        for (let i = 0; i < 3; i++) {
          await DirectiveParser.extractDirectivesFromSchema(file, `Type0_${i}`);
          await DirectiveParser.extractDirectivesFromSchema(file, `Type0_${i}`, 'field0');
        }
      }
      
      const optimizedTime = Date.now() - optimizedStart;
      
      // Optimized should be faster (allowing for some variance in test environment)
      const speedup = legacyTime / optimizedTime;
      console.log(`Performance test: Legacy ${legacyTime}ms, Optimized ${optimizedTime}ms, Speedup: ${speedup.toFixed(2)}x`);
      
      // Should be at least 20% faster, but allow for test environment variance
      expect(speedup).toBeGreaterThan(1.2);
    }, 30000); // Longer timeout for performance test

    it('should handle batch extraction efficiently', async () => {
      const optimizedParser = getGlobalOptimizedDirectiveParser();
      
      // Generate test schema
      testGenerator.updateConfig(TestCodebasePresets.small);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFile = path.join(tempDir, schemaFiles.find(f => f.endsWith('.gql'))!);
      
      // Test batch extraction
      const requests = [
        { typeName: 'Type0_0' },
        { typeName: 'Type0_0', fieldName: 'field0' },
        { typeName: 'Type0_0', fieldName: 'field1' },
        { typeName: 'Type0_1' },
        { typeName: 'Type0_1', fieldName: 'field0' }
      ];
      
      const results = await optimizedParser.batchExtractDirectives(testFile, requests);
      
      expect(results.size).toBe(requests.length);
      expect(results.has('Type0_0')).toBe(true);
      expect(results.has('Type0_0.field0')).toBe(true);
      expect(results.has('Type0_1.field0')).toBe(true);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics', async () => {
      const optimizedParser = getGlobalOptimizedDirectiveParser();
      optimizedParser.resetMetrics();
      
      // Generate test schema
      testGenerator.updateConfig(TestCodebasePresets.small);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFile = path.join(tempDir, schemaFiles.find(f => f.endsWith('.gql'))!);
      
      // Perform some operations
      await optimizedParser.extractDirectivesFromSchema(testFile, 'Type0_0');
      await optimizedParser.extractDirectivesFromSchema(testFile, 'Type0_0', 'field0');
      await optimizedParser.extractDirectivesFromSchema(testFile, 'Type0_1');
      
      const metrics = optimizedParser.getMetrics();
      
      expect(metrics.totalCalls).toBe(3);
      expect(metrics.averageParseTime).toBeGreaterThan(0);
      expect(metrics.batchParseCount).toBeGreaterThan(0);
    });

    it('should provide cache statistics', async () => {
      const cache = getGlobalCommentDirectiveCache();
      const optimizedParser = getGlobalOptimizedDirectiveParser();
      
      // Generate test schema
      testGenerator.updateConfig(TestCodebasePresets.small);
      await testGenerator.generateTestCodebase();
      
      const schemaFiles = await fs.readdir(tempDir);
      const testFile = path.join(tempDir, schemaFiles.find(f => f.endsWith('.gql'))!);
      
      // Perform operations to generate cache activity
      await optimizedParser.extractDirectivesFromSchema(testFile, 'Type0_0');
      await optimizedParser.extractDirectivesFromSchema(testFile, 'Type0_0'); // Should hit cache
      
      const cacheStats = optimizedParser.getCacheStatistics();
      
      expect(cacheStats.directive).toBeDefined();
      expect(cacheStats.batch).toBeDefined();
      expect(cacheStats.invalidator).toBeDefined();
    });
  });

  describe('Integration Tests', () => {
    it('should work with real schema files', async () => {
      // Create a realistic schema file
      const schemaContent = `
# @import(import { UserService } from '../services/user-service')
type User {
  id: ID!
  # @methodCall(UserService.getName(obj.id))
  name: String!
  # @methodCall(UserService.getEmail(obj.id))
  # @import(import { EmailValidator } from '../utils/email-validator')
  email: String!
  # @field(metadata: UserMetadata)
  metadata: UserMetadata
}

type UserMetadata {
  createdAt: String!
  updatedAt: String!
}

type Query {
  # @methodCall(UserService.findById(args.id))
  user(id: ID!): User
  # @methodCall(UserService.findAll())
  users: [User!]!
}
      `;
      
      const schemaFile = path.join(tempDir, 'user.gql');
      await fs.writeFile(schemaFile, schemaContent);
      
      // Test directive extraction
      const userDirectives = await DirectiveParser.extractDirectivesFromSchema(schemaFile, 'User');
      expect(userDirectives.imports).toHaveLength(1);
      expect(userDirectives.imports[0].content).toContain('UserService');
      
      const nameDirectives = await DirectiveParser.extractDirectivesFromSchema(schemaFile, 'User', 'name');
      expect(nameDirectives.methodCalls).toHaveLength(1);
      expect(nameDirectives.methodCalls[0].content).toContain('UserService.getName');
      
      const emailDirectives = await DirectiveParser.extractDirectivesFromSchema(schemaFile, 'User', 'email');
      expect(emailDirectives.methodCalls).toHaveLength(1);
      expect(emailDirectives.imports).toHaveLength(1);
      expect(emailDirectives.imports[0].content).toContain('EmailValidator');
      
      const metadataDirectives = await DirectiveParser.extractDirectivesFromSchema(schemaFile, 'User', 'metadata');
      expect(metadataDirectives.fieldFields).toHaveLength(1);
      expect(metadataDirectives.fieldFields[0].raw).toContain('metadata: UserMetadata');
    });
  });

  describe('Error Handling', () => {
    it('should gracefully handle missing files', async () => {
      const nonExistentFile = path.join(tempDir, 'nonexistent.gql');
      
      const result = await DirectiveParser.extractDirectivesFromSchema(nonExistentFile, 'Test');
      
      expect(result.imports).toHaveLength(0);
      expect(result.methodCalls).toHaveLength(0);
      expect(result.fieldFields).toHaveLength(0);
    });

    it('should handle malformed schema files', async () => {
      const malformedFile = path.join(tempDir, 'malformed.gql');
      await fs.writeFile(malformedFile, 'this is not valid GraphQL syntax');
      
      const result = await DirectiveParser.extractDirectivesFromSchema(malformedFile, 'Test');
      
      // Should not throw and return empty results
      expect(result.imports).toHaveLength(0);
      expect(result.methodCalls).toHaveLength(0);
      expect(result.fieldFields).toHaveLength(0);
    });

    it('should fallback to legacy parser on optimization errors', async () => {
      // This test would require mocking the optimized parser to throw errors
      // For now, we'll just verify the fallback mechanism exists
      const result = await DirectiveParser.extractDirectivesFromSchema(
        path.join(tempDir, 'nonexistent.gql'), 
        'Test'
      );
      
      expect(result).toBeDefined();
    });
  });
});
