import { EnhancedWatchService } from '../core/enhanced-watch-service';
import { WatchService } from '../core/watch-service';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 4: Smart Watcher with Decorator Changes', () => {
  let tempDir: string;
  let schemaDir: string;
  let outputDir: string;
  let codebaseDir: string;

  beforeEach(async () => {
    // Create temporary directories
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'gql-generator-test-'));
    schemaDir = path.join(tempDir, 'schema');
    outputDir = path.join(tempDir, 'output');
    codebaseDir = path.join(tempDir, 'codebase');

    await fs.ensureDir(schemaDir);
    await fs.ensureDir(outputDir);
    await fs.ensureDir(codebaseDir);

    // Create test schema
    const schemaContent = `
      interface BaseEntity {
        id: ID!
        createdAt: String!
      }

      type User implements BaseEntity {
        id: ID!
        createdAt: String!
        name: String!
        email: String!
      }

      type Query {
        user(id: ID!): User
      }
    `;

    await fs.writeFile(path.join(schemaDir, 'schema.gql'), schemaContent);
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('Base WatchService with Decorators', () => {
    it('should include codebase directory in watch paths when decorators are enabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false, // Don't run initial generation
      });

      // Access private method for testing
      const watchPaths = (watchService as any).getWatchPaths();
      
      expect(watchPaths).toContain(path.resolve(codebaseDir));
    });

    it('should not include codebase directory when decorators are disabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: false,
        initial: false,
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      expect(watchPaths).not.toContain(path.resolve(codebaseDir));
    });

    it('should watch TypeScript files when decorators are enabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      // Access private method for testing
      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // TypeScript files should not be ignored when decorators are enabled
      expect(ignoredPatterns(path.join(codebaseDir, 'test.ts'), { isFile: () => true })).toBe(false);
      
      // Non-TypeScript files should be ignored
      expect(ignoredPatterns(path.join(codebaseDir, 'test.txt'), { isFile: () => true })).toBe(true);
    });

    it('should properly log decorator file changes', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      // Mock console.log to capture output
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Access private method for testing
      const logFileChange = (watchService as any).logFileChange.bind(watchService);
      
      // Test decorator file logging
      logFileChange('changed', path.join(codebaseDir, 'decorators.ts'));
      
      // Should log as decorator file
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('decorator file changed')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('EnhancedWatchService with Decorators', () => {
    it('should inherit codebase directory watching from base service', () => {
      const enhancedWatchService = new EnhancedWatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        enableBidirectionalSync: true,
        initial: false,
      });

      // The enhanced service should inherit the base functionality
      // We can't easily test the private methods, but we can verify the service is created
      expect(enhancedWatchService).toBeInstanceOf(EnhancedWatchService);
      expect(enhancedWatchService).toBeInstanceOf(WatchService);
    });

    it('should support bidirectional sync with decorator changes', async () => {
      const enhancedWatchService = new EnhancedWatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        enableBidirectionalSync: true,
        initial: false,
      });

      // Test that the service can be started and stopped without errors
      // In a real scenario, this would monitor both schema and codebase changes
      expect(async () => {
        await enhancedWatchService.start();
        await enhancedWatchService.stop();
      }).not.toThrow();
    });

    it('should handle decorator file patterns correctly', () => {
      const enhancedWatchService = new EnhancedWatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        enableBidirectionalSync: true,
        codeWatchPatterns: [path.join(codebaseDir, '**/*.ts')],
        initial: false,
      });

      // Access private method for testing
      const codeWatchPaths = (enhancedWatchService as any).getCodeWatchPaths();
      
      // Should include the output directory and custom patterns
      expect(codeWatchPaths.length).toBeGreaterThan(0);
    });

    it('should ignore infrastructure files by default', () => {
      const enhancedWatchService = new EnhancedWatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        enableBidirectionalSync: true,
        watchInfrastructureFiles: false,
        initial: false,
      });

      // Access private method for testing
      const ignoredPatterns = (enhancedWatchService as any).getCodeIgnoredPatterns();
      
      // Infrastructure files should be ignored
      expect(ignoredPatterns(path.join(outputDir, 'index.ts'), { isFile: () => true })).toBe(true);
      expect(ignoredPatterns(path.join(outputDir, 'graphql.ts'), { isFile: () => true })).toBe(true);
      
      // Regular resolver files should not be ignored
      expect(ignoredPatterns(path.join(outputDir, 'user-resolver.ts'), { isFile: () => true })).toBe(false);
    });

    it('should watch infrastructure files when enabled', () => {
      const enhancedWatchService = new EnhancedWatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        enableBidirectionalSync: true,
        watchInfrastructureFiles: true,
        initial: false,
      });

      // Access private method for testing
      const ignoredPatterns = (enhancedWatchService as any).getCodeIgnoredPatterns();
      
      // Infrastructure files should not be ignored when explicitly enabled
      expect(ignoredPatterns(path.join(outputDir, 'index.ts'), { isFile: () => true })).toBe(false);
      expect(ignoredPatterns(path.join(outputDir, 'graphql.ts'), { isFile: () => true })).toBe(false);
    });
  });

  describe('Integration with Decorator Changes', () => {
    it('should handle decorator file creation and modification', async () => {
      // Create a decorator file
      const decoratorFile = path.join(codebaseDir, 'user-decorators.ts');
      const decoratorContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      await fs.writeFile(decoratorFile, decoratorContent);

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      // Verify the file would be watched
      const watchPaths = (watchService as any).getWatchPaths();
      expect(watchPaths).toContain(path.resolve(codebaseDir));

      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      expect(ignoredPatterns(decoratorFile, { isFile: () => true })).toBe(false);
    });

    it('should handle multiple decorator files in subdirectories', async () => {
      // Create nested decorator files
      const subDir = path.join(codebaseDir, 'resolvers');
      await fs.ensureDir(subDir);

      const decoratorFile1 = path.join(subDir, 'user-decorators.ts');
      const decoratorFile2 = path.join(codebaseDir, 'base-decorators.ts');

      await fs.writeFile(decoratorFile1, '@GQLMethodCall({ type: "User", field: "email", call: "getUserEmail(obj.id)" })');
      await fs.writeFile(decoratorFile2, '@GQLMethodCall({ type: "BaseEntity", field: "createdAt", call: "getCreatedAt(obj.id)" })');

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // Both files should be watched
      expect(ignoredPatterns(decoratorFile1, { isFile: () => true })).toBe(false);
      expect(ignoredPatterns(decoratorFile2, { isFile: () => true })).toBe(false);
    });
  });
});
