import * as path from 'path';
import * as fs from 'fs-extra';
import { createProgram } from '../cli/commands';

describe('Windows Path Handling', () => {
  let tempDir: string;
  let originalPlatform: string;

  beforeEach(async () => {
    // Create a temporary directory for testing
    tempDir = await fs.mkdtemp(path.join(require('os').tmpdir(), 'gql-generator-test-'));
    originalPlatform = process.platform;
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
    // Restore original platform
    Object.defineProperty(process, 'platform', {
      value: originalPlatform,
      writable: true,
      configurable: true
    });
  });

  describe('Path Resolution', () => {
    it('should handle Windows absolute paths with drive letters', () => {
      // Mock Windows platform
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        writable: true,
        configurable: true
      });

      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      
      expect(generateCommand).toBeDefined();

      // Test that Windows paths are accepted without throwing errors
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', 'C:\\Users\\<USER>\\schema',
        '--output', 'C:\\Users\\<USER>\\output'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
      expect(Array.isArray(capturedOptions.schema) ? capturedOptions.schema.join(' ') : capturedOptions.schema).toContain('C:');
      expect(Array.isArray(capturedOptions.output) ? capturedOptions.output.join(' ') : capturedOptions.output).toContain('C:');
    });

    it('should handle UNC paths on Windows', () => {
      // Mock Windows platform
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        writable: true,
        configurable: true
      });

      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', '\\\\server\\share\\schema',
        '--output', '\\\\server\\share\\output'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
    });

    it('should normalize mixed path separators', () => {
      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', 'C:\\Users/test\\schema',
        '--output', 'C:/Users\\test/output'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
    });
  });

  describe('Cross-Platform Compatibility', () => {
    it('should work on Unix-like systems', () => {
      // Mock Unix platform
      Object.defineProperty(process, 'platform', {
        value: 'linux',
        writable: true,
        configurable: true
      });

      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', '/home/<USER>/schema',
        '--output', '/home/<USER>/output'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
      expect(Array.isArray(capturedOptions.schema) ? capturedOptions.schema : [capturedOptions.schema]).toContain('/home/<USER>/schema');
      expect(Array.isArray(capturedOptions.output) ? capturedOptions.output : [capturedOptions.output]).toContain('/home/<USER>/output');
    });

    it('should work on macOS', () => {
      // Mock macOS platform
      Object.defineProperty(process, 'platform', {
        value: 'darwin',
        writable: true,
        configurable: true
      });

      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', '/Users/<USER>/schema',
        '--output', '/Users/<USER>/output'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
      expect(Array.isArray(capturedOptions.schema) ? capturedOptions.schema : [capturedOptions.schema]).toContain('/Users/<USER>/schema');
      expect(Array.isArray(capturedOptions.output) ? capturedOptions.output : [capturedOptions.output]).toContain('/Users/<USER>/output');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid paths gracefully', () => {
      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', 'invalid<>path',
        '--output', 'invalid|path'
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      // Should not throw during parsing, but may warn during path resolution
      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
    });

    it('should handle non-existent paths gracefully', () => {
      const program = createProgram();
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', path.join(tempDir, 'non-existent-schema'),
        '--output', path.join(tempDir, 'non-existent-output')
      ];

      let capturedOptions: any;
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      expect(() => {
        program.parse(testArgs);
      }).not.toThrow();

      expect(capturedOptions).toBeDefined();
    });
  });
});
