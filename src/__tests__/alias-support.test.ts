import { DecoratorParser, DecoratorContainer } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { AliasConfig, AliasConfigUtils } from '../utils/alias-config';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Alias Support for Import Paths', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'alias-support-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('AliasConfigUtils', () => {
    it('should generate default alias from directory name', () => {
      const alias = AliasConfigUtils.generateDefaultAlias('/project/src');
      expect(alias).toBe('@src');
    });

    it('should handle special characters in directory names', () => {
      const alias = AliasConfigUtils.generateDefaultAlias('/project/my-app_core');
      expect(alias).toBe('@my-app_core'); // Hyphens are preserved, underscores are preserved
    });

    it('should validate alias format correctly', () => {
      expect(AliasConfigUtils.validateAlias('@src')).toBe(true);
      expect(AliasConfigUtils.validateAlias('@user_codebase')).toBe(true);
      expect(AliasConfigUtils.validateAlias('@my-app/core')).toBe(true);
      
      // Invalid formats
      expect(AliasConfigUtils.validateAlias('src')).toBe(false); // Missing @
      expect(AliasConfigUtils.validateAlias('@src/../other')).toBe(false); // Path traversal
      expect(AliasConfigUtils.validateAlias('@src//')).toBe(false); // Double slash
      expect(AliasConfigUtils.validateAlias('@src/')).toBe(false); // Trailing slash
    });

    it('should create alias config correctly', () => {
      const config = AliasConfigUtils.createAliasConfig('/project/src', '@custom');
      expect(config).toEqual({
        codebaseAlias: '@custom',
        codebasePath: '/project/src'
      });
    });

    it('should return null when no codebase path provided', () => {
      const config = AliasConfigUtils.createAliasConfig(undefined, '@custom');
      expect(config).toBeNull();
    });

    it('should detect files within codebase correctly', () => {
      const codebasePath = '/project/src';
      expect(AliasConfigUtils.isWithinCodebase('/project/src/resolvers/user.ts', codebasePath)).toBe(true);
      expect(AliasConfigUtils.isWithinCodebase('/project/other/file.ts', codebasePath)).toBe(false);
      expect(AliasConfigUtils.isWithinCodebase('/other/project/file.ts', codebasePath)).toBe(false);
    });

    it('should convert file paths to alias imports', () => {
      const aliasConfig: AliasConfig = {
        codebaseAlias: '@src',
        codebasePath: '/project/src'
      };

      const aliasImport = AliasConfigUtils.convertToAliasImport('/project/src/resolvers/user.ts', aliasConfig);
      expect(aliasImport).toBe('@src/resolvers/user');
    });

    it('should return null for files outside codebase', () => {
      const aliasConfig: AliasConfig = {
        codebaseAlias: '@src',
        codebasePath: '/project/src'
      };

      const aliasImport = AliasConfigUtils.convertToAliasImport('/project/other/file.ts', aliasConfig);
      expect(aliasImport).toBeNull();
    });
  });

  describe('DecoratorProcessor with Alias Support', () => {
    it('should generate alias-based imports when configured', async () => {
      // Create test resolver file
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Set alias configuration
      const aliasConfig = AliasConfigUtils.createAliasConfig(tempDir, '@codebase');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart import
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      const importStatement = processor.generateSmartImport(resolverPath, targetOutputPath, 'getUserName');

      expect(importStatement).toBe("import { getUserName } from '@codebase/resolvers/user';");
    });

    it('should fall back to relative paths for files outside codebase', async () => {
      // Create test resolver file outside codebase
      const externalDir = path.join(tempDir, 'external');
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      const resolverPath = path.join(externalDir, 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan external directory
      const result = await parser.scanCodebase(externalDir);
      processor.setDecoratorMetadata(result);

      // Set alias configuration for different codebase
      const codebaseDir = path.join(tempDir, 'src');
      const aliasConfig = AliasConfigUtils.createAliasConfig(codebaseDir, '@src');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart import
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      const importStatement = processor.generateSmartImport(resolverPath, targetOutputPath, 'getUserName');

      // Should use relative path since file is outside configured codebase
      expect(importStatement).toContain('from \'../');
      expect(importStatement).not.toContain('@src');
    });

    it('should work without alias configuration', async () => {
      // Create test resolver file
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Don't set alias configuration

      // Generate smart import
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      const importStatement = processor.generateSmartImport(resolverPath, targetOutputPath, 'getUserName');

      // Should use relative path
      expect(importStatement).toContain('from \'../');
      expect(importStatement).not.toContain('@');
    });

    it('should handle nested directory structures', async () => {
      // Create nested resolver structure
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "profile", call: "getUserProfile(obj.id)" })
        export const getUserProfile = (id: string) => ({ bio: "User bio" });
      `;

      const resolverPath = path.join(tempDir, 'src', 'modules', 'user', 'resolvers', 'profile.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(path.join(tempDir, 'src'));
      processor.setDecoratorMetadata(result);

      // Set alias configuration
      const aliasConfig = AliasConfigUtils.createAliasConfig(path.join(tempDir, 'src'), '@app');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart import
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'profile.ts');
      const importStatement = processor.generateSmartImport(resolverPath, targetOutputPath, 'getUserProfile');

      expect(importStatement).toBe("import { getUserProfile } from '@app/modules/user/resolvers/profile';");
    });

    it('should handle different file extensions', async () => {
      // Test with .tsx file
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "avatar", call: "getUserAvatar(obj.id)" })
        export const getUserAvatar = (id: string) => ({ url: "avatar.jpg" });
      `;

      const resolverPath = path.join(tempDir, 'components', 'user.tsx');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Set alias configuration
      const aliasConfig = AliasConfigUtils.createAliasConfig(tempDir, '@components');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart import
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'avatar.ts');
      const importStatement = processor.generateSmartImport(resolverPath, targetOutputPath, 'getUserAvatar');

      expect(importStatement).toBe("import { getUserAvatar } from '@components/components/user';");
    });
  });

  describe('Cross-platform compatibility', () => {
    it('should normalize path separators to forward slashes', () => {
      // Test with Unix-style paths (which should work on all platforms)
      const aliasConfig: AliasConfig = {
        codebaseAlias: '@src',
        codebasePath: '/project/src'
      };

      const filePath = '/project/src/resolvers/user.ts';
      const aliasImport = AliasConfigUtils.convertToAliasImport(filePath, aliasConfig);

      // Should use forward slashes
      expect(aliasImport).toBe('@src/resolvers/user');
      expect(aliasImport).not.toContain('\\');
    });
  });
});
