import { DecoratorParser, DecoratorContainer } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 3: @GQLMethodCall Complete Implementation', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'gql-test-'));
    // Create a proper project structure that won't be filtered out
    const srcDir = path.join(tempDir, 'src', 'resolvers');
    await fs.ensureDir(srcDir);

    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Complex @GQLMethodCall scenarios', () => {
    it('should handle complex method calls with context parameters', async () => {
      const resolverContent = `
        @GQLMethodCall({ type: "Query", field: "user", call: "getUserById({id: args.id, context})" })
        export const getUserById = ({id, context}: {id: string, context: any}) => ({
          id, name: "Test User", email: "<EMAIL>"
        });
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'complex-resolver.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.methodCalls).toHaveLength(1);

      const methodCall = result.methodCalls[0];
      expect(methodCall.data.type).toBe('Query');
      expect(methodCall.data.field).toBe('user');
      expect(methodCall.data.call).toBe('getUserById({id: args.id, context})');
    });

    it('should handle method calls with type casting', async () => {
      const resolverContent = `
        @GQLMethodCall({ 
          type: "Query", 
          field: "users", 
          call: "getAllUsers() as User[]",
          enableTypeCasting: true
        })
        export const getAllUsers = () => [
          { id: "1", name: "User 1", email: "<EMAIL>" }
        ];
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'user-resolver.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.methodCalls).toHaveLength(1);
      expect(directives.methodCalls[0].content).toBe('getAllUsers() as User[]');
    });

    it('should handle async method calls', async () => {
      const resolverContent = `
        @GQLMethodCall({ 
          type: "User", 
          field: "email", 
          call: "getUserEmail(obj.id)",
          async: true
        })
        export const getUserEmail = async (userId: string) => \`user-\${userId}@example.com\`;
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'async-resolver.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.methodCalls).toHaveLength(1);
      expect(directives.methodCalls[0].content).toBe('await getUserEmail(obj.id)');
    });

    it('should generate smart imports for method calls', async () => {
      const resolverContent = `
        @GQLMethodCall({ type: "Product", field: "price", call: "calculatePrice(obj.id, context.currency)" })
        export const calculatePrice = (id: string, currency: string) => 99.99;
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'product-resolver.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const targetOutputPath = path.join(tempDir, 'output', 'product', 'price.ts');
      const smartImports = processor.generateSmartImportsForTypeField(
        'Product',
        'price',
        targetOutputPath
      );

      expect(smartImports).toHaveLength(1);
      expect(smartImports[0]).toContain('import { calculatePrice }');
      expect(smartImports[0]).toContain('from');
    });
  });

  describe('Method call filtering', () => {
    it('should properly filter method calls by type and field', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
          email: String!
        }
        type Product {
          id: ID!
          title: String!
        }
        type Query {
          user: User
          product: Product
        }
      `);

      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;

        @GQLMethodCall({ type: "Product", field: "title", call: "getProductTitle(obj.id)" })
        export const getProductTitle = (id: string) => \`Product \${id}\`;

        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ id, name: "Test" });
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'multi-resolver.ts'), resolverContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize the generator to scan decorators
      await generator.generate();

      // Test filtering for User.name
      const userNameDirectives = generator.getDecoratorDirectives('User', 'name');
      expect(userNameDirectives.methodCalls).toHaveLength(1);
      expect(userNameDirectives.methodCalls[0].content).toBe('getUserName(obj.id)');

      // Test filtering for Product.title
      const productTitleDirectives = generator.getDecoratorDirectives('Product', 'title');
      expect(productTitleDirectives.methodCalls).toHaveLength(1);
      expect(productTitleDirectives.methodCalls[0].content).toBe('getProductTitle(obj.id)');

      // Test filtering for Query.user
      const queryUserDirectives = generator.getDecoratorDirectives('Query', 'user');
      expect(queryUserDirectives.methodCalls).toHaveLength(1);
      expect(queryUserDirectives.methodCalls[0].content).toBe('getUser(args.id)');
    });
  });

  describe('Smart import resolution', () => {
    it('should generate correct relative import paths', async () => {
      const decoratorFilePath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      
      await fs.ensureDir(path.dirname(decoratorFilePath));
      await fs.ensureDir(path.dirname(targetOutputPath));

      const importStatement = processor.generateSmartImport(
        decoratorFilePath,
        targetOutputPath,
        'getUserName'
      );

      expect(importStatement).toContain('import { getUserName }');
      expect(importStatement).toContain('from');
      expect(importStatement).toMatch(/['"].*src.*resolvers.*user['"];?$/);
    });

    it('should handle different file extensions', async () => {
      const decoratorFilePath = path.join(tempDir, 'src', 'resolvers', 'user.tsx');
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      
      const importStatement = processor.generateSmartImport(
        decoratorFilePath,
        targetOutputPath,
        'getUserName'
      );

      expect(importStatement).toContain('import { getUserName }');
      // Should remove the .tsx extension
      expect(importStatement).not.toContain('.tsx');
    });
  });

  describe('Integration with field resolver generator', () => {
    it('should integrate decorator directives with field resolver generation', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
        }
        type Query {
          user: User
        }
      `);

      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'user-resolver.ts'), resolverContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize the generator to scan decorators
      await generator.generate();

      // Check that decorator directives are available
      const decoratorDirectives = generator.getDecoratorDirectives('User', 'name');
      expect(decoratorDirectives.methodCalls).toHaveLength(1);
      expect(decoratorDirectives.methodCalls[0].content).toBe('getUserName(obj.id)');

      // Check that smart imports are generated
      const smartImports = generator.getDecoratorSmartImports(
        'User',
        'name',
        path.join(tempDir, 'output', 'user', 'name.ts')
      );
      expect(smartImports).toHaveLength(1);
      expect(smartImports[0]).toContain('getUserName');
    });
  });
});
