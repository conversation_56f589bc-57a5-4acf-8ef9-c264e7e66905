import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 2: SchemaBasedGenerator Integration', () => {
  let tempDir: string;
  let codebaseDir: string;
  let outputDir: string;

  beforeEach(async () => {
    // Create temporary directories
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase2-schema-generator-test-'));
    codebaseDir = path.join(tempDir, 'codebase');
    outputDir = path.join(tempDir, 'output');
    
    await fs.ensureDir(codebaseDir);
    await fs.ensureDir(outputDir);
  });

  afterEach(async () => {
    if (tempDir) {
      await fs.remove(tempDir);
    }
  });

  describe('Decorator Scanning Integration', () => {
    it('should scan decorators when enableDecorators is true', async () => {
      // Create test schema
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
          email: String!
        }
        
        type Query {
          user(id: ID!): User
          users: [User!]!
        }
      `);

      // Create test codebase with decorators
      const resolverContent = `
        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ id, name: "Test User", email: "<EMAIL>" });

        @GQLMethodCall({ type: "Query", field: "users", call: "getAllUsers()" })
        export const getAllUsers = () => [{ id: "1", name: "User 1", email: "<EMAIL>" }];

        @GQLImport("import { UserService } from '../services/user'")
        @GQLMethodCall({ type: "User", field: "email", call: "UserService.getEmail(obj.id)" })
        export const getUserEmail = () => null;
      `;

      await fs.writeFile(path.join(codebaseDir, 'resolvers.ts'), resolverContent);

      // Create generator with decorator options
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        debug: true,
      });

      // Test that decorator scanning is called
      const scanDecoratorsSpy = jest.spyOn(generator as any, 'scanDecorators');
      
      await generator.generate();

      expect(scanDecoratorsSpy).toHaveBeenCalled();
    });

    it('should not scan decorators when enableDecorators is false', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: false,
      });

      const scanDecoratorsSpy = jest.spyOn(generator as any, 'scanDecorators');
      
      await generator.generate();

      expect(scanDecoratorsSpy).not.toHaveBeenCalled();
    });

    it('should handle missing codebase directory gracefully', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir: '/non/existent/path',
        enableDecorators: true,
        debug: true,
      });

      // Should not throw an error
      await expect(generator.generate()).resolves.not.toThrow();
    });

    it('should provide decorator directives through getDecoratorDirectives method', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
        }
        type Query {
          user: User
        }
      `);

      // Create test codebase
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      await fs.writeFile(path.join(codebaseDir, 'user-resolver.ts'), resolverContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
      });

      // Initialize the generator (this will scan decorators)
      await generator.generate();

      // Test getDecoratorDirectives method
      const directives = generator.getDecoratorDirectives('User', 'name');
      
      expect(directives).toBeDefined();
      expect(directives.methodCalls).toBeDefined();
      expect(directives.imports).toBeDefined();
      expect(directives.fieldFields).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle decorator parsing errors gracefully', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      // Create invalid TypeScript file
      const invalidContent = `
        @GQLMethodCall({ invalid: "syntax" })
        export const invalid = () => null;
        
        // This will cause parsing errors
        const unclosedFunction = (
      `;

      await fs.writeFile(path.join(codebaseDir, 'invalid.ts'), invalidContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        debug: true,
      });

      // Should not throw an error even with invalid decorators
      await expect(generator.generate()).resolves.not.toThrow();
    });
  });
});
