import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DecoratorParser } from '../utils/decorator-parser';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 5: Cross-Platform Compatibility', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase5-cross-platform-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Path Handling Across Platforms', () => {
    it('should handle different path separators correctly', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      // Create nested directory structure
      const nestedCodebaseDir = path.join(codebaseDir, 'services', 'user');
      await fs.ensureDir(nestedCodebaseDir);

      await fs.writeFile(path.join(schemaDir, 'user.gql'), `
        type User {
          id: ID!
          name: String!
          email: String!
        }

        type Query {
          user(id: ID!): User
        }
      `);

      // Create decorator file in nested directory
      await fs.writeFile(path.join(nestedCodebaseDir, 'user-service.ts'), `
        @GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
        export const getUser = () => null;

        @GQLMethodCall({ type: "User", field: "name", call: "UserService.getName(obj.id)" })
        export const getUserName = () => "Test User";
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'user.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
      });

      await generator.generate();

      // Verify files are generated with correct paths regardless of platform
      const queryUserFile = path.join(outputDir, 'query', 'user.ts');
      const userNameFile = path.join(outputDir, 'user', 'name.ts');

      expect(fs.existsSync(queryUserFile)).toBe(true);
      expect(fs.existsSync(userNameFile)).toBe(true);

      // Verify import paths are correctly normalized for the platform
      const queryUserContent = await fs.readFile(queryUserFile, 'utf8');
      const userNameContent = await fs.readFile(userNameFile, 'utf8');

      expect(queryUserContent).toContain('UserService.findById');
      expect(userNameContent).toContain('UserService.getName');

      // Import paths should use forward slashes regardless of platform
      expect(queryUserContent).toMatch(/import.*from.*['"].*\/.*['"];?/);
    });

    it('should handle absolute vs relative paths correctly', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      await fs.writeFile(path.join(schemaDir, 'test.gql'), `
        type Query {
          test: String!
        }
      `);

      await fs.writeFile(path.join(codebaseDir, 'test-resolver.ts'), `
        @GQLMethodCall({ type: "Query", field: "test", call: "getTestValue()" })
        export const getTestValue = () => "test";
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'test.gql'), 'utf8'));

      // Test with absolute paths
      const generatorAbsolute = new SchemaBasedGenerator(schema, {
        output: path.resolve(outputDir),
        codebaseDir: path.resolve(codebaseDir),
        enableDecorators: true,
      });

      await generatorAbsolute.generate();

      const testFile = path.join(outputDir, 'query', 'test.ts');
      expect(fs.existsSync(testFile)).toBe(true);

      const content = await fs.readFile(testFile, 'utf8');
      expect(content).toContain('getTestValue');
    });
  });

  describe('File System Operations', () => {
    it('should handle file permissions correctly on Unix systems', async () => {
      // Skip this test on Windows
      if (os.platform() === 'win32') {
        console.log('Skipping Unix permission test on Windows');
        return;
      }

      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      const testFile = path.join(codebaseDir, 'permissions-test.ts');
      await fs.writeFile(testFile, `
        @GQLMethodCall({ type: "Query", field: "test", call: "test()" })
        export const test = () => "test";
      `);

      // Set specific permissions
      await fs.chmod(testFile, 0o644);

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].data.call).toBe('test()');
    });

    it('should handle case-sensitive file systems correctly', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Create files with different cases
      await fs.writeFile(path.join(codebaseDir, 'TestFile.ts'), `
        @GQLMethodCall({ type: "Query", field: "test1", call: "test1()" })
        export const test1 = () => "test1";
      `);

      await fs.writeFile(path.join(codebaseDir, 'testfile.ts'), `
        @GQLMethodCall({ type: "Query", field: "test2", call: "test2()" })
        export const test2 = () => "test2";
      `);

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      // Should find both files on case-sensitive systems, one on case-insensitive
      expect(result.methodCalls.length).toBeGreaterThanOrEqual(1);
      expect(result.methodCalls.length).toBeLessThanOrEqual(2);
    });

    it('should handle long file paths correctly', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      
      // Create a deeply nested directory structure
      const deepPath = path.join(
        codebaseDir,
        'very', 'deeply', 'nested', 'directory', 'structure',
        'that', 'tests', 'long', 'path', 'handling', 'across',
        'different', 'operating', 'systems'
      );
      
      await fs.ensureDir(deepPath);

      const longFileName = 'very-long-file-name-that-tests-path-length-limits.ts';
      const testFile = path.join(deepPath, longFileName);

      await fs.writeFile(testFile, `
        @GQLMethodCall({ type: "Query", field: "longPath", call: "handleLongPath()" })
        export const handleLongPath = () => "success";
      `);

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].data.call).toBe('handleLongPath()');
    });
  });

  describe('Line Ending Handling', () => {
    it('should handle different line endings correctly', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Test with different line endings
      const testCases = [
        { name: 'unix-lf.ts', content: '@GQLMethodCall({ type: "Query", field: "unix", call: "unixTest()" })\nexport const unixTest = () => "unix";' },
        { name: 'windows-crlf.ts', content: '@GQLMethodCall({ type: "Query", field: "windows", call: "windowsTest()" })\r\nexport const windowsTest = () => "windows";' },
        { name: 'mac-cr.ts', content: '@GQLMethodCall({ type: "Query", field: "mac", call: "macTest()" })\rexport const macTest = () => "mac";' }
      ];

      for (const testCase of testCases) {
        await fs.writeFile(path.join(codebaseDir, testCase.name), testCase.content);
      }

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(3);
      
      const calls = result.methodCalls.map(mc => mc.data.call).sort();
      expect(calls).toEqual(['macTest()', 'unixTest()', 'windowsTest()']);
    });
  });

  describe('Character Encoding', () => {
    it('should handle UTF-8 characters correctly', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Test with UTF-8 characters in comments and strings
      await fs.writeFile(path.join(codebaseDir, 'utf8-test.ts'), `
        // Test with UTF-8 characters: 测试中文字符, émojis 🚀, and special chars: ñáéíóú
        @GQLMethodCall({ type: "Query", field: "utf8Test", call: "handleUtf8()" })
        export const handleUtf8 = () => "UTF-8 test: 你好世界 🌍";
      `, 'utf8');

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].data.call).toBe('handleUtf8()');
    });
  });

  describe('Environment Variables and Configuration', () => {
    it('should respect platform-specific environment variables', async () => {
      const originalEnv = process.env.NODE_ENV;
      
      try {
        // Test with different NODE_ENV values
        process.env.NODE_ENV = 'test';

        const codebaseDir = path.join(tempDir, 'codebase');
        await fs.ensureDir(codebaseDir);

        await fs.writeFile(path.join(codebaseDir, 'env-test.ts'), `
          @GQLMethodCall({ type: "Query", field: "envTest", call: "getEnv()" })
          export const getEnv = () => process.env.NODE_ENV;
        `);

        const parser = new DecoratorParser();
        const result = await parser.scanCodebase(codebaseDir);

        expect(result.methodCalls).toHaveLength(1);
        expect(result.methodCalls[0].data.call).toBe('getEnv()');
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
  });

  describe('Platform-Specific Features', () => {
    it('should work correctly on Windows', async () => {
      // This test will run on all platforms but includes Windows-specific considerations
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Test with Windows-style paths in imports (should be normalized)
      await fs.writeFile(path.join(codebaseDir, 'windows-test.ts'), `
        @GQLImport("import { WindowsService } from '..\\\\services\\\\windows-service'")
        @GQLMethodCall({ type: "Query", field: "windowsTest", call: "WindowsService.test()" })
        export const windowsTest = () => null;
      `);

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.imports).toHaveLength(1);
      
      // Import should be normalized regardless of platform
      expect(result.imports[0].data.importStatement).toContain('windows-service');
    });

    it('should work correctly on Unix-like systems', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Test with Unix-style paths
      await fs.writeFile(path.join(codebaseDir, 'unix-test.ts'), `
        @GQLImport("import { UnixService } from '../services/unix-service'")
        @GQLMethodCall({ type: "Query", field: "unixTest", call: "UnixService.test()" })
        export const unixTest = () => null;
      `);

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      expect(result.methodCalls).toHaveLength(1);
      expect(result.imports).toHaveLength(1);
      expect(result.imports[0].data.importStatement).toContain('unix-service');
    });
  });

  describe('Performance Across Platforms', () => {
    it('should maintain consistent performance across platforms', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Create a moderate number of files to test performance
      const fileCount = 50;
      for (let i = 0; i < fileCount; i++) {
        await fs.writeFile(path.join(codebaseDir, `perf-test-${i}.ts`), `
          @GQLMethodCall({ type: "Query", field: "perf${i}", call: "perfTest${i}()" })
          export const perfTest${i} = () => "result${i}";
        `);
      }

      const startTime = Date.now();
      
      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result.methodCalls).toHaveLength(fileCount);
      
      // Performance should be reasonable on all platforms (adjust threshold as needed)
      expect(duration).toBeLessThan(10000); // 10 seconds

      console.log(`Cross-platform performance test completed in ${duration}ms on ${os.platform()}`);
    });
  });
});
