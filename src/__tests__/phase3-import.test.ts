import { DecoratorParser } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 3: @GQLImport Processing', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase3-import-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Import statement processing', () => {
    it('should process explicit import declarations', async () => {
      const resolverContent = `
        @GQLImport("import { UserService } from '../services/user-service'")
        @GQLImport("import { Logger } from '../utils/logger'")
        @GQLMethodCall({ type: "Query", field: "user", call: "UserService.getById(args.id)" })
        export const userResolver = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'imports.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.imports).toHaveLength(2);
      expect(result.imports[0].data.importStatement).toBe("import { UserService } from '../services/user-service'");
      expect(result.imports[1].data.importStatement).toBe("import { Logger } from '../utils/logger'");
    });

    it('should handle conditional imports', async () => {
      const resolverContent = `
        @GQLImport({ 
          importStatement: "import { DevService } from '../dev-service'",
          conditional: true,
          condition: "process.env.NODE_ENV === 'development'"
        })
        @GQLMethodCall({ type: "User", field: "debug", call: "DevService.getDebugInfo()" })
        export const debugResolver = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'conditional.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.imports).toHaveLength(1);
      
      const importData = result.imports[0].data;
      expect(importData.conditional).toBe(true);
      expect(importData.condition).toContain("process.env.NODE_ENV === 'development");
    });

    it('should resolve relative import paths', async () => {
      const resolverContent = `
        @GQLImport("import { ProductAPI } from './product-api'")
        @GQLMethodCall({ type: "Product", field: "title", call: "ProductAPI.getTitle(obj.id)" })
        export const productTitleResolver = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'relative-imports.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.imports).toHaveLength(1);
      
      // The import should be processed and potentially resolved
      const importDirective = directives.imports[0];
      expect(importDirective.content).toContain('ProductAPI');
    });
  });

  describe('Import deduplication', () => {
    it('should deduplicate identical imports', async () => {
      const imports = [
        "import { UserService } from '../services/user'",
        "import { UserService } from '../services/user'",
        "import { ProductService } from '../services/product'",
      ];

      const deduplicated = DecoratorProcessor.deduplicateImports(imports);
      expect(deduplicated).toHaveLength(2);
      expect(deduplicated).toContain("import { UserService } from '../services/user';");
      expect(deduplicated).toContain("import { ProductService } from '../services/product';");
    });

    it('should merge imports from the same module', async () => {
      const imports = [
        "import { UserService } from '../services/user'",
        "import { UserRepository } from '../services/user'",
        "import { UserValidator } from '../services/user'",
      ];

      const deduplicated = DecoratorProcessor.deduplicateImports(imports);
      expect(deduplicated).toHaveLength(1);
      expect(deduplicated[0]).toBe("import { UserRepository, UserService, UserValidator } from '../services/user';");
    });

    it('should handle mixed import formats', async () => {
      const imports = [
        "import { UserService } from '../services/user';",
        "import { ProductService } from '../services/product'",
        "import { OrderService } from '../services/order';",
      ];

      const deduplicated = DecoratorProcessor.deduplicateImports(imports);
      expect(deduplicated).toHaveLength(3);
      deduplicated.forEach(imp => {
        expect(imp).toMatch(/^import \{ \w+ \} from ['"][^'"]+['"];$/);
      });
    });

    it('should skip empty or invalid imports', async () => {
      const imports = [
        "import { UserService } from '../services/user'",
        "",
        "   ",
        "invalid import statement",
        "import { ProductService } from '../services/product'",
      ];

      const deduplicated = DecoratorProcessor.deduplicateImports(imports);
      expect(deduplicated).toHaveLength(3); // 2 valid + 1 invalid (kept as-is)
    });
  });

  describe('Import validation', () => {
    it('should validate import statement syntax', async () => {
      const validImports = [
        "import { UserService } from '../services/user'",
        "import UserService from '../services/user'",
        "import * as UserService from '../services/user'",
        "import UserService, { UserRepository } from '../services/user'",
      ];

      for (const importStatement of validImports) {
        const resolverContent = `
          @GQLImport("${importStatement}")
          export const test = () => null;
        `;

        await fs.writeFile(path.join(tempDir, 'valid-import.ts'), resolverContent);
        const result = await parser.scanCodebase(tempDir);
        expect(result.imports).toHaveLength(1);
      }
    });

    it('should handle conditional import comments', async () => {
      const resolverContent = `
        @GQLImport({ 
          importStatement: "import { DevService } from '../dev-service'",
          conditional: true,
          condition: "process.env.NODE_ENV === 'development'"
        })
        export const test = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'conditional-import.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.imports).toHaveLength(1);
      
      const importDirective = directives.imports[0];
      expect(importDirective.content).toContain('Conditional:');
      expect(importDirective.content).toContain("process.env.NODE_ENV === 'development");
    });
  });

  describe('Integration with resolver generation', () => {
    it('should include decorator imports in resolver templates', async () => {
      const resolverContent = `
        @GQLImport("import { UserService } from '../services/user-service'")
        @GQLMethodCall({ type: "Query", field: "user", call: "UserService.getById(args.id)" })
        export const getUserResolver = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'integration.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.imports).toHaveLength(1);
      expect(directives.methodCalls).toHaveLength(1);

      // Verify that imports and method calls are properly linked
      const importDirective = directives.imports[0];
      const methodCallDirective = directives.methodCalls[0];
      
      expect(importDirective.content).toContain('UserService');
      expect(methodCallDirective.content).toContain('UserService.getById');
    });

    it('should handle schema-specific imports', async () => {
      const resolverContent = `
        @GQLImport({ 
          importStatement: "import { AdminService } from '../services/admin'",
          schema: "admin"
        })
        @GQLMethodCall({ 
          type: "Query", 
          field: "adminUser", 
          call: "AdminService.getUser(args.id)",
          schema: "admin"
        })
        export const adminUserResolver = () => null;
      `;

      await fs.ensureDir(path.join(tempDir, 'src', 'resolvers'));
      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'admin-resolvers.ts'), resolverContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Test with admin schema
      const adminDirectives = processor.convertToDirectiveContainer(result, 'admin');
      expect(adminDirectives.imports).toHaveLength(1);
      expect(adminDirectives.methodCalls).toHaveLength(1);

      // Test with default schema (should be empty)
      const defaultDirectives = processor.convertToDirectiveContainer(result, 'default');
      expect(defaultDirectives.imports).toHaveLength(0);
      expect(defaultDirectives.methodCalls).toHaveLength(0);
    });
  });
});
