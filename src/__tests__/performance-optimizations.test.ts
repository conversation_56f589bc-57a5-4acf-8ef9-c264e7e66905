import { DecoratorParser } from '../utils/decorator-parser';
import { parseTypeScriptFile, getParserPerformanceMetrics, resetParserPerformanceMetrics } from '../utils/ts-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Performance Optimizations', () => {
  let tempDir: string;
  let parser: DecoratorParser;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'perf-opt-test-'));
    parser = new DecoratorParser();
    resetParserPerformanceMetrics();
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Enhanced @swc/core TypeScript Parser', () => {
    it('should parse TypeScript files with improved function detection', async () => {
      const testFile = path.join(tempDir, 'test-resolver.ts');
      await fs.writeFile(testFile, `
import { User } from '../types';

export const getUser = async (args: { id: string }) => {
  try {
    return await userService.findById(args.id);
  } catch (error) {
    throw new Error('User not found');
  }
};

export default getUser;
      `);

      const result = await parseTypeScriptFile(testFile);
      
      expect(result).not.toBeNull();
      expect(result?.functionSignature).toContain('getUser');
      expect(result?.functionBody).toContain('userService.findById');
      expect(result?.isDefault).toBe(true);
    });

    it('should track parser performance metrics', async () => {
      const testFile = path.join(tempDir, 'test-resolver.ts');
      await fs.writeFile(testFile, `
export const simpleResolver = () => {
  return { id: '1', name: 'Test' };
};
      `);

      const result = await parseTypeScriptFile(testFile);
      const metrics = getParserPerformanceMetrics();

      expect(result).not.toBeNull();
      expect(metrics.swcSuccessCount + metrics.fallbackCount).toBeGreaterThan(0);
      // Note: totalParseTime might be 0 for very fast operations, so we check for non-negative
      expect(metrics.totalParseTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Enhanced Decorator Parser with @swc/core', () => {
    it('should parse decorators using AST when possible', async () => {
      const testFile = path.join(tempDir, 'decorated-resolver.ts');
      await fs.writeFile(testFile, `
@GQLMethodCall({ 
  type: "Query", 
  field: "user", 
  call: "getUser(args.id)" 
})
export const getUser = (args: { id: string }) => {
  return { id: args.id, name: 'Test User' };
};

@GQLImport("import { UserService } from '../services/user-service'")
@GQLMethodCall({ type: "Query", field: "users", call: "UserService.findAll()" })
export const getUsers = () => null;
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.methodCalls).toHaveLength(2);
      expect(result.imports).toHaveLength(1);
      
      const metrics = parser.getPerformanceMetrics();
      expect(metrics.filesProcessed).toBe(1);
      expect(metrics.decoratorsFound).toBe(3);
    });

    it('should fall back to regex parsing when AST fails', async () => {
      const testFile = path.join(tempDir, 'complex-decorator.ts');
      // Create a file with complex syntax that might challenge AST parsing
      await fs.writeFile(testFile, `
// Complex multi-line decorator
@GQLMethodCall({
  type: "Query",
  field: "complexQuery",
  call: \`
    complexService.process({
      id: args.id,
      options: {
        nested: true,
        callback: () => console.log('done')
      }
    })
  \`
})
export const complexQuery = (args: any) => null;
      `);

      const result = await parser.scanCodebase(tempDir);
      
      expect(result.methodCalls).toHaveLength(1);
      expect(result.methodCalls[0].data.type).toBe('Query');
      expect(result.methodCalls[0].data.field).toBe('complexQuery');
    });
  });

  describe('Enhanced File Pre-filtering', () => {
    it('should skip files that do not contain decorators', async () => {
      // Create files without decorators
      await fs.writeFile(path.join(tempDir, 'utils.ts'), `
export const helper = () => 'helper';
export const constant = 'CONSTANT_VALUE';
      `);
      
      await fs.writeFile(path.join(tempDir, 'types.ts'), `
export interface User {
  id: string;
  name: string;
}
      `);
      
      // Create one file with decorators
      await fs.writeFile(path.join(tempDir, 'resolver.ts'), `
@GQLMethodCall({ type: "Query", field: "test" })
export const testResolver = () => null;
      `);

      const result = await parser.scanCodebase(tempDir);
      const metrics = parser.getPerformanceMetrics();
      
      expect(result.methodCalls).toHaveLength(1);
      expect(metrics.preFilterEfficiency).toBeGreaterThan(0); // Should skip some files
      expect(metrics.filesSkippedByPreFilter).toBeGreaterThan(0);
    });

    it('should optimize chunk size based on file count', async () => {
      // Create multiple files to test chunk optimization
      for (let i = 0; i < 10; i++) {
        await fs.writeFile(path.join(tempDir, `file${i}.ts`), `
@GQLMethodCall({ type: "Query", field: "field${i}" })
export const resolver${i} = () => null;
        `);
      }

      const result = await parser.scanCodebase(tempDir);
      const metrics = parser.getPerformanceMetrics();
      
      expect(result.methodCalls).toHaveLength(10);
      expect(metrics.filesProcessed).toBe(10);
      expect(metrics.averageTimePerFile).toBeGreaterThan(0);
    });
  });

  describe('Performance Monitoring', () => {
    it('should provide detailed performance metrics', async () => {
      await fs.writeFile(path.join(tempDir, 'test.ts'), `
@GQLMethodCall({ type: "Query", field: "test" })
export const test = () => null;
      `);

      await parser.scanCodebase(tempDir);
      const metrics = parser.getPerformanceMetrics();
      
      expect(metrics).toHaveProperty('totalDuration');
      expect(metrics).toHaveProperty('filesProcessed');
      expect(metrics).toHaveProperty('decoratorsFound');
      expect(metrics).toHaveProperty('preFilterDuration');
      expect(metrics).toHaveProperty('oxcParsingSuccessCount');
      expect(metrics).toHaveProperty('swcParsingSuccessCount');
      expect(metrics).toHaveProperty('regexParsingCount');
      expect(metrics).toHaveProperty('cacheHitRate');
      expect(metrics).toHaveProperty('averageTimePerFile');
      
      expect(metrics.filesProcessed).toBe(1);
      expect(metrics.decoratorsFound).toBe(1);
    });

    it('should reset performance metrics correctly', () => {
      parser.resetPerformanceMetrics();
      const metrics = parser.getPerformanceMetrics();
      
      expect(metrics.totalDuration).toBe(0);
      expect(metrics.filesProcessed).toBe(0);
      expect(metrics.decoratorsFound).toBe(0);
    });
  });
});
