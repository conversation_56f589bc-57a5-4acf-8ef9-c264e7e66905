import { MigrationService, type MigrationOptions } from '../utils/migration-service';
import { TypeScriptMethodResolver } from '../utils/typescript-method-resolver';
import { DecoratorInjector } from '../utils/decorator-injector';
import { SafeFileModifier } from '../utils/safe-file-modifier';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('MigrationService', () => {
  let tempDir: string;
  let migrationService: MigrationService;

  beforeEach(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'migration-test-'));
    
    const options: MigrationOptions = {
      schemaPath: path.join(tempDir, 'schema'),
      codebasePath: path.join(tempDir, 'src'),
      dryRun: false,
      verbose: false,
      createBackups: true
    };

    migrationService = new MigrationService(options);

    // Create test directories
    await fs.ensureDir(options.schemaPath);
    await fs.ensureDir(options.codebasePath);
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('Basic Migration', () => {
    it('should migrate simple methodCall directive', async () => {
      // Create test GraphQL file with directive
      const schemaContent = `
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  name: String!
}

type Query {
  # @methodCall(UserService.findById(args.id))
  user(id: ID!): User
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create test TypeScript file with methods
      const tsContent = `
export const getUserName = (id: string) => {
  return \`User \${id}\`;
};

export class UserService {
  static findById(id: string) {
    return { id, name: "Test User" };
  }
}
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'user-service.ts'), tsContent);

      // Run migration
      const result = await migrationService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.graphqlFilesProcessed).toBe(1);
      expect(result.directivesMigrated).toBeGreaterThan(0);
      expect(result.decoratorsAdded).toBeGreaterThan(0);
      expect(result.errors).toHaveLength(0);

      // Verify TypeScript file has decorators
      const modifiedTsContent = await fs.readFile(path.join(tempDir, 'src', 'user-service.ts'), 'utf8');
      expect(modifiedTsContent).toContain('@GQLMethodCall');

      // Verify GraphQL file has directives removed
      const modifiedSchemaContent = await fs.readFile(path.join(tempDir, 'schema', 'user.gql'), 'utf8');
      expect(modifiedSchemaContent).not.toContain('@methodCall');
    });

    it('should handle import directives', async () => {
      // Create test GraphQL file with import directive
      const schemaContent = `
# @import(import { UserService } from '../services/user-service')
type User {
  id: ID!
  name: String!
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create test TypeScript file
      const tsContent = `
export const someFunction = () => {
  return "test";
};
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'test.ts'), tsContent);

      // Run migration
      const result = await migrationService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);
    });

    it('should handle field directives', async () => {
      // Create test GraphQL file with field directive
      const schemaContent = `
type User {
  id: ID!
  name: String!
  # @field(metadata: UserMetadata)
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create test TypeScript file
      const tsContent = `
export interface UserMetadata {
  createdAt: string;
  updatedAt: string;
}
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'types.ts'), tsContent);

      // Run migration
      const result = await migrationService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);
    });
  });

  describe('Dry Run Mode', () => {
    it('should preview migration without making changes', async () => {
      // Create test files
      const schemaContent = `
type User {
  # @methodCall(getUserName(obj.id))
  name: String!
}
      `;

      const tsContent = `
export const getUserName = (id: string) => {
  return \`User \${id}\`;
};
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);
      await fs.writeFile(path.join(tempDir, 'src', 'user.ts'), tsContent);

      // Create migration service with dry-run enabled
      const dryRunOptions: MigrationOptions = {
        schemaPath: path.join(tempDir, 'schema'),
        codebasePath: path.join(tempDir, 'src'),
        dryRun: true,
        verbose: false,
        createBackups: false
      };

      const dryRunService = new MigrationService(dryRunOptions);

      // Run dry-run migration
      const result = await dryRunService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);

      // Verify files were not actually modified
      const originalSchemaContent = await fs.readFile(path.join(tempDir, 'schema', 'user.gql'), 'utf8');
      const originalTsContent = await fs.readFile(path.join(tempDir, 'src', 'user.ts'), 'utf8');

      expect(originalSchemaContent).toContain('@methodCall');
      expect(originalTsContent).not.toContain('@GQLMethodCall');
    });
  });

  describe('Multi-Schema Support', () => {
    it('should handle schema identifiers', async () => {
      // Create migration service with schema ID
      const multiSchemaOptions: MigrationOptions = {
        schemaPath: path.join(tempDir, 'schema'),
        codebasePath: path.join(tempDir, 'src'),
        schemaId: 'admin',
        dryRun: false,
        verbose: false,
        createBackups: true
      };

      const multiSchemaService = new MigrationService(multiSchemaOptions);

      // Create test files
      const schemaContent = `
type AdminUser {
  # @methodCall(getAdminUserName(obj.id))
  name: String!
}
      `;

      const tsContent = `
export const getAdminUserName = (id: string) => {
  return \`Admin User \${id}\`;
};
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'admin.gql'), schemaContent);
      await fs.writeFile(path.join(tempDir, 'src', 'admin.ts'), tsContent);

      // Run migration
      const result = await multiSchemaService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);

      // Verify decorator includes schema ID
      const modifiedTsContent = await fs.readFile(path.join(tempDir, 'src', 'admin.ts'), 'utf8');
      expect(modifiedTsContent).toContain('schema: "admin"');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing schema directory', async () => {
      const invalidOptions: MigrationOptions = {
        schemaPath: path.join(tempDir, 'nonexistent'),
        codebasePath: path.join(tempDir, 'src'),
        dryRun: false,
        verbose: false,
        createBackups: true
      };

      const invalidService = new MigrationService(invalidOptions);

      // Run migration
      const result = await invalidService.migrate();

      // Verify error handling
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle missing codebase directory', async () => {
      const invalidOptions: MigrationOptions = {
        schemaPath: path.join(tempDir, 'schema'),
        codebasePath: path.join(tempDir, 'nonexistent'),
        dryRun: false,
        verbose: false,
        createBackups: true
      };

      const invalidService = new MigrationService(invalidOptions);

      // Run migration
      const result = await invalidService.migrate();

      // Verify error handling
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle unresolvable method references', async () => {
      // Create test GraphQL file with unresolvable method
      const schemaContent = `
type User {
  # @methodCall(nonExistentMethod(obj.id))
  name: String!
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create empty TypeScript file
      const tsContent = `
// No methods here
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'empty.ts'), tsContent);

      // Run migration
      const result = await migrationService.migrate();

      // Verify warnings for unresolved methods
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(w => w.includes('No matching method found'))).toBe(true);
    });
  });

  describe('Backup Functionality', () => {
    it('should create backups when enabled', async () => {
      // Create test files
      const schemaContent = `
type User {
  # @methodCall(getUserName(obj.id))
  name: String!
}
      `;

      const tsContent = `
export const getUserName = (id: string) => {
  return \`User \${id}\`;
};
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);
      await fs.writeFile(path.join(tempDir, 'src', 'user.ts'), tsContent);

      // Run migration
      const result = await migrationService.migrate();

      // Verify backup was created
      expect(result.success).toBe(true);

      // Check for backup directory (new naming system uses .backup base directory)
      const backupBasePath = path.join(tempDir, '.backup');
      if (await fs.pathExists(backupBasePath)) {
        const backupDirs = await fs.readdir(backupBasePath);
        expect(backupDirs.length).toBeGreaterThan(0);

        // Verify backup contains expected structure
        const firstBackupDir = path.join(backupBasePath, backupDirs[0]);
        expect(await fs.pathExists(path.join(firstBackupDir, 'schema'))).toBe(true);
        expect(await fs.pathExists(path.join(firstBackupDir, 'codebase'))).toBe(true);
      } else {
        // Fallback: check for legacy backup naming in temp directory
        const backupDirs = await fs.readdir(tempDir);
        const backupDir = backupDirs.find(dir => dir.startsWith('.migration-backup-') || dir.startsWith('.backup'));
        expect(backupDir).toBeDefined();
      }
    });
  });

  describe('Alias Import Support', () => {
    it('should preserve alias imports in directives', async () => {
      // Create test GraphQL file with alias import directive
      const schemaContent = `
# @import(import { UserService } from '@services/user-service')
type User {
  id: ID!
  # @methodCall(UserService.getName(obj.id))
  name: String!
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create test TypeScript file
      const tsContent = `
export class UserService {
  static getName(id: string) {
    return \`User \${id}\`;
  }
}
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'user-service.ts'), tsContent);

      // Create migration service with alias configuration
      const aliasOptions: MigrationOptions = {
        schemaPath: path.join(tempDir, 'schema'),
        codebasePath: path.join(tempDir, 'src'),
        dryRun: false,
        verbose: false,
        createBackups: true,
        aliasConfig: {
          codebaseAlias: '@services',
          codebasePath: path.join(tempDir, 'src')
        }
      };

      const aliasService = new MigrationService(aliasOptions);

      // Run migration
      const result = await aliasService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);

      // Verify TypeScript file has decorators with preserved alias
      const modifiedTsContent = await fs.readFile(path.join(tempDir, 'src', 'user-service.ts'), 'utf8');
      expect(modifiedTsContent).toContain('@GQLImport');
      expect(modifiedTsContent).toContain('@services/user-service');
    });

    it('should handle relative imports when no alias is configured', async () => {
      // Create test GraphQL file with relative import directive
      const schemaContent = `
# @import(import { UserHelper } from '../utils/user-helper')
type User {
  id: ID!
  # @methodCall(UserHelper.formatName(obj.name))
  displayName: String!
}
      `;

      await fs.writeFile(path.join(tempDir, 'schema', 'user.gql'), schemaContent);

      // Create test TypeScript file
      const tsContent = `
export class UserHelper {
  static formatName(name: string) {
    return name.toUpperCase();
  }
}
      `;

      await fs.writeFile(path.join(tempDir, 'src', 'user-helper.ts'), tsContent);

      // Run migration without alias configuration
      const result = await migrationService.migrate();

      // Verify results
      expect(result.success).toBe(true);
      expect(result.directivesMigrated).toBeGreaterThan(0);

      // Verify TypeScript file has decorators with preserved relative import
      const modifiedTsContent = await fs.readFile(path.join(tempDir, 'src', 'user-helper.ts'), 'utf8');
      expect(modifiedTsContent).toContain('@GQLImport');
      expect(modifiedTsContent).toContain('../utils/user-helper');
    });
  });
});
