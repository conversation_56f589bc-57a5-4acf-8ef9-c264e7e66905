import { 
  initializeTemplateOptimization,
  getGlobalTemplateOptimizationIntegration,
  resetGlobalTemplateOptimizationIntegration 
} from '../utils/template-optimization-integration';
import { 
  getGlobalTemplateCache,
  resetGlobalTemplateCache 
} from '../utils/template-compilation-cache';
import { 
  resetGlobalPrecompilationService 
} from '../utils/template-precompilation-service';
import { 
  resetGlobalInitializationService 
} from '../utils/template-initialization-service';
import { 
  resetGlobalTemplatePerformanceMonitor 
} from '../utils/template-performance-monitor';
import { 
  resetGlobalTemplateDataOptimizer 
} from '../utils/template-data-optimizer';

describe('Template Optimization Integration', () => {
  beforeEach(() => {
    // Reset all global instances before each test
    resetGlobalTemplateOptimizationIntegration();
    resetGlobalTemplateCache();
    resetGlobalPrecompilationService();
    resetGlobalInitializationService();
    resetGlobalTemplatePerformanceMonitor();
    resetGlobalTemplateDataOptimizer();
  });

  afterEach(() => {
    // Clean up after each test
    resetGlobalTemplateOptimizationIntegration();
    resetGlobalTemplateCache();
    resetGlobalPrecompilationService();
    resetGlobalInitializationService();
    resetGlobalTemplatePerformanceMonitor();
    resetGlobalTemplateDataOptimizer();
  });

  describe('Basic Integration', () => {
    it('should initialize template optimization system successfully', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        autoInitialize: true,
        enableMonitoring: true,
      });

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.monitoringStatus.isActive).toBe(true);
      expect(result.dataOptimizationStatus.isActive).toBe(true);
    });

    it('should handle initialization with high priority templates only', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        initialization: {
          highPriorityOnly: true,
          validateCatalog: true,
        },
      });

      expect(result.success).toBe(true);
      if (result.initializationResult) {
        expect(result.initializationResult.precompilationSummary.totalTemplates).toBeGreaterThan(0);
      }
    });

    it('should provide optimization status after integration', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
      });

      const integration = getGlobalTemplateOptimizationIntegration();
      const status = integration.getOptimizationStatus();

      expect(status.isIntegrated).toBe(true);
      expect(status.templateCache.isActive).toBe(true);
      expect(status.performanceMonitoring.isActive).toBe(true);
      expect(status.dataOptimization.isActive).toBe(true);
    });
  });

  describe('Template Cache Integration', () => {
    it('should use cached template compilation', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
      });

      const templateCache = getGlobalTemplateCache();
      const testTemplate = 'Hello {{name}}!';

      // First compilation (cache miss)
      const template1 = templateCache.compile(testTemplate);
      const stats1 = templateCache.getStatistics();

      // Second compilation (cache hit)
      const template2 = templateCache.compile(testTemplate);
      const stats2 = templateCache.getStatistics();

      expect(template1).toBe(template2); // Same template function
      expect(stats2.hits).toBeGreaterThan(stats1.hits);
      expect(stats2.hitRate).toBeGreaterThan(0);
    });

    it('should handle template compilation errors gracefully', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
      });

      const templateCache = getGlobalTemplateCache();
      const invalidTemplate = 'Hello {{#if}}{{/if'; // Invalid Handlebars syntax

      try {
        templateCache.compile(invalidTemplate);
        // If it doesn't throw, that's also acceptable behavior
      } catch (error) {
        // Error is expected for invalid syntax
        expect(error).toBeDefined();
      }

      // Cache should still be functional
      const validTemplate = 'Hello {{name}}!';
      expect(() => {
        templateCache.compile(validTemplate);
      }).not.toThrow();
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should track template performance metrics', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
        monitoring: {
          enableRealtimeMonitoring: true,
          monitoringInterval: 100, // Fast interval for testing
        },
      });

      const integration = getGlobalTemplateOptimizationIntegration();
      
      // Perform some template operations
      const templateCache = getGlobalTemplateCache();
      templateCache.compile('Test template {{value}}');
      templateCache.compile('Test template {{value}}'); // Cache hit

      // Wait a bit for monitoring to collect metrics
      await new Promise(resolve => setTimeout(resolve, 150));

      const status = integration.getOptimizationStatus();
      expect(status.templateCache.entryCount).toBeGreaterThan(0);
      expect(status.templateCache.hitRate).toBeGreaterThan(0);
    });

    it('should provide performance summary', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
      });

      const integration = getGlobalTemplateOptimizationIntegration();
      
      // Perform some operations to generate metrics
      const templateCache = getGlobalTemplateCache();
      for (let i = 0; i < 10; i++) {
        templateCache.compile(`Template ${i}: {{value}}`);
      }

      const summary = integration.getPerformanceSummary();
      expect(summary.overall).toMatch(/excellent|good|warning|critical/);
      expect(summary.summary).toBeTruthy();
      expect(Array.isArray(summary.recommendations)).toBe(true);
      expect(typeof summary.metrics.templateCompilationHitRate).toBe('number');
    });
  });

  describe('Data Optimization Integration', () => {
    it('should optimize template data preparation', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
        dataOptimization: {
          enableCaching: true,
          enablePerformanceTracking: true,
        },
      });

      const integration = getGlobalTemplateOptimizationIntegration();
      const status = integration.getOptimizationStatus();

      expect(status.dataOptimization.isActive).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle partial initialization failures gracefully', async () => {
      // Test with invalid configuration that might cause some components to fail
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        initialization: {
          precompilationOptions: {
            maxPrecompilationTime: 1, // Very short timeout
          },
        },
      });

      // Should still succeed overall even if some parts fail
      expect(result.success || result.warnings.length > 0).toBe(true);
      expect(result.monitoringStatus.isActive || result.dataOptimizationStatus.isActive).toBe(true);
    });

    it('should provide meaningful error messages', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        initialization: {
          precompilationOptions: {
            maxPrecompilationTime: 0, // Invalid timeout
          },
        },
      });

      if (!result.success) {
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0]).toContain('initialization');
      }
    });
  });

  describe('Configuration Options', () => {
    it('should respect disabled monitoring configuration', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        enableMonitoring: false,
      });

      expect(result.success).toBe(true);
      expect(result.monitoringStatus.isActive).toBe(false);
    });

    it('should respect disabled auto-initialization configuration', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        autoInitialize: false,
      });

      expect(result.success).toBe(true);
      expect(result.initializationResult).toBeUndefined();
    });

    it('should handle custom cache configuration', async () => {
      const result = await initializeTemplateOptimization({
        enableLogging: false,
        initialization: {
          cacheOptions: {
            maxEntries: 500,
            maxMemoryUsage: 25 * 1024 * 1024, // 25MB
          },
        },
      });

      expect(result.success).toBe(true);
      
      const templateCache = getGlobalTemplateCache();
      const stats = templateCache.getStatistics();
      expect(stats).toBeDefined();
    });
  });

  describe('Memory Management', () => {
    it('should track memory usage across all components', async () => {
      await initializeTemplateOptimization({
        enableLogging: false,
      });

      const integration = getGlobalTemplateOptimizationIntegration();
      
      // Generate some cache entries
      const templateCache = getGlobalTemplateCache();
      for (let i = 0; i < 50; i++) {
        templateCache.compile(`Template ${i}: {{value${i}}}`);
      }

      const status = integration.getOptimizationStatus();
      expect(status.templateCache.memoryUsage).toBeGreaterThan(0);
      expect(status.templateCache.entryCount).toBeGreaterThanOrEqual(50); // May include pre-compiled templates
    });
  });
});

describe('Template Optimization Performance', () => {
  beforeEach(() => {
    resetGlobalTemplateOptimizationIntegration();
    resetGlobalTemplateCache();
    resetGlobalPrecompilationService();
    resetGlobalInitializationService();
    resetGlobalTemplatePerformanceMonitor();
    resetGlobalTemplateDataOptimizer();
  });

  afterEach(() => {
    resetGlobalTemplateOptimizationIntegration();
    resetGlobalTemplateCache();
    resetGlobalPrecompilationService();
    resetGlobalInitializationService();
    resetGlobalTemplatePerformanceMonitor();
    resetGlobalTemplateDataOptimizer();
  });

  it('should demonstrate performance improvement with caching', async () => {
    await initializeTemplateOptimization({
      enableLogging: false,
    });

    const templateCache = getGlobalTemplateCache();
    const complexTemplate = `
{{#if directiveImports}}
{{#each directiveImports}}
{{safeRaw this}}
{{/each}}
{{/if}}
import { {{typeName}}Resolvers } from '{{importPath}}';
const {{fieldName}} = async (obj, args, context) => {
  return {{defaultValue}};
};
export default {{fieldName}};`;

    // Measure uncached compilation
    const uncachedStart = Date.now();
    for (let i = 0; i < 100; i++) {
      templateCache.compile(complexTemplate + i); // Make each unique
    }
    const uncachedTime = Date.now() - uncachedStart;

    // Clear cache and measure cached compilation
    templateCache.clear();
    templateCache.compile(complexTemplate); // Pre-compile once

    const cachedStart = Date.now();
    for (let i = 0; i < 100; i++) {
      templateCache.compile(complexTemplate); // Same template (cache hits)
    }
    const cachedTime = Date.now() - cachedStart;

    // Cached should be significantly faster
    expect(cachedTime).toBeLessThan(uncachedTime);
    
    const stats = templateCache.getStatistics();
    expect(stats.hitRate).toBeGreaterThan(90); // Should have high hit rate
  });
});
