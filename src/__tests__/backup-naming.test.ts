import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { BackupNamingUtils, type BackupNamingOptions } from '../utils/backup-naming';

describe('BackupNamingUtils', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'backup-naming-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Path Extraction and Sanitization', () => {
    it('should extract clean names from various path formats', () => {
      const testCases = [
        { input: '/path/to/my-codebase', expected: 'my-codebase' },
        { input: './schema/user-schema', expected: 'user-schema' },
        { input: 'project', expected: 'project' }, // Simplified Windows path test
        { input: '/complex/path/with.dots/and-dashes_underscores', expected: 'and-dashes_underscores' },
        { input: 'schema.gql', expected: 'schema' },
        { input: 'user.service.ts', expected: 'user-service' } // Only removes final extension
      ];

      testCases.forEach(({ input, expected }) => {
        const result = BackupNamingUtils.extractNameFromPath(input);
        expect(result).toBe(expected);
      });
    });

    it('should sanitize names with special characters', () => {
      const testCases = [
        { input: 'my@project#name', expected: 'my-project-name' },
        { input: 'user service', expected: 'user-service' },
        { input: 'schema/with/slashes', expected: 'schema-with-slashes' },
        { input: '---multiple---dashes---', expected: 'multiple-dashes' },
        { input: '', expected: 'unnamed' },
        { input: '!@#$%^&*()', expected: 'unnamed' }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = BackupNamingUtils.sanitizeName(input);
        expect(result).toBe(expected);
      });
    });

    it('should handle long names by truncating appropriately', () => {
      const longName = 'a'.repeat(100);
      const result = BackupNamingUtils.sanitizeName(longName, 20);
      expect(result.length).toBeLessThanOrEqual(20);
      expect(result).toBe('a'.repeat(20));
    });
  });

  describe('Backup Directory Creation', () => {
    it('should create backup directory with default options', async () => {
      const codebasePath = path.join(tempDir, 'src');
      const schemaPath = path.join(tempDir, 'schema');
      
      await fs.ensureDir(codebasePath);
      await fs.ensureDir(schemaPath);

      const backupInfo = await BackupNamingUtils.createBackupDirectory(
        codebasePath,
        schemaPath
      );

      expect(backupInfo.codebaseName).toBe('src');
      expect(backupInfo.schemaName).toBe('schema');
      expect(backupInfo.timestamp).toMatch(/\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}/);
      expect(await fs.pathExists(backupInfo.backupDir)).toBe(true);
      expect(await fs.pathExists(path.join(backupInfo.backupDir, 'schema'))).toBe(true);
      expect(await fs.pathExists(path.join(backupInfo.backupDir, 'codebase'))).toBe(true);
    });

    it('should create backup directory with custom options', async () => {
      const codebasePath = path.join(tempDir, 'my-app');
      const schemaPath = path.join(tempDir, 'graphql-schemas');
      
      await fs.ensureDir(codebasePath);
      await fs.ensureDir(schemaPath);

      const options: BackupNamingOptions = {
        baseBackupDir: 'custom-backups',
        separator: '-',
        customPrefix: 'test',
        namingTemplate: '{prefix}-{timestamp}-{codebase}-{schema}',
        useSubdirectories: false
      };

      const backupInfo = await BackupNamingUtils.createBackupDirectory(
        codebasePath,
        schemaPath,
        options
      );

      expect(backupInfo.backupDir).toContain('custom-backups');
      expect(backupInfo.backupDir).toContain('test_'); // Uses underscore separator
      expect(backupInfo.backupDir).toContain('my_app'); // Sanitized name
      expect(backupInfo.backupDir).toContain('graphql_schemas'); // Sanitized name
      expect(await fs.pathExists(backupInfo.backupDir)).toBe(true);
    });

    it('should handle complex path structures', async () => {
      const testCases = [
        {
          codebase: path.join(tempDir, 'projects', 'web-app', 'src'),
          schema: path.join(tempDir, 'projects', 'web-app', 'schema', 'public'),
          expectedCodebase: 'src',
          expectedSchema: 'public'
        },
        {
          codebase: path.join(tempDir, 'monorepo', 'packages', 'api'),
          schema: path.join(tempDir, 'monorepo', 'schemas', 'admin'),
          expectedCodebase: 'api',
          expectedSchema: 'admin'
        }
      ];

      for (const testCase of testCases) {
        await fs.ensureDir(testCase.codebase);
        await fs.ensureDir(testCase.schema);

        const backupInfo = await BackupNamingUtils.createBackupDirectory(
          testCase.codebase,
          testCase.schema
        );

        expect(backupInfo.codebaseName).toBe(testCase.expectedCodebase);
        expect(backupInfo.schemaName).toBe(testCase.expectedSchema);
        expect(await fs.pathExists(backupInfo.backupDir)).toBe(true);
      }
    });
  });

  describe('Session Management', () => {
    it('should generate unique session IDs', () => {
      const sessionId1 = BackupNamingUtils.generateSessionId();
      const sessionId2 = BackupNamingUtils.generateSessionId();
      const sessionId3 = BackupNamingUtils.generateSessionId('test');

      expect(sessionId1).not.toBe(sessionId2);
      expect(sessionId3).toContain('test_');
      expect(sessionId1).toMatch(/^\d+_[a-z0-9]+$/);
    });

    it('should create session-aware backup directories', async () => {
      const codebasePath = path.join(tempDir, 'src');
      const schemaPath = path.join(tempDir, 'schema');
      const sessionId = 'test-session-123';
      
      await fs.ensureDir(codebasePath);
      await fs.ensureDir(schemaPath);

      const backupInfo = await BackupNamingUtils.createSessionBackupDirectory(
        codebasePath,
        schemaPath,
        sessionId
      );

      expect(backupInfo.backupDir).toContain(sessionId.replace(/-/g, '_')); // Session ID gets sanitized
      expect(await fs.pathExists(backupInfo.backupDir)).toBe(true);
    });
  });

  describe('Naming Collision Resolution', () => {
    it('should resolve naming collisions by appending numbers', async () => {
      const basePath = path.join(tempDir, 'test-backup');
      
      // Create the base directory to simulate collision
      await fs.ensureDir(basePath);
      
      const resolvedPath = await BackupNamingUtils.resolveNamingCollision(basePath);
      
      expect(resolvedPath).not.toBe(basePath);
      expect(resolvedPath).toContain('test-backup-1');
    });

    it('should handle multiple collisions', async () => {
      const basePath = path.join(tempDir, 'collision-test');
      
      // Create multiple directories to simulate collisions
      await fs.ensureDir(basePath);
      await fs.ensureDir(`${basePath}-1`);
      await fs.ensureDir(`${basePath}-2`);
      
      const resolvedPath = await BackupNamingUtils.resolveNamingCollision(basePath);
      
      expect(resolvedPath).toBe(`${basePath}-3`);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate backup naming configuration', () => {
      const validConfig: BackupNamingOptions = {
        baseBackupDir: '.backup',
        maxNameLength: 50,
        separator: '_',
        namingTemplate: '{timestamp}_{codebase}_{schema}'
      };

      const result = BackupNamingUtils.validateConfiguration(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid configuration', () => {
      const invalidConfig: BackupNamingOptions = {
        baseBackupDir: '../../../dangerous/path',
        maxNameLength: 5,
        separator: '<invalid>',
        maxBackupsToKeep: -1
      };

      const result = BackupNamingUtils.validateConfiguration(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Template Processing', () => {
    it('should process naming templates correctly', () => {
      const template = '{timestamp}_{codebase}_{schema}_{prefix}';
      const variables = {
        timestamp: '2025-01-01_12-00-00',
        codebase: 'my-app',
        schema: 'public',
        prefix: 'test'
      };

      const result = BackupNamingUtils.processNamingTemplate(template, variables);
      expect(result).toBe('2025_01_01_12_00_00_my_app_public_test'); // Template processing uses underscores
    });

    it('should handle missing variables gracefully', () => {
      const template = '{timestamp}_{codebase}_{missing}_{schema}';
      const variables = {
        timestamp: '2025-01-01_12-00-00',
        codebase: 'my-app',
        schema: 'public'
      };

      const result = BackupNamingUtils.processNamingTemplate(template, variables);
      expect(result).toBe('2025_01_01_12_00_00_my_app_public'); // Template processing uses underscores
    });
  });

  describe('Backward Compatibility', () => {
    it('should handle legacy backup directory structures', async () => {
      // Create legacy backup directories
      const legacyBackupDir1 = path.join(tempDir, '.backup-2025-01-01T10-30-45-123Z');
      const legacyBackupDir2 = path.join(tempDir, '.migration-backup-2025-01-01T11-30-45-456Z');

      await fs.ensureDir(legacyBackupDir1);
      await fs.ensureDir(legacyBackupDir2);

      // Create some test files in legacy backups
      await fs.writeFile(path.join(legacyBackupDir1, 'test.backup'), 'legacy content 1');
      await fs.writeFile(path.join(legacyBackupDir2, 'test.backup'), 'legacy content 2');

      // Verify they exist
      expect(await fs.pathExists(legacyBackupDir1)).toBe(true);
      expect(await fs.pathExists(legacyBackupDir2)).toBe(true);
    });

    it('should work with existing SafeFileModifier backup patterns', async () => {
      // Create a backup directory that mimics SafeFileModifier pattern
      const safeFileBackupDir = path.join(tempDir, '.backup-2025-01-01T12-00-00-000Z');
      await fs.ensureDir(safeFileBackupDir);

      // Create backup files with SafeFileModifier naming pattern
      const testFilePath = path.join(tempDir, 'src', 'test.ts');
      await fs.ensureDir(path.dirname(testFilePath));
      await fs.writeFile(testFilePath, 'original content');

      const relativePath = path.relative(process.cwd(), testFilePath);
      const safeRelativePath = relativePath.replace(/[/\\]/g, '_');
      const timestamp = '2025-01-01T12-00-00-000Z';
      const backupFileName = `${safeRelativePath}.${timestamp}.backup`;
      const backupPath = path.join(safeFileBackupDir, backupFileName);

      await fs.writeFile(backupPath, 'backup content');

      // Verify backup file exists and can be read
      expect(await fs.pathExists(backupPath)).toBe(true);
      const backupContent = await fs.readFile(backupPath, 'utf8');
      expect(backupContent).toBe('backup content');
    });

    it('should handle mixed backup directory structures', async () => {
      // Create both new and legacy backup structures
      const newBackupInfo = await BackupNamingUtils.createBackupDirectory(
        path.join(tempDir, 'src'),
        path.join(tempDir, 'schema'),
        { baseBackupDir: path.join(tempDir, '.backup') }
      );

      const legacyBackupDir = path.join(tempDir, '.migration-backup-2025-01-01T10-30-45-123Z');
      await fs.ensureDir(legacyBackupDir);

      // Both should coexist
      expect(await fs.pathExists(newBackupInfo.backupDir)).toBe(true);
      expect(await fs.pathExists(legacyBackupDir)).toBe(true);

      // List all backup-related directories
      const allEntries = await fs.readdir(tempDir);
      const backupEntries = allEntries.filter(entry =>
        entry.includes('backup') || entry.includes('.backup')
      );

      expect(backupEntries.length).toBeGreaterThanOrEqual(2);
    });

    it('should validate configuration for backward compatibility', () => {
      // Test configuration that maintains backward compatibility
      const compatibleConfig: BackupNamingOptions = {
        baseBackupDir: '.backup',
        includeTimestamp: true,
        useSubdirectories: true,
        createMetadata: false, // Disable for legacy compatibility
        namingTemplate: '{timestamp}_{codebase}_{schema}' // Simple template
      };

      const result = BackupNamingUtils.validateConfiguration(compatibleConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
});
