import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DecoratorParser } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { DEFAULT_DECORATOR_CONFIG } from '../utils/decorator-types';
import { DirectiveManager } from '../utils/directive-manager';
import { WatchService } from '../core/watch-service';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { spawn } from 'child_process';

describe('Phase 5: Comprehensive System Testing', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase5-comprehensive-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Complete System Integration', () => {
    it('should handle full decorator system end-to-end', async () => {
      // Create comprehensive test schema
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');

      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      // Create complex schema with inheritance
      await fs.writeFile(path.join(schemaDir, 'base.gql'), `
        interface Node {
          id: ID!
          createdAt: String!
        }

        interface Timestamped {
          updatedAt: String!
        }

        type User implements Node & Timestamped {
          id: ID!
          createdAt: String!
          updatedAt: String!
          name: String!
          email: String!
          posts: [Post!]!
        }

        type Post implements Node {
          id: ID!
          createdAt: String!
          title: String!
          content: String!
          author: User!
        }

        type Query {
          user(id: ID!): User
          users: [User!]!
          post(id: ID!): Post
          posts: [Post!]!
        }

        type Mutation {
          createUser(input: CreateUserInput!): User!
          updateUser(id: ID!, input: UpdateUserInput!): User!
          createPost(input: CreatePostInput!): Post!
        }

        input CreateUserInput {
          name: String!
          email: String!
        }

        input UpdateUserInput {
          name: String
          email: String
        }

        input CreatePostInput {
          title: String!
          content: String!
          authorId: ID!
        }
      `);

      // Create comprehensive decorator implementations
      await fs.writeFile(path.join(codebaseDir, 'user-resolvers.ts'), `
        import { User, CreateUserInput, UpdateUserInput } from '../types';

        // Base interface implementations
        @GQLMethodCall({ type: "Node", field: "createdAt", call: "getCreatedAt(obj.id)" })
        export const getCreatedAt = (id: string) => new Date().toISOString();

        @GQLMethodCall({ type: "Timestamped", field: "updatedAt", call: "getUpdatedAt(obj.id)" })
        export const getUpdatedAt = (id: string) => new Date().toISOString();

        // User-specific resolvers
        @GQLImport("import { UserService } from '../services/user-service'")
        @GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
        export const getUser = () => null;

        @GQLMethodCall({ type: "Query", field: "users", call: "UserService.findAll()" })
        export const getAllUsers = () => [];

        @GQLMethodCall({ type: "User", field: "posts", call: "UserService.getUserPosts(obj.id)" })
        export const getUserPosts = () => [];

        // Mutations
        @GQLMethodCall({ type: "Mutation", field: "createUser", call: "UserService.create(args.input)" })
        export const createUser = () => null;

        @GQLMethodCall({ type: "Mutation", field: "updateUser", call: "UserService.update(args.id, args.input)" })
        export const updateUser = () => null;
      `);

      await fs.writeFile(path.join(codebaseDir, 'post-resolvers.ts'), `
        import { Post, CreatePostInput } from '../types';

        @GQLImport("import { PostService } from '../services/post-service'")
        @GQLImport("import { UserService } from '../services/user-service'")
        @GQLMethodCall({ type: "Query", field: "post", call: "PostService.findById(args.id)" })
        export const getPost = () => null;

        @GQLMethodCall({ type: "Query", field: "posts", call: "PostService.findAll()" })
        export const getAllPosts = () => [];

        @GQLMethodCall({ type: "Post", field: "author", call: "UserService.findById(obj.authorId)" })
        export const getPostAuthor = () => null;

        @GQLMethodCall({ type: "Mutation", field: "createPost", call: "PostService.create(args.input)" })
        export const createPost = () => null;
      `);

      // Test the complete generation pipeline
      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'base.gql'), 'utf8'));

      const generator = new SchemaBasedGenerator(schema, {
        schema: schemaDir,
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
      });

      await generator.generate();

      // Verify all resolvers were generated correctly
      const expectedFiles = [
        'query/user.ts',
        'query/users.ts',
        'query/post.ts',
        'query/posts.ts',
        'base/user/posts.ts',
        'base/post/author.ts',
        'mutation/create-user.ts',
        'mutation/update-user.ts',
        'mutation/create-post.ts',
        'base/node/created-at.ts',
        'base/timestamped/updated-at.ts'
      ];

      for (const file of expectedFiles) {
        const filePath = path.join(outputDir, file);
        expect(fs.existsSync(filePath)).toBe(true);
        
        // Verify file contains expected imports and method calls
        const content = await fs.readFile(filePath, 'utf8');
        expect(content).toContain('export');
        
        // Check for proper imports based on decorators
        if (file.includes('user') || file.includes('post')) {
          expect(content).toMatch(/import.*Service.*from/);
        }
      }

      // Verify inheritance worked correctly
      const userCreatedAtFile = path.join(outputDir, 'user/created-at.ts');
      const postCreatedAtFile = path.join(outputDir, 'post/created-at.ts');
      
      expect(fs.existsSync(userCreatedAtFile)).toBe(true);
      expect(fs.existsSync(postCreatedAtFile)).toBe(true);

      // Both should delegate to the same base implementation
      const userCreatedAtContent = await fs.readFile(userCreatedAtFile, 'utf8');
      const postCreatedAtContent = await fs.readFile(postCreatedAtFile, 'utf8');
      
      expect(userCreatedAtContent).toContain('getCreatedAt');
      expect(postCreatedAtContent).toContain('getCreatedAt');
    });

    it('should maintain backward compatibility with comment-based directives', async () => {
      // Create mixed schema with both comment-based and decorator-based directives
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');

      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      // Schema with comment-based directives
      await fs.writeFile(path.join(schemaDir, 'mixed.gql'), `
        type User {
          id: ID!
          # @methodCall(getUserName(obj.id))
          name: String!
          email: String!
        }

        type Query {
          # @methodCall(getUserById(args.id))
          user(id: ID!): User
        }
      `);

      // Codebase with decorators (should take precedence)
      await fs.writeFile(path.join(codebaseDir, 'mixed-resolvers.ts'), `
        @GQLMethodCall({ type: "User", field: "email", call: "getUserEmail(obj.id)" })
        export const getUserEmail = (id: string) => \`user\${id}@example.com\`;

        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ id, name: "Test", email: "<EMAIL>" });
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'mixed.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        schema: schemaDir,
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
      });

      await generator.generate();

      // Verify comment-based directive still works for name field
      const nameFile = path.join(outputDir, 'mixed/user/name.ts');
      expect(fs.existsSync(nameFile)).toBe(true);
      const nameContent = await fs.readFile(nameFile, 'utf8');
      expect(nameContent).toContain('getUserName');

      // Verify decorator takes precedence for user query
      const userFile = path.join(outputDir, 'query/user.ts');
      expect(fs.existsSync(userFile)).toBe(true);
      const userContent = await fs.readFile(userFile, 'utf8');
      expect(userContent).toContain('getUser'); // From decorator
      expect(userContent).not.toContain('getUserById'); // Comment-based should be overridden

      // Verify decorator-only email field works
      const emailFile = path.join(outputDir, 'user/email.ts');
      expect(fs.existsSync(emailFile)).toBe(true);
      const emailContent = await fs.readFile(emailFile, 'utf8');
      expect(emailContent).toContain('getUserEmail');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid decorator syntax gracefully', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Create file with invalid decorator syntax
      await fs.writeFile(path.join(codebaseDir, 'invalid.ts'), `
        @GQLMethodCall({ invalid: "syntax", missing: "required fields" })
        export const invalidDecorator = () => null;

        @GQLImport("invalid import syntax without proper quotes")
        export const invalidImport = () => null;
      `);

      const parser = new DecoratorParser();
      const processor = new DecoratorProcessor(DEFAULT_DECORATOR_CONFIG);

      // Should not throw but should handle errors gracefully
      const rawResult = await parser.scanCodebase(codebaseDir);

      // Process the decorators through validation
      const processedResult = processor.convertToDirectiveContainer(rawResult);

      // Should have empty results due to invalid syntax being filtered out
      expect(processedResult.methodCalls).toHaveLength(0);
      expect(processedResult.imports).toHaveLength(0);
    });

    it('should handle large codebases efficiently', async () => {
      const codebaseDir = path.join(tempDir, 'large-codebase');
      await fs.ensureDir(codebaseDir);

      // Generate many decorator files
      const fileCount = 100;
      for (let i = 0; i < fileCount; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
          export const resolver${i} = () => "result${i}";
        `;
        await fs.writeFile(path.join(codebaseDir, `resolver${i}.ts`), content);
      }

      const startTime = Date.now();
      
      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(30000); // 30 seconds
      expect(result.methodCalls).toHaveLength(fileCount);
    });
  });
});
