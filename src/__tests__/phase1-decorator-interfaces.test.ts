import {
  isGQLMethodCallData,
  isGQLImportData,
  isGQLFieldData,
  isGQLContextData,
  DEFAULT_DECORATOR_CONFIG,
  DEFAULT_PRECEDENCE_RULES,
} from '../utils/decorator-types';
import { DecoratorValidator } from '../utils/decorator-validator';
import { ParsedDecorator } from '../utils/decorator-parser';

describe('Decorator Types and Interfaces', () => {
  describe('Type Guards', () => {
    describe('isGQLMethodCallData', () => {
      it('should return true for valid method call data', () => {
        const validData = {
          type: 'User',
          call: 'getUser(obj.id)',
          field: 'name',
        };

        expect(isGQLMethodCallData(validData)).toBe(true);
      });

      it('should return false for invalid method call data', () => {
        const invalidData = {
          type: 'User',
          // Missing call
        };

        expect(isGQLMethodCallData(invalidData)).toBe(false);
      });

      it('should return false for null or undefined', () => {
        expect(isGQLMethodCallData(null)).toBe(false);
        expect(isGQLMethodCallData(undefined)).toBe(false);
      });
    });

    describe('isGQLImportData', () => {
      it('should return true for valid import data', () => {
        const validData = {
          importStatement: 'import { User } from "./types"',
        };

        expect(isGQLImportData(validData)).toBe(true);
      });

      it('should return false for invalid import data', () => {
        const invalidData = {
          // Missing importStatement
          schema: 'default',
        };

        expect(isGQLImportData(invalidData)).toBe(false);
      });
    });

    describe('isGQLFieldData', () => {
      it('should return true for valid field data', () => {
        const validData = {
          ref: 'User',
          name: 'metadata',
          type: 'UserMetadata',
        };

        expect(isGQLFieldData(validData)).toBe(true);
      });

      it('should return false for invalid field data', () => {
        const invalidData = {
          ref: 'User',
          // Missing name and type
        };

        expect(isGQLFieldData(invalidData)).toBe(false);
      });
    });

    describe('isGQLContextData', () => {
      it('should return true for valid context data', () => {
        const validData = {
          path: './types/Context',
          name: 'AppContext',
        };

        expect(isGQLContextData(validData)).toBe(true);
      });

      it('should return false for invalid context data', () => {
        const invalidData = {
          path: './types/Context',
          // Missing name
        };

        expect(isGQLContextData(invalidData)).toBe(false);
      });
    });
  });

  describe('Default Configurations', () => {
    it('should have valid default decorator config', () => {
      expect(DEFAULT_DECORATOR_CONFIG).toBeDefined();
      expect(DEFAULT_DECORATOR_CONFIG.enabled).toBe(false);
      expect(DEFAULT_DECORATOR_CONFIG.includePatterns).toContain('**/*.ts');
      expect(DEFAULT_DECORATOR_CONFIG.includePatterns).toContain('**/*.tsx');
      expect(DEFAULT_DECORATOR_CONFIG.excludePatterns).toContain('**/node_modules/**');
      expect(DEFAULT_DECORATOR_CONFIG.enableCaching).toBe(true);
      expect(DEFAULT_DECORATOR_CONFIG.overrideComments).toBe(true);
    });

    it('should have valid default precedence rules', () => {
      expect(DEFAULT_PRECEDENCE_RULES).toBeDefined();
      expect(DEFAULT_PRECEDENCE_RULES.decoratorsOverrideComments).toBe(true);
      expect(DEFAULT_PRECEDENCE_RULES.laterOverridesEarlier).toBe(true);
      expect(DEFAULT_PRECEDENCE_RULES.schemaSpecificOverridesDefault).toBe(true);
    });
  });
});

describe('DecoratorValidator', () => {
  const createMockDecorator = (name: string): ParsedDecorator => ({
    name,
    arguments: '',
    raw: `@${name}()`,
    filePath: '/test/file.ts',
    lineNumber: 1,
    target: 'function test',
  });

  describe('validate', () => {
    it('should validate GQLMethodCall decorator', () => {
      const decorator = createMockDecorator('GQLMethodCall');
      const data = {
        type: 'User',
        call: 'getUser(obj.id)',
        field: 'name',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required fields in GQLMethodCall', () => {
      const decorator = createMockDecorator('GQLMethodCall');
      const data = {
        type: '',
        call: '',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing or empty "type" parameter');
      expect(result.errors).toContain('Missing or empty "call" parameter');
    });

    it('should validate GraphQL type name format', () => {
      const decorator = createMockDecorator('GQLMethodCall');
      const data = {
        type: 'invalid-type-name',
        call: 'getUser()',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid GraphQL type name: "invalid-type-name"');
    });

    it('should validate GraphQL field name format', () => {
      const decorator = createMockDecorator('GQLMethodCall');
      const data = {
        type: 'User',
        field: 'invalid-field-name',
        call: 'getUser()',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid GraphQL field name: "invalid-field-name"');
    });

    it('should validate GQLImport decorator', () => {
      const decorator = createMockDecorator('GQLImport');
      const data = {
        importStatement: 'import { User } from "./types"',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid import statement syntax', () => {
      const decorator = createMockDecorator('GQLImport');
      const data = {
        importStatement: 'invalid import syntax',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid import statement syntax: "invalid import syntax"');
    });

    it('should validate GQLField decorator', () => {
      const decorator = createMockDecorator('GQLField');
      const data = {
        ref: 'User',
        name: 'metadata',
        type: 'UserMetadata',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required fields in GQLField', () => {
      const decorator = createMockDecorator('GQLField');
      const data = {
        ref: '',
        name: '',
        type: '',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing or empty "ref" parameter');
      expect(result.errors).toContain('Missing or empty "name" parameter');
      expect(result.errors).toContain('Missing or empty "type" parameter');
    });

    it('should validate GQLContext decorator', () => {
      const decorator = createMockDecorator('GQLContext');
      const data = {
        path: './types/Context',
        name: 'AppContext',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid TypeScript identifier in context name', () => {
      const decorator = createMockDecorator('GQLContext');
      const data = {
        path: './types/Context',
        name: 'invalid-context-name',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid TypeScript identifier for context name: "invalid-context-name"');
    });

    it('should handle unknown decorator types', () => {
      const decorator = createMockDecorator('UnknownDecorator');
      const data = {};

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true); // No errors, but warnings
      expect(result.warnings).toContain('Unknown decorator type: UnknownDecorator');
    });

    it('should detect type casting patterns', () => {
      const decorator = createMockDecorator('GQLMethodCall');
      const data = {
        type: 'User',
        call: 'getUser() as User',
      };

      const result = DecoratorValidator.validate(decorator, data);

      expect(result.valid).toBe(true);
      expect(result.warnings).toContain('Method call contains type casting but enableTypeCasting is not set to true');
    });
  });

  describe('validateBatch', () => {
    it('should validate multiple decorators', () => {
      const decorators = [
        {
          decorator: createMockDecorator('GQLMethodCall'),
          data: { type: 'User', call: 'getUser()' },
        },
        {
          decorator: createMockDecorator('GQLImport'),
          data: { importStatement: 'import { User } from "./types"' },
        },
      ];

      const results = DecoratorValidator.validateBatch(decorators);

      expect(results).toHaveLength(2);
      expect(results[0].result.valid).toBe(true);
      expect(results[1].result.valid).toBe(true);
    });
  });

  describe('getValidationSummary', () => {
    it('should provide validation summary', () => {
      const results = [
        {
          decorator: createMockDecorator('GQLMethodCall'),
          result: { valid: true, errors: [], warnings: [] },
        },
        {
          decorator: createMockDecorator('GQLImport'),
          result: { valid: false, errors: ['Test error'], warnings: [] },
        },
      ];

      const summary = DecoratorValidator.getValidationSummary(results);

      expect(summary.total).toBe(2);
      expect(summary.valid).toBe(1);
      expect(summary.invalid).toBe(1);
      expect(summary.totalErrors).toBe(1);
      expect(summary.errorsByType.GQLImport).toBe(1);
    });
  });

  describe('validateFileExists', () => {
    it('should return true for module names', async () => {
      const result = await DecoratorValidator.validateFileExists('lodash', '/test');
      expect(result).toBe(true);
    });

    it('should handle relative paths', async () => {
      // This test would need actual file system setup
      const result = await DecoratorValidator.validateFileExists('./non-existent', '/test');
      expect(result).toBe(false);
    });
  });
});
