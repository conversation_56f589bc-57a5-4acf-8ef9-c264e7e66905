import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DecoratorParser } from '../utils/decorator-parser';
import { WatchService } from '../core/watch-service';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

interface PerformanceMetrics {
  duration: number;
  memoryBefore: NodeJS.MemoryUsage;
  memoryAfter: NodeJS.MemoryUsage;
  memoryDelta: number;
  filesProcessed: number;
  decoratorsFound: number;
}

describe('Phase 5: Performance Benchmarking', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase5-performance-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  const measurePerformance = async <T>(
    operation: () => Promise<T>,
    context: { filesProcessed?: number; decoratorsFound?: number } = {}
  ): Promise<PerformanceMetrics & { result: T }> => {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    const memoryBefore = process.memoryUsage();
    const startTime = Date.now();

    const result = await operation();

    const endTime = Date.now();
    const memoryAfter = process.memoryUsage();

    return {
      duration: endTime - startTime,
      memoryBefore,
      memoryAfter,
      memoryDelta: memoryAfter.heapUsed - memoryBefore.heapUsed,
      filesProcessed: context.filesProcessed || 0,
      decoratorsFound: context.decoratorsFound || 0,
      result
    };
  };

  describe('Large Codebase Performance', () => {
    it('should handle 1000+ decorator files efficiently', async () => {
      const codebaseDir = path.join(tempDir, 'large-codebase');
      await fs.ensureDir(codebaseDir);

      const fileCount = 1000;
      
      // Generate large codebase
      console.log(`Generating ${fileCount} decorator files...`);
      for (let i = 0; i < fileCount; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
          export const resolver${i} = () => "result${i}";

          @GQLImport("import { Service${i} } from '../services/service${i}'")
          @GQLMethodCall({ type: "Type${i}", field: "data", call: "Service${i}.getData()" })
          export const getData${i} = () => null;
        `;
        await fs.writeFile(path.join(codebaseDir, `file${i}.ts`), content);
      }

      const metrics = await measurePerformance(async () => {
        const parser = new DecoratorParser();
        return await parser.scanCodebase(codebaseDir);
      }, { filesProcessed: fileCount });

      console.log(`Performance Metrics for ${fileCount} files:`);
      console.log(`- Duration: ${metrics.duration}ms`);
      console.log(`- Memory Delta: ${Math.round(metrics.memoryDelta / 1024 / 1024)}MB`);
      console.log(`- Files/sec: ${Math.round(fileCount / (metrics.duration / 1000))}`);

      // Performance expectations (adjust based on system capabilities)
      expect(metrics.duration).toBeLessThan(120000); // 2 minutes
      expect(metrics.memoryDelta).toBeLessThan(500 * 1024 * 1024); // 500MB
      expect(metrics.result.methodCalls.length).toBe(fileCount * 2); // 2 decorators per file
    }, 180000); // 3 minute timeout

    it('should handle complex schema generation efficiently', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      const typeCount = 200;
      
      // Generate complex schema
      let schemaContent = `
        type Query {
          ${Array.from({ length: typeCount }, (_, i) => `entity${i}(id: ID!): Entity${i}`).join('\n          ')}
        }

        type Mutation {
          ${Array.from({ length: typeCount }, (_, i) => `createEntity${i}(input: CreateEntity${i}Input!): Entity${i}!`).join('\n          ')}
        }
      `;

      for (let i = 0; i < typeCount; i++) {
        schemaContent += `
        type Entity${i} {
          id: ID!
          name: String!
          field${i}: String!
          relatedEntities: [Entity${(i + 1) % typeCount}!]!
        }

        input CreateEntity${i}Input {
          name: String!
          field${i}: String!
        }
        `;
      }

      await fs.writeFile(path.join(schemaDir, 'complex-schema.gql'), schemaContent);

      // Generate corresponding decorators
      for (let i = 0; i < typeCount; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "entity${i}", call: "getEntity${i}(args.id)" })
          export const getEntity${i} = (id: string) => ({ id, name: "Entity${i}" });

          @GQLMethodCall({ type: "Mutation", field: "createEntity${i}", call: "createEntity${i}(args.input)" })
          export const createEntity${i} = (input: any) => ({ id: "new", ...input });

          @GQLMethodCall({ type: "Entity${i}", field: "field${i}", call: "getField${i}(obj.id)" })
          export const getField${i} = (id: string) => \`field${i}-\${id}\`;
        `;
        await fs.writeFile(path.join(codebaseDir, `entity${i}.ts`), content);
      }

      const schema = buildSchema(schemaContent);

      const metrics = await measurePerformance(async () => {
        const generator = new SchemaBasedGenerator(schema, {
          output: outputDir,
          codebaseDir,
          enableDecorators: true,
        });
        return await generator.generate();
      }, { filesProcessed: typeCount });

      console.log(`Complex Schema Generation Metrics:`);
      console.log(`- Duration: ${metrics.duration}ms`);
      console.log(`- Memory Delta: ${Math.round(metrics.memoryDelta / 1024 / 1024)}MB`);
      console.log(`- Types processed: ${typeCount}`);

      // Verify output
      const queryFiles = await fs.readdir(path.join(outputDir, 'query'));
      const mutationFiles = await fs.readdir(path.join(outputDir, 'mutation'));
      
      expect(queryFiles.length).toBe(typeCount);
      expect(mutationFiles.length).toBe(typeCount);

      // Performance expectations
      expect(metrics.duration).toBeLessThan(300000); // 5 minutes
      expect(metrics.memoryDelta).toBeLessThan(1024 * 1024 * 1024); // 1GB
    }, 360000); // 6 minute timeout
  });

  describe('Memory Usage Monitoring', () => {
    it('should not have memory leaks during repeated operations', async () => {
      const codebaseDir = path.join(tempDir, 'memory-test');
      await fs.ensureDir(codebaseDir);

      // Create test files
      const fileCount = 100;
      for (let i = 0; i < fileCount; i++) {
        await fs.writeFile(path.join(codebaseDir, `memory-test-${i}.ts`), `
          @GQLMethodCall({ type: "Query", field: "test${i}", call: "test${i}()" })
          export const test${i} = () => "test${i}";
        `);
      }

      const iterations = 10;
      const memoryMeasurements: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const metrics = await measurePerformance(async () => {
          const parser = new DecoratorParser();
          return await parser.scanCodebase(codebaseDir);
        });

        memoryMeasurements.push(metrics.memoryAfter.heapUsed);

        // Force garbage collection between iterations
        if (global.gc) {
          global.gc();
        }
      }

      // Check for memory leaks
      const firstMeasurement = memoryMeasurements[0];
      const lastMeasurement = memoryMeasurements[memoryMeasurements.length - 1];
      const memoryIncrease = lastMeasurement - firstMeasurement;

      console.log(`Memory Leak Test:`);
      console.log(`- First measurement: ${Math.round(firstMeasurement / 1024 / 1024)}MB`);
      console.log(`- Last measurement: ${Math.round(lastMeasurement / 1024 / 1024)}MB`);
      console.log(`- Total increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);

      // Memory increase should be minimal (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle concurrent operations without excessive memory usage', async () => {
      const codebaseDir = path.join(tempDir, 'concurrent-test');
      await fs.ensureDir(codebaseDir);

      // Create test files
      for (let i = 0; i < 50; i++) {
        await fs.writeFile(path.join(codebaseDir, `concurrent-${i}.ts`), `
          @GQLMethodCall({ type: "Query", field: "concurrent${i}", call: "concurrent${i}()" })
          export const concurrent${i} = () => "concurrent${i}";
        `);
      }

      const metrics = await measurePerformance(async () => {
        // Run multiple parsers concurrently
        const promises = Array.from({ length: 5 }, () => {
          const parser = new DecoratorParser();
          return parser.scanCodebase(codebaseDir);
        });

        return await Promise.all(promises);
      });

      console.log(`Concurrent Operations Metrics:`);
      console.log(`- Duration: ${metrics.duration}ms`);
      console.log(`- Memory Delta: ${Math.round(metrics.memoryDelta / 1024 / 1024)}MB`);

      // All parsers should find the same decorators
      const results = metrics.result;
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result.methodCalls).toHaveLength(50);
      });

      // Memory usage should be reasonable for concurrent operations
      expect(metrics.memoryDelta).toBeLessThan(200 * 1024 * 1024); // 200MB
    });
  });

  describe('Watch Mode Performance', () => {
    it('should handle file changes efficiently', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      await fs.writeFile(path.join(schemaDir, 'watch-test.gql'), `
        type User {
          id: ID!
          name: String!
        }

        type Query {
          user(id: ID!): User
        }
      `);

      await fs.writeFile(path.join(codebaseDir, 'initial.ts'), `
        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ id, name: "Test" });
      `);

      // Test watch service setup performance
      const setupMetrics = await measurePerformance(async () => {
        return new WatchService({
          schema: path.join(schemaDir, '**/*.gql'),
          output: outputDir,
          codebaseDir,
          enableDecorators: true,
          initial: false,
        });
      });

      console.log(`Watch Service Setup Metrics:`);
      console.log(`- Duration: ${setupMetrics.duration}ms`);
      console.log(`- Memory Delta: ${Math.round(setupMetrics.memoryDelta / 1024 / 1024)}MB`);

      // Setup should be fast
      expect(setupMetrics.duration).toBeLessThan(5000); // 5 seconds
      expect(setupMetrics.memoryDelta).toBeLessThan(50 * 1024 * 1024); // 50MB

      const watchService = setupMetrics.result;

      // Test that watch paths are correctly configured
      const watchPaths = (watchService as any).getWatchPaths();
      expect(watchPaths.length).toBeGreaterThan(0);
      expect(watchPaths).toContain(path.resolve(codebaseDir));
    });
  });

  describe('Scalability Benchmarks', () => {
    it('should scale linearly with file count', async () => {
      const fileCounts = [10, 50, 100, 200];
      const results: Array<{ fileCount: number; duration: number; throughput: number }> = [];

      for (const fileCount of fileCounts) {
        const testDir = path.join(tempDir, `scale-test-${fileCount}`);
        await fs.ensureDir(testDir);

        // Generate files
        for (let i = 0; i < fileCount; i++) {
          await fs.writeFile(path.join(testDir, `file${i}.ts`), `
            @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
            export const resolver${i} = () => "result${i}";
          `);
        }

        const metrics = await measurePerformance(async () => {
          const parser = new DecoratorParser();
          return await parser.scanCodebase(testDir);
        });

        const throughput = fileCount / (metrics.duration / 1000); // files per second

        results.push({
          fileCount,
          duration: metrics.duration,
          throughput
        });

        console.log(`Scale Test - ${fileCount} files: ${metrics.duration}ms (${throughput.toFixed(2)} files/sec)`);
      }

      // Verify roughly linear scaling (throughput shouldn't degrade significantly)
      const firstThroughput = results[0].throughput;
      const lastThroughput = results[results.length - 1].throughput;
      const throughputRatio = lastThroughput / firstThroughput;

      console.log(`Throughput ratio (last/first): ${throughputRatio.toFixed(2)}`);
      
      // Throughput shouldn't degrade by more than 50%
      expect(throughputRatio).toBeGreaterThan(0.5);
    });
  });
});
