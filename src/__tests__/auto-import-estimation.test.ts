import { DecoratorParser, DecoratorContainer } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { AliasConfig, AliasConfigUtils } from '../utils/alias-config';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Auto-Import Estimation Enhancement', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'auto-import-test-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Auto-import when @GQLImport is missing', () => {
    it('should generate auto-import using alias configuration', async () => {
      // Create resolver with method call but no explicit import
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Set alias configuration
      const aliasConfig = AliasConfigUtils.createAliasConfig(tempDir, '@app');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart imports
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      const imports = processor.generateSmartImportsForTypeField(
        'User',
        'name',
        targetOutputPath
      );

      expect(imports).toHaveLength(1);
      expect(imports[0]).toContain('import { getUserName }');
      expect(imports[0]).toContain('@app/src/resolvers/user');
    });

    it('should generate auto-import using relative paths when no alias', async () => {
      // Create resolver with method call but no explicit import
      const resolverContent = `
        @GQLMethodCall({ type: "Product", field: "title", call: "getProductTitle(obj.id)" })
        export const getProductTitle = (id: string) => \`Product \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'resolvers', 'product.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Don't set alias configuration

      // Generate smart imports
      const targetOutputPath = path.join(tempDir, 'output', 'product', 'title.ts');
      const imports = processor.generateSmartImportsForTypeField(
        'Product',
        'title',
        targetOutputPath
      );

      expect(imports).toHaveLength(1);
      expect(imports[0]).toContain('import { getProductTitle }');
      expect(imports[0]).toContain('from \'../');
    });

    it('should detect and use index file exports', async () => {
      // Create index file with exports
      const indexContent = `
        export { getUserProfile } from './profile';
        export { getUserSettings } from './settings';
      `;

      const indexPath = path.join(tempDir, 'src', 'user', 'index.ts');
      await fs.ensureDir(path.dirname(indexPath));
      await fs.writeFile(indexPath, indexContent);

      // Create resolver that uses function from index
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "profile", call: "getUserProfile(obj.id)" })
        export const someOtherFunction = () => null;
      `;

      const resolverPath = path.join(tempDir, 'src', 'user', 'resolver.ts');
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Set alias configuration
      const aliasConfig = AliasConfigUtils.createAliasConfig(tempDir, '@app');
      processor.setAliasConfig(aliasConfig || undefined);

      // Generate smart imports
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'profile.ts');
      const imports = processor.generateSmartImportsForTypeField(
        'User',
        'profile',
        targetOutputPath
      );

      expect(imports).toHaveLength(1);
      expect(imports[0]).toContain('import { getUserProfile }');
      expect(imports[0]).toContain('@app/src/user'); // Should import from directory (index)
    });

    it('should not generate auto-import when explicit @GQLImport exists', async () => {
      // Create resolver with both explicit import and method call
      const resolverContent = `
        @GQLImport("import { getUserEmail } from '../services/user-service'")
        @GQLMethodCall({ type: "User", field: "email", call: "getUserEmail(obj.id)" })
        export const getUserEmail = (id: string) => \`user\${id}@example.com\`;
      `;

      const resolverPath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Generate smart imports
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'email.ts');
      const imports = processor.generateSmartImportsForTypeField(
        'User',
        'email',
        targetOutputPath
      );

      // Should use the existing smart import logic, not auto-import
      expect(imports).toHaveLength(1);
      expect(imports[0]).toContain('import { getUserEmail }');
    });
  });

  describe('Schema identifier support', () => {
    it('should handle schema identifiers in method calls', async () => {
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)", schema: "admin" })
        export const getUserName = (id: string) => \`Admin User \${id}\`;

        @GQLMethodCall({ type: "User", field: "name", call: "getPublicUserName(obj.id)", schema: "public" })
        export const getPublicUserName = (id: string) => \`Public User \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Generate smart imports for admin schema
      const targetOutputPath = path.join(tempDir, 'output', 'user', 'name.ts');
      const adminImports = processor.generateSmartImportsForTypeField(
        'User',
        'name',
        targetOutputPath,
        'admin'
      );

      // Generate smart imports for public schema
      const publicImports = processor.generateSmartImportsForTypeField(
        'User',
        'name',
        targetOutputPath,
        'public'
      );

      // Should get different imports for different schemas
      expect(adminImports).toHaveLength(1);
      expect(adminImports[0]).toContain('getUserName');

      expect(publicImports).toHaveLength(1);
      expect(publicImports[0]).toContain('getPublicUserName');
    });

    it('should validate schema identifiers and detect collisions', async () => {
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName1(obj.id)", schema: "schema1" })
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName2(obj.id)", schema: "schema2" })
        @GQLMethodCall({ type: "User", field: "email", call: "getUserEmail(obj.id)", schema: "invalid-schema!" })
        export const getUserName1 = (id: string) => \`User1 \${id}\`;
        export const getUserName2 = (id: string) => \`User2 \${id}\`;
        export const getUserEmail = (id: string) => \`user\${id}@example.com\`;
      `;

      const resolverPath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);

      // Validate schema identifiers
      const validation = processor.validateSchemaIdentifiers(result);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toHaveLength(1);
      expect(validation.errors[0]).toContain('invalid-schema!');
      expect(validation.warnings).toHaveLength(1);
      expect(validation.warnings[0]).toContain('Schema collision detected');
      expect(validation.collisions).toHaveLength(1);
      expect(validation.collisions[0].type).toBe('User');
      expect(validation.collisions[0].field).toBe('name');
      expect(validation.collisions[0].schemas).toEqual(['schema1', 'schema2']);
    });

    it('should include schema identifier in generated comments', async () => {
      const resolverContent = `
        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)", schema: "admin" })
        export const getUserName = (id: string) => \`Admin User \${id}\`;
      `;

      const resolverPath = path.join(tempDir, 'src', 'resolvers', 'user.ts');
      await fs.ensureDir(path.dirname(resolverPath));
      await fs.writeFile(resolverPath, resolverContent);

      // Scan codebase
      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      // Convert to directive container
      const directives = processor.convertToDirectiveContainer(result, 'admin');

      expect(directives.methodCalls).toHaveLength(1);
      expect(directives.methodCalls[0].raw).toContain('[Schema: admin]');
    });
  });
});
