import { DecoratorValidator, InheritanceValidationContext, InheritanceConflict } from '../utils/decorator-validator';
import { DecoratorContainer, ParsedDecorator } from '../utils/decorator-parser';
import { DirectiveContainer } from '../utils/directive-parser';

describe('Phase 4: Comprehensive Error Handling and Validation', () => {
  describe('Inheritance Context Validation', () => {
    it('should validate inheritance context without conflicts', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserName(obj.id)',
            raw: '@methodCall(getUserName(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
      };

      const result = DecoratorValidator.validateInheritanceContext(context);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should detect conflicts between decorator and comment directives', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserNameFromDecorator(obj.id)',
            raw: '@methodCall(getUserNameFromDecorator(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserNameFromComment(obj.id)',
            raw: '@methodCall(getUserNameFromComment(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
      };

      const result = DecoratorValidator.validateInheritanceContext(context);
      
      expect(result.valid).toBe(true); // Should be valid but with warnings
      expect(result.errors).toHaveLength(0);
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings[0]).toContain('Conflicting method calls');
    });

    it('should warn about decorator types not in inheritance chain', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'type: "SomeOtherType", field: "name", call: "getSomething(obj.id)"',
            raw: '@methodCall(type: "SomeOtherType", field: "name", call: "getSomething(obj.id)")',
          }],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
      };

      const result = DecoratorValidator.validateInheritanceContext(context);
      
      expect(result.valid).toBe(true);
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(w => w.includes('not in the inheritance chain'))).toBe(true);
    });

    it('should warn about precedence when both decorator and comment directives exist', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'email',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [{
            name: 'import',
            content: 'import { UserService } from "../services"',
            raw: '@import(import { UserService } from "../services")',
          }],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [{
            name: 'import',
            content: 'import { EmailService } from "../email"',
            raw: '@import(import { EmailService } from "../email")',
          }],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
      };

      const result = DecoratorValidator.validateInheritanceContext(context);
      
      expect(result.valid).toBe(true);
      expect(result.warnings.some(w => w.includes('Decorator directives will take precedence'))).toBe(true);
    });
  });

  describe('Conflict Detection', () => {
    it('should detect method call conflicts', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserName(obj.id)',
            raw: '@methodCall(getUserName(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getNameFromComment(obj.id)',
            raw: '@methodCall(getNameFromComment(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
      };

      const conflicts = DecoratorValidator.detectInheritanceConflicts(context);
      
      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].type).toBe('method_call');
      expect(conflicts[0].severity).toBe('warning');
      expect(conflicts[0].message).toContain('Conflicting method calls');
    });

    it('should detect import conflicts', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [{
            name: 'import',
            content: 'import { UserService } from "../services"',
            raw: '@import(import { UserService } from "../services")',
          }],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [{
            name: 'import',
            content: 'import { UserHelper } from "../services"',
            raw: '@import(import { UserHelper } from "../services")',
          }],
          methodCalls: [],
          fieldFields: [],
          others: {},
        },
      };

      const conflicts = DecoratorValidator.detectInheritanceConflicts(context);
      
      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].type).toBe('import');
      expect(conflicts[0].severity).toBe('warning');
      expect(conflicts[0].message).toContain('Conflicting imports');
    });

    it('should not detect conflicts when directives are identical', () => {
      const context: InheritanceValidationContext = {
        typeName: 'User',
        fieldName: 'name',
        interfaces: ['BaseEntity'],
        decoratorDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserName(obj.id)',
            raw: '@methodCall(getUserName(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
        commentDirectives: {
          imports: [],
          methodCalls: [{
            name: 'methodCall',
            content: 'getUserName(obj.id)',
            raw: '@methodCall(getUserName(obj.id))',
          }],
          fieldFields: [],
          others: {},
        },
      };

      const conflicts = DecoratorValidator.detectInheritanceConflicts(context);
      
      expect(conflicts).toHaveLength(0);
    });
  });

  describe('Decorator Container Validation', () => {
    it('should validate a complete decorator container', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ type: "User", field: "name", call: "getUserName(obj.id)" }',
              raw: '@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })',
              filePath: '/test/decorators.ts',
              lineNumber: 1,
              target: 'function getUserName',
            },
            data: {
              type: 'User',
              field: 'name',
              call: 'getUserName(obj.id)',
            },
          },
        ],
        imports: [
          {
            decorator: {
              name: 'GQLImport',
              arguments: '"import { UserService } from \'../services\'"',
              raw: '@GQLImport("import { UserService } from \'../services\'")',
              filePath: '/test/decorators.ts',
              lineNumber: 5,
              target: 'function getUserName',
            },
            data: {
              importStatement: 'import { UserService } from \'../services\'',
            },
          },
        ],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = DecoratorValidator.validateDecoratorContainer(decoratorContainer, ['BaseEntity']);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect validation errors in decorator container', () => {
      const decoratorContainer: DecoratorContainer = {
        methodCalls: [
          {
            decorator: {
              name: 'GQLMethodCall',
              arguments: '{ invalid: "data" }', // Invalid structure
              raw: '@GQLMethodCall({ invalid: "data" })',
              filePath: '/test/decorators.ts',
              lineNumber: 1,
              target: 'function invalid',
            },
            data: {
              type: '', // Invalid: empty type
              call: '', // Invalid: empty call
            } as any,
          },
        ],
        imports: [],
        fields: [],
        contexts: [],
        others: {},
      };

      const result = DecoratorValidator.validateDecoratorContainer(decoratorContainer);
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Helper Methods', () => {
    it('should extract module names from import statements', () => {
      const extractModuleName = (DecoratorValidator as any).extractModuleName;
      
      expect(extractModuleName('import { UserService } from "../services"')).toBe('../services');
      expect(extractModuleName('import UserService from "user-service"')).toBe('user-service');
      expect(extractModuleName('import * as Utils from "./utils"')).toBe('./utils');
    });

    it('should extract import names from import statements', () => {
      const extractImportNames = (DecoratorValidator as any).extractImportNames;
      
      expect(extractImportNames('import { UserService, EmailService } from "../services"'))
        .toEqual(['UserService', 'EmailService']);
      expect(extractImportNames('import UserService from "user-service"'))
        .toEqual(['UserService']);
      expect(extractImportNames('import * as Utils from "./utils"'))
        .toEqual([]);
    });

    it('should validate field names for types', () => {
      const isValidFieldForType = (DecoratorValidator as any).isValidFieldForType;
      
      // Currently returns true for all fields (simplified implementation)
      expect(isValidFieldForType('name', 'User')).toBe(true);
      expect(isValidFieldForType('email', 'User')).toBe(true);
      expect(isValidFieldForType('invalidField', 'User')).toBe(true);
    });
  });
});
