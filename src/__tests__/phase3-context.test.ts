import { DecoratorParser } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { DecoratorValidator } from '../utils/decorator-validator';
import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 3: @GQLContext Configuration', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase3-context-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Context decorator processing', () => {
    it('should process @GQLContext decorators', async () => {
      const contextContent = `
        @GQLContext({ path: "./types/AppContext", name: "AppContext", schema: "default" })
        export class ContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'context.ts'), contextContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.contexts).toHaveLength(1);

      const contextData = result.contexts[0].data;
      expect(contextData.path).toBe('./types/AppContext');
      expect(contextData.name).toBe('AppContext');
      expect(contextData.schema).toBe('default');
    });

    it('should handle context without schema (default)', async () => {
      const contextContent = `
        @GQLContext({ path: "./types/Context", name: "Context" })
        export class DefaultContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'default-context.ts'), contextContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.contexts).toHaveLength(1);

      const contextData = result.contexts[0].data;
      expect(contextData.path).toBe('./types/Context');
      expect(contextData.name).toBe('Context');
      expect(contextData.schema).toBeUndefined();
    });

    it('should handle multiple schema contexts', async () => {
      const contextContent = `
        @GQLContext({ path: "./types/AppContext", name: "AppContext", schema: "default" })
        export class DefaultContextConfig {}

        @GQLContext({ path: "./types/AdminContext", name: "AdminContext", schema: "admin" })
        export class AdminContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'multi-context.ts'), contextContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.contexts).toHaveLength(2);

      const defaultContext = result.contexts.find(c => c.data.schema === 'default');
      const adminContext = result.contexts.find(c => c.data.schema === 'admin');

      expect(defaultContext).toBeDefined();
      expect(defaultContext!.data.name).toBe('AppContext');

      expect(adminContext).toBeDefined();
      expect(adminContext!.data.name).toBe('AdminContext');
    });
  });

  describe('Context directive conversion', () => {
    it('should convert @GQLContext to context directive', async () => {
      const contextContent = `
        @GQLContext({ path: "./types/MyContext", name: "MyContext" })
        export class ContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'context-conversion.ts'), contextContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.others.context).toHaveLength(1);

      const contextDirective = directives.others.context[0];
      expect(contextDirective.content).toBe('{path: "./types/MyContext", name: "MyContext"}');
    });
  });

  describe('Context resolution integration', () => {
    it('should use decorator context in SchemaBasedGenerator', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const contextContent = `
        @GQLContext({ path: "./types/CustomContext", name: "CustomContext" })
        export class ContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'context-integration.ts'), contextContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize to scan decorators
      await generator.generate();

      // Check that context is resolved from decorators
      const contextConfig = generator.getContextForSchema();
      expect(contextConfig).toBeDefined();
      expect(contextConfig!.path).toBe('./types/CustomContext');
      expect(contextConfig!.name).toBe('CustomContext');
    });

    it('should support per-schema context configuration', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const contextContent = `
        @GQLContext({ path: "./types/DefaultContext", name: "DefaultContext", schema: "default" })
        export class DefaultContextConfig {}

        @GQLContext({ path: "./types/AdminContext", name: "AdminContext", schema: "admin" })
        export class AdminContextConfig {}
      `;

      await fs.ensureDir(path.join(tempDir, 'src', 'resolvers'));
      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'user-resolvers.ts'), contextContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize to scan decorators
      await generator.generate();

      // Test default schema context
      const defaultContext = generator.getContextForSchema('default');
      expect(defaultContext).toBeDefined();
      expect(defaultContext!.name).toBe('DefaultContext');

      // Test admin schema context
      const adminContext = generator.getContextForSchema('admin');
      expect(adminContext).toBeDefined();
      expect(adminContext!.name).toBe('AdminContext');

      // Test fallback to default when no schema specified
      const fallbackContext = generator.getContextForSchema();
      expect(fallbackContext).toBeDefined();
      expect(fallbackContext!.name).toBe('DefaultContext');
    });

    it('should fall back to options when no decorator context found', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
        context: './types/OptionsContext',
        contextName: 'OptionsContext',
      });

      // Initialize to scan decorators (none exist)
      await generator.generate();

      // Should fall back to options
      const contextConfig = generator.getContextForSchema();
      expect(contextConfig).toBeDefined();
      expect(contextConfig!.path).toBe('./types/OptionsContext');
      expect(contextConfig!.name).toBe('OptionsContext');
    });

    it('should prioritize decorator context over options', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      const contextContent = `
        @GQLContext({ path: "./types/DecoratorContext", name: "DecoratorContext" })
        export class ContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'priority-context.ts'), contextContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
        context: './types/OptionsContext',
        contextName: 'OptionsContext',
      });

      // Initialize to scan decorators
      await generator.generate();

      // Should use decorator context, not options
      const contextConfig = generator.getContextForSchema();
      expect(contextConfig).toBeDefined();
      expect(contextConfig!.path).toBe('./types/DecoratorContext');
      expect(contextConfig!.name).toBe('DecoratorContext');
    });
  });

  describe('Context validation', () => {
    it('should validate required context parameters', async () => {
      const invalidContextContent = `
        @GQLContext({ name: "Context" })  // Missing path
        export class InvalidContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'invalid-context.ts'), invalidContextContent);

      const result = await parser.scanCodebase(tempDir);
      expect(result.contexts).toHaveLength(0); // Should be filtered out due to validation
    });

    it('should validate context name format', async () => {
      const contextContent = `
        @GQLContext({ path: "./types/Context", name: "ValidContext" })
        export class ValidContextConfig {}

        @GQLContext({ path: "./types/Context", name: "123Invalid" })
        export class InvalidContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'context-validation.ts'), contextContent);

      const result = await parser.scanCodebase(tempDir);
      // Both contexts should be parsed, but validation should flag the invalid one
      expect(result.contexts).toHaveLength(2);

      // Validate the contexts
      const validationResults = result.contexts.map(context =>
        DecoratorValidator.validate(context.decorator, context.data)
      );

      // One should be valid, one should have errors
      const validContexts = validationResults.filter(r => r.valid);
      const invalidContexts = validationResults.filter(r => !r.valid);

      expect(validContexts).toHaveLength(1);
      expect(invalidContexts).toHaveLength(1);

      // Find the valid context from the original data
      const validContext = result.contexts.find(context =>
        DecoratorValidator.validate(context.decorator, context.data).valid
      );
      expect(validContext?.data.name).toBe('ValidContext');
    });
  });

  describe('Integration with existing context resolution', () => {
    it('should work alongside schema-based context directives', async () => {
      const schema = buildSchema(`
        type Query {
          hello: String
        }
      `);

      // Create a schema file with context directive
      const schemaContent = `
        # @context(path: "./types/SchemaContext", name: "SchemaContext")
        type Query {
          hello: String
        }
      `;

      await fs.writeFile(path.join(tempDir, 'schema.gql'), schemaContent);

      // Create decorator context (should take precedence)
      const contextContent = `
        @GQLContext({ path: "./types/DecoratorContext", name: "DecoratorContext" })
        export class ContextConfig {}
      `;

      await fs.writeFile(path.join(tempDir, 'decorator-context.ts'), contextContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      // Initialize to scan decorators
      await generator.generate();

      // Decorator context should take precedence over schema directive
      const contextConfig = generator.getContextForSchema();
      expect(contextConfig).toBeDefined();
      expect(contextConfig!.name).toBe('DecoratorContext');
    });
  });
});
