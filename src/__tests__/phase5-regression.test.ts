import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DirectiveManager } from '../utils/directive-manager';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 5: Regression Testing', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase5-regression-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Comment-Based Directive System Regression', () => {
    it('should maintain all existing comment-based directive functionality', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      // Test all existing comment-based directive patterns
      await fs.writeFile(path.join(schemaDir, 'legacy.gql'), `
        type User {
          id: ID!
          # @methodCall(getUserName(obj.id))
          name: String!
          # @methodCall(getUserEmail(obj.id))
          # @import(import { UserService } from '../services/user-service';)
          email: String!
          # @field(ref: "User", name: "metadata", type: "UserMetadata")
          metadata: UserMetadata
        }

        type UserMetadata {
          createdAt: String!
          updatedAt: String!
        }

        type Query {
          # @methodCall(UserService.findById(args.id))
          # @import(import { UserService } from '../services/user-service';)
          user(id: ID!): User
          # @methodCall(UserService.findAll())
          users: [User!]!
        }

        type Mutation {
          # @methodCall(UserService.create(args.input))
          createUser(input: CreateUserInput!): User!
        }

        input CreateUserInput {
          name: String!
          email: String!
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'legacy.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        // Note: NOT enabling decorators to test pure comment-based functionality
        enableDecorators: false,
      });

      await generator.generate();

      // Verify all comment-based directives still work
      const expectedFiles = [
        'user/name.ts',
        'user/email.ts',
        'query/user.ts',
        'query/users.ts',
        'mutation/create-user.ts'
      ];

      for (const file of expectedFiles) {
        const filePath = path.join(outputDir, file);
        expect(fs.existsSync(filePath)).toBe(true);
        
        const content = await fs.readFile(filePath, 'utf8');
        expect(content).toContain('export');
        
        // Verify imports are properly generated
        if (file.includes('email') || file.includes('user')) {
          expect(content).toContain('UserService');
        }
      }

      // Verify method calls are correctly implemented
      const userNameContent = await fs.readFile(path.join(outputDir, 'user/name.ts'), 'utf8');
      expect(userNameContent).toContain('getUserName');

      const userEmailContent = await fs.readFile(path.join(outputDir, 'user/email.ts'), 'utf8');
      expect(userEmailContent).toContain('getUserEmail');
      expect(userEmailContent).toContain('import { UserService }');

      const queryUserContent = await fs.readFile(path.join(outputDir, 'query/user.ts'), 'utf8');
      expect(queryUserContent).toContain('UserService.findById');
    });

    it('should maintain interface inheritance functionality', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'inheritance.gql'), `
        interface Node {
          id: ID!
          # @methodCall(getNodeCreatedAt(obj.id))
          createdAt: String!
        }

        interface Timestamped {
          # @methodCall(getTimestampedUpdatedAt(obj.id))
          updatedAt: String!
        }

        type User implements Node & Timestamped {
          id: ID!
          createdAt: String!
          updatedAt: String!
          # @methodCall(getUserName(obj.id))
          name: String!
        }

        type Post implements Node {
          id: ID!
          createdAt: String!
          # @methodCall(getPostTitle(obj.id))
          title: String!
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'inheritance.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false,
      });

      await generator.generate();

      // Verify inheritance delegation works
      const userCreatedAtFile = path.join(outputDir, 'user/created-at.ts');
      const postCreatedAtFile = path.join(outputDir, 'post/created-at.ts');
      const userUpdatedAtFile = path.join(outputDir, 'user/updated-at.ts');

      expect(fs.existsSync(userCreatedAtFile)).toBe(true);
      expect(fs.existsSync(postCreatedAtFile)).toBe(true);
      expect(fs.existsSync(userUpdatedAtFile)).toBe(true);

      // Both User and Post should delegate to Node's createdAt implementation
      const userCreatedAtContent = await fs.readFile(userCreatedAtFile, 'utf8');
      const postCreatedAtContent = await fs.readFile(postCreatedAtFile, 'utf8');
      
      expect(userCreatedAtContent).toContain('getNodeCreatedAt');
      expect(postCreatedAtContent).toContain('getNodeCreatedAt');

      // User should delegate to Timestamped's updatedAt implementation
      const userUpdatedAtContent = await fs.readFile(userUpdatedAtFile, 'utf8');
      expect(userUpdatedAtContent).toContain('getTimestampedUpdatedAt');

      // User should have its own name implementation
      const userNameFile = path.join(outputDir, 'user/name.ts');
      expect(fs.existsSync(userNameFile)).toBe(true);
      const userNameContent = await fs.readFile(userNameFile, 'utf8');
      expect(userNameContent).toContain('getUserName');
    });

    it('should maintain multi-deep inheritance chains', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'multi-deep.gql'), `
        interface Level1 {
          # @methodCall(getLevel1Field())
          level1Field: String!
        }

        interface Level2 implements Level1 {
          level1Field: String!
          # @methodCall(getLevel2Field())
          level2Field: String!
        }

        interface Level3 implements Level2 {
          level1Field: String!
          level2Field: String!
          # @methodCall(getLevel3Field())
          level3Field: String!
        }

        type ComplexType implements Level3 {
          level1Field: String!
          level2Field: String!
          level3Field: String!
          # @methodCall(getOwnField())
          ownField: String!
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'multi-deep.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false,
      });

      await generator.generate();

      // Verify all levels of inheritance work
      const complexTypeLevel1File = path.join(outputDir, 'complex-type/level1-field.ts');
      const complexTypeLevel2File = path.join(outputDir, 'complex-type/level2-field.ts');
      const complexTypeLevel3File = path.join(outputDir, 'complex-type/level3-field.ts');
      const complexTypeOwnFile = path.join(outputDir, 'complex-type/own-field.ts');

      expect(fs.existsSync(complexTypeLevel1File)).toBe(true);
      expect(fs.existsSync(complexTypeLevel2File)).toBe(true);
      expect(fs.existsSync(complexTypeLevel3File)).toBe(true);
      expect(fs.existsSync(complexTypeOwnFile)).toBe(true);

      // Verify each level delegates to the correct implementation
      const level1Content = await fs.readFile(complexTypeLevel1File, 'utf8');
      const level2Content = await fs.readFile(complexTypeLevel2File, 'utf8');
      const level3Content = await fs.readFile(complexTypeLevel3File, 'utf8');
      const ownContent = await fs.readFile(complexTypeOwnFile, 'utf8');

      expect(level1Content).toContain('getLevel1Field');
      expect(level2Content).toContain('getLevel2Field');
      expect(level3Content).toContain('getLevel3Field');
      expect(ownContent).toContain('getOwnField');
    });

    it('should maintain smart watcher functionality without decorators', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'watch-test.gql'), `
        type User {
          id: ID!
          # @methodCall(getUserName(obj.id))
          name: String!
        }

        type Query {
          # @methodCall(getUser(args.id))
          user(id: ID!): User
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'watch-test.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false,
      });

      await generator.generate();

      // Verify initial generation works
      const userNameFile = path.join(outputDir, 'user/name.ts');
      expect(fs.existsSync(userNameFile)).toBe(true);

      // Modify schema and regenerate
      await fs.writeFile(path.join(schemaDir, 'watch-test.gql'), `
        type User {
          id: ID!
          # @methodCall(getUserName(obj.id))
          name: String!
          # @methodCall(getUserEmail(obj.id))
          email: String!
        }

        type Query {
          # @methodCall(getUser(args.id))
          user(id: ID!): User
        }
      `);

      const updatedSchema = buildSchema(await fs.readFile(path.join(schemaDir, 'watch-test.gql'), 'utf8'));
      
      const updatedGenerator = new SchemaBasedGenerator(updatedSchema, {
        output: outputDir,
        enableDecorators: false,
      });

      await updatedGenerator.generate();

      // Verify new field was added
      const userEmailFile = path.join(outputDir, 'user/email.ts');
      expect(fs.existsSync(userEmailFile)).toBe(true);
      
      const emailContent = await fs.readFile(userEmailFile, 'utf8');
      expect(emailContent).toContain('getUserEmail');
    });
  });

  describe('Core Functionality Regression', () => {
    it('should maintain type generation accuracy', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'types.gql'), `
        scalar DateTime
        scalar JSON

        enum UserRole {
          ADMIN
          USER
          MODERATOR
        }

        type User {
          id: ID!
          name: String!
          email: String!
          role: UserRole!
          metadata: JSON
          createdAt: DateTime!
          posts: [Post!]!
        }

        type Post {
          id: ID!
          title: String!
          content: String!
          author: User!
          tags: [String!]!
          published: Boolean!
        }

        type Query {
          user(id: ID!): User
          users(role: UserRole): [User!]!
          post(id: ID!): Post
          posts(authorId: ID): [Post!]!
        }

        type Mutation {
          createUser(input: CreateUserInput!): User!
          updateUser(id: ID!, input: UpdateUserInput!): User!
          deleteUser(id: ID!): Boolean!
        }

        input CreateUserInput {
          name: String!
          email: String!
          role: UserRole = USER
        }

        input UpdateUserInput {
          name: String
          email: String
          role: UserRole
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'types.gql'), 'utf8'));
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false,
      });

      await generator.generate();

      // Verify core type files are generated
      const graphqlTypesFile = path.join(outputDir, 'graphql.ts');
      expect(fs.existsSync(graphqlTypesFile)).toBe(true);

      const typesContent = await fs.readFile(graphqlTypesFile, 'utf8');
      
      // Verify all types are properly generated
      expect(typesContent).toContain('export type User');
      expect(typesContent).toContain('export type Post');
      expect(typesContent).toContain('export enum UserRole');
      expect(typesContent).toContain('export type CreateUserInput');
      expect(typesContent).toContain('export type UpdateUserInput');
      
      // Verify scalar mappings
      expect(typesContent).toContain('DateTime');
      expect(typesContent).toContain('JSON');
    });
  });
});
