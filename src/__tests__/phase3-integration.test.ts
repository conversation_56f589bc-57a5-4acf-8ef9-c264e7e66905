import { DecoratorParser } from '../utils/decorator-parser';
import { DecoratorProcessor } from '../utils/decorator-processor';
import { DecoratorValidator } from '../utils/decorator-validator';
import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DEFAULT_DECORATOR_CONFIG, DEFAULT_PRECEDENCE_RULES } from '../utils/decorator-types';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 3: Integration and Multi-Schema Support', () => {
  let tempDir: string;
  let parser: DecoratorParser;
  let processor: DecoratorProcessor;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase3-integration-'));
    parser = new DecoratorParser();
    processor = new DecoratorProcessor(
      { ...DEFAULT_DECORATOR_CONFIG, enabled: true, codebaseDir: tempDir },
      DEFAULT_PRECEDENCE_RULES
    );
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Complete decorator system integration', () => {
    it('should handle all decorator types together', async () => {
      const schema = buildSchema(`
        type UserMetadata {
          createdAt: String
          updatedAt: String
        }

        type User {
          id: ID!
          name: String!
          email: String!
          metadata: UserMetadata
        }
        type Query {
          user(id: ID!): User
          users: [User!]!
        }
      `);

      const completeResolverContent = `// Context configuration
@GQLContext({ path: "./types/AppContext", name: "AppContext" })
export class ContextConfig {}

// Import declarations
@GQLImport("import { UserService } from '../services/user-service'")
@GQLImport("import { Logger } from '../utils/logger'")
export class ImportConfig {}

// Field definitions
@GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
export interface UserMetadata {
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}

// Method call resolvers
@GQLMethodCall({ type: "Query", field: "user", call: "UserService.getById(args.id)" })
export const getUserById = (id: string) => ({ id, name: "Test User", email: "<EMAIL>" });

@GQLMethodCall({ type: "Query", field: "users", call: "UserService.getAll()" })
export const getAllUsers = () => [{ id: "1", name: "User 1", email: "<EMAIL>" }];

@GQLMethodCall({ type: "User", field: "email", call: "UserService.getEmail(obj.id)" })
export const getUserEmail = (id: string) => \`user-\${id}@example.com\`;`;

      await fs.ensureDir(path.join(tempDir, 'src', 'resolvers'));
      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'user-resolvers.ts'), completeResolverContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      await generator.generate();

      // Verify all decorator types are processed
      const result = await parser.scanCodebase(tempDir);
      expect(result.contexts).toHaveLength(1);
      expect(result.imports).toHaveLength(2);
      expect(result.fields).toHaveLength(1);
      expect(result.methodCalls).toHaveLength(3);

      // Verify context resolution
      const contextConfig = generator.getContextForSchema();
      expect(contextConfig).toBeDefined();
      expect(contextConfig!.name).toBe('AppContext');

      // Verify method call filtering
      const userEmailDirectives = generator.getDecoratorDirectives('User', 'email');
      expect(userEmailDirectives.methodCalls).toHaveLength(1);
      expect(userEmailDirectives.imports).toHaveLength(2);

      // Verify smart imports
      const smartImports = generator.getDecoratorSmartImports(
        'User',
        'email',
        path.join(tempDir, 'output', 'user', 'email.ts')
      );
      expect(smartImports).toHaveLength(1);
      expect(smartImports[0]).toContain('getEmail'); // The actual import name from the decorator
    });
  });

  describe('Multi-schema support', () => {
    it('should handle schema-specific decorators', async () => {
      const defaultSchema = buildSchema(`
        type User {
          id: ID!
          name: String!
        }
        type Query {
          user: User
        }
      `);

      const adminSchema = buildSchema(`
        type Admin {
          id: ID!
          role: String!
        }
        type Query {
          admin: Admin
        }
      `);

      const multiSchemaContent = `
        // Default schema context
        @GQLContext({ path: "./types/DefaultContext", name: "DefaultContext", schema: "default" })
        export class DefaultContextConfig {}

        // Admin schema context
        @GQLContext({ path: "./types/AdminContext", name: "AdminContext", schema: "admin" })
        export class AdminContextConfig {}

        // Default schema resolvers
        @GQLMethodCall({ type: "Query", field: "user", schema: "default", call: "getUser()" })
        export const getUser = () => ({ id: "1", name: "User" });

        // Admin schema resolvers
        @GQLMethodCall({ type: "Query", field: "admin", schema: "admin", call: "getAdmin()" })
        export const getAdmin = () => ({ id: "1", role: "super" });

        // Schema-specific imports
        @GQLImport({ importStatement: "import { UserService } from '../services/user'", schema: "default" })
        @GQLImport({ importStatement: "import { AdminService } from '../services/admin'", schema: "admin" })

        // Schema-specific fields
        @GQLField({ ref: "User", name: "profile", type: "UserProfile", schema: "default" })
        export interface UserProfile {
          bio: string;
        }

        @GQLField({ ref: "Admin", name: "permissions", type: "AdminPermissions", schema: "admin" })
        export interface AdminPermissions {
          actions: string[];
        }
      `;

      await fs.writeFile(path.join(tempDir, 'multi-schema.ts'), multiSchemaContent);

      const defaultGenerator = new SchemaBasedGenerator(defaultSchema, {
        output: path.join(tempDir, 'output', 'default'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      const adminGenerator = new SchemaBasedGenerator(adminSchema, {
        output: path.join(tempDir, 'output', 'admin'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      await defaultGenerator.generate();
      await adminGenerator.generate();

      // Test default schema
      const defaultContext = defaultGenerator.getContextForSchema('default');
      expect(defaultContext!.name).toBe('DefaultContext');

      const defaultDirectives = defaultGenerator.getDecoratorDirectives('Query', 'user', 'default');
      expect(defaultDirectives.methodCalls).toHaveLength(1);
      expect(defaultDirectives.imports).toHaveLength(1);

      // Test admin schema
      const adminContext = adminGenerator.getContextForSchema('admin');
      expect(adminContext!.name).toBe('AdminContext');

      const adminDirectives = adminGenerator.getDecoratorDirectives('Query', 'admin', 'admin');
      expect(adminDirectives.methodCalls).toHaveLength(1);
      expect(adminDirectives.imports).toHaveLength(1);
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle malformed decorators gracefully', async () => {
      const malformedContent = `
        // Valid decorator
        @GQLMethodCall({ type: "Query", field: "valid", call: "validMethod()" })
        export const validMethod = () => "valid";

        // Invalid decorators (should be skipped)
        @GQLMethodCall({ invalid: "syntax" })
        export const invalidMethod1 = () => null;

        @GQLMethodCall({ type: "Query" }) // Missing required 'call'
        export const invalidMethod2 = () => null;

        @GQLImport("invalid import syntax")
        export const invalidImport = () => null;

        @GQLField({ ref: "User" }) // Missing required 'name' and 'type'
        export const invalidField = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'malformed.ts'), malformedContent);

      const result = await parser.scanCodebase(tempDir);

      // Parser should parse only valid decorators (invalid ones are filtered out during parsing)
      expect(result.methodCalls).toHaveLength(1); // Only the valid one
      expect(result.imports).toHaveLength(1); // String import is parseable
      expect(result.fields).toHaveLength(0); // Invalid field decorator is filtered out

      // Since invalid decorators are filtered out during parsing,
      // all remaining decorators should be valid
      expect(result.methodCalls[0].data.call).toBe('validMethod()');
    });

    it('should handle complex call expressions', async () => {
      const complexCallContent = `
        @GQLMethodCall({ 
          type: "Query", 
          field: "complexCall", 
          call: "await context.dataSources.userAPI.getById(obj.authorId)",
          enableTypeCasting: true
        })
        export const complexCall = () => null;

        @GQLMethodCall({ 
          type: "User", 
          field: "computed", 
          call: "return computeValue(obj.data) as ComputedType"
        })
        export const computedField = () => null;
      `;

      await fs.writeFile(path.join(tempDir, 'complex-calls.ts'), complexCallContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      expect(directives.methodCalls).toHaveLength(2);

      const complexCall = directives.methodCalls.find(mc => 
        mc.content.includes('context.dataSources.userAPI.getById')
      );
      expect(complexCall).toBeDefined();

      const computedCall = directives.methodCalls.find(mc => 
        mc.content.includes('computeValue')
      );
      expect(computedCall).toBeDefined();
    });

    it('should handle import deduplication edge cases', async () => {
      const duplicateImportsContent = `@GQLImport("import { UserService } from '../services/user'")
export class ImportConfig1 {}

@GQLImport("import { UserService } from '../services/user'") // Exact duplicate
export class ImportConfig2 {}

@GQLImport("import { UserRepository } from '../services/user'") // Same module, different import
export class ImportConfig3 {}

@GQLImport("import { ProductService } from '../services/product'")
export class ImportConfig4 {}

@GQLMethodCall({ type: "Query", field: "test", call: "UserService.test()" })
export const test = () => null;`;

      await fs.ensureDir(path.join(tempDir, 'src', 'resolvers'));
      await fs.writeFile(path.join(tempDir, 'src', 'resolvers', 'user-resolvers.ts'), duplicateImportsContent);

      const result = await parser.scanCodebase(tempDir);
      processor.setDecoratorMetadata(result);

      const directives = processor.convertToDirectiveContainer(result);
      
      // Should have 3 import directives before deduplication
      expect(directives.imports).toHaveLength(4);

      // Test deduplication
      const importStatements = directives.imports.map(imp => imp.content);
      const deduplicated = DecoratorProcessor.deduplicateImports(importStatements);
      
      // Should merge imports from same module and remove duplicates
      expect(deduplicated).toHaveLength(2);
      expect(deduplicated.some(imp => imp.includes('UserRepository, UserService'))).toBe(true);
      expect(deduplicated.some(imp => imp.includes('ProductService'))).toBe(true);
    });

    it('should handle hidden fields correctly', async () => {
      const schema = buildSchema(`
        type User {
          id: ID!
          name: String!
          secret: String!
          internal: String!
        }
      `);

      const hiddenFieldsContent = `
        @GQLField({ ref: "User", name: "secret", type: "string", hidden: true })
        export type UserSecret = string;

        @GQLField({ ref: "User", name: "internal", type: "string", hidden: true })
        export type UserInternal = string;

        @GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
        export const getUserName = (id: string) => \`User \${id}\`;
      `;

      await fs.writeFile(path.join(tempDir, 'hidden-fields.ts'), hiddenFieldsContent);

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      await generator.generate();

      // Verify hidden fields are in directives but marked as hidden
      const secretDirectives = generator.getDecoratorDirectives('User', 'secret');
      expect(secretDirectives.fieldFields).toHaveLength(1);
      expect(secretDirectives.fieldFields[0].hidden).toBe(true);

      // Verify that resolver files are not generated for hidden fields
      const secretResolverPath = path.join(tempDir, 'output', 'types', 'user', 'user', 'secret.ts');
      const internalResolverPath = path.join(tempDir, 'output', 'types', 'user', 'user', 'internal.ts');
      const nameResolverPath = path.join(tempDir, 'output', 'types', 'user', 'user', 'name.ts');

      expect(fs.existsSync(secretResolverPath)).toBe(false);
      expect(fs.existsSync(internalResolverPath)).toBe(false);
      expect(fs.existsSync(nameResolverPath)).toBe(true); // Non-hidden field should have resolver
    });
  });

  describe('Performance with large codebase', () => {
    it('should handle many decorators efficiently', async () => {
      const schema = buildSchema(`
        type Query {
          ${Array.from({ length: 50 }, (_, i) => `field${i}: String`).join('\n          ')}
        }
      `);

      // Generate many decorator files
      for (let i = 0; i < 50; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
          export const resolver${i} = () => "result${i}";
        `;
        await fs.writeFile(path.join(tempDir, `resolver${i}.ts`), content);
      }

      const startTime = Date.now();

      const generator = new SchemaBasedGenerator(schema, {
        output: path.join(tempDir, 'output'),
        codebaseDir: tempDir,
        enableDecorators: true,
      });

      await generator.generate();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(10000); // 10 seconds

      // Verify all decorators were processed
      const result = await parser.scanCodebase(tempDir);
      expect(result.methodCalls).toHaveLength(50);
    });
  });
});
