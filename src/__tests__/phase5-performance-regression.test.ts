import { SchemaBasedGenerator } from '../generators/schema-based-generator';
import { DecoratorParser } from '../utils/decorator-parser';
import { WatchService } from '../core/watch-service';
import { buildSchema } from 'graphql';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 5: Performance Regression Testing', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase5-performance-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Large Schema Performance', () => {
    it('should handle large schemas efficiently', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      // Generate a large schema with many types
      const typeCount = 50;
      let schemaContent = `
        type Query {
          ${Array.from({ length: typeCount }, (_, i) => `entity${i}(id: ID!): Entity${i}`).join('\n          ')}
        }

        type Mutation {
          ${Array.from({ length: typeCount }, (_, i) => `createEntity${i}(input: CreateEntity${i}Input!): Entity${i}!`).join('\n          ')}
        }
      `;

      // Add entity types
      for (let i = 0; i < typeCount; i++) {
        schemaContent += `
        type Entity${i} {
          id: ID!
          name: String!
          field${i}: String!
          relatedEntities: [Entity${(i + 1) % typeCount}!]!
        }

        input CreateEntity${i}Input {
          name: String!
          field${i}: String!
        }
        `;
      }

      await fs.writeFile(path.join(schemaDir, 'large-schema.gql'), schemaContent);

      const schema = buildSchema(schemaContent);
      
      const startTime = Date.now();
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        enableDecorators: false,
      });

      await generator.generate();
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust based on system capabilities)
      expect(duration).toBeLessThan(60000); // 60 seconds

      // Verify all files were generated
      const queryFiles = await fs.readdir(path.join(outputDir, 'query'));
      const mutationFiles = await fs.readdir(path.join(outputDir, 'mutation'));
      
      expect(queryFiles.length).toBe(typeCount);
      expect(mutationFiles.length).toBe(typeCount);

      console.log(`Large schema generation completed in ${duration}ms`);
    });

    it('should handle large codebases with decorators efficiently', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      // Create schema
      const fieldCount = 100;
      const schemaContent = `
        type Query {
          ${Array.from({ length: fieldCount }, (_, i) => `field${i}: String`).join('\n          ')}
        }
      `;

      await fs.writeFile(path.join(schemaDir, 'large-query.gql'), schemaContent);

      // Create many decorator files
      for (let i = 0; i < fieldCount; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
          export const resolver${i} = () => "result${i}";
        `;
        await fs.writeFile(path.join(codebaseDir, `resolver${i}.ts`), content);
      }

      const schema = buildSchema(schemaContent);
      
      const startTime = Date.now();
      
      const generator = new SchemaBasedGenerator(schema, {
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
      });

      await generator.generate();
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(120000); // 2 minutes

      // Verify all decorator-based resolvers were generated
      const queryFiles = await fs.readdir(path.join(outputDir, 'query'));
      expect(queryFiles.length).toBe(fieldCount);

      console.log(`Large codebase with decorators generation completed in ${duration}ms`);
    });
  });

  describe('Memory Usage Monitoring', () => {
    it('should not have memory leaks during generation', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const outputDir = path.join(tempDir, 'output');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'memory-test.gql'), `
        type User {
          id: ID!
          name: String!
          email: String!
        }

        type Query {
          user(id: ID!): User
          users: [User!]!
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'memory-test.gql'), 'utf8'));

      // Measure memory before
      const memBefore = process.memoryUsage();

      // Run generation multiple times
      for (let i = 0; i < 10; i++) {
        const generator = new SchemaBasedGenerator(schema, {
          output: path.join(outputDir, `run${i}`),
          enableDecorators: false,
        });

        await generator.generate();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Measure memory after
      const memAfter = process.memoryUsage();

      // Memory increase should be reasonable (less than 100MB)
      const memoryIncrease = memAfter.heapUsed - memBefore.heapUsed;
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 100MB

      console.log(`Memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
    });

    it('should handle decorator parsing memory efficiently', async () => {
      const codebaseDir = path.join(tempDir, 'codebase');
      await fs.ensureDir(codebaseDir);

      // Create many files with decorators
      const fileCount = 200;
      for (let i = 0; i < fileCount; i++) {
        const content = `
          @GQLMethodCall({ type: "Query", field: "field${i}", call: "resolver${i}()" })
          export const resolver${i} = () => "result${i}";

          @GQLImport("import { Service${i} } from '../services/service${i}'")
          @GQLMethodCall({ type: "Type${i}", field: "data", call: "Service${i}.getData()" })
          export const getData${i} = () => null;
        `;
        await fs.writeFile(path.join(codebaseDir, `file${i}.ts`), content);
      }

      const memBefore = process.memoryUsage();

      const parser = new DecoratorParser();
      const result = await parser.scanCodebase(codebaseDir);

      const memAfter = process.memoryUsage();

      // Verify parsing worked
      expect(result.methodCalls.length).toBeGreaterThan(fileCount);
      expect(result.imports.length).toBeGreaterThan(0);

      // Memory increase should be reasonable
      const memoryIncrease = memAfter.heapUsed - memBefore.heapUsed;
      expect(memoryIncrease).toBeLessThan(200 * 1024 * 1024); // 200MB

      console.log(`Decorator parsing memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
    });
  });

  describe('Watch Mode Performance', () => {
    it('should handle file changes efficiently in watch mode', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      const codebaseDir = path.join(tempDir, 'codebase');
      const outputDir = path.join(tempDir, 'output');
      
      await fs.ensureDir(schemaDir);
      await fs.ensureDir(codebaseDir);

      await fs.writeFile(path.join(schemaDir, 'watch-perf.gql'), `
        type User {
          id: ID!
          name: String!
        }

        type Query {
          user(id: ID!): User
        }
      `);

      await fs.writeFile(path.join(codebaseDir, 'initial.ts'), `
        @GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
        export const getUser = (id: string) => ({ id, name: "Test" });
      `);

      // Create watch service but don't start watching (just test setup performance)
      const startTime = Date.now();

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      const setupTime = Date.now() - startTime;

      // Setup should be fast
      expect(setupTime).toBeLessThan(5000); // 5 seconds

      console.log(`Watch service setup completed in ${setupTime}ms`);

      // Test that watch paths are correctly configured
      const watchPaths = (watchService as any).getWatchPaths();
      expect(watchPaths.length).toBeGreaterThan(0);
      expect(watchPaths).toContain(path.resolve(codebaseDir));
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent generation requests', async () => {
      const schemaDir = path.join(tempDir, 'schema');
      await fs.ensureDir(schemaDir);

      await fs.writeFile(path.join(schemaDir, 'concurrent.gql'), `
        type User {
          id: ID!
          name: String!
        }

        type Query {
          user(id: ID!): User
        }
      `);

      const schema = buildSchema(await fs.readFile(path.join(schemaDir, 'concurrent.gql'), 'utf8'));

      const startTime = Date.now();

      // Run multiple generations concurrently
      const promises = Array.from({ length: 5 }, (_, i) => {
        const generator = new SchemaBasedGenerator(schema, {
          output: path.join(tempDir, `output${i}`),
          enableDecorators: false,
        });
        return generator.generate();
      });

      await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(30000); // 30 seconds

      // Verify all outputs were generated
      for (let i = 0; i < 5; i++) {
        const userFile = path.join(tempDir, `output${i}`, 'query', 'user.ts');
        expect(fs.existsSync(userFile)).toBe(true);
      }

      console.log(`Concurrent generation completed in ${duration}ms`);
    });
  });
});
