import { WatchService } from '../core/watch-service';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('Phase 2: WatchService Integration', () => {
  let tempDir: string;
  let schemaDir: string;
  let codebaseDir: string;
  let outputDir: string;

  beforeEach(async () => {
    // Create temporary directories
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'phase2-watch-service-test-'));
    schemaDir = path.join(tempDir, 'schema');
    codebaseDir = path.join(tempDir, 'codebase');
    outputDir = path.join(tempDir, 'output');
    
    await fs.ensureDir(schemaDir);
    await fs.ensureDir(codebaseDir);
    await fs.ensureDir(outputDir);

    // Create basic schema file
    await fs.writeFile(
      path.join(schemaDir, 'user.gql'),
      `
        type User {
          id: ID!
          name: String!
        }
        type Query {
          user: User
        }
      `
    );
  });

  afterEach(async () => {
    if (tempDir) {
      await fs.remove(tempDir);
    }
  });

  describe('Codebase Directory Monitoring', () => {
    it('should include codebase directory in watch paths when decorators are enabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false, // Don't run initial generation
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      expect(watchPaths).toContain(path.resolve(codebaseDir));
    });

    it('should not include codebase directory when decorators are disabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: false,
        initial: false,
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      expect(watchPaths).not.toContain(path.resolve(codebaseDir));
    });

    it('should not include codebase directory when codebaseDir is not provided', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: true,
        initial: false,
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      // Should only contain schema directory
      expect(watchPaths).toHaveLength(1);
      expect(watchPaths[0]).toContain('schema');
    });
  });

  describe('File Type Filtering', () => {
    it('should include TypeScript files when decorators are enabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // Test TypeScript files
      expect(ignoredPatterns('/test/file.ts', { isFile: () => true })).toBe(false);
      expect(ignoredPatterns('/test/file.tsx', { isFile: () => true })).toBe(false);
    });

    it('should exclude TypeScript files when decorators are disabled', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: false,
        initial: false,
      });

      const ignoredPatterns = (watchService as any).getIgnoredPatterns();
      
      // Test TypeScript files - should be ignored when decorators disabled
      expect(ignoredPatterns('/test/file.ts', { isFile: () => true })).toBe(true);
      expect(ignoredPatterns('/test/file.tsx', { isFile: () => true })).toBe(true);
    });

    it('should always include GraphQL files regardless of decorator setting', () => {
      const watchServiceWithDecorators = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: true,
        initial: false,
      });

      const watchServiceWithoutDecorators = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: false,
        initial: false,
      });

      const ignoredPatternsWithDecorators = (watchServiceWithDecorators as any).getIgnoredPatterns();
      const ignoredPatternsWithoutDecorators = (watchServiceWithoutDecorators as any).getIgnoredPatterns();
      
      // GraphQL files should never be ignored
      expect(ignoredPatternsWithDecorators('/test/schema.gql', { isFile: () => true })).toBe(false);
      expect(ignoredPatternsWithoutDecorators('/test/schema.gql', { isFile: () => true })).toBe(false);
      expect(ignoredPatternsWithDecorators('/test/schema.graphql', { isFile: () => true })).toBe(false);
      expect(ignoredPatternsWithoutDecorators('/test/schema.graphql', { isFile: () => true })).toBe(false);
    });
  });

  describe('File Change Logging', () => {
    it('should log decorator file changes appropriately', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      // Test the private logFileChange method
      const decoratorFilePath = path.join(codebaseDir, 'resolver.ts');
      (watchService as any).logFileChange('changed', decoratorFilePath);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('decorator file changed')
      );

      consoleSpy.mockRestore();
    });

    it('should log schema file changes appropriately', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        enableDecorators: true,
        initial: false,
      });

      const schemaFilePath = path.join(schemaDir, 'user.gql');
      (watchService as any).logFileChange('changed', schemaFilePath);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('schema file changed')
      );

      consoleSpy.mockRestore();
    });

    it('should log TypeScript files outside codebase as TypeScript files', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        initial: false,
      });

      const otherTsFile = '/other/path/file.ts';
      (watchService as any).logFileChange('added', otherTsFile);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('TypeScript file added')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Integration with Existing Watch Functionality', () => {
    it('should maintain existing debouncing behavior', () => {
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        debounce: 100,
        initial: false,
      });

      // Verify debounce option is preserved
      expect((watchService as any).options.debounce).toBe(100);
    });

    it('should preserve additional watch paths functionality', () => {
      const additionalPath = '/additional/watch/path';
      
      const watchService = new WatchService({
        schema: path.join(schemaDir, '**/*.gql'),
        output: outputDir,
        codebaseDir,
        enableDecorators: true,
        watchPaths: [additionalPath],
        initial: false,
      });

      const watchPaths = (watchService as any).getWatchPaths();
      
      expect(watchPaths).toContain(additionalPath);
      expect(watchPaths).toContain(path.resolve(codebaseDir));
    });
  });
});
