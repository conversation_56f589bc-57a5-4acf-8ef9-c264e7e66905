import { createProgram } from '../cli/commands';
import { Command } from 'commander';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

// Mock the generator to prevent actual execution
jest.mock('../core/generator-orchestrator', () => ({
  generateAll: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../core/enhanced-watch-service', () => ({
  EnhancedWatchService: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('CLI Decorator Integration', () => {
  let program: Command;
  let tempDir: string;

  beforeEach(async () => {
    program = createProgram();
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'cli-decorator-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('generate command', () => {
    it('should include --codebase-dir option', () => {
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      expect(generateCommand).toBeDefined();

      const options = generateCommand!.options;
      const codebaseDirOption = options.find(opt => opt.long === '--codebase-dir');
      
      expect(codebaseDirOption).toBeDefined();
      expect(codebaseDirOption!.description).toContain('codebase directory');
    });

    it('should not include --enable-decorators option (removed in favor of auto-enabling)', () => {
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      expect(generateCommand).toBeDefined();

      const options = generateCommand!.options;
      const enableDecoratorsOption = options.find(opt => opt.long === '--enable-decorators');

      expect(enableDecoratorsOption).toBeUndefined();
    });

    it('should parse codebase-dir option correctly', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--codebase-dir', './src'
      ];

      // Mock the action to capture options
      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      program.parse(testArgs);

      expect(capturedOptions).toBeDefined();
      expect(capturedOptions.codebaseDir).toBe('./src');
    });

    it('should auto-enable decorators when codebase-dir is provided', () => {
      // Create a test codebase directory
      const fs = require('fs');
      const path = require('path');
      const tempDir = path.join(process.cwd(), 'temp-test-codebase');

      try {
        fs.mkdirSync(tempDir, { recursive: true });

        const testArgs = [
          'node',
          'cli.js',
          'generate',
          '--schema', './schema',
          '--output', './output',
          '--codebase-dir', tempDir
        ];

        // Mock the action to capture resolved options
        let capturedResolvedOptions: any;
        const originalGenerateAll = require('../core/generator-orchestrator').generateAll;
        require('../core/generator-orchestrator').generateAll = async (options: any) => {
          capturedResolvedOptions = options;
          return Promise.resolve();
        };

        try {
          const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
          generateCommand!.action(async (options) => {
            // This would normally call generateAll with resolved options
            // We'll simulate the resolution logic here
            const resolvedOptions = {
              ...options,
              codebaseDir: tempDir,
              enableDecorators: !!options.codebaseDir
            };
            await require('../core/generator-orchestrator').generateAll(resolvedOptions);
          });

          program.parse(testArgs);

          // Verify that decorators are auto-enabled
          expect(capturedResolvedOptions).toBeDefined();
          expect(capturedResolvedOptions.enableDecorators).toBe(true);
        } finally {
          // Restore original function
          require('../core/generator-orchestrator').generateAll = originalGenerateAll;
        }
      } finally {
        // Clean up temp directory
        if (fs.existsSync(tempDir)) {
          fs.rmSync(tempDir, { recursive: true, force: true });
        }
      }
    });

    it('should not enable decorators when codebase-dir is not provided', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output'
      ];

      // Mock the action to capture resolved options
      let capturedResolvedOptions: any;
      const originalGenerateAll = require('../core/generator-orchestrator').generateAll;
      require('../core/generator-orchestrator').generateAll = async (options: any) => {
        capturedResolvedOptions = options;
        return Promise.resolve();
      };

      try {
        const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
        generateCommand!.action(async (options) => {
          // Simulate the resolution logic
          const resolvedOptions = {
            ...options,
            enableDecorators: !!options.codebaseDir
          };
          await require('../core/generator-orchestrator').generateAll(resolvedOptions);
        });

        program.parse(testArgs);

        // Verify that decorators are not enabled
        expect(capturedResolvedOptions).toBeDefined();
        expect(capturedResolvedOptions.enableDecorators).toBe(false);
      } finally {
        // Restore original function
        require('../core/generator-orchestrator').generateAll = originalGenerateAll;
      }
    });

    it('should handle missing decorator options gracefully', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output'
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;
      });

      program.parse(testArgs);

      expect(capturedOptions).toBeDefined();
      expect(capturedOptions.codebaseDir).toBeUndefined();
    });
  });

  describe('watch command', () => {
    it('should include decorator options', () => {
      const watchCommand = program.commands.find(cmd => cmd.name() === 'watch');
      expect(watchCommand).toBeDefined();

      const options = watchCommand!.options;
      const codebaseDirOption = options.find(opt => opt.long === '--codebase-dir');
      const enableDecoratorsOption = options.find(opt => opt.long === '--enable-decorators');

      expect(codebaseDirOption).toBeDefined();
      expect(enableDecoratorsOption).toBeUndefined();
    });

    it('should parse watch command with decorator options', () => {
      const testArgs = [
        'node',
        'cli.js',
        'watch',
        '--schema', './schema',
        '--output', './output',
        '--codebase-dir', './src'
      ];

      let capturedOptions: any;
      const watchCommand = program.commands.find(cmd => cmd.name() === 'watch');
      watchCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions).toBeDefined();
      expect(capturedOptions.codebaseDir).toBe('./src');
    });
  });

  describe('help text', () => {
    it('should include decorator options in help', () => {
      // The main program help doesn't show command-specific options
      // This is expected behavior for commander.js
      const helpText = program.helpInformation();

      expect(helpText).toContain('generate');
      expect(helpText).toContain('watch');
    });

    it('should include decorator options in generate command help', () => {
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      const helpText = generateCommand!.helpInformation();

      expect(helpText).toContain('--codebase-dir');
      expect(helpText).not.toContain('--enable-decorators');
    });

    it('should include decorator options in watch command help', () => {
      const watchCommand = program.commands.find(cmd => cmd.name() === 'watch');
      const helpText = watchCommand!.helpInformation();

      expect(helpText).toContain('--codebase-dir');
      expect(helpText).not.toContain('--enable-decorators');
    });
  });

  describe('option validation', () => {
    it('should handle relative paths for codebase-dir', async () => {
      // Create a test codebase directory
      const codebaseDir = path.join(tempDir, 'src');
      await fs.ensureDir(codebaseDir);

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--codebase-dir', codebaseDir
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions.codebaseDir).toBe(codebaseDir);
    });

    it('should handle absolute paths for codebase-dir', () => {
      const absolutePath = path.resolve(tempDir, 'src');

      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--codebase-dir', absolutePath
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions.codebaseDir).toBe(absolutePath);
    });
  });

  describe('option combinations', () => {
    it('should work with all existing options', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--force',
        '--context', './context',
        '--context-name', 'MyContext',
        '--test',
        '--debug',
        '--codebase-dir', './src'
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions).toBeDefined();
      expect(capturedOptions.force).toBe(true);
      expect(capturedOptions.context).toBe('./context');
      expect(capturedOptions.contextName).toBe('MyContext');
      expect(capturedOptions.test).toBe(true);
      expect(capturedOptions.debug).toBe(true);
      expect(capturedOptions.codebaseDir).toBe('./src');
      expect(capturedOptions.enableDecorators).toBe(true);
    });

    it('should work with watch-specific options', () => {
      const testArgs = [
        'node',
        'cli.js',
        'watch',
        '--schema', './schema',
        '--output', './output',
        '--bidirectional',
        '--debounce', '500',
        '--codebase-dir', './src'
      ];

      let capturedOptions: any;
      const watchCommand = program.commands.find(cmd => cmd.name() === 'watch');
      watchCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions).toBeDefined();
      expect(capturedOptions.bidirectional).toBe(true);
      expect(capturedOptions.debounce).toBe('500');
      expect(capturedOptions.codebaseDir).toBe('./src');
      expect(capturedOptions.enableDecorators).toBe(true);
    });
  });

  describe('backward compatibility', () => {
    it('should work without decorator options', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output'
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      expect(() => program.parse(testArgs)).not.toThrow();
      expect(capturedOptions).toBeDefined();
    });

    it('should maintain existing option behavior', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--force',
        '--debug'
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      program.parse(testArgs);

      expect(capturedOptions.force).toBe(true);
      expect(capturedOptions.debug).toBe(true);
      expect(capturedOptions.schema).toContain('./schema');
      expect(capturedOptions.output).toContain('./output');
    });
  });

  describe('error handling', () => {
    it('should handle missing required options gracefully', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate'
        // Missing required schema and output
      ];

      // This should not throw during parsing, but might during execution
      expect(() => program.parse(testArgs)).not.toThrow();
    });

    it('should handle non-existent codebase directory', () => {
      const testArgs = [
        'node',
        'cli.js',
        'generate',
        '--schema', './schema',
        '--output', './output',
        '--codebase-dir', './non-existent-directory'
      ];

      let capturedOptions: any;
      const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
      generateCommand!.action((options) => {
        capturedOptions = options;

      });

      expect(() => program.parse(testArgs)).not.toThrow();
      expect(capturedOptions.codebaseDir).toBe('./non-existent-directory');
    });

    it('should validate codebase directory exists', () => {
      // Mock console.error to capture error messages
      const originalConsoleError = console.error;
      const errorMessages: string[] = [];
      console.error = (...args: any[]) => {
        errorMessages.push(args.join(' '));
      };

      // Mock process.exit to prevent actual exit
      const originalProcessExit = process.exit;
      let exitCode: number | undefined;
      process.exit = ((code?: number) => {
        exitCode = code;
        throw new Error('Process exit called');
      }) as any;

      try {
        const testArgs = [
          'node',
          'cli.js',
          'generate',
          '--schema', './schema',
          '--output', './output',
          '--codebase-dir', './definitely-non-existent-directory-12345'
        ];

        let capturedOptions: any;
        const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
        generateCommand!.action(async (options) => {
          capturedOptions = options;
          // This would normally trigger the validation and exit
        });

        expect(() => program.parse(testArgs)).toThrow('Process exit called');
        expect(exitCode).toBe(1);
        expect(errorMessages.some(msg => msg.includes('Codebase directory does not exist'))).toBe(true);
      } finally {
        // Restore original functions
        console.error = originalConsoleError;
        process.exit = originalProcessExit;
      }
    });

    it('should validate codebase path is a directory', () => {
      // Create a temporary file to test with
      const fs = require('fs');
      const path = require('path');
      const tempFile = path.join(process.cwd(), 'temp-test-file.txt');

      try {
        fs.writeFileSync(tempFile, 'test');

        // Mock console.error to capture error messages
        const originalConsoleError = console.error;
        const errorMessages: string[] = [];
        console.error = (...args: any[]) => {
          errorMessages.push(args.join(' '));
        };

        // Mock process.exit to prevent actual exit
        const originalProcessExit = process.exit;
        let exitCode: number | undefined;
        process.exit = ((code?: number) => {
          exitCode = code;
          throw new Error('Process exit called');
        }) as any;

        try {
          const testArgs = [
            'node',
            'cli.js',
            'generate',
            '--schema', './schema',
            '--output', './output',
            '--codebase-dir', tempFile
          ];

          let capturedOptions: any;
          const generateCommand = program.commands.find(cmd => cmd.name() === 'generate');
          generateCommand!.action(async (options) => {
            capturedOptions = options;
          });

          expect(() => program.parse(testArgs)).toThrow('Process exit called');
          expect(exitCode).toBe(1);
          expect(errorMessages.some(msg => msg.includes('Codebase path is not a directory'))).toBe(true);
        } finally {
          // Restore original functions
          console.error = originalConsoleError;
          process.exit = originalProcessExit;
        }
      } finally {
        // Clean up temp file
        if (fs.existsSync(tempFile)) {
          fs.unlinkSync(tempFile);
        }
      }
    });
  });
});
