import { DirectiveParser } from '../utils/directive-parser';
import type { DirectiveContainer } from '../utils/directive-parser';

describe('Union Type @field Directive Support', () => {
  describe('Basic Union Type Parsing', () => {
    it('should parse simple union types without quotes', () => {
      const content = 'status: Active | Inactive | Pending';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('status');
      expect(directives.fieldFields[0].type).toBe('Active | Inactive | Pending');
    });

    it('should parse union types with quotes around entire expression', () => {
      const content = 'status: "Active | Inactive | Pending"';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('status');
      expect(directives.fieldFields[0].type).toBe('"Active | Inactive | Pending"');
    });

    it('should parse union types with string literal types', () => {
      const content = 'mode: "light" | "dark" | "auto"';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('mode');
      expect(directives.fieldFields[0].type).toBe('"light" | "dark" | "auto"');
    });

    it('should parse union types with mixed types', () => {
      const content = 'value: string | number | null';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('value');
      expect(directives.fieldFields[0].type).toBe('string | number | null');
    });
  });

  describe('Union Types with Import Paths', () => {
    it('should parse union types with import paths', () => {
      const content = 'result: Success | Error "@/types/results"';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('result');
      expect(directives.fieldFields[0].type).toBe('Success | Error');
      expect(directives.fieldFields[0].importPath).toBe('@/types/results');
    });

    it('should parse complex union types with import paths', () => {
      const content = 'data: UserData | AdminData | GuestData "@/types/user-types"';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('data');
      expect(directives.fieldFields[0].type).toBe('UserData | AdminData | GuestData');
      expect(directives.fieldFields[0].importPath).toBe('@/types/user-types');
    });
  });

  describe('Multiple Fields with Union Types', () => {
    it('should parse multiple fields including union types', () => {
      const content = 'status: Active | Inactive, priority: High | Medium | Low, count: number';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(3);
      
      expect(directives.fieldFields[0].name).toBe('status');
      expect(directives.fieldFields[0].type).toBe('Active | Inactive');
      
      expect(directives.fieldFields[1].name).toBe('priority');
      expect(directives.fieldFields[1].type).toBe('High | Medium | Low');
      
      expect(directives.fieldFields[2].name).toBe('count');
      expect(directives.fieldFields[2].type).toBe('number');
    });
  });

  describe('Complex Union Types', () => {
    it('should parse union types with arrays', () => {
      const content = 'items: string[] | number[] | boolean[]';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('items');
      expect(directives.fieldFields[0].type).toBe('string[] | number[] | boolean[]');
    });

    it('should parse union types with generics', () => {
      const content = 'response: Promise<Success> | Promise<Error>';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('response');
      expect(directives.fieldFields[0].type).toBe('Promise<Success> | Promise<Error>');
    });

    it('should parse union types with object types', () => {
      const content = 'config: { mode: "dev" } | { mode: "prod"; ssl: boolean }';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('config');
      expect(directives.fieldFields[0].type).toBe('{ mode: "dev" } | { mode: "prod"; ssl: boolean }');
    });
  });

  describe('Backward Compatibility', () => {
    it('should still parse simple types without union operators', () => {
      const content = 'name: string, age: number, active: boolean';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(3);
      expect(directives.fieldFields[0].type).toBe('string');
      expect(directives.fieldFields[1].type).toBe('number');
      expect(directives.fieldFields[2].type).toBe('boolean');
    });

    it('should still parse complex types without union operators', () => {
      const content = 'data: Array<string>, config: { enabled: boolean }, callback: () => void';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(3);
      expect(directives.fieldFields[0].type).toBe('Array<string>');
      expect(directives.fieldFields[1].type).toBe('{ enabled: boolean }');
      expect(directives.fieldFields[2].type).toBe('() => void');
    });

    it('should still parse types with import paths', () => {
      const content = 'user: User "@/types/user", settings: Settings "@/types/settings"';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(2);
      expect(directives.fieldFields[0].type).toBe('User');
      expect(directives.fieldFields[0].importPath).toBe('@/types/user');
      expect(directives.fieldFields[1].type).toBe('Settings');
      expect(directives.fieldFields[1].importPath).toBe('@/types/settings');
    });
  });

  describe('Edge Cases', () => {
    it('should handle union types with optional fields', () => {
      const content = 'status?: Active | Inactive | Pending';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('status');
      expect(directives.fieldFields[0].type).toBe('Active | Inactive | Pending');
      expect(directives.fieldFields[0].optional).toBe(true);
    });

    it('should handle whitespace around union operators', () => {
      const content = 'value:string|number|boolean';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('value');
      expect(directives.fieldFields[0].type).toBe('string|number|boolean');
    });

    it('should handle nested union types', () => {
      const content = 'complex: (string | number) | (boolean | null)';
      const directives: DirectiveContainer = {
        imports: [],
        methodCalls: [],
        fieldFields: [],
        others: {}
      };

      DirectiveParser.parseFieldDirectiveFields(content, directives, false);

      expect(directives.fieldFields).toHaveLength(1);
      expect(directives.fieldFields[0].name).toBe('complex');
      expect(directives.fieldFields[0].type).toBe('(string | number) | (boolean | null)');
    });
  });
});
