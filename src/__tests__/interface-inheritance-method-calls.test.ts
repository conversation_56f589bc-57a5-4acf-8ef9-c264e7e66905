import { buildSchema } from 'graphql';
import { InterfaceInheritanceHandler } from '../utils/interface-inheritance';
import { SchemaMapper } from '../utils/schema-mapper';
import { DirectiveParser } from '../utils/directive-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('InterfaceInheritanceHandler - Method Call Inheritance', () => {
  let handler: InterfaceInheritanceHandler;
  let schemaMapper: SchemaMapper;
  let tempDir: string;

  beforeEach(async () => {
    // Create temporary directory for schema files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'interface-inheritance-test-'));
    const schemaContent = `
      # Base interface with method call
      interface Node {
        # @methodCall(getNodeId(obj.id))
        id: ID!
      }

      # Interface that extends Node concept and adds its own method calls
      interface Auditable {
        # Inherits id from Node
        id: ID!
        # @methodCall(getAuditLog(obj.id))
        # @import(import { getAuditLog } from './audit-service')
        auditLog: String
        # @methodCall(getCreatedBy(obj.createdBy))
        createdBy: String
      }

      # Interface with conflicting method call for same field
      interface Versioned {
        # Different method call for id field
        # @methodCall(getVersionedId(obj.id, obj.version))
        id: ID!
        # @methodCall(getVersion(obj.version))
        version: Int!
      }

      # Type implementing multiple interfaces with potential conflicts
      type User implements Node & Auditable & Versioned {
        # Should inherit from Node (first interface) for id
        # Should inherit auditLog from Auditable
        # Should inherit createdBy from Auditable
        # Should inherit version from Versioned
        id: ID!
        auditLog: String
        createdBy: String
        version: Int!
        name: String!
        # Direct method call (should override inheritance)
        # @methodCall(getUserEmail(obj.id))
        email: String!
      }

      # Type with direct method call that overrides inheritance
      type Product implements Node {
        # Direct method call should override Node's method call
        # @methodCall(getProductId(obj.sku))
        id: ID!
        name: String!
      }

      # Type that inherits through multiple levels
      type Document implements Node & Auditable {
        # Should inherit id from Node (via Auditable)
        # Should inherit auditLog from Auditable
        # Should inherit createdBy from Auditable
        id: ID!
        auditLog: String
        createdBy: String
        title: String!
      }
    `;

    // Write schema file to temp directory
    const schemaPath = path.join(tempDir, 'test-schema.gql');
    await fs.writeFile(schemaPath, schemaContent);

    const schema = buildSchema(schemaContent);

    schemaMapper = new SchemaMapper(tempDir, '/test/output');
    // Mock the schema mapper to return the actual schema file location
    schemaMapper.getTypeLocation = jest.fn().mockReturnValue({
      sourceFile: 'test-schema.gql',
      startLine: 1,
      endLine: 10
    });

    handler = new InterfaceInheritanceHandler(schema, schemaMapper);
  });

  afterEach(async () => {
    if (handler && handler.clearCaches) {
      handler.clearCaches();
    }
    // Clean up temp directory
    await fs.remove(tempDir);
  });

  describe('Method Call Inheritance', () => {
    test('should inherit method call from direct interface', async () => {
      const inheritanceInfo = await handler.findInheritedMethodCall('Product', 'id');
      
      expect(inheritanceInfo).not.toBeNull();
      expect(inheritanceInfo?.interfaceName).toBe('Node');
      expect(inheritanceInfo?.methodCall).toBe('getNodeId(obj.id)');
    });

    test('should inherit method call through multiple levels', async () => {
      const inheritanceInfo = await handler.findInheritedMethodCall('Document', 'id');
      
      expect(inheritanceInfo).not.toBeNull();
      expect(inheritanceInfo?.interfaceName).toBe('Node');
      expect(inheritanceInfo?.methodCall).toBe('getNodeId(obj.id)');
    });

    test('should inherit method call from mid-level interface', async () => {
      const inheritanceInfo = await handler.findInheritedMethodCall('Document', 'auditLog');
      
      expect(inheritanceInfo).not.toBeNull();
      expect(inheritanceInfo?.interfaceName).toBe('Auditable');
      expect(inheritanceInfo?.methodCall).toBe('getAuditLog(obj.id)');
    });

    test('should not inherit when direct method call exists', async () => {
      // Product has its own @methodCall for id, so should not inherit
      const inheritanceInfo = await handler.findInheritedMethodCall('Product', 'id');
      
      // This test assumes the field has a direct method call
      // In a real scenario, the field resolver generator would check for direct method calls first
      // For this test, we're testing the inheritance logic when no direct method call exists
      expect(inheritanceInfo).not.toBeNull(); // Would inherit if no direct method call
    });
  });

  describe('Precedence Resolution', () => {
    test('should prioritize first interface in multiple inheritance', async () => {
      // User implements Node, Auditable, Versioned
      // Multiple interfaces have method calls for 'id' field
      // Should prioritize Node (first interface)
      const inheritanceInfo = await handler.findInheritedMethodCall('User', 'id');

      expect(inheritanceInfo).not.toBeNull();
      expect(inheritanceInfo?.interfaceName).toBe('Node');
      expect(inheritanceInfo?.methodCall).toBe('getNodeId(obj.id)');
    });

    test('should prioritize direct interfaces over inherited interfaces', async () => {
      // User implements Auditable (direct) and Versioned (direct)
      // Both ultimately inherit from Node, but direct interfaces have precedence
      const allInterfaces = handler.getAllInterfacesRecursively('User');
      const directInterfaces = handler.getImplementedInterfaces('User');
      
      expect(directInterfaces).toEqual(['Node', 'Auditable', 'Versioned']);
      expect(allInterfaces).toContain('Node');
      
      // When looking for inheritance, direct interfaces should be checked first
      const inheritanceInfo = await handler.findInheritedMethodCall('User', 'id');
      expect(inheritanceInfo?.interfaceName).toBe('Node'); // First direct interface
    });
  });

  describe('Field Inheritance Conflict Analysis', () => {
    test('should analyze conflicts for fields with multiple interface definitions', async () => {
      const conflicts = await handler.analyzeFieldInheritanceConflicts('User', 'id');
      
      expect(conflicts.fieldName).toBe('id');
      expect(conflicts.hasConflicts).toBe(true);
      expect(conflicts.interfacesWithDirectives.length).toBeGreaterThan(1);
      expect(conflicts.selectedInterface).toBe('Node'); // First interface wins
      expect(conflicts.conflictDetails.length).toBeGreaterThan(0);
    });

    test('should handle fields with no conflicts', async () => {
      const conflicts = await handler.analyzeFieldInheritanceConflicts('User', 'auditLog');
      
      expect(conflicts.fieldName).toBe('auditLog');
      expect(conflicts.hasConflicts).toBe(false);
      expect(conflicts.interfacesWithDirectives.length).toBe(1);
      expect(conflicts.selectedInterface).toBe('Auditable');
    });

    test('should handle fields with no inheritance', async () => {
      const conflicts = await handler.analyzeFieldInheritanceConflicts('User', 'name');
      
      expect(conflicts.fieldName).toBe('name');
      expect(conflicts.hasConflicts).toBe(false);
      expect(conflicts.interfacesWithDirectives.length).toBe(0);
      expect(conflicts.selectedInterface).toBeUndefined();
    });
  });

  describe('Directive Inheritance', () => {
    test('should inherit directives from interfaces', async () => {
      const directivesInfo = await handler.findInheritedDirectives('Document', 'auditLog');
      
      expect(directivesInfo).not.toBeNull();
      expect(directivesInfo?.interfaceName).toBe('Auditable');
      expect(directivesInfo?.directives).toBeDefined();
    });

    test('should prioritize direct interface directives', async () => {
      const directivesInfo = await handler.findInheritedDirectives('User', 'id');
      
      expect(directivesInfo).not.toBeNull();
      expect(directivesInfo?.interfaceName).toBe('Node'); // First direct interface
    });
  });

  describe('Performance and Caching', () => {
    test('should cache method call inheritance results', async () => {
      // First call
      const inheritanceInfo1 = await handler.findInheritedMethodCall('Document', 'id');
      
      // Second call should use cache
      const inheritanceInfo2 = await handler.findInheritedMethodCall('Document', 'id');
      
      expect(inheritanceInfo1).toEqual(inheritanceInfo2);
      
      const stats = handler.getCacheStats();
      expect(stats.inheritanceInfoCacheSize).toBeGreaterThan(0);
    });

    test('should handle deep inheritance chains efficiently', async () => {
      const startTime = Date.now();
      
      // Test multiple inheritance lookups
      await handler.findInheritedMethodCall('User', 'id');
      await handler.findInheritedMethodCall('User', 'auditLog');
      await handler.findInheritedMethodCall('User', 'version');
      await handler.findInheritedMethodCall('Document', 'id');
      await handler.findInheritedMethodCall('Document', 'auditLog');
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete quickly (less than 100ms for this simple test)
      expect(duration).toBeLessThan(100);
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent types gracefully', async () => {
      const inheritanceInfo = await handler.findInheritedMethodCall('NonExistentType', 'id');
      expect(inheritanceInfo).toBeNull();
    });

    test('should handle non-existent fields gracefully', async () => {
      const inheritanceInfo = await handler.findInheritedMethodCall('User', 'nonExistentField');
      expect(inheritanceInfo).toBeNull();
    });

    test('should validate interface implementation correctly', () => {
      const allInterfaces = handler.getAllInterfacesRecursively('User');
      expect(allInterfaces).toContain('Auditable');
      expect(allInterfaces).toContain('Versioned');
      expect(allInterfaces).toContain('Node');
    });
  });
});
