import { DecoratorParser } from '../utils/decorator-parser';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('@GQLField Auto-Inference', () => {
  let parser: DecoratorParser;
  let tempDir: string;

  beforeEach(async () => {
    parser = new DecoratorParser();
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'gqlfield-auto-inference-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('Interface Auto-Inference', () => {
    it('should auto-infer type from decorated interface', async () => {
      const content = `
        @GQLField({ ref: "User", name: "metadata" })
        export interface UserMetadata {
          createdAt: Date;
          updatedAt: Date;
          lastLogin?: Date;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'interface-test.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.ref).toBe('User');
      expect(fieldData.name).toBe('metadata');
      expect(fieldData.type).toBe('UserMetadata'); // Auto-inferred
    });

    it('should handle multiple auto-inferred interfaces', async () => {
      const content = `
        @GQLField({ ref: "User", name: "profile" })
        export interface UserProfile {
          bio: string;
          avatar: string;
        }

        @GQLField({ ref: "Post", name: "analytics" })
        export interface PostAnalytics {
          views: number;
          likes: number;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'multiple-interfaces.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(2);

      const profileField = result.fields.find(f => f.data.name === 'profile');
      expect(profileField?.data.type).toBe('UserProfile');

      const analyticsField = result.fields.find(f => f.data.name === 'analytics');
      expect(analyticsField?.data.type).toBe('PostAnalytics');
    });
  });

  describe('Type Alias Auto-Inference', () => {
    it('should auto-infer type from decorated type alias', async () => {
      const content = `
        @GQLField({ ref: "User", name: "status" })
        export type UserStatus = 'active' | 'inactive' | 'pending';

        @GQLField({ ref: "Product", name: "category" })
        export type ProductCategory = string;
      `;

      await fs.writeFile(path.join(tempDir, 'type-alias-test.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(2);

      const statusField = result.fields.find(f => f.data.name === 'status');
      expect(statusField?.data.type).toBe('UserStatus');

      const categoryField = result.fields.find(f => f.data.name === 'category');
      expect(categoryField?.data.type).toBe('ProductCategory');
    });
  });

  describe('Backward Compatibility', () => {
    it('should still work with explicit type parameter', async () => {
      const content = `
        @GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
        export interface UserMetadata {
          createdAt: Date;
          updatedAt: Date;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'explicit-type.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.type).toBe('UserMetadata');
    });

    it('should prefer explicit type over auto-inference', async () => {
      const content = `
        @GQLField({ ref: "User", name: "metadata", type: "CustomMetadata" })
        export interface UserMetadata {
          createdAt: Date;
          updatedAt: Date;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'explicit-override.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.type).toBe('CustomMetadata'); // Explicit type wins
    });
  });

  describe('Error Cases', () => {
    it('should fail when type cannot be auto-inferred from unsupported element', async () => {
      const content = `
        @GQLField({ ref: "User", name: "metadata" })
        export const userMetadata = {
          createdAt: new Date(),
          updatedAt: new Date()
        };
      `;

      await fs.writeFile(path.join(tempDir, 'unsupported-element.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(0); // Should fail to parse
    });

    it('should fail when no type provided and no decorator context', async () => {
      // This would be an edge case where parseGQLField is called without decorator context
      // In practice, this shouldn't happen in normal usage
      const fieldData = (parser as any).parseGQLField('{ ref: "User", name: "metadata" }');
      expect(fieldData).toBeNull();
    });
  });

  describe('Schema Identifier Support', () => {
    it('should auto-infer type with schema identifier', async () => {
      const content = `
        @GQLField({ ref: "User", name: "privateData", schema: "private" })
        export interface UserPrivateData {
          ssn: string;
          internalNotes: string;
        }

        @GQLField({ ref: "User", name: "publicData", schema: "public" })
        export interface UserPublicData {
          displayName: string;
          avatar: string;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'schema-identifier.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(2);

      const privateField = result.fields.find(f => f.data.schema === 'private');
      expect(privateField?.data.type).toBe('UserPrivateData');

      const publicField = result.fields.find(f => f.data.schema === 'public');
      expect(publicField?.data.type).toBe('UserPublicData');
    });
  });

  describe('Optional Fields', () => {
    it('should auto-infer type with optional field syntax', async () => {
      const content = `
        @GQLField({ ref: "User", name: "preferences", optional: true })
        export interface UserPreferences {
          theme: 'light' | 'dark';
          notifications: boolean;
        }
      `;

      await fs.writeFile(path.join(tempDir, 'optional-field.ts'), content);

      const result = await parser.scanCodebase(tempDir);
      expect(result.fields).toHaveLength(1);

      const fieldData = result.fields[0].data;
      expect(fieldData.type).toBe('UserPreferences');
      expect(fieldData.optional).toBe(true);
    });
  });
});
