/**
 * Configuration options for directives
 */
export interface DirectiveConfig {
    /** Whether directives are enabled */
    enabled: boolean;

    /** Path to custom directive handlers */
    customHandlersPath?: string;

    /** Whether to validate directives strictly (fail on invalid directive syntax) */
    strictValidation: boolean;

    /** Log level for directive processing (error, warn, info, debug) */
    logLevel: 'error' | 'warn' | 'info' | 'debug';

    /** Whether to enable directive composition (multiple directives affecting the same entity) */
    enableComposition: boolean;

    /** Whether to allow custom directives (not predefined in the system) */
    allowCustomDirectives: boolean;

    /** Whether to preprocess all directives at the start (vs. processing on-demand) */
    preprocessDirectives: boolean;
}

/**
 * Default configuration for directives
 */
export const defaultDirectiveConfig: DirectiveConfig = {
    enabled: true,
    strictValidation: false,
    logLevel: 'warn',
    enableComposition: true,
    allowCustomDirectives: true,
    preprocessDirectives: false
};

/**
 * Singleton class to manage directive configuration
 */
export class DirectiveConfigManager {
    private static instance: DirectiveConfigManager;
    private config: DirectiveConfig;

    private constructor() {
        this.config = { ...defaultDirectiveConfig };
    }

    /**
     * Get the instance of the config manager
     */
    public static getInstance(): DirectiveConfigManager {
        if (!DirectiveConfigManager.instance) {
            DirectiveConfigManager.instance = new DirectiveConfigManager();
        }
        return DirectiveConfigManager.instance;
    }

    /**
     * Get the current directive configuration
     */
    public getConfig(): DirectiveConfig {
        return { ...this.config };
    }

    /**
     * Update the directive configuration
     * @param config New configuration options (partial)
     */
    public updateConfig(config: Partial<DirectiveConfig>): void {
        this.config = { ...this.config, ...config };
    }

    /**
     * Reset the configuration to defaults
     */
    public resetConfig(): void {
        this.config = { ...defaultDirectiveConfig };
    }

    /**
     * Check if directives are enabled
     */
    public isEnabled(): boolean {
        return this.config.enabled;
    }

    /**
     * Check if directive composition is enabled
     */
    public isCompositionEnabled(): boolean {
        return this.config.enableComposition;
    }

    /**
     * Check if custom directives are allowed
     */
    public areCustomDirectivesAllowed(): boolean {
        return this.config.allowCustomDirectives;
    }

    /**
     * Get the log level
     */
    public getLogLevel(): string {
        return this.config.logLevel;
    }
} 