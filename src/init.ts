import * as fs from 'fs-extra';
import * as path from 'path';

interface InitOptions {
  directory?: string;
}

// Define TypeScript config interface
interface TSConfig {
  compilerOptions?: {
    baseUrl?: string;
    paths?: Record<string, string[]>;
    [key: string]: any;
  };
  [key: string]: any;
}

// Default initialization function
export default function init(options: InitOptions = {}) {
  const targetDir = options.directory ?? process.cwd();

  // Define paths
  const SCHEMA_PATH = path.join(targetDir, 'schema');
  const GENERATED_PATH = path.join(targetDir, 'src', 'generated');
  const CONTEXT_PATH = path.join(targetDir, 'src', 'context');

  console.log('Initializing GraphQL schema and configuration...');

  // Ensure directories exist
  fs.ensureDirSync(SCHEMA_PATH);
  fs.ensureDirSync(GENERATED_PATH);
  fs.ensureDirSync(CONTEXT_PATH);

  // Create sample schema file if it doesn't exist
  const sampleSchemaPath = path.join(SCHEMA_PATH, 'schema.graphql');
  if (!fs.existsSync(sampleSchemaPath)) {
    const sampleSchema = `# This is a sample GraphQL schema
# You can define your types, queries, mutations, etc. here

type User {
  id: ID!
  name: String!
  email: String!
  createdAt: String!
}

type Query {
  users: [User!]!
  user(id: ID!): User
}

type Mutation {
  createUser(name: String!, email: String!): User!
  updateUser(id: ID!, name: String, email: String): User
  deleteUser(id: ID!): Boolean!
}

schema {
  query: Query
  mutation: Mutation
}
`;

    fs.writeFileSync(sampleSchemaPath, sampleSchema);
    console.log(`Created sample schema: ${sampleSchemaPath}`);
  }

  // Create sample context file if it doesn't exist
  const contextFilePath = path.join(CONTEXT_PATH, 'index.ts');
  if (!fs.existsSync(contextFilePath)) {
    const contextFileContent = `/**
 * GraphQL Context definition
 * This file defines the Context type that will be passed to resolvers
 */

export interface Context {
  // Add your context properties here
  // For example:
  // user?: {
  //   id: string;
  //   roles: string[];
  // };
  // dataSources?: {
  //   userAPI: UserAPI;
  // };
  [key: string]: any;
}

/**
 * Create a context for GraphQL resolvers
 * This function will be called for each request
 */
export function createContext(req: any): Context {
  return {
    // Initialize your context properties here
    // Example:
    // user: getAuthenticatedUser(req),
    // dataSources: {
    //   userAPI: new UserAPI(),
    // },
  };
}
`;

    fs.ensureDirSync(path.dirname(contextFilePath));
    fs.writeFileSync(contextFilePath, contextFileContent);
    console.log(`Created context file: ${contextFilePath}`);
  }

  // Create or update tsconfig.json for path mapping
  const tsconfigPath = path.join(targetDir, 'tsconfig.json');
  let tsconfig: TSConfig = {};

  if (fs.existsSync(tsconfigPath)) {
    try {
      tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8')) as TSConfig;
    } catch (error) {
      console.warn(`Could not parse existing tsconfig.json: ${error}`);
      tsconfig = {};
    }
  }

  // Ensure compilerOptions and paths exist
  tsconfig.compilerOptions = tsconfig.compilerOptions ?? {};
  tsconfig.compilerOptions.baseUrl = tsconfig.compilerOptions.baseUrl ?? '.';
  tsconfig.compilerOptions.paths = tsconfig.compilerOptions.paths ?? {};

  // Add path mappings for the generated files
  tsconfig.compilerOptions.paths['@gql-generator/context'] = ['./src/context'];
  tsconfig.compilerOptions.paths['@gql-generator/generated/*'] = ['./src/generated/*'];

  // Write updated tsconfig
  fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));
  console.log(`Updated tsconfig.json with path mappings`);

  // Note: Scalar mappings are now automatically detected from schema files
  // Known scalars (built-in + common) keep proper types, unknown scalars are mapped to 'unknown'

  // Create or update codegen.yml
  const codegenPath = path.join(targetDir, 'codegen.ts');
  if (!fs.existsSync(codegenPath)) {
    const codegenContent = `import type { CodegenConfig } from '@graphql-codegen/cli';

/**
 * Note: Scalar types are automatically detected from schema files.
 * Known scalars (built-in + common) keep proper TypeScript types, unknown scalars use 'unknown'.
 *
 * This configuration is used as a fallback when the gql-generator
 * CLI is not available.
 */

const config: CodegenConfig = {
  schema: './schema/**/*.graphql',
  generates: {
    './src/generated/graphql.ts': {
      plugins: ['typescript', 'typescript-resolvers'],
      config: {
        useIndexSignature: true,
        // This path will be adjusted by the gql-generator CLI
        contextType: '@gql-generator/context#Context',
        // Scalars are automatically handled by gql-generator
        scalars: {
          // Known scalars keep proper types, unknown scalars map to 'unknown'
        }
      }
    }
  }
};

export default config;
`;

    fs.writeFileSync(codegenPath, codegenContent);
    console.log(`Created GraphQL codegen configuration: ${codegenPath}`);
  }

  console.log('Initialization completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Edit schema files in the schema/ directory');
  console.log('2. Define scalar types in your schema files (unknown scalars map to "unknown")');
  console.log('3. Run "npx gql-generator generate" to generate code');
}