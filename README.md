# GQL-Generator

![npm version](https://img.shields.io/npm/v/gql-generator.svg)
![license](https://img.shields.io/github/license/rzelian/gql-generator.svg)
![node version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen)

A sophisticated code generation system that transforms GraphQL schemas into production-ready TypeScript implementations with complete type safety and developer-friendly workflows.

## Table of Contents

- [Overview](#overview)
- [Core Features](#core-features)
- [System Architecture](#system-architecture)
- [Installation](#installation)
- [Quick Start](#quick-start)
- [Smart Watcher Mode](#smart-watcher-mode)
- [Project Structure](#project-structure)
- [Generated Files](#generated-files)
  - [Schema Type Enums](#schema-type-enums)
  - [Function Maps](#function-maps)
- [Configuration Options](#configuration-options)
- [Directive System](#directive-system)
- [TypeScript Decorator System](#typescript-decorator-system)
  - [Decorator Overview](#decorator-overview)
  - [@GQLMethodCall Decorator](#gqlmethodcall-decorator)
  - [@GQLImport Decorator](#gqlimport-decorator)
  - [@GQLField Decorator](#gqlfield-decorator)
  - [@GQLContext Decorator](#gqlcontext-decorator)
  - [Decorator Configuration](#decorator-configuration)
  - [Migration from Comment-Based Directives](#migration-from-comment-based-directives)
- [Migration Tool](#migration-tool)
  - [Alias Import Handling](#alias-import-handling)
- [Interface Inheritance](#interface-inheritance)
- [Smart Code Preservation](#smart-code-preservation)
- [Test Generation](#test-generation)
- [Workflow Integration](#workflow-integration)
- [Performance Considerations](#performance-considerations)
- [Compatibility](#compatibility)
- [Troubleshooting](#troubleshooting)
- [Comparison with Similar Tools](#comparison-with-similar-tools)
- [Roadmap](#roadmap)
- [Contributing](#contributing)
- [License](#license)
- [Using Hidden Fields with @field Directive](#using-hidden-fields-with-field-directive)

## Overview

GQL-Generator automates the generation of TypeScript code from GraphQL schemas, eliminating extensive manual boilerplate while ensuring type safety across your GraphQL backend. It employs a schema-first approach where the GraphQL schema serves as the single source of truth, automatically generating corresponding TypeScript interfaces, resolver stubs, and test scaffolding.

<div align="center">

```mermaid
graph TD
    %% Main Components
    subgraph Input ["Input Layer"]
        Schema["GraphQL Schema Files"]
        SchemaLoader["Schema Loader"]
        SchemaMapper["Schema Mapper"]
        DirectiveParser["Directive Parser"]
    end

    subgraph Core ["Core Processing Layer"]
        TypeGen["GraphQL TypeScript Generator"]
        SBG["Schema-Based Generator"]

        subgraph Generators ["Code Generators"]
            FRG["Field Resolver Generator"]
            TRG["Type Resolver Generator"]
            TestGen["Test Generator"]
            RootGen["Root Index Generator"]
            DirectiveProc["Directive Processor"]
        end

        subgraph TypeProcessors ["Type Processors"]
            ObjProc["Object Type Processor"]
            OpProc["Operation Type Processor"]
            InterfProc["Interface/Union Processor"]
            MutTestGen["Mutation Test Generator"]
        end
    end

    subgraph Output ["Output Layer"]
        TypeDefs["TypeScript Type Definitions"]
        Resolvers["Resolver Implementations"]
        Tests["Test Files"]
        Indices["Index Files"]
    end

    %% Flow Relationships
    Schema --> SchemaLoader
    SchemaLoader --> SchemaMapper
    SchemaMapper --> TypeGen
    SchemaMapper --> SBG

    Schema --> DirectiveParser
    DirectiveParser --> DirectiveProc
    DirectiveProc --> FRG
    DirectiveProc --> TRG

    SBG --> FRG
    SBG --> TRG
    SBG --> TestGen
    SBG --> RootGen

    FRG --> ObjProc
    FRG --> OpProc
    TRG --> InterfProc
    TestGen --> MutTestGen

    TypeGen --> TypeDefs
    ObjProc --> Resolvers
    OpProc --> Resolvers
    InterfProc --> Resolvers
    MutTestGen --> Tests
    RootGen --> Indices

    %% Styling
    classDef inputClass fill:#e1f5fe,stroke:#0288d1,stroke-width:2px;
    classDef coreClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px;
    classDef outputClass fill:#e8f5e9,stroke:#388e3c,stroke-width:2px;
    classDef genClass fill:#fff8e1,stroke:#ffa000,stroke-width:1px;
    classDef procClass fill:#fff3e0,stroke:#e65100,stroke-width:1px;

    class Input inputClass;
    class Core coreClass;
    class Output outputClass;
    class Generators genClass;
    class TypeProcessors procClass;
```

</div>

## Core Features

- **End-to-End Type Safety**: Generate TypeScript types directly from your GraphQL schema for complete type safety
- **Smart Watcher Mode**: Real-time auto-generation with bidirectional sync between code and schema
- **TypeScript Decorator System**: Modern decorator-based annotations (`@GQLMethodCall`, `@GQLImport`, `@GQLField`) with full IDE support
- **Comment-Based Directives**: Traditional schema comments (`@import`, `@methodCall`, `@resolver`, `@context`) for schema-first development
- **Smart Code Preservation**: Intelligently preserves custom code during regeneration with advanced AST parsing
- **Bidirectional Sync**: Automatically updates schema when code changes, supporting await/cast patterns
- **Smart Import Resolution**: Automatic calculation of import paths between generated resolvers and your codebase
- **Multi-Schema Support**: Handle multiple GraphQL schemas with schema identifiers and separate outputs
- **Schema-Based Organization**: Output directory structure mirrors your schema organization
- **Interface & Union Support**: Automatic handling of GraphQL interfaces and unions with type narrowing
- **Interface Inheritance**: Multi-deep inheritance support for both comment-based and decorator-based directives
- **Test Generation**: Creates fully typed test scaffolding for resolvers, queries and mutations
- **Modular Architecture**: Schema-first design supporting complex, multi-file schemas
- **Single Source of Truth**: Schema is the definitive reference for all generated code

## System Architecture

GQL-Generator follows a modular architecture with clear separation of concerns:

```mermaid
classDiagram
    BaseGenerator <|-- SchemaBasedGenerator
    BaseGenerator <|-- MutationTestGenerator

    SchemaBasedGenerator *-- FieldResolverGenerator
    SchemaBasedGenerator *-- TypeResolverGenerator
    SchemaBasedGenerator *-- ObjectTypeProcessor
    SchemaBasedGenerator *-- OperationTypeProcessor
    SchemaBasedGenerator *-- InterfaceUnionProcessor
    SchemaBasedGenerator *-- RootIndexGenerator
    SchemaBasedGenerator *-- CleanupService

    BaseGenerator *-- SchemaMapper
    MutationTestGenerator ..> MockDataUtils

    ObjectTypeProcessor --> FieldResolverGenerator
    OperationTypeProcessor --> FieldResolverGenerator
    InterfaceUnionProcessor --> TypeResolverGenerator

    FieldResolverGenerator ..> DirectiveParser
    TypeResolverGenerator ..> DirectiveParser
    DirectiveParser ..> DirectiveProcessor

    class BaseGenerator {
        +schemaMap: SchemaMap
        +config: GeneratorConfig
        +generate(): void
    }

    class SchemaBasedGenerator {
        +processObjectTypes()
        +processOperationTypes()
        +processInterfaceTypes()
        +generateRootIndex()
    }

    class FieldResolverGenerator {
        +generateFieldResolver()
        +applyDirectives()
    }

    class TypeResolverGenerator {
        +generateTypeResolver()
        +generateTypeGuard()
    }

    class DirectiveParser {
        +parseDirectives()
        +extractDirective()
    }
```

## Installation

### From npm (Not Yet Released)

```bash
npm install -g gql-generator
```

### From Source

```bash
git clone https://github.com/rzelian/gql-ts-gen.git
cd gql-ts-gen
npm install
npm run build
npm install -g .
```

### Using the Cross-Platform Installation Script

For convenience, this package includes cross-platform installation scripts that perform all necessary installation steps in one command:

```bash
# Basic installation script (works on both Windows and macOS/Linux)
npm run build-and-install

# Enhanced installation script with better error handling and feedback
npm run setup
```

Both scripts will:

1. Install all dependencies (`npm install`)
2. Build the project (`npm run build`)
3. Install the package globally (`npm install -g .`)

The `setup` script provides additional features:

- Colorized console output for better readability
- Detailed step-by-step progress information
- Better error handling with helpful messages
- Verification of executable permissions on all platforms
- Confirmation of successful installation

These scripts are designed to work correctly on both Windows and macOS/Linux environments, handling platform-specific requirements automatically.

## Quick Start

### 1. Define Your GraphQL Schema

Create a schema directory structure:

```
schema/
├── schema.gql                 # Main schema definition
├── root_query.gql             # Root query operations
├── root_mutation.gql          # Root mutation operations
├── types/                     # Type definitions
│   ├── user.gql               # User type definition
│   └── post.gql               # Post type definition
├── query_wrappers/            # Nested query definitions
└── mutation_wrappers/         # Nested mutation definitions
```

**Example `schema.gql`:**

```graphql
# @context(path: "@context/context", name: "Context")
schema {
  query: RootQuery
  mutation: RootMutation
}
```

**Example `root_query.gql`:**

```graphql
type RootQuery {
  node(id: ID!): Node
  user(id: ID!): User
  posts(limit: Int = 10, offset: Int = 0): [Post!]!
}
```

**Example `types/user.gql`:**

```graphql
type User {
  id: ID!
  name: String!
  email: String!
  posts: [Post!]!
  createdAt: String!
}
```

### 2. Generate TypeScript Code

```bash
# Basic usage with default options
gql-generator generate

# Or with custom options
gql-generator generate --schema "./schema" --output "./output"
```

### 3. Implement Your Resolvers

The generated resolver stubs maintain type safety while allowing you to implement business logic:

```typescript
// output/root-query/user.ts
import { RootQueryUserArgs, ResolversParentTypes, User } from '../graphql';
import { Context } from '@gql-generator/context';

/**
 * Resolver for RootQuery.user
 * Resolved field: `RootQuery.user: User`
 * Defined in: `root_query.gql`
 *
 * GraphQL Schema Representation:
 * type RootQuery { user(id: ID!): User }
 */
const user = async (
  obj: ResolversParentTypes['RootQuery'],
  args: RootQueryUserArgs,
  context: Context
): Promise<User | null> => {
  try {
    // Your implementation here
    const { id } = args;
    return await context.dataSources.users.findById(id);
  } catch (error) {
    console.error(`Error in user resolver:`, error);
    throw error;
  }
};

export default user;
```

## Multiple Schema-Output Mappings

GQL-Generator supports multiple schema-output directory mappings, allowing you to organize different parts of your GraphQL API into separate directories with their own generated code.

### Easy CLI Syntax

Use multiple `--schema` and `--output` flags that pair up in order:

```bash
# Each --schema pairs with the corresponding --output
gql-generator generate \
  --schema "./schema/api" --output "./src/generated/api" \
  --schema "./schema/admin" --output "./src/generated/admin" \
  --schema "./external-schemas" --output "./src/external"

# Works with all generation options
gql-generator generate \
  --schema "./schema/api" --output "./src/api" \
  --schema "./schema/admin" --output "./src/admin" \
  --force --debug
```

### Watch Mode with Multiple Mappings

The smart watcher supports multiple schema-output pairs with full bidirectional sync:

```bash
# Watch multiple schema-output pairs
gql-generator watch \
  --schema "./schema/api" --output "./src/api" \
  --schema "./schema/admin" --output "./src/admin" \
  --bidirectional

# Each mapping gets its own watch service
gql-generator watch --bidirectional --debug \
  --schema "./schema/core" --output "./src/core" \
  --schema "./schema/plugins" --output "./src/plugins"
```

### Benefits

- **Organized Codebase**: Keep different API domains separate
- **Independent Development**: Teams can work on different schema-output pairs independently
- **Selective Watching**: Each mapping has its own file watcher for optimal performance
- **Full Feature Support**: All features (bidirectional sync, testing, etc.) work with multiple mappings

## Smart Watcher Mode

GQL-Generator includes a powerful smart watcher mode that provides real-time auto-generation and bidirectional synchronization between your code and schema. This eliminates the need to manually run generation commands and automatically keeps your schema up-to-date when you modify resolver implementations.

### Overview

Smart watcher mode offers two primary modes of operation:

1. **Basic Watch Mode**: Monitors schema files and automatically regenerates code when schemas change
2. **Enhanced Bidirectional Sync**: Monitors both schema and generated code files, automatically updating schemas when code patterns change

### Basic Watch Mode

Monitor your schema files and automatically regenerate code when changes are detected:

```bash
# Basic watch mode - monitors schema files only
gql-generator watch

# With custom paths
gql-generator watch --schema "./graphql/**/*.gql" --output "./src/api"

# Watch multiple schema directories
gql-generator watch --watch-paths "./schema,./external-schemas"
```

**Features:**
- Real-time schema monitoring with file system events
- Automatic code regeneration on schema changes
- Debounced updates to prevent excessive regeneration
- Preserves custom implementations during regeneration

### Enhanced Bidirectional Sync

The enhanced mode provides true bidirectional synchronization, automatically updating your schema when you modify resolver code:

```bash
# Enable bidirectional sync
gql-generator watch --bidirectional

# With debug output for development
gql-generator watch --bidirectional --debug

# Custom sync settings
gql-generator watch --bidirectional --sync-debounce 1000 --max-sync-attempts 5
```

### How Bidirectional Sync Works

When you modify a resolver file, the system automatically:

1. **Detects Code Patterns**: Analyzes your resolver implementations for methodCall patterns
2. **Extracts Imports**: Identifies new import statements that should be added to the schema
3. **Updates Schema**: Automatically adds `@methodCall` and `@import` directives to the appropriate schema files
4. **Regenerates Code**: Triggers a new generation cycle to ensure consistency

#### Supported Patterns

The bidirectional sync supports various TypeScript patterns:

**Basic Method Calls:**
```typescript
// In resolver file
return UserService.findById(args.id);
```
```graphql
# Automatically added to schema
# @methodCall(UserService.findById(args.id))
# @import(import { UserService } from '@/services/user-service';)
user(id: ID!): User
```

**Await Patterns:**
```typescript
// In resolver file
return await UserService.findById(args.id);
```
```graphql
# Automatically added to schema
# @methodCall(await UserService.findById(args.id))
# @import(import { UserService } from '@/services/user-service';)
user(id: ID!): User
```

**Type Casting:**
```typescript
// In resolver file
return await UserService.findById(args.id) as User;
```
```graphql
# Automatically added to schema
# @methodCall(await UserService.findById(args.id) as User)
# @import(import { UserService } from '@/services/user-service';)
user(id: ID!): User
```

**Complex Property Chains:**
```typescript
// In resolver file
return await context.dataSources.users.findById(args.id);
```
```graphql
# Automatically added to schema
# @methodCall(await context.dataSources.users.findById(args.id))
user(id: ID!): User
```

### Configuration Options

| Option                | Description                                      | Default |
| --------------------- | ------------------------------------------------ | ------- |
| `--bidirectional`     | Enable bidirectional sync between code/schema   | `false` |
| `--sync-debounce`     | Debounce time for sync operations (ms)          | `500`   |
| `--max-sync-attempts` | Maximum retry attempts for sync operations      | `3`     |
| `--watch-paths`       | Comma-separated list of paths to watch          | Schema path |
| `--debug`             | Enable verbose debug logging                    | `false` |

### Advanced Usage Examples

**Multi-Path Watching:**
```bash
# Watch multiple schema directories
gql-generator watch --bidirectional --watch-paths "./schema,./external-schemas,./shared-types"
```

**Development Mode with Debug:**
```bash
# Full debug output for troubleshooting
gql-generator watch --bidirectional --debug --sync-debounce 1000
```

**Production-Ready Watching:**
```bash
# Optimized for production with longer debounce
gql-generator watch --bidirectional --sync-debounce 2000 --max-sync-attempts 5
```

### Benefits

**Developer Productivity:**
- Eliminates manual schema updates when implementing resolvers
- Reduces context switching between code and schema files
- Automatically maintains consistency between implementation and schema

**Code Quality:**
- Ensures schema always reflects actual implementation
- Prevents drift between code and schema definitions
- Maintains proper import statements automatically

**Team Collaboration:**
- Automatic schema updates when APIs are renamed or modified
- Consistent patterns across team members
- Reduces merge conflicts in schema files

### Best Practices

1. **Use Simple Patterns**: Bidirectional sync works best with straightforward resolver implementations
2. **Avoid Complex Logic**: Keep complex business logic separate from simple methodCall patterns
3. **Test Thoroughly**: Always test generated schemas after significant changes
4. **Version Control**: Commit both code and schema changes together
5. **Monitor Debug Output**: Use `--debug` flag during development to understand sync behavior

### Troubleshooting

**Sync Not Working:**
- Ensure file paths are correct and accessible
- Check that resolver patterns match supported formats
- Verify import statements are properly formatted

**Performance Issues:**
- Increase `--sync-debounce` value for large projects
- Use selective watching with `--watch-paths`
- Monitor system resources during watch operations

**Pattern Detection Issues:**
- Use `--debug` flag to see pattern analysis output
- Ensure methodCall patterns are simple and direct
- Check that import statements follow standard TypeScript syntax

## Project Structure

GQL-Generator generates a well-organized directory structure that mirrors your schema organization:

```
output/
├── graphql.ts                 # Generated TypeScript type definitions
├── schema.graphql             # Merged GraphQL schema
├── index.ts                   # Root index file exporting all resolvers
├── root-query/                # Root Query resolvers
│   ├── index.ts               # Root Query index file
│   ├── user.ts                # User query resolver
│   └── posts.ts               # Posts query resolver
├── root-mutation/             # Root Mutation resolvers
│   ├── index.ts               # Root Mutation index file
│   ├── create-user.ts         # Create user mutation resolver
│   └── update-user.ts         # Update user mutation resolver
├── types/                     # Object type field resolvers
│   ├── user/                  # User type field resolvers
│   │   ├── index.ts           # User index file
│   │   └── posts.ts           # Posts field resolver
│   └── post/                  # Post type field resolvers
│       ├── index.ts           # Post index file
│       └── author.ts          # Author field resolver
├── query-wrappers/            # Nested query resolvers
├── mutation-wrappers/         # Nested mutation resolvers
└── __tests__/                 # Generated test files
    ├── utils/                 # Test utilities
    │   └── mock-context.ts    # Mock context for testing
    ├── root-query/            # Query tests
    └── root-mutation/         # Mutation tests
```

## Generated Files

GQL-Generator automatically creates several types of files to enhance your development experience with type safety and code organization. Beyond the standard resolver files and TypeScript definitions, the generator produces specialized files that provide additional functionality and developer convenience.

### Schema Type Enums

GQL-Generator automatically generates type-safe constants for all GraphQL types in your schema, eliminating the need for hardcoded strings and providing better IDE support and refactoring capabilities.

#### Overview

The Schema Type Enums feature creates a `SchemaTypes.ts` file containing:

- **Type Constants**: Object with all GraphQL type names as type-safe constants
- **Type Union**: Union type of all valid schema type names
- **Helper Functions**: Utilities for type checking and validation
- **Multi-Schema Support**: Namespaced enums when using `--schema-id` flag

#### Basic Usage

```bash
# Generate with schema type enums (enabled by default)
gql-generator generate --schema ./schema --output ./output

# Multi-schema setup with schema identifiers
gql-generator generate \
  --schema ./schema/public --output ./output/public --schema-id public \
  --schema ./schema/admin --output ./output/admin --schema-id admin
```

#### Generated File Structure

**Single Schema (`SchemaTypes.ts`):**

```typescript
/**
 * Auto-generated GraphQL Schema Type Constants
 * Generated from: ./schema/**/*.gql
 *
 * Use these constants instead of hardcoded strings for type-safe GraphQL type references.
 */

export const SchemaTypes = {
  RootQuery: 'RootQuery',
  RootMutation: 'RootMutation',
  User: 'User',
  Post: 'Post',
  Comment: 'Comment',
  // ... all other types
} as const;

/**
 * Union type of all valid schema type names
 */
export type SchemaTypeNames = typeof SchemaTypes[keyof typeof SchemaTypes];

/**
 * Type-safe helper for checking if a string is a valid schema type
 */
export function isValidSchemaType(type: string): type is SchemaTypeNames {
  return Object.values(SchemaTypes).includes(type as SchemaTypeNames);
}

/**
 * Get all available schema type names as an array
 */
export function getSchemaTypeNames(): SchemaTypeNames[] {
  return Object.values(SchemaTypes);
}
```

**Multi-Schema with `--schema-id` (`SchemaTypes.ts`):**

```typescript
// For --schema-id admin
export const AdminSchemaTypes = {
  RootQuery: 'RootQuery',
  RootMutation: 'RootMutation',
  AdminUser: 'AdminUser',
  AdminRole: 'AdminRole',
  // ... admin-specific types
} as const;

export type AdminSchemaTypeNames = typeof AdminSchemaTypes[keyof typeof AdminSchemaTypes];

export function isValidAdminSchemaType(type: string): type is AdminSchemaTypeNames {
  return Object.values(AdminSchemaTypes).includes(type as AdminSchemaTypeNames);
}
```

#### Practical Usage Examples

**In Resolver Maps:**

```typescript
import { SchemaTypes } from './SchemaTypes';

const resolvers = {
  [SchemaTypes.RootQuery]: {
    user: userResolver,
    posts: postsResolver,
  },
  [SchemaTypes.RootMutation]: {
    createUser: createUserResolver,
    updatePost: updatePostResolver,
  },
  [SchemaTypes.User]: {
    posts: userPostsResolver,
    profile: userProfileResolver,
  }
};
```

**In Type Guards:**

```typescript
import { SchemaTypes, isValidSchemaType } from './SchemaTypes';

function processGraphQLType(typeName: string) {
  if (!isValidSchemaType(typeName)) {
    throw new Error(`Invalid GraphQL type: ${typeName}`);
  }

  // TypeScript now knows typeName is a valid schema type
  switch (typeName) {
    case SchemaTypes.User:
      return processUserType();
    case SchemaTypes.Post:
      return processPostType();
    // ... other cases
  }
}
```

**In Dynamic Resolver Loading:**

```typescript
import { SchemaTypes, getSchemaTypeNames } from './SchemaTypes';

// Load all available resolvers dynamically
const allTypes = getSchemaTypeNames();
const resolverMap = {};

for (const typeName of allTypes) {
  if (typeName !== SchemaTypes.RootQuery && typeName !== SchemaTypes.RootMutation) {
    resolverMap[typeName] = await import(`./types/${typeName.toLowerCase()}`);
  }
}
```

#### Benefits

- **Type Safety**: Eliminates typos in type names with compile-time checking
- **IDE Support**: Full autocomplete and refactoring support for type names
- **Refactoring**: Automatic updates when schema types are renamed
- **Multi-Schema**: Clean separation of type constants for different schemas
- **Runtime Validation**: Helper functions for runtime type checking

### Function Maps

GQL-Generator creates comprehensive function maps that provide a complete overview of all available methods and fields for each GraphQL type and interface. These maps serve as both documentation and runtime references for your resolvers.

#### Overview

Function Maps generate TypeScript files in the `output/function-maps/` directory containing:

- **Method Maps**: All available method calls for each type/interface
- **Field Maps**: All field directives and their types
- **Resolver References**: Direct function references for idiomatic usage
- **Inheritance Information**: Methods inherited from interfaces
- **Master Index**: Centralized exports of all function maps

#### Directory Structure

```
output/
├── function-maps/
│   ├── index.ts                    # Master index exporting all maps
│   ├── types/                      # Object type function maps
│   │   ├── user.ts                 # User type function map
│   │   ├── post.ts                 # Post type function map
│   │   └── comment.ts              # Comment type function map
│   └── interfaces/                 # Interface type function maps
│       ├── node.ts                 # Node interface function map
│       └── timestamped.ts          # Timestamped interface function map
```

#### Generated Function Map Structure

**Individual Type Map (`output/function-maps/types/user.ts`):**

```typescript
/**
 * Function map for object User
 * Auto-generated - contains all available methods and fields
 */

import { getUserPosts } from '../../types/user/posts';
import { getUserProfile } from '../../types/user/profile';

export const UserMethods = {
  posts: 'getUserPosts(obj.id)',
  profile: 'getUserProfile(obj.id)',
  avatar: 'await getAvatarUrl(obj.id) as string' // inherited from UserInterface
};

export const UserFields = {
  metadata: 'UserMetadata',
  internalId: 'string'
};

export const UserFunctionMap = {
  posts: { type: 'method', source: 'directive', call: 'getUserPosts(obj.id)' },
  profile: { type: 'method', source: 'directive', call: 'getUserProfile(obj.id)' },
  avatar: { type: 'method', source: 'inherited', call: 'await getAvatarUrl(obj.id) as string' }, // inherited from UserInterface
  metadata: { type: 'field', source: 'directive', fieldType: 'UserMetadata' },
  internalId: { type: 'field', source: 'directive', fieldType: 'string' }
};

/**
 * User resolver function references
 * Contains actual function references for idiomatic usage
 */
export const UserType = {
  getUserPosts,
  getUserProfile
};

export default UserFunctionMap;
```

**Master Index (`output/function-maps/index.ts`):**

```typescript
/**
 * Master function map index
 * Auto-generated - exports all available function maps
 */

export { default as UserFunctionMap, UserMethods, UserFields, UserType } from './types/user';
export { default as PostFunctionMap, PostMethods, PostFields, PostType } from './types/post';
export { default as NodeFunctionMap, NodeMethods, NodeFields } from './interfaces/node';

// Type definitions
export interface FunctionMapEntry {
  type: 'method' | 'field';
  source: 'directive' | 'decorator' | 'inherited';
  call?: string;
  fieldType?: string;
}

export type FunctionMap = Record<string, FunctionMapEntry>;
```

#### Practical Usage Examples

**Exploring Available Methods:**

```typescript
import { UserMethods, UserFunctionMap } from './function-maps';

// See all available methods for User type
console.log('User methods:', UserMethods);
// Output: { posts: 'getUserPosts(obj.id)', profile: 'getUserProfile(obj.id)' }

// Get detailed information about each entry
console.log('User function map:', UserFunctionMap);
// Shows type, source, call information for each method/field
```

**Dynamic Resolver Execution:**

```typescript
import { UserType, UserFunctionMap } from './function-maps';

// Use actual function references
const user = { id: '123', name: 'John' };
const userPosts = await UserType.getUserPosts(user.id);

// Or use the function map for dynamic execution
const methodName = 'posts';
const methodInfo = UserFunctionMap[methodName];
if (methodInfo && methodInfo.type === 'method') {
  console.log(`Method call: ${methodInfo.call}`);
  console.log(`Source: ${methodInfo.source}`);
}
```

**Code Generation and Tooling:**

```typescript
import { UserFunctionMap, PostFunctionMap } from './function-maps';

// Generate documentation or tooling based on available methods
function generateResolverDocs(typeName: string, functionMap: any) {
  const methods = Object.entries(functionMap)
    .filter(([_, info]: [string, any]) => info.type === 'method')
    .map(([name, info]: [string, any]) => ({
      name,
      call: info.call,
      source: info.source,
      inherited: info.source === 'inherited'
    }));

  return {
    typeName,
    methodCount: methods.length,
    methods
  };
}

const userDocs = generateResolverDocs('User', UserFunctionMap);
const postDocs = generateResolverDocs('Post', PostFunctionMap);
```

**Interface Inheritance Tracking:**

```typescript
import { UserFunctionMap } from './function-maps';

// Find inherited methods
const inheritedMethods = Object.entries(UserFunctionMap)
  .filter(([_, info]: [string, any]) => info.source === 'inherited')
  .map(([name, info]: [string, any]) => ({
    name,
    call: info.call,
    inheritedFrom: info.interfaceSource
  }));

console.log('Methods inherited by User:', inheritedMethods);
```

#### Integration with Development Workflow

**IDE Integration:**

Function maps provide excellent IDE support for exploring your GraphQL schema:

```typescript
// Import and explore available methods
import {
  UserMethods,
  PostMethods,
  CommentMethods
} from './function-maps';

// IDE will show all available methods with autocomplete
const availableUserMethods = UserMethods.
//                                      ^ IDE shows: posts, profile, avatar, etc.
```

**Testing and Mocking:**

```typescript
import { UserType, UserFunctionMap } from './function-maps';

// Use function references in tests
describe('User resolvers', () => {
  it('should get user posts', async () => {
    const result = await UserType.getUserPosts('123');
    expect(result).toBeDefined();
  });

  // Generate tests based on function map
  Object.entries(UserFunctionMap).forEach(([methodName, info]) => {
    if (info.type === 'method' && info.source !== 'inherited') {
      it(`should have ${methodName} method`, () => {
        expect(UserType[methodName]).toBeDefined();
      });
    }
  });
});
```

#### Benefits

- **Complete Overview**: See all available methods and fields for each type
- **Source Tracking**: Know whether methods come from directives, decorators, or inheritance
- **Function References**: Direct access to resolver functions for idiomatic usage
- **Inheritance Visibility**: Clear indication of methods inherited from interfaces
- **Development Tools**: Foundation for building custom tooling and documentation
- **Runtime Introspection**: Programmatically explore your GraphQL schema structure

## Configuration Options

GQL-Generator provides comprehensive configuration options for both generation and watch modes:

### Generate Command Options

| Option            | Alias | Description                                      | Default                |
| ----------------- | ----- | ------------------------------------------------ | ---------------------- |
| `--schema`        | `-s`  | GraphQL schema files glob pattern                | `"./schema/**/*.gql"`  |
| `--output`        | `-o`  | Output directory for generated files             | `"./output"`           |
| `--force`         | `-f`  | Force overwrite of existing files                | `false`                |
| `--skip-codegen`  | -     | Skip running GraphQL codegen                     | `false`                |
| `--context`       | -     | Path to custom Context interface file            | -                      |
| `--context-name`  | -     | Name of the Context interface                    | `"Context"`            |
| `--test`          | -     | Generate test stubs for queries and mutations    | `false`                |
| `--test-output`   | -     | Custom directory for generated test files        | `"<output>/__tests__"` |
| `--test-style`    | -     | Test generation style: 'grouped' or 'individual' | `"grouped"`            |
| `--apply-aliases` | -     | Apply test aliases defined in schema             | `true`                 |
| `--debug`         | -     | Enable debug mode with verbose logging           | `false`                |
| `--schema-id`     | -     | Schema identifier for multi-schema support (generates namespaced type enums) | -                      |

### Watch Command Options

| Option                | Description                                      | Default                |
| --------------------- | ------------------------------------------------ | ---------------------- |
| `--schema`            | GraphQL schema files glob pattern                | `"./schema/**/*.gql"`  |
| `--output`            | Output directory for generated files             | `"./output"`           |
| `--bidirectional`     | Enable bidirectional sync between code/schema   | `false`                |
| `--sync-debounce`     | Debounce time for sync operations (ms)          | `500`                  |
| `--max-sync-attempts` | Maximum retry attempts for sync operations      | `3`                    |
| `--watch-paths`       | Comma-separated list of paths to watch          | Schema path            |
| `--debug`             | Enable verbose debug logging                    | `false`                |
| `--debounce`          | Debounce time for file changes (ms)             | `300`                  |
| `--initial`           | Run initial generation before watching          | `true`                 |
| `--schema-id`         | Schema identifier for multi-schema support (generates namespaced type enums) | -                      |

### Configuration Examples

```bash
# Generate with custom schema and output directories
gql-generator generate --schema "./graphql/**/*.graphql" --output "./src/api/generated"

# Generate with test files in a separate directory
gql-generator generate --test --test-output "./tests/unit"

# Use a specific context type
gql-generator generate --context "./src/types/AppContext.ts" --context-name "AppContext"

# Force regeneration of all files
gql-generator generate --force

# Multiple schema-output pairs
gql-generator generate \
  --schema "./schema/api" --output "./src/generated/api" \
  --schema "./schema/admin" --output "./src/generated/admin" \
  --schema "./external-schemas" --output "./src/external"

# Multiple schemas with schema identifiers (generates namespaced type enums)
gql-generator generate \
  --schema "./schema/public" --output "./src/public" --schema-id public \
  --schema "./schema/admin" --output "./src/admin" --schema-id admin

# Watch mode examples
gql-generator watch --bidirectional --debug

# Watch multiple schema-output pairs with bidirectional sync
gql-generator watch --bidirectional \
  --schema "./schema/api" --output "./src/api" \
  --schema "./schema/admin" --output "./src/admin"

# Watch multiple paths with custom sync settings
gql-generator watch --bidirectional --watch-paths "./schema,./external" --sync-debounce 1000
```

## Directive System

GQL-Generator uses a powerful comment-based directive system to customize code generation and add advanced functionality. Directives are specified as GraphQL comments using the syntax `# @directiveName(directiveContent)` and can be applied at both the type and field levels.

### Available Directives

| Directive     | Usage                 | Description                                                                      |
| ------------- | --------------------- | -------------------------------------------------------------------------------- |
| `@context`    | Schema-level          | Configures the context type used in all resolver functions                       |
| `@import`     | Type/Field-level      | Adds custom import statements to the resolver file                               |
| `@methodCall` | Field-level           | Provides a direct implementation for field resolvers                             |
| `@resolver`   | Interface/Union-level | Adds type narrowing logic for interfaces and unions                              |
| `@alias`      | Schema-level          | Specifies a base path for all resolver imports in test files                     |
| `@default`    | Field-level           | Generates a default resolver that returns the field value from the parent object |
| `@field`      | Type-level            | Adds TypeScript-only fields to types without affecting the GraphQL schema        |

### Examples

**Schema Context Configuration**

```graphql
# @context(path: "./my-context", name: "Context")
schema {
  query: RootQuery
  mutation: RootMutation
}
```

**Type-Level Imports**

```graphql
# @import(import { validateUser } from "../utils/validation")
type User {
  id: ID!
  name: String!
}
```

**Field-Level Imports**

```graphql
# @import(import { validateUser } from "../utils/validation")
type UserMutation {
  # @import(import { updateUser } from "../utils/user-mutations")
  updateUser(id: ID!, name: String!): User!
}
```

**Method Calls**

```graphql
type User {
  id: ID!
  name: String!

  # @methodCall(context.dataSources.posts.getByUserId(parent.id))
  posts: [Post!]!

  # @import(import { formatDate } from "../utils/date-formatter")
  # @methodCall(formatDate(parent.createdAt))
  formattedCreatedAt: String!
}
```

**Field Aliases for Testing**

The `@alias` directive provides a powerful way to control test file generation in test files without affecting resolver implementation.

Example usage:

```graphql
# @alias directive should be placed at the schema level
# This will serve as a base path for all resolver imports in test files
# @alias(path: "@my-graphql/resolvers")
schema {
  query: RootQuery
  mutation: RootMutation
}
```

When this @alias directive is used, the generated test files will import resolvers from the specified base path combined with their relative paths:

```typescript
// In generated test file
// Without @alias, would be something like: import '../resolvers/mutations/createUser'
// With @alias, it combines the base path with the relative resolver path:
beforeAll(async () => {
  resolverFn = await import('@my-graphql/resolvers/mutation_wrappers/acknowledge/create-chat').then(
    module => module.default
  );
});
```

This allows you to:

1. Use path aliases configured in your tsconfig.json
2. Redirect tests to use mock implementations instead of actual resolvers
3. Handle complex or deeply nested import scenarios more cleanly
4. Create consistent import patterns across test files

**Interface Type Resolution**

```graphql
interface Node {
  id: ID!
}

# @resolver(isUser(obj) { return obj.__typename === 'User' || obj.type === 'USER'; })
type User implements Node {
  id: ID!
  name: String!
}

# @import(import { resolvePostType } from "../utils/type-guards")
# @resolver(resolvePostType(obj))
type Post implements Node {
  id: ID!
  title: String!
}
```

**Union Type Resolution with TypeName**

```graphql
# @import(import { resolveResultType } from "../utils/type-guards")
# @resolver(resolveResultType(obj), { useTypeName: true })
union Result = SuccessResponse | ErrorResponse

type SuccessResponse {
  data: String!
}

type ErrorResponse {
  message: String!
  code: Int!
}
```

**Object Type Resolution**

Object types can also generate `__resolveType` functions when they have a `@resolver` directive. This is useful for object types that need custom type resolution logic, such as polymorphic types or types with complex inheritance patterns.

```graphql
# Object type with custom resolver function
# @resolver(customObjectResolver(obj, context))
type CustomObject {
  id: ID!
  name: String!
  value: Int
}

# Object type with useTypeName support
# @resolver(anotherCustomResolver(obj, context), { useTypeName: true })
type AnotherCustomObject {
  id: ID!
  type: String!
  data: String
}

# Object type with empty resolver (generates TODO template)
# @resolver()
type DefaultResolverObject {
  id: ID!
  status: String!
}

# Regular object type without @resolver directive
# Will NOT generate __resolveType function
type RegularObject {
  id: ID!
  description: String
}
```

**Generated Object Type Resolvers:**

For `CustomObject` with custom resolver:

```typescript
const __resolveType = async (
  obj: ResolversParentTypes['CustomObject'] & { __typename?: string },
  context: Context,
  info: GraphQLResolveInfo
): Promise<'CustomObject'> => {
  // Use custom resolver from @resolver directive
  return customObjectResolver(obj, context);
};
```

For `AnotherCustomObject` with useTypeName:

```typescript
const __resolveType = async (
  obj: ResolversParentTypes['AnotherCustomObject'] & { __typename?: string },
  context: Context,
  info: GraphQLResolveInfo
): Promise<'AnotherCustomObject'> => {
  // If __typename is available, use it
  if (obj?.__typename) {
    return obj?.__typename as 'AnotherCustomObject';
  }

  // Use custom resolver from @resolver directive
  return anotherCustomResolver(obj, context);
};
```

For `DefaultResolverObject` with empty resolver:

```typescript
const __resolveType = async (
  obj: ResolversParentTypes['DefaultResolverObject'] & { __typename?: string },
  context: Context,
  info: GraphQLResolveInfo
): Promise<'DefaultResolverObject'> => {
  // TODO: Implement type resolution logic for DefaultResolverObject object
  // You can check specific properties of obj to determine the concrete type
  // Example:
  // if ('id' in obj) return 'DefaultResolverObject';

  // Default fallback - you should implement proper type resolution above
  throw new Error(
    'Unable to resolve type for DefaultResolverObject object. Please implement type resolution logic in this resolver.'
  );
};
```

**Key Features:**

- **Conditional Generation**: Only object types with `@resolver` directive generate `__resolveType` functions
- **Custom Resolver Support**: Supports custom resolver functions with proper parameter passing
- **TypeName Support**: Supports `useTypeName` option for automatic type resolution via `__typename` field
- **Type Safety**: Generated functions have proper TypeScript signatures with correct parent and return types
- **Template Generation**: Empty `@resolver()` directives generate helpful TODO templates
- **Consistent API**: Uses the same directive syntax and patterns as interface and union resolvers

**Default Field Resolution**

The `@default` directive generates a default resolver for fields that returns the field value from the parent object with optional fallback support. This is useful for fields that don't require custom resolution logic.

```graphql
type User {
  id: ID!
  name: String!

  # Basic default resolver with safe optional chaining
  # @default
  email: String

  # Default resolver with fallback value
  # @default("<EMAIL>")
  backupEmail: String

  # Default resolver with method call fallback
  # @default(generateDefaultAvatar())
  avatar: String

  # Direct return mode - returns value directly
  # @default(direct: "ACTIVE")
  status: String

  # Fields that need custom resolvers
  posts: [Post!]!
  friends: [User!]!
}
```

**@default Directive Syntax:**

1. **Basic usage**: `@default` - Returns `obj?.fieldName` with safe optional chaining
2. **With fallback**: `@default(fallback)` - Returns `obj?.fieldName ?? fallback`
3. **Direct return**: `@default(direct: returnValue)` - Returns `returnValue` directly
4. **Legacy syntax**: `@default(true, value)` - Still supported for backward compatibility

**Generated Implementation Examples:**

- `@default` → `return obj?.email;`
- `@default("unknown")` → `return obj?.email ?? "unknown";`
- `@default(getDefaultEmail())` → `return obj?.email ?? getDefaultEmail();`
- `@default(direct: "ACTIVE")` → `return "ACTIVE";`
- `@default(true, "ACTIVE")` → `return "ACTIVE";` (legacy syntax)

**Key Features:**

1. **Safe by default**: Uses optional chaining (`obj?.fieldName`) to prevent null/undefined errors
2. **No TODO statements**: Generated code is production-ready without placeholder comments
3. **Flexible fallbacks**: Supports literal values, variables, or method calls as fallbacks
4. **Direct return mode**: Can return values directly without checking the parent object
5. **Backward compatible**: Existing `@default` usage continues to work
6. **Auto-overwrite**: Files with `@default` directive are automatically regenerated on schema changes

**TypeScript-Only Field Extensions**

The `@field` directive allows you to add hidden fields to TypeScript types without affecting the GraphQL schema. This is useful for extending types with additional fields that only exist in your TypeScript code but not in the GraphQL schema. The directive supports both named and default exports, enabling seamless integration with any TypeScript module system.

```graphql
# @field(secretInfo: SecretInfo "@/types/SecretInfo")
type User {
  id: ID!
  name: String!
  email: String
  # @field(CustomType: "@/types/CustomType")
  customField: String
}
```

This will generate TypeScript types with the additional fields:

```typescript
export type User = {
  id: string;
  name: string;
  email?: string | null | undefined;
  // Added from @field directive:
  secretInfo: SecretInfo | null | undefined;
  // Added from @field directive - overrides the default field type
  customField: CustomType | null | undefined;
};
```

Benefits of using `@field`:

- Keep your GraphQL schema clean while adding TypeScript-only fields
- Add internal fields that clients shouldn't see in the schema
- Avoid schema changes when you only need to extend TypeScript types
- Maintain type safety across your implementation

The `@field` directive is particularly useful for:

1. Adding internal utility fields to types
2. Including server-only properties in shared types
3. Extending generated types with custom behavior without modifying the schema
4. Adding integration with other systems through "virtual fields"

### Advanced @field Directive Usage

The `@field` directive supports several advanced features:

#### Type-Level Directives

Type-level directives add fields to the entire type:

```graphql
# @field(internalId: string, metadata: Metadata "@/types/Metadata")
type Product {
  id: ID!
  name: String!
}
```

#### Field-Level Directives

Field-level directives can override the TypeScript type for a specific field:

```graphql
type Order {
  # @field(DetailedProduct "@/types/products/DetailedProduct")
  product: Product
}
```

This changes the TypeScript type for the `product` field to `DetailedProduct` without affecting the GraphQL schema.

#### Import Paths

The `@field` directive accepts import paths in quotes to automatically generate proper imports:

```graphql
# @field(metadata: Metadata "@/types/metadata")
```

This will generate:

```typescript
import { Metadata } from '@/types/metadata';
```

#### Named Exports

For types that use named exports different from their type name:

```graphql
# @field(config: Config "@/config/types:ConfigInterface")
```

This generates:

```typescript
import { ConfigInterface as Config } from '@/config/types';
```

#### Default Exports

For types that use default exports, wrap the exported name in curly braces:

```graphql
# @field(configManager: ConfigManager "@/config/manager:{ConfigManager}")
```

This generates:

```typescript
import ConfigManager from '@/config/manager';
```

The curly braces around the type name indicate that it should be imported as a default export rather than a named export. This is particularly useful when integrating with libraries or modules that use default exports.

##### Benefits of Default Export Support

The default export support provides several advantages:

1. **Complete Module Coverage** - Integrate with any JavaScript/TypeScript module regardless of its export style
2. **Third-Party Library Integration** - Easily work with libraries that primarily use default exports
3. **Flexible Code Organization** - Use the most appropriate export style for each module without limitations
4. **Migration Support** - Seamlessly migrate between named and default exports as your codebase evolves
5. **Framework Compatibility** - Better compatibility with frameworks that rely on default exports

#### @field Directive Import Formats Diagram

```
┌───────────────────────────────────────────────────────────┐
│ @field Directive - TypeScript Import Format Support       │
├───────────────────┬───────────────────┬───────────────────┤
│ Directive Format  │ Generated Import  │ Import Type       │
├───────────────────┼───────────────────┼───────────────────┤
│ Type "@/path"     │ import { Type }   │ Named Import      │
│                   │ from '@/path';    │                   │
├───────────────────┼───────────────────┼───────────────────┤
│ Type "@/path:     │ import { Export   │ Named Import      │
│ ExportName"       │ as Type } from    │ with Alias        │
│                   │ '@/path';         │                   │
├───────────────────┼───────────────────┼───────────────────┤
│ Type "@/path:     │ import Type       │ Default Import    │
│ {ExportName}"     │ from '@/path';    │                   │
├───────────────────┴───────────────────┴───────────────────┤
│                                                           │
│ Complete Example:                                         │
│                                                           │
│ # @field(                                                 │
│     user: User "@/types/user",                            │
│     config: Config "@/config:ConfigInterface",            │
│     manager: Manager "@/services:{DefaultManager}"        │
│ )                                                         │
│                                                           │
│ Generates:                                                │
│ import { User } from '@/types/user';                      │
│ import { ConfigInterface as Config } from '@/config';     │
│ import Manager from '@/services';                         │
└───────────────────────────────────────────────────────────┘
```

#### Multiple Fields

You can define multiple fields in a single directive:

```graphql
# @field(field1: Type1 "@/path1", field2: Type2 "@/path2")
```

#### Validation

GQL-Generator includes a validation tool for `@field` directives that can be run separately:

```bash
npx gql-generator validate-field-directives
```

This validates:

- Syntax correctness
- Duplicate field definitions
- Import path validity (optional)
- Default export syntax (ensuring curly braces are properly formatted)

Run with `--help` to see all available options.

## TypeScript Decorator System

GQL-Generator introduces a modern TypeScript decorator-based annotation system that complements the existing comment-based directive system. This system allows you to define GraphQL resolver implementations directly in your TypeScript codebase using decorators, providing better type safety, IDE support, and developer experience.

### Decorator Overview

The decorator system scans your TypeScript codebase for decorated functions and automatically generates GraphQL resolvers that delegate to these functions. This approach offers several advantages:

- **Type Safety**: Full TypeScript type checking for your resolver implementations
- **IDE Support**: Better autocomplete, refactoring, and navigation
- **Separation of Concerns**: Keep business logic separate from schema definitions
- **Smart Import Resolution**: Automatic calculation of import paths
- **Multi-Schema Support**: Support for multiple GraphQL schemas with schema identifiers

### Enabling Decorators

To use the decorator system, provide the codebase directory which automatically enables decorator scanning:

```bash
# Enable decorator scanning with codebase directory
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src

# Watch mode with decorators
gql-generator watch \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src
```

### @GQLMethodCall Decorator

The `@GQLMethodCall` decorator is the primary decorator for implementing GraphQL field resolvers. It tells the generator to delegate field resolution to your decorated function.

**Syntax:**
```typescript
@GQLMethodCall({
  type: string,
  field: string,
  call: string,
  schema?: string
})
```

**Parameters:**
- `type`: The GraphQL type name (e.g., "Query", "User", "Post")
- `field`: The field name within the type
- `call`: The function call expression to use in the generated resolver
- `schema`: Optional schema identifier for multi-schema setups

**Basic Examples:**

```typescript
// Query resolver
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => {
  return { id, name: "John Doe", email: "<EMAIL>" };
};

// Field resolver with object context
@GQLMethodCall({ type: "User", field: "posts", call: "getUserPosts(obj.id)" })
export const getUserPosts = (userId: string) => {
  return [{ id: "1", title: "Hello World", content: "..." }];
};

// Mutation resolver
@GQLMethodCall({ type: "Mutation", field: "createUser", call: "createUser(args.input)" })
export const createUser = (input: CreateUserInput) => {
  return { id: generateId(), ...input };
};
```

**Advanced Examples:**

```typescript
// With context parameter
@GQLMethodCall({
  type: "Query",
  field: "currentUser",
  call: "getCurrentUser(context.userId)"
})
export const getCurrentUser = (userId: string) => {
  return userService.findById(userId);
};

// With type casting
@GQLMethodCall({
  type: "Query",
  field: "users",
  call: "getAllUsers() as User[]"
})
export const getAllUsers = () => {
  return userService.findAll();
};

// Async operations
@GQLMethodCall({
  type: "User",
  field: "avatar",
  call: "await getUserAvatar(obj.id)"
})
export const getUserAvatar = async (userId: string) => {
  return await imageService.getAvatar(userId);
};

// Complex expressions
@GQLMethodCall({
  type: "Post",
  field: "author",
  call: "userService.findById(obj.authorId, { include: ['profile'] })"
})
export const getPostAuthor = () => null; // Implementation handled by call expression
```

### @GQLImport Decorator

The `@GQLImport` decorator adds custom import statements to generated resolver files. This is useful when your method calls reference external services or utilities.

**Syntax:**
```typescript
@GQLImport(importStatement: string)
```

**Examples:**

```typescript
// Single import
@GQLImport("import { UserService } from '../services/user-service'")
@GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
export const getUser = () => null;

// Multiple imports
@GQLImport("import { UserService } from '../services/user-service'")
@GQLImport("import { Logger } from '../utils/logger'")
@GQLMethodCall({ type: "Query", field: "users", call: "UserService.findAll()" })
export const getAllUsers = () => null;

// Named and default imports
@GQLImport("import EmailService, { EmailTemplate } from '../services/email'")
@GQLMethodCall({ type: "Mutation", field: "sendEmail", call: "EmailService.send(args.template)" })
export const sendEmail = () => null;

// Relative path imports
@GQLImport("import { validateInput } from './validation'")
@GQLMethodCall({ type: "Mutation", field: "createPost", call: "createPost(validateInput(args.input))" })
export const createPost = () => null;
```

**Smart Import Resolution:**

When you don't provide explicit `@GQLImport` decorators, the system automatically calculates import paths from the generated resolver location to your decorated function:

```typescript
// File: src/resolvers/user-resolvers.ts
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => ({ id, name: "User" });

// Generated resolver automatically includes:
// import { getUser } from '../../src/resolvers/user-resolvers';
```

### @GQLField Decorator

The `@GQLField` decorator adds TypeScript-only fields to types without affecting the GraphQL schema. This is equivalent to the comment-based `@field` directive.

**Syntax:**
```typescript
@GQLField({
  ref: string,
  name: string,
  type: string,
  schema?: string
})
```

**Parameters:**
- `ref`: The GraphQL type to add the field to
- `name`: The field name
- `type`: The TypeScript type for the field
- `schema`: Optional schema identifier

**Examples:**

```typescript
// Add metadata field to User type
@GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
export interface UserMetadata {
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}

// Add computed fields
@GQLField({ ref: "Product", name: "internalId", type: "string" })
export type ProductInternalId = string;

// Add complex nested types
@GQLField({ ref: "User", name: "permissions", type: "UserPermissions[]" })
export interface UserPermissions {
  resource: string;
  actions: string[];
  granted: boolean;
}

// Multi-schema support
@GQLField({
  ref: "User",
  name: "adminData",
  type: "AdminUserData",
  schema: "admin"
})
export interface AdminUserData {
  role: string;
  permissions: string[];
}
```

### @GQLContext Decorator

The `@GQLContext` decorator configures the context type used in resolver functions for specific schemas.

**Syntax:**
```typescript
@GQLContext({
  path: string,
  name: string,
  schema?: string
})
```

**Parameters:**
- `path`: Path to the context type definition
- `name`: Name of the context interface/type
- `schema`: Optional schema identifier (defaults to "default")

**Examples:**

```typescript
// Default schema context
@GQLContext({ path: "./types/AppContext", name: "AppContext" })
export class ContextConfig {}

// Admin schema context
@GQLContext({
  path: "./types/AdminContext",
  name: "AdminContext",
  schema: "admin"
})
export class AdminContextConfig {}

// API-specific context
@GQLContext({
  path: "../shared/ApiContext",
  name: "ApiContext",
  schema: "api"
})
export class ApiContextConfig {}
```

### Decorator Configuration

You can configure decorator behavior through CLI options and configuration files:

**CLI Options:**
```bash
# Specify codebase directory (automatically enables decorator scanning)
--codebase-dir ./src

# Custom file patterns (default: **/*.ts, **/*.tsx)
--decorator-patterns "**/*.resolver.ts,**/*.service.ts"

# Exclude patterns
--decorator-exclude "**/node_modules/**,**/*.test.ts"
```

**Configuration File (gql-generator.config.js):**
```javascript
module.exports = {
  decorators: {
    enabled: true,
    codebaseDir: './src',
    includePatterns: ['**/*.ts', '**/*.tsx'],
    excludePatterns: [
      '**/node_modules/**',
      '**/dist/**',
      '**/*.d.ts',
      '**/__tests__/**',
      '**/*.test.ts',
      '**/*.spec.ts'
    ],
    enableCaching: true,
    overrideComments: true, // Decorators take precedence over comment-based directives
    maxConcurrency: 10
  }
};
```

### Multi-Schema Support

The decorator system supports multiple GraphQL schemas through schema identifiers:

```typescript
// Default schema (no schema parameter needed)
@GQLMethodCall({ type: "Query", field: "publicUser", call: "getPublicUser(args.id)" })
export const getPublicUser = (id: string) => ({ id, name: "Public User" });

// Admin schema
@GQLMethodCall({
  type: "Query",
  field: "adminUser",
  call: "getAdminUser(args.id)",
  schema: "admin"
})
export const getAdminUser = (id: string) => ({ id, name: "Admin User", role: "admin" });

// API schema
@GQLMethodCall({
  type: "Query",
  field: "apiUser",
  call: "getApiUser(args.id)",
  schema: "api"
})
export const getApiUser = (id: string) => ({ id, name: "API User" });
```

Generate for multiple schemas:
```bash
gql-generator generate \
  --schema ./schema/public --output ./output/public \
  --schema ./schema/admin --output ./output/admin \
  --codebase-dir ./src
```

### Precedence Rules

When both decorator and comment-based directives exist for the same field, decorators take precedence:

```graphql
# schema/user.gql
type User {
  id: ID!
  # @methodCall(getUserNameFromComment(obj.id))
  name: String!
}
```

```typescript
// src/resolvers/user.ts
@GQLMethodCall({ type: "User", field: "name", call: "getUserNameFromDecorator(obj.id)" })
export const getUserNameFromDecorator = (id: string) => `Decorator: ${id}`;
```

**Result:** The generated resolver will use `getUserNameFromDecorator` (decorator takes precedence).

### Migration from Comment-Based Directives

Migrating from comment-based directives to decorators is straightforward and can be done incrementally. For a comprehensive guide, see [migration_guide.md](./migration_guide.md).

**Step 1: Enable Decorators**
```bash
# Add codebase directory to your existing commands
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src
```

**Step 2: Create Decorator Files**

**Before (Comment-based):**
```graphql
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  # @import(import { UserService } from '../services/user-service';)
  name: String!
  # @methodCall(UserService.getUserEmail(obj.id))
  email: String!
}

type Query {
  # @methodCall(UserService.findById(args.id))
  user(id: ID!): User
}
```

**After (Decorator-based):**
```typescript
// src/resolvers/user-resolvers.ts
@GQLImport("import { UserService } from '../services/user-service'")
@GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
export const getUser = () => null;

@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)" })
export const getUserName = (id: string) => `User ${id}`;

@GQLMethodCall({ type: "User", field: "email", call: "UserService.getUserEmail(obj.id)" })
export const getUserEmail = () => null;
```

**Step 3: Use Migration Tool (Recommended)**

For automated migration, use the built-in migration command:

```bash
# Basic migration
gql-generator migrate --schema ./schema --codebase ./src

# Multi-schema migration (single command)
gql-generator migrate --schema ./schema/public --schema-id public --schema ./schema/admin --schema-id admin --codebase ./src

# Multi-schema migration (separate commands)
gql-generator migrate --schema ./schema/public --codebase ./src --schema-id public
gql-generator migrate --schema ./schema/admin --codebase ./src --schema-id admin

# Dry run (preview only)
gql-generator migrate --schema ./schema --codebase ./src --dry-run
```

**Step 4: Remove Comment Directives (Optional)**

You can remove comment-based directives once you've migrated to decorators, or keep both systems running in parallel.

**Migration Benefits:**
- **Type Safety**: Full TypeScript checking for your resolver logic
- **Better IDE Support**: Autocomplete, refactoring, and navigation
- **Centralized Logic**: Keep related resolvers together in TypeScript files
- **Testing**: Easier unit testing of individual resolver functions
- **Maintainability**: Better code organization and discoverability

**Migration Strategy:**
1. Start with new features using decorators
2. Gradually migrate existing comment-based directives
3. Use both systems in parallel during transition
4. Remove comment-based directives when migration is complete

## Migration Tool

The GQL-Generator includes a powerful migration tool that automatically converts comment-based directives in GraphQL files to TypeScript decorators. This tool provides a safe, automated way to modernize your codebase.

> 📖 **For a comprehensive migration guide with detailed examples and troubleshooting, see [migration_guide.md](./migration_guide.md)**

### Command Options

The migration tool can be run in several ways:

```bash
# Global command (recommended - install with: npm install -g .)
gql-generator migrate [options]

# Short alias
gqgen migrate [options]

# NPX (no installation required)
npx gql-generator migrate [options]

# Direct execution (for development)
./src/cli.js migrate [options]
```

### Migration Command

```bash
gql-generator migrate [options]
```

**Options:**
- `-s, --schema <path>` - Path to GraphQL schema directory (default: ./schema)
- `-c, --codebase <path>` - Path to TypeScript codebase directory (default: ./src)
- `--schema-id <identifier>` - Schema identifier for multi-schema support
- `--alias-codebase <prefix>` - Alias prefix for import path resolution (e.g., "@app")
- `--dry-run` - Preview migration without making changes
- `--verbose` - Enable verbose logging
- `--backup` - Create backups before migration
- `--include <patterns>` - TypeScript file patterns to include (comma-separated)
- `--exclude <patterns>` - TypeScript file patterns to exclude (comma-separated)

### Migration Process

The migration tool follows a systematic process:

1. **Scans GraphQL files** for comment-based directives (`#@methodCall`, `#@import`, `#@field`)
2. **Analyzes method references** to extract function/method names from directive call expressions
3. **Searches TypeScript files** to locate matching function/method definitions
4. **Adds TypeScript decorators** (`@GQLMethodCall`, `@GQLImport`, `@GQLField`) to the located functions
5. **Removes comment-based directives** from GraphQL files
6. **Creates backups** and provides rollback capabilities

### Migration Examples

**Basic Migration:**
```bash
# Migrate a single schema
gql-generator migrate --schema ./schema --codebase ./src
```

**Multi-Schema Migration:**
```bash
# Migrate multiple schemas with identifiers
gql-generator migrate --schema ./schema/public --codebase ./src --schema-id public
gql-generator migrate --schema ./schema/admin --codebase ./src --schema-id admin
```

**Dry Run (Preview):**
```bash
# Preview migration without making changes
gql-generator migrate --schema ./schema --codebase ./src --dry-run --verbose
```

**Custom File Patterns:**
```bash
# Include only specific TypeScript files
gql-generator migrate \
  --schema ./schema \
  --codebase ./src \
  --include "**/*.resolver.ts,**/*.service.ts" \
  --exclude "**/test/**,**/mock/**"
```

**With Alias Support:**
```bash
# Resolve alias imports during migration
gql-generator migrate \
  --schema ./schema \
  --codebase ./src \
  --alias-codebase "@app"
```

### Alias Import Handling

The migration tool provides robust support for alias imports commonly used in TypeScript projects. This allows you to use path aliases in your GraphQL directives that match your TypeScript configuration.

#### Alias Configuration

Configure aliases using the `--alias-codebase` flag:

```bash
# Configure @app alias to resolve to codebase directory
gql-generator migrate --alias-codebase "@app"
```

#### Alias Resolution Process

The tool automatically resolves alias paths to actual file locations:

```
@services/user-service → ./src/services/user-service.ts
@utils/date-utils      → ./src/utils/date-utils.ts
@types/user-metadata   → ./src/types/user-metadata.ts
```

#### Supported Alias Patterns

**Utility Functions:**
```graphql
# @import(import { getUserName } from '@utils/user-utils')
type User {
  # @methodCall(getUserName(obj.id))
  name: String!
}
```

**Date/Time Functions:**
```graphql
# @import(import { formatDate } from '@utils/date-utils')
type User {
  # @methodCall(formatDate(obj.createdAt))
  createdAt: String!
}
```

**Type Definitions:**
```graphql
# @import(import { UserMetadata } from '@types/user-metadata')
type User {
  # @field(metadata: UserMetadata)
}
```

**Nested Paths:**
```graphql
# @import(import { executeQuery } from '@services/database/connection')
type Query {
  # @methodCall(executeQuery(args.sql))
  executeQuery(sql: String!): QueryResult
}
```

#### Migration with Aliases Example

**Before Migration (GraphQL with aliases):**
```graphql
# @import(import { getUserName } from '@utils/user-utils')
# @import(import { formatDate } from '@utils/date-utils')
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  name: String!
  # @methodCall(formatDate(obj.createdAt))
  createdAt: String!
}
```

**Command:**
```bash
gql-generator migrate --schema ./schema --codebase ./src --alias-codebase "@app" --verbose
```

**After Migration (TypeScript with decorators):**
```typescript
// src/utils/user-utils.ts
@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)", schema: "default" })
export function getUserName(id: string): string {
  return `User ${id}`;
}

// src/utils/date-utils.ts
@GQLMethodCall({ type: "User", field: "createdAt", call: "formatDate(obj.createdAt)", schema: "default" })
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}
```

**After Migration (Clean GraphQL):**
```graphql
type User {
  id: ID!
  name: String!
  createdAt: String!
}
```

### Migration Safety Features

- **Automatic Backups**: Creates timestamped backups before any modifications
- **Dry Run Mode**: Preview changes without modifying files
- **Duplicate Prevention**: Prevents duplicate decorators when running multiple times
- **Validation**: Validates file syntax after modifications
- **Rollback Support**: Restore from backups if needed
- **Progress Reporting**: Detailed logging and progress information
- **Alias Import Support**: Preserves and handles alias imports correctly

### Testing the Migration Tool

The `examples/` folder contains comprehensive test cases for the migration tool:

```bash
# Test basic migration scenarios
gql-generator migrate --schema ./examples/basic/schema --codebase ./examples/basic/codebase --schema-id basic --alias-codebase "@app" --verbose

# Test complex scenarios with multiple classes
gql-generator migrate --schema ./examples/complex/schema --codebase ./examples/complex/codebase --schema-id complex --alias-codebase "@app" --verbose

# Test multi-schema scenarios
gql-generator migrate --schema ./examples/multi-schema/public --codebase ./examples/multi-schema/codebase --schema-id public --alias-codebase "@app" --verbose
```

See [examples/README.md](./examples/README.md) for detailed testing instructions.

### Migration Output

The migration tool provides comprehensive reporting:

```
🚀 Starting GraphQL directive to TypeScript decorator migration...
📄 Found 15 GraphQL files to process
📄 Found 42 TypeScript files to scan
🔍 Found 28 directives to migrate
✅ Successfully resolved 26 method references
🎯 Adding decorators to TypeScript files...
✅ Added 26 decorators to 8 files
🧹 Removing directives from GraphQL files...
✅ Removed 28 directives from 15 files

📊 Migration Summary:
   GraphQL files processed: 15
   TypeScript files modified: 8
   Directives migrated: 26
   Decorators added: 26
   Errors: 0
   Warnings: 2

✅ Migration completed successfully!
```

### Best Practices

**File Organization:**
```
src/
├── resolvers/
│   ├── user-resolvers.ts      # User-related resolvers
│   ├── post-resolvers.ts      # Post-related resolvers
│   └── query-resolvers.ts     # Root query resolvers
├── services/
│   ├── user-service.ts        # Business logic
│   └── post-service.ts
└── types/
    ├── context.ts             # GraphQL context types
    └── inputs.ts              # Input type definitions
```

**Naming Conventions:**
```typescript
// Use descriptive function names
@GQLMethodCall({ type: "Query", field: "user", call: "getUserById(args.id)" })
export const getUserById = (id: string) => { /* ... */ };

// Group related resolvers
@GQLMethodCall({ type: "User", field: "posts", call: "getUserPosts(obj.id)" })
@GQLMethodCall({ type: "User", field: "followers", call: "getUserFollowers(obj.id)" })
export const getUserPosts = (userId: string) => { /* ... */ };
export const getUserFollowers = (userId: string) => { /* ... */ };
```

**Error Handling:**
```typescript
@GQLMethodCall({ type: "Query", field: "user", call: "getUserSafely(args.id)" })
export const getUserSafely = async (id: string) => {
  try {
    return await userService.findById(id);
  } catch (error) {
    console.error('Failed to fetch user:', error);
    throw new Error('User not found');
  }
};
```

## Interface Inheritance

GQL-Generator supports automatic directive inheritance from GraphQL interfaces to implementing types, including **multi-deep interface inheritance** where interfaces can implement other interfaces. When a type implements an interface that has directives defined, the implementing type will automatically inherit those directives as default implementations if it doesn't define its own directives for the same fields.

### Supported Directive Inheritance

- **@methodCall directives**: Inherit method call implementations
- **@import directives**: Inherit import statements
- **@field directives**: Inherit field directive configurations
- **All other directives**: Complete directive inheritance support
- **Multi-deep inheritance**: Support for interface-to-interface inheritance chains
- **Circular dependency detection**: Automatic detection and prevention of circular inheritance

### How Interface Inheritance Works

1. **Interface Definition**: Define directives on interface fields using `@methodCall`, `@import`, or other directives
2. **Interface-to-Interface Inheritance**: Interfaces can implement other interfaces, creating inheritance chains
3. **Type Implementation**: Types that implement interfaces automatically inherit directives through the entire inheritance chain
4. **Precedence Rules**:
   - Direct type directives always take precedence over inherited interface directives
   - Direct interfaces take precedence over inherited interfaces
   - Shallower inheritance paths take precedence over deeper ones
5. **Multiple Interfaces**: When implementing multiple interfaces, the first interface found with directives takes precedence
6. **Mixed Inheritance**: Types can inherit @import directives even without @methodCall directives
7. **Performance Optimization**: Comprehensive caching system for efficient multi-deep inheritance resolution

### Examples

#### Example 1: @methodCall and @import Inheritance

**Interface with Both @methodCall and @import Directives:**

```graphql
interface Node {
  # @import(import NodeService from '../services/node-service')
  # @import(import Logger from '../utils/logger')
  # @methodCall(await NodeService.getById(obj.id))
  id: ID!

  # @import(import DateUtils from '../utils/date-utils')
  # @methodCall(DateUtils.formatDate(obj.createdAt))
  createdAt: String!
}
```

#### Example 2: @import-only Inheritance

**Interface with Only @import Directives:**

```graphql
interface Auditable {
  # @import(import AuditLogger from '../utils/audit-logger')
  # No @methodCall - only imports for manual implementation
  auditLog: String

  # @import(import UserService from '../services/user-service')
  # @import(import PermissionChecker from '../utils/permissions')
  # No @methodCall - implementing types handle the logic
  permissions: [String!]
}
```

**Implementing Types:**

```graphql
type User implements Node, Auditable {
  # No directives - inherits both @import and @methodCall from Node interface
  id: ID!

  # @methodCall(formatUserDate(obj.createdAt))
  createdAt: String!  # Uses its own method call, not inherited

  # No directives - inherits @import from Auditable interface
  auditLog: String

  # No directives - inherits @import from Auditable interface
  permissions: [String!]

  # @methodCall(context.dataSources.users.getName(obj.id))
  name: String!
}

type Post implements Node {
  # No directives - inherits both @import and @methodCall from Node interface
  id: ID!

  # No directives - inherits both @import and @methodCall from Node interface
  createdAt: String!

  title: String!
}
```

**Generated Resolvers:**

For `User.id` (inherited @import and @methodCall from Node):
```typescript
import NodeService from '../services/node-service';
import Logger from '../utils/logger';
import { UserIdArgs, ResolversParentTypes } from './graphql';
import { Context } from '../context';

/**
 * Resolver for User.id
 * Method call inherited from interface: Node
 */
const id = async (
  obj: ResolversParentTypes['User'],
  args: Record<string, never>,
  context: Context
): Promise<ID> => {
  return await NodeService.getById(obj.id);
};
```

For `User.auditLog` (inherited @import-only from Auditable):
```typescript
import AuditLogger from '../utils/audit-logger';
import { UserAuditLogArgs, ResolversParentTypes } from './graphql';
import { Context } from '../context';

/**
 * Resolver for User.auditLog
 * Directives inherited from interface: Auditable
 */
const auditLog = async (
  obj: ResolversParentTypes['User'],
  args: Record<string, never>,
  context: Context
): Promise<String | null> => {
  try {
    // TODO: Implement your resolver logic here
    // AuditLogger is available from inherited @import directive
    return null;
  } catch (error) {
    console.error('Error in auditLog resolver:', error);
    throw error;
  }
};
```

For `User.createdAt` (uses own method call):
```typescript
/**
 * Resolver for User.createdAt
 */
const createdAt = async (
  obj: ResolversParentTypes['User'],
  args: Record<string, never>,
  context: Context
): Promise<String | null> => {
  return formatUserDate(obj.createdAt);
};
```

For `Post.id` and `Post.createdAt` (both inherited from Node):
```typescript
/**
 * Resolver for Post.id
 * Method call inherited from interface: Node
 */
const id = async (
  obj: ResolversParentTypes['Post'],
  args: Record<string, never>,
  context: Context
): Promise<ID> => {
  return await context.dataSources.node.getById(obj.id);
};
```

### Benefits

- **Code Reuse**: Avoid duplicating method calls across implementing types
- **Consistency**: Ensure consistent behavior across types that implement the same interface
- **Maintainability**: Update interface method calls to automatically update all implementing types
- **Flexibility**: Override interface method calls on a per-type basis when needed

### Multi-Deep Interface Inheritance

GQL-Generator now supports **interface-to-interface inheritance**, allowing you to create complex inheritance hierarchies where interfaces can implement other interfaces.

#### Example: Multi-Level Interface Chain

```graphql
# Base interface - Level 0
interface Node {
  # @methodCall(getNodeId(obj.id))
  # @import(import { getNodeId } from '../services/node-service')
  id: ID!
}

# Auditable interface - Level 1 (implements Node)
interface Auditable implements Node {
  # Inherits id from Node
  id: ID!

  # @methodCall(getAuditLog(obj.id))
  # @import(import { getAuditLog } from '../services/audit-service')
  auditLog: String
}

# Timestamped interface - Level 2 (implements Auditable)
interface Timestamped implements Auditable {
  # Inherits id from Node (via Auditable)
  # Inherits auditLog from Auditable
  id: ID!
  auditLog: String

  # @methodCall(getTimestamp(obj.createdAt))
  # @import(import { getTimestamp } from '../services/time-service')
  createdAt: String!
}

# Type implementing deep inheritance chain
type User implements Timestamped {
  # Inherits id from Node (via Timestamped → Auditable → Node)
  # Inherits auditLog from Auditable (via Timestamped → Auditable)
  # Inherits createdAt from Timestamped
  id: ID!
  auditLog: String
  createdAt: String!
  name: String!
}
```

#### Precedence Rules for Multi-Deep Inheritance

1. **Direct Type Directives**: Always override inherited directives
2. **Direct Interface Priority**: Direct interfaces take precedence over inherited interfaces
3. **Interface Order**: First interface in implementation list wins conflicts
4. **Inheritance Depth**: Shallower paths take precedence over deeper ones

#### Example: Multiple Inheritance with Conflicts

```graphql
interface Versioned implements Node {
  # Different method call for id field
  # @methodCall(getVersionedId(obj.id, obj.version))
  id: ID!

  # @methodCall(getVersion(obj.version))
  version: Int!
}

type Document implements Auditable, Versioned {
  # Conflict resolution for id field:
  # - Auditable → Node: getNodeId(obj.id)
  # - Versioned → Node: getVersionedId(obj.id, obj.version)
  # Result: Uses Auditable path (first interface)
  id: ID!
  auditLog: String  # From Auditable
  version: Int!     # From Versioned
}
```

#### Debugging Multi-Deep Inheritance

Use the built-in analysis tools to understand complex inheritance:

```typescript
import { InterfaceInheritanceHandler } from './utils/interface-inheritance';

const handler = new InterfaceInheritanceHandler(schema, schemaMapper);

// Get comprehensive inheritance analysis
const analysis = handler.getInheritanceAnalysis('User');
console.log(`Max inheritance depth: ${analysis.inheritanceDepth}`);
console.log(`All interfaces: ${analysis.allInterfaces.join(', ')}`);

// Visualize inheritance tree
console.log(handler.getInheritanceTree('User'));

// Analyze field conflicts
const conflicts = await handler.analyzeFieldInheritanceConflicts('Document', 'id');
if (conflicts.hasConflicts) {
  console.log(`Conflicts found for ${conflicts.fieldName}:`);
  console.log(conflicts.conflictDetails.join('\n'));
}
```

### Best Practices

1. **Define Common Logic in Interfaces**: Put shared method calls in interfaces rather than repeating them in implementing types
2. **Use Descriptive Interface Names**: Make it clear what behavior the interface provides
3. **Document Interface Contracts**: Clearly document what method calls interfaces provide
4. **Test Interface Inheritance**: Verify that implementing types correctly inherit interface behavior
5. **Keep Inheritance Chains Reasonable**: Avoid excessive nesting (>4 levels) for maintainability
6. **Monitor Performance**: Use caching and analysis tools for complex inheritance scenarios
7. **Handle Circular Dependencies**: The system automatically detects and prevents circular inheritance

## Smart Code Preservation

GQL-Generator employs an intelligent code preservation system that allows you to regenerate code from schema changes without losing your custom implementations. This ensures your business logic remains intact even as your schema evolves.

### How It Works

1. **Detection**: The system automatically detects custom implementations by looking for:

   - Business logic patterns (if/else statements, loops, try/catch blocks)
   - Custom imports beyond standard ones
   - Non-placeholder code replacing TODOs
   - Custom console logging

2. **Preservation Markers**: When custom code is detected, the file is marked with special comments:

   ```typescript
   // THIS FILE IS OBSOLETE BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION
   ```

3. **AST-Based Merging**: For sophisticated preservation, the system uses TypeScript's Abstract Syntax Tree (AST) to:
   - Parse existing files and extract function bodies
   - Preserve custom imports while adding new required imports
   - Maintain custom code between imports and function declarations
   - Update type signatures and boilerplate while keeping implementation details

### Examples

**Before Schema Change**:

```typescript
import { QueryResolvers, ResolversParentTypes } from '../graphql';
import { Context } from '../context';
import { transformUserData } from '../utils/transformers';

const user = async (
  parent: ResolversParentTypes['Query'],
  args: { id: string },
  context: Context
): Promise<User | null> => {
  const { id } = args;
  // Custom implementation
  const userData = await context.dataSources.users.findById(id);
  return transformUserData(userData);
};

export default user;
```

**After Schema Change** (args type updated, custom code preserved):

```typescript
import { QueryResolvers, ResolversParentTypes, QueryUserArgs } from '../graphql';
import { Context } from '../context';
import { transformUserData } from '../utils/transformers';

const user = async (
  parent: ResolversParentTypes['Query'],
  args: QueryUserArgs, // Updated to use the generated args type
  context: Context
): Promise<User | null> => {
  const { id } = args;
  // Custom implementation
  const userData = await context.dataSources.users.findById(id);
  return transformUserData(userData);
};

export default user;
```

### Configuration

The preservation system works automatically without configuration. However, you can control it with:

```bash
# Force overwrite all files, even those with custom implementations
gql-generator generate --force
```

## Test Generation

GQL-Generator generates fully typed test files for your resolvers:

```typescript
// output/__test__/root-query/user.test.ts
import { RootQueryResolvers, ResolversParentTypes } from '../../graphql';
import { Context } from '@gql-generator/context';
import { createMockContext } from '../utils/mock-context';

describe('RootMutation Mutations', () => {
  const mockContext = createMockContext();

  // Create a strongly-typed mock parent object
  const mockParent: ResolversParentTypes['RootMutation'] = {
    createUser: {
      name: 'mock-string',
      email: 'mock-string',
    },
    updateUser: 'mock-string',
    deleteUser: 'mock-string',
  };

  describe('createUser Mutation', () => {
    // Import resolver function
    let resolverFn;

    beforeAll(async () => {
      resolverFn = await import('@create_user/createUser').then(module => module.default);
    });

    // Add teardown placeholder
    afterAll(() => {
      // Teardown code can be added here
    });

    // User test cases can be added here
  });

  describe('updateUser Mutation', () => {
    // Import resolver function
    let resolverFn;

    beforeAll(async () => {
      resolverFn = await import('@update_user/updateUser').then(module => module.default);
    });

    // Add teardown placeholder
    afterAll(() => {
      // Teardown code can be added here
    });

    // User test cases can be added here
  });

  describe('deleteUser Mutation', () => {
    // Import resolver function
    let resolverFn;

    beforeAll(async () => {
      resolverFn = await import('@delete_user/deleteUser').then(module => module.default);
    });

    // Add teardown placeholder
    afterAll(() => {
      // Teardown code can be added here
    });
  });
});
```

### Test Options

GQL-Generator provides several options to control test generation:

#### Basic Test Generation

To enable test generation, use the `--test` flag:

```bash
gql-generator generate --test
```

This will create test stubs for all operations in your schema, with a focus on mutations. By default, tests are generated in a `__test__` directory within your output directory.

#### Customizing Test Output Directory

You can specify a custom output directory for test files using the `--test-output` option:

```bash
gql-generator generate --test --test-output "./tests/unit"
```

#### Test File Organization Style

You can control how test files are organized using the `--test-style` option:

- `grouped` (default): Creates a single test file per parent type, with all operations grouped
- `individual`: Creates a separate test file for each operation

```bash
# Create individual test files for each operation
gql-generator generate --test --test-style individual

# Create grouped test files (default)
gql-generator generate --test --test-style grouped
```

#### Applying Test Aliases

If you've defined `@alias` directives in your schema to customize test mocks, alias paths will be applied by default when generating test files. If you want to disable this behavior, use the `--apply-aliases=false` flag:

```bash
# Disable the application of alias directives in the schema
gql-generator generate --test --apply-aliases=false
```

By default, test files will use the alias paths specified in your schema directives, which is useful for using path aliases configured in your tsconfig.json or redirecting tests to use mock implementations instead of actual resolvers.

## Workflow Integration

GQL-Generator seamlessly integrates into your development workflow:

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant CLI as GQL-Generator CLI
    participant SM as SchemaMapper
    participant CG as CodeGenerator
    participant SBG as SchemaBasedGenerator
    participant TG as TestGenerator
    participant FS as FileSystem

    Dev->>CLI: Execute generate command
    CLI->>SM: Load schema files
    SM->>SM: Analyze schema structure
    SM->>FS: Write merged schema
    CLI->>CG: Generate base type definitions
    CG->>FS: Write graphql.ts
    CLI->>SBG: Generate implementations
    SBG->>SBG: Process types by category
    SBG->>FS: Write resolvers, interfaces, etc.
    SBG->>FS: Preserve custom implementations
    CLI->>TG: Generate test stubs
    TG->>FS: Write mock context
    TG->>FS: Write mutation tests
    FS-->>Dev: Final generated code

    note over Dev,FS: Schema Updated
    Dev->>CLI: Re-execute generate command
    CLI->>SM: Load updated schema files
    SM->>SM: Analyze schema changes
    CLI->>CG: Update type definitions
    CG->>FS: Write updated graphql.ts
    CLI->>SBG: Update implementations
    SBG->>FS: Preserve custom code in existing files
    SBG->>FS: Generate new files for added types
    CLI->>TG: Update test stubs
    FS-->>Dev: Updated code with preserved implementations
```

## Performance Considerations

GQL-Generator is designed for performance even with large schemas:

- **Incremental Generation**: Only regenerates files affected by schema changes
- **Parallel Processing**: Utilizes worker threads for large schemas
- **Efficient Schema Parsing**: Uses optimized parsing algorithms
- **File System Caching**: Minimizes unnecessary disk I/O
- **Memory Optimization**: Manages memory usage for large schemas
- **Comment Directive Optimization**: 15x faster comment directive parsing with intelligent caching

### Comment Directive Performance Optimization

The optimized comment directive parser provides dramatic performance improvements:

- **15x faster** parsing on average
- **93% reduction** in processing time
- **100% cache hit rate** for repeated operations
- **Automatic cache invalidation** when schema files change

#### Performance Testing Commands

```bash
# Quick performance benchmark
gql-generator quick-benchmark --test-size small

# Comprehensive benchmark with detailed metrics
gql-generator benchmark --test-size medium --iterations 10

# Generate large test schema for performance testing
gql-generator generate-test-schema --size large --output-dir ./test-schema

# Real-time performance monitoring
gql-generator performance-monitor --interval 30000
```

#### Disable Optimization (if needed)

```bash
# Disable comment directive optimization
export ENABLE_DIRECTIVE_OPTIMIZATION=false

# Disable Phase 1 performance optimizations
export ENABLE_TEMPLATE_PRECOMPILATION=false
export ENABLE_BATCH_IO=false
export ENABLE_PARALLEL_PROCESSING=false

# Disable Phase 2 performance optimizations
export ENABLE_OPTIMIZED_CODEGEN=false
export ENABLE_CODEGEN_CACHING=false
export ENABLE_INCREMENTAL_PROCESSING=false

gql-generator generate --schema ./schema --output ./output
```

#### Phase 1 Performance Optimizations

The gql-generator includes Phase 1 performance optimizations that provide a 3-5x speedup:

- **Template Precompilation**: Pre-compiles all Handlebars templates at startup (20-30% faster)
- **Batch File I/O**: Queues and batches file writes for reduced I/O overhead (50-70% faster)
- **Parallel Type Processing**: Processes independent type categories in parallel (2-4x faster on multi-core systems)

#### Phase 2 Performance Optimizations

Phase 2 optimizations provide an additional 2-3x speedup (6-15x total) through pipeline optimization:

- **Direct GraphQL CodeGen Integration**: Replaces child process execution with direct API calls (10-20% faster)
- **Schema Hash-Based Change Detection**: Skips CodeGen for unchanged schemas using file fingerprinting
- **Streaming Code Generation**: Processes large schemas without memory constraints using streaming
- **Enhanced Dependency Tracking**: Smart cache invalidation based on dependency graphs
- **Incremental Type Processing**: Skips processing of unchanged type definitions (50-70% reduction)
- **Memory Pressure Monitoring**: Adaptive processing based on system resource availability

These optimizations are enabled by default and include automatic fallback mechanisms for compatibility.

For extremely large schemas, consider:

```bash
# Skip the codegen step if you've already generated types
gql-generator generate --skip-codegen

# Generate only specific parts of your schema
gql-generator generate --schema "./schema/user/**/*.gql"
```

See [Comment Directive Performance Optimization](docs/comment-directive-performance-optimization.md) for detailed information.

## Compatibility

GQL-Generator is compatible with:

- **GraphQL Versions**: 15.x, 16.x
- **TypeScript Versions**: 4.x, 5.x
- **Node.js Versions**: 16.x and above
- **Frameworks**: Apollo Server, Express GraphQL, GraphQL Yoga, etc.
- **Testing Frameworks**: Jest, Mocha, Vitest

## Troubleshooting

### Common Issues

#### Installation Issues

If you encounter problems during installation:

**Permission Issues on macOS/Linux**

```
Error: EACCES: permission denied
```

**Solution**: Use sudo for global installation:

```bash
sudo npm run setup
# OR
sudo npm install -g .
```

**Missing Dependencies on Windows**

```
Error: Cannot find module
```

**Solution**: Ensure you have the proper development tools installed:

```bash
npm install --global --production windows-build-tools
npm run setup
```

**Path Issues After Installation**

If commands aren't found after installation:

**Solution**: Ensure the npm global bin directory is in your PATH. You can also try:

```bash
# Find where npm installs global packages
npm config get prefix
# Add the bin directory to your PATH (e.g., for bash)
echo 'export PATH="$PATH:$(npm config get prefix)/bin"' >> ~/.bashrc
source ~/.bashrc
```

#### Schema Loading Errors

```
Error: No schema files found matching './schema/**/*.gql'
```

**Solution**: Check that your schema path is correct and files use the `.gql` extension.

#### Type Generation Errors

```
Error: Cannot find module '@graphql-codegen/typescript'
```

**Solution**: Ensure all dependencies are installed properly:

```bash
npm install --save-dev @graphql-codegen/typescript @graphql-codegen/typescript-resolvers
```

#### Resolver Generation Errors

```
Error: Cannot determine parent type for field 'posts'
```

**Solution**: Make sure your schema is valid and all referenced types are defined.

#### Custom Code Preservation Issues

If your custom code is not being preserved:

1. Ensure your code has sufficient complexity to be detected
2. Add a comment `// CUSTOM IMPLEMENTATION` above your code
3. Run without `--force` flag

## Comparison with Similar Tools

| Feature                  | GQL-Generator | GraphQL Code Generator     | Apollo Codegen | TypeGraphQL |
| ------------------------ | ------------- | -------------------------- | -------------- | ----------- |
| Approach                 | Schema-First  | Schema-First or Code-First | Schema-First   | Code-First  |
| Smart Code Preservation  | ✅            | ❌                         | ❌             | ❌          |
| Comment-Based Directives | ✅            | ❌                         | ❌             | ❌          |
| Test Generation          | ✅            | Plugins Available          | ❌             | ❌          |
| Interface/Union Support  | ✅            | ✅                         | ✅             | ✅          |
| AST-Based Preservation   | ✅            | ❌                         | ❌             | ❌          |
| Schema Organization      | Mirrored      | Customizable               | Limited        | N/A         |

## Roadmap

The GQL-Generator roadmap includes:

- **Plugin System**: Support for custom code generators
- **GraphQL Extensions**: Support for GraphQL subscriptions and custom scalars
- **React/Vue Client Generation**: Generate typed client hooks
- **Integration Enhancements**: Better integration with CI/CD pipelines
- **IDE Extensions**: VSCode and other IDE integrations
- **Visualization Tools**: Schema visualization and dependency graphs
- **Performance Optimizations**: Further improvements for large schemas

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch: `git checkout -b my-new-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin my-new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Using Hidden Fields with @field Directive

The `@field` directive allows you to define hidden fields that are only accessible in TypeScript code but not exposed in the GraphQL schema. This is useful for:

- Passing internal IDs or metadata between resolvers
- Including implementation details that should not be visible to API consumers
- Enabling resolver context that follows the GraphQL execution model

### Example Usage

1. Add a hidden field to a type using the `@field` directive in a comment:

```graphql
type AdminMutation {
  # @field(internalId: String! "path/to/optional/import")
  createUser(userInfo: UserInput): User
  updateUser(id: ID!, userInfo: UserInput): User
}
```

2. Access the hidden field in the resolver:

```typescript
// In the resolver for AdminMutation.createUser
const createUser = async (
  obj: ResolversParentTypes['AdminMutation'],
  args: AdminMutationCreateUserArgs,
  context: Context
): Promise<User> => {
  try {
    // Access the hidden field from the parent object
    console.log(`Using internal ID: ${obj.internalId}`);

    // Use the internal ID in your logic
    return db.createUser(obj.internalId, args.userInfo, context);
  } catch (error) {
    console.error(`Error in createUser resolver:`, error);
    throw error;
  }
};
```

### Field Format

The `@field` directive uses the following format:

```
# @field(fieldName: TypeName "path/to/optional/import")
```

Parameters:

- `fieldName`: The name of the field in TypeScript (immediately followed by colon)
- `TypeName`: The GraphQL type of the field (supports standard GraphQL types, including non-null `!` suffix)
- `"path"`: Optional path string in quotes for importing custom types

### Implementation Notes

- The hidden field is added to both the TypeScript type and the resolver parent type
- This allows it to be accessible in all resolvers for that type
- Hidden fields are not included in the GraphQL schema or exposed to clients
