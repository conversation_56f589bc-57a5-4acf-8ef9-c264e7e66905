# TypeScript Decorator System Usage Guide

This guide covers the modern TypeScript decorator-based annotation system in GQL-Generator, which provides a powerful alternative to comment-based directives for implementing GraphQL resolvers.

## Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Enabling Decorators](#enabling-decorators)
- [Core Decorators](#core-decorators)
- [Advanced Usage](#advanced-usage)
- [Import Alias Support](#import-alias-support)
- [Integration with Generated Files](#integration-with-generated-files)
  - [Schema Type Enums Integration](#schema-type-enums-integration)
  - [Function Maps Integration](#function-maps-integration)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

The TypeScript decorator system allows you to define GraphQL resolver implementations directly in your TypeScript codebase using decorators. This approach is an alternative to comment-based directives and offers:

- **Type Safety**: Full TypeScript type checking for resolver implementations
- **IDE Support**: Better autocomplete, refactoring, and navigation
- **Separation of Concerns**: Keep business logic separate from schema definitions
- **Smart Import Resolution**: Automatic calculation of import paths with alias support
- **Import Aliases**: Clean, maintainable import paths using configurable aliases
- **Multi-Schema Support**: Support for multiple GraphQL schemas
- **Developer Choice**: Use alongside or instead of comment-based directives based on your preference

## Quick Start

### 1. Enable Decorators

```bash
# Generate with decorators (automatically enabled when --codebase-dir is provided)
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src

# Generate with decorators and import aliases
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --alias-codebase "@src"

# Watch mode with decorators
gql-generator watch \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src

# Watch mode with decorators and import aliases
gql-generator watch \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --alias-codebase "@src"
```

### 2. Create Your First Resolver

```typescript
// src/resolvers/user-resolvers.ts
import { @GQLMethodCall } from 'gql-generator';

@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => {
  return { id, name: "John Doe", email: "<EMAIL>" };
};
```

### 3. Generate Code

The generator will automatically create resolvers that delegate to your decorated functions:

```typescript
// output/root-query/user.ts (generated)
import { getUser } from '../../src/resolvers/user-resolvers';

const user = async (obj, args, context) => {
  return getUser(args.id);
};

export default user;
```

**With import aliases:**

```typescript
// output/root-query/user.ts (generated with --alias-codebase "@src")
import { getUser } from '@src/resolvers/user-resolvers';

const user = async (obj, args, context) => {
  return getUser(args.id);
};

export default user;
```

## Enabling Decorators

### CLI Configuration

```bash
# Required flags
--codebase-dir ./src        # Directory to scan for decorators (automatically enables decorator scanning)

# Optional flags
--decorator-patterns "**/*.ts,**/*.tsx"  # File patterns to scan
--decorator-exclude "**/node_modules/**" # Patterns to exclude
--schema-id <identifier>     # Schema identifier for multi-schema support (generates namespaced type enums)
--alias-codebase <alias>     # Import alias for codebase directory (e.g., "@src")
```

### Configuration File

Create `gql-generator.config.js`:

```javascript
module.exports = {
  decorators: {
    enabled: true,
    codebaseDir: './src',
    includePatterns: ['**/*.ts', '**/*.tsx'],
    excludePatterns: [
      '**/node_modules/**',
      '**/dist/**',
      '**/*.d.ts',
      '**/__tests__/**'
    ],
    enableCaching: true,
    overrideComments: false, // Set to true if you want decorators to take precedence over comment-based directives
    maxConcurrency: 10
  }
};
```

## Core Decorators

### @GQLMethodCall

Implements GraphQL field resolvers by delegating to your decorated function.

**Syntax:**
```typescript
@GQLMethodCall({
  type: string,      // GraphQL type name
  field: string,     // Field name
  call: string,      // Function call expression
  schema?: string    // Optional schema identifier
})
```

**Examples:**

```typescript
// Query resolver
@GQLMethodCall({ type: "Query", field: "users", call: "getAllUsers()" })
export const getAllUsers = () => [
  { id: "1", name: "Alice" },
  { id: "2", name: "Bob" }
];

// Field resolver with object context
@GQLMethodCall({ type: "User", field: "posts", call: "getUserPosts(obj.id)" })
export const getUserPosts = (userId: string) => [
  { id: "1", title: "Hello World", authorId: userId }
];

// Mutation resolver
@GQLMethodCall({ 
  type: "Mutation", 
  field: "createUser", 
  call: "createUser(args.input)" 
})
export const createUser = (input: CreateUserInput) => {
  return { id: generateId(), ...input };
};

// With context parameter
@GQLMethodCall({
  type: "Query",
  field: "currentUser",
  call: "getCurrentUser(context.userId)"
})
export const getCurrentUser = (userId: string) => {
  return userService.findById(userId);
};

// Async operations with type casting
@GQLMethodCall({
  type: "User",
  field: "avatar",
  call: "await getUserAvatar(obj.id) as string"
})
export const getUserAvatar = async (userId: string): Promise<string> => {
  return await imageService.getAvatar(userId);
};
```

### @GQLImport

Adds custom import statements to generated resolver files.

**Syntax:**
```typescript
@GQLImport(importStatement: string)
```

**Examples:**

```typescript
// Single import
@GQLImport("import { UserService } from '../services/user-service'")
@GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
export const getUser = () => null;

// Multiple imports
@GQLImport("import { UserService } from '../services/user-service'")
@GQLImport("import { Logger } from '../utils/logger'")
@GQLMethodCall({ type: "Query", field: "users", call: "UserService.findAll()" })
export const getAllUsers = () => null;

// Named and default imports
@GQLImport("import EmailService, { EmailTemplate } from '../services/email'")
@GQLMethodCall({ 
  type: "Mutation", 
  field: "sendEmail", 
  call: "EmailService.send(args.template)" 
})
export const sendEmail = () => null;
```

### @GQLField

Adds TypeScript-only fields to types without affecting the GraphQL schema.

**Syntax:**
```typescript
@GQLField({
  ref: string,       // GraphQL type to add field to
  name: string,      // Field name
  type: string,      // TypeScript type
  schema?: string    // Optional schema identifier
})
```

**Examples:**

```typescript
// Add metadata field to User type
@GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
export interface UserMetadata {
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
}

// Add computed fields
@GQLField({ ref: "Product", name: "internalId", type: "string" })
export type ProductInternalId = string;

// Multi-schema support
@GQLField({
  ref: "User",
  name: "adminData",
  type: "AdminUserData",
  schema: "admin"
})
export interface AdminUserData {
  role: string;
  permissions: string[];
}
```

### @GQLContext

Configures the context type used in resolver functions.

**Syntax:**
```typescript
@GQLContext({
  path: string,      // Path to context type definition
  name: string,      // Context interface/type name
  schema?: string    // Optional schema identifier
})
```

**Examples:**

```typescript
// Default schema context
@GQLContext({ path: "./types/AppContext", name: "AppContext" })
export class ContextConfig {}

// Admin schema context
@GQLContext({
  path: "./types/AdminContext",
  name: "AdminContext",
  schema: "admin"
})
export class AdminContextConfig {}
```

## Advanced Usage

### Multi-Schema Support

The decorator system fully supports multiple GraphQL schemas with schema identifiers. Each decorator can specify which schema it belongs to using the optional `schema` parameter.

#### Schema Organization

```
project/
├── schema/
│   ├── public/           # Public API schema
│   │   ├── user.gql
│   │   └── post.gql
│   ├── admin/            # Admin API schema
│   │   ├── user.gql
│   │   └── analytics.gql
│   └── internal/         # Internal API schema
│       └── system.gql
├── src/
│   └── resolvers/
│       ├── public-resolvers.ts
│       ├── admin-resolvers.ts
│       └── internal-resolvers.ts
└── output/
    ├── public/           # Generated public API code
    ├── admin/            # Generated admin API code
    └── internal/         # Generated internal API code
```

#### Multi-Schema Decorators

```typescript
// src/resolvers/public-resolvers.ts
// Default schema (no schema parameter needed)
@GQLMethodCall({ type: "Query", field: "publicUser", call: "getPublicUser(args.id)" })
export const getPublicUser = (id: string) => ({ id, name: "Public User" });

@GQLField({ ref: "User", name: "publicMetadata", type: "PublicMetadata" })
export interface PublicMetadata {
  joinDate: Date;
  postCount: number;
}

// src/resolvers/admin-resolvers.ts
// Admin schema
@GQLMethodCall({
  type: "Query",
  field: "adminUser",
  call: "getAdminUser(args.id)",
  schema: "admin"
})
export const getAdminUser = (id: string) => ({
  id,
  name: "Admin User",
  role: "admin",
  permissions: ["read", "write", "delete"]
});

@GQLField({
  ref: "User",
  name: "adminMetadata",
  type: "AdminMetadata",
  schema: "admin"
})
export interface AdminMetadata {
  role: string;
  permissions: string[];
  lastAdminAction: Date;
}

@GQLContext({
  path: "./types/AdminContext",
  name: "AdminContext",
  schema: "admin"
})
export class AdminContextConfig {}

// src/resolvers/internal-resolvers.ts
// Internal schema
@GQLMethodCall({
  type: "Query",
  field: "systemHealth",
  call: "getSystemHealth()",
  schema: "internal"
})
export const getSystemHealth = () => ({
  status: "healthy",
  uptime: process.uptime(),
  memory: process.memoryUsage()
});

@GQLImport("import { SystemMonitor } from '../services/system-monitor'")
@GQLMethodCall({
  type: "Query",
  field: "metrics",
  call: "SystemMonitor.getMetrics()",
  schema: "internal"
})
export const getMetrics = () => null;
```

#### Generate Multiple Schemas

```bash
# Generate all schemas with decorators
gql-generator generate \
  --schema ./schema/public --output ./output/public \
  --schema ./schema/admin --output ./output/admin \
  --schema ./schema/internal --output ./output/internal \
  --codebase-dir ./src

# Generate with schema identifiers (creates namespaced type enums)
gql-generator generate \
  --schema ./schema/public --output ./output/public --schema-id public \
  --schema ./schema/admin --output ./output/admin --schema-id admin \
  --schema ./schema/internal --output ./output/internal --schema-id internal \
  --codebase-dir ./src

# Watch mode for multiple schemas
gql-generator watch \
  --schema ./schema/public --output ./output/public \
  --schema ./schema/admin --output ./output/admin \
  --schema ./schema/internal --output ./output/internal \
  --codebase-dir ./src \
  --bidirectional

# Watch mode with schema identifiers
gql-generator watch \
  --schema ./schema/public --output ./output/public --schema-id public \
  --schema ./schema/admin --output ./output/admin --schema-id admin \
  --schema ./schema/internal --output ./output/internal --schema-id internal \
  --codebase-dir ./src \
  --enable-decorators \
  --bidirectional
```

#### Schema-Specific Context Types

```typescript
// src/types/PublicContext.ts
export interface PublicContext {
  userId?: string;
  userAgent: string;
  ip: string;
}

// src/types/AdminContext.ts
export interface AdminContext {
  adminId: string;
  permissions: string[];
  auditLog: AuditLogger;
}

// src/types/InternalContext.ts
export interface InternalContext {
  serviceId: string;
  requestId: string;
  metrics: MetricsCollector;
}

// Configure contexts for each schema
@GQLContext({ path: "./types/PublicContext", name: "PublicContext" })
export class PublicContextConfig {}

@GQLContext({
  path: "./types/AdminContext",
  name: "AdminContext",
  schema: "admin"
})
export class AdminContextConfig {}

@GQLContext({
  path: "./types/InternalContext",
  name: "InternalContext",
  schema: "internal"
})
export class InternalContextConfig {}
```

#### Benefits of Multi-Schema Decorators

- **Schema Isolation**: Keep different API concerns separate
- **Type Safety**: Each schema gets its own TypeScript types
- **Context Separation**: Different context types for different APIs
- **Independent Development**: Teams can work on different schemas independently
- **Selective Generation**: Generate only the schemas you need

### Smart Import Resolution

When you don't provide explicit `@GQLImport` decorators, the system automatically calculates import paths:

```typescript
// File: src/resolvers/user-resolvers.ts
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => ({ id, name: "User" });

// Generated resolver automatically includes:
// import { getUser } from '../../src/resolvers/user-resolvers';
```

### Complex Call Expressions

```typescript
// Property chains
@GQLMethodCall({
  type: "Post",
  field: "author",
  call: "context.dataSources.users.findById(obj.authorId)"
})

// Method chaining with parameters
@GQLMethodCall({
  type: "User",
  field: "posts",
  call: "userService.findById(obj.id, { include: ['posts'] }).posts"
})

// Conditional expressions
@GQLMethodCall({
  type: "User",
  field: "displayName",
  call: "obj.nickname || obj.firstName + ' ' + obj.lastName"
})
```

## Import Alias Support

The TypeScript decorator system supports import aliases to replace long relative import paths with clean, maintainable aliases. This feature significantly improves code readability and makes imports more resilient to directory structure changes.

### Overview

Instead of generating verbose relative imports like:
```typescript
import { getUser } from '../../../../src/resolvers/user-resolvers';
```

You can use clean alias-based imports:
```typescript
import { getUser } from '@src/resolvers/user-resolvers';
```

### Enabling Import Aliases

#### CLI Configuration

```bash
# Basic alias usage
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --alias-codebase "@src"

# Multiple schemas with aliases
gql-generator generate \
  --schema ./schema/public --output ./output/public \
  --schema ./schema/admin --output ./output/admin \
  --codebase-dir ./src \
  --alias-codebase "@app"

# Watch mode with aliases
gql-generator watch \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --alias-codebase "@src"
```

#### Alias Format Requirements

Aliases must follow these rules:
- Start with `@` symbol
- Contain only alphanumeric characters, underscores, hyphens, and forward slashes
- No path traversal attempts (`..`, `./`)
- No double slashes (`//`) or trailing slashes

**Valid aliases:**
- `@src`
- `@user_codebase`
- `@my-app/core`
- `@components`

**Invalid aliases:**
- `src` (missing @)
- `@src/../other` (path traversal)
- `@src//utils` (double slash)
- `@src/` (trailing slash)

### Default Alias Generation

When no explicit alias is provided, the system automatically generates a default alias based on the codebase directory name:

```bash
# Codebase directory: ./src
# Generated alias: @src

# Codebase directory: ./user_codebase
# Generated alias: @user_codebase

# Codebase directory: ./my-app/core
# Generated alias: @my_app_core
```

### Alias Behavior

#### Files Within Codebase

For files within the configured codebase directory, the generator uses alias-based imports:

```typescript
// File: src/resolvers/user-resolvers.ts
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })
export const getUser = (id: string) => ({ id, name: "User" });

// Generated resolver (with --alias-codebase "@src"):
import { getUser } from '@src/resolvers/user-resolvers';
```

#### Files Outside Codebase

For files outside the codebase directory, the generator falls back to relative imports:

```typescript
// File: external/services/auth.ts (outside codebase)
@GQLMethodCall({ type: "Query", field: "auth", call: "authenticate(args.token)" })
export const authenticate = (token: string) => ({ valid: true });

// Generated resolver:
import { authenticate } from '../../external/services/auth';
```

### Complex Directory Structures

Aliases work seamlessly with nested directory structures:

```
src/
├── modules/
│   ├── user/
│   │   ├── resolvers/
│   │   │   ├── user-queries.ts
│   │   │   └── user-mutations.ts
│   │   └── services/
│   │       └── user-service.ts
│   └── post/
│       ├── resolvers/
│       │   └── post-resolvers.ts
│       └── services/
│           └── post-service.ts
└── shared/
    ├── utils/
    │   └── validation.ts
    └── types/
        └── common.ts
```

```typescript
// File: src/modules/user/resolvers/user-queries.ts
@GQLMethodCall({ type: "Query", field: "user", call: "getUserById(args.id)" })
export const getUserById = (id: string) => ({ id, name: "User" });

// Generated resolver (with --alias-codebase "@app"):
import { getUserById } from '@app/modules/user/resolvers/user-queries';

// File: src/modules/post/resolvers/post-resolvers.ts
@GQLImport("import { validateInput } from '@app/shared/utils/validation'")
@GQLMethodCall({ type: "Mutation", field: "createPost", call: "createPost(args.input)" })
export const createPost = (input: any) => ({ id: "1", ...input });

// Generated resolver includes both alias imports:
import { validateInput } from '@app/shared/utils/validation';
import { createPost } from '@app/modules/post/resolvers/post-resolvers';
```

### TypeScript Configuration

To ensure your IDE and TypeScript compiler recognize the aliases, update your `tsconfig.json`:

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@src/*": ["src/*"],
      "@app/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/utils/*"]
    }
  }
}
```

### Multi-Schema Alias Usage

Aliases work seamlessly with multi-schema setups:

```bash
# Generate multiple schemas with shared alias
gql-generator generate \
  --schema ./schema/public --output ./output/public --schema-id public \
  --schema ./schema/admin --output ./output/admin --schema-id admin \
  --codebase-dir ./src \
  --enable-decorators \
  --alias-codebase "@shared"
```

```typescript
// src/resolvers/shared-resolvers.ts
@GQLMethodCall({
  type: "Query",
  field: "publicUser",
  call: "getPublicUser(args.id)"
})
export const getPublicUser = (id: string) => ({ id, name: "Public User" });

@GQLMethodCall({
  type: "Query",
  field: "adminUser",
  call: "getAdminUser(args.id)",
  schema: "admin"
})
export const getAdminUser = (id: string) => ({ id, name: "Admin User" });

// Both generated resolvers use the same alias:
// output/public/query/publicUser.ts:
import { getPublicUser } from '@shared/resolvers/shared-resolvers';

// output/admin/query/adminUser.ts:
import { getAdminUser } from '@shared/resolvers/shared-resolvers';
```

### Benefits of Import Aliases

1. **Improved Readability**: Clean, descriptive import paths
2. **Maintainability**: Easier to refactor and reorganize code
3. **Consistency**: Uniform import style across generated files
4. **IDE Support**: Better autocomplete and navigation with proper tsconfig.json setup
5. **Reduced Errors**: Less prone to import path mistakes when moving files
6. **Cross-Platform**: Works consistently across different operating systems

### Troubleshooting Aliases

**Alias not being used:**
- Verify the file is within the codebase directory
- Check alias format follows validation rules
- Ensure `--enable-decorators` and `--codebase-dir` are specified

**TypeScript errors with aliases:**
- Update `tsconfig.json` with corresponding path mappings
- Ensure alias paths match your project structure
- Check that the alias doesn't conflict with existing npm packages

**Generated imports still relative:**
- Verify files are within the specified codebase directory
- Check that the alias configuration is being passed correctly
- Use `--debug` flag to see alias resolution details

## Integration with Generated Files

The TypeScript decorator system seamlessly integrates with GQL-Generator's auto-generated files, providing enhanced type safety and developer experience through Schema Type Enums and Function Maps.

### Schema Type Enums Integration

When using decorators with the `--schema-id` flag, GQL-Generator creates namespaced type enums that work perfectly with your decorator-based resolvers.

#### Using Schema Type Enums with Decorators

```typescript
// Generated SchemaTypes.ts (with --schema-id admin)
export const AdminSchemaTypes = {
  RootQuery: 'RootQuery',
  RootMutation: 'RootMutation',
  AdminUser: 'AdminUser',
  AdminRole: 'AdminRole',
} as const;

// Your decorator-based resolvers
import { AdminSchemaTypes } from './SchemaTypes';

@GQLMethodCall({
  type: AdminSchemaTypes.RootQuery,
  field: "adminUser",
  call: "getAdminUser(args.id)",
  schema: "admin"
})
export const getAdminUser = (id: string) => ({
  id,
  name: "Admin User",
  role: "admin"
});

@GQLMethodCall({
  type: AdminSchemaTypes.AdminUser,
  field: "permissions",
  call: "getUserPermissions(obj.id)",
  schema: "admin"
})
export const getUserPermissions = (userId: string) => ["read", "write"];
```

#### Benefits of Schema Type Enums with Decorators

- **Type Safety**: Compile-time checking of GraphQL type names in decorators
- **Refactoring**: Automatic updates when schema types are renamed
- **IDE Support**: Full autocomplete for type names in decorator parameters
- **Multi-Schema**: Clean separation of type constants for different schemas

#### Multi-Schema Type Enum Usage

```typescript
// Public schema decorators
import { PublicSchemaTypes } from './output/public/SchemaTypes';

@GQLMethodCall({
  type: PublicSchemaTypes.RootQuery,
  field: "publicUser",
  call: "getPublicUser(args.id)"
})
export const getPublicUser = (id: string) => ({ id, name: "Public User" });

// Admin schema decorators
import { AdminSchemaTypes } from './output/admin/SchemaTypes';

@GQLMethodCall({
  type: AdminSchemaTypes.RootQuery,
  field: "adminUser",
  call: "getAdminUser(args.id)",
  schema: "admin"
})
export const getAdminUser = (id: string) => ({ id, name: "Admin User" });
```

## Migration from Comment-Based Directives

The GQL-Generator provides an automated migration tool to convert legacy comment-based directives to modern TypeScript decorators. This tool ensures a smooth transition while maintaining all existing functionality.

### Migration Command

```bash
# Basic migration
gql-generator migrate --schema ./schema --codebase ./src

# Multi-schema migration (single command)
gql-generator migrate --schema ./schema/public --schema-id public --schema ./schema/admin --schema-id admin --codebase ./src

# Multi-schema migration (separate commands)
gql-generator migrate --schema ./schema/public --codebase ./src --schema-id public
gql-generator migrate --schema ./schema/admin --codebase ./src --schema-id admin

# Preview migration (dry run)
gql-generator migrate --schema ./schema --codebase ./src --dry-run --verbose
```

### Migration Process Example

**Before Migration (GraphQL with comment directives):**

```graphql
# schema/user.gql
# @import(import { UserService } from '@services/user-service')
type User {
  id: ID!
  # @methodCall(UserService.getName(obj.id))
  name: String!
  # @methodCall(UserService.getEmail(obj.id))
  email: String!
  # @field(metadata: UserMetadata)
}

type Query {
  # @methodCall(UserService.findById(args.id))
  user(id: ID!): User
}
```

**After Migration (TypeScript with decorators):**

```typescript
// src/resolvers/user-resolvers.ts
@GQLImport("import { UserService } from '@services/user-service'")
@GQLMethodCall({ type: "Query", field: "user", call: "UserService.findById(args.id)" })
export const getUser = () => null;

@GQLMethodCall({ type: "User", field: "name", call: "UserService.getName(obj.id)" })
export const getUserName = () => null;

@GQLMethodCall({ type: "User", field: "email", call: "UserService.getEmail(obj.id)" })
export const getUserEmail = () => null;

@GQLField({ ref: "User", name: "metadata", type: "UserMetadata" })
export interface UserMetadata {
  createdAt: string;
  updatedAt: string;
}
```

**GraphQL file after migration (directives removed):**

```graphql
# schema/user.gql
type User {
  id: ID!
  name: String!
  email: String!
}

type Query {
  user(id: ID!): User
}
```

### Migration Features

- **Automatic Method Resolution**: Intelligently finds TypeScript methods referenced in GraphQL directives
- **Multi-Schema Support**: Handles complex multi-schema environments with proper schema tagging
- **Backup Creation**: Automatically creates backups before making any changes
- **Dry Run Mode**: Preview all changes before applying them
- **Duplicate Prevention**: Prevents duplicate decorators when running migration multiple times
- **Progress Reporting**: Detailed logging and progress information
- **Error Recovery**: Rollback capabilities if migration encounters issues
- **Alias Import Preservation**: Maintains alias imports from comment directives

### Migration Best Practices

1. **Start with Dry Run**: Always preview changes first
   ```bash
   gql-generator migrate --schema ./schema --codebase ./src --dry-run --verbose
   ```

2. **Backup Your Code**: While the tool creates automatic backups, ensure you have version control
   ```bash
   git add . && git commit -m "Pre-migration backup"
   ```

3. **Migrate Incrementally**: For large codebases, consider migrating schema by schema
   ```bash
   # Migrate one schema at a time
   gql-generator migrate --schema ./schema/users --codebase ./src --schema-id users
   gql-generator migrate --schema ./schema/posts --codebase ./src --schema-id posts
   ```

4. **Preserve Alias Imports**: Use alias configuration to maintain clean import paths
   ```bash
   # Migrate with alias support
   gql-generator migrate --schema ./schema --codebase ./src --alias-codebase "@src"
   ```

4. **Test After Migration**: Run your tests to ensure everything works correctly
   ```bash
   npm test
   gql-generator generate --schema ./schema --output ./output --codebase-dir ./src
   ```

5. **Clean Up**: Remove old comment-based directives once you've verified the migration
   ```bash
   # The migration tool automatically removes directives, but verify manually
   grep -r "@methodCall\|@import\|@field" ./schema/
   ```

### Alias Import Handling

The migration tool intelligently handles alias imports in comment-based directives:

**Alias Import Preservation:**
```graphql
# Before migration
# @import(import { UserService } from '@services/user-service')
type User {
  # @methodCall(UserService.getName(obj.id))
  name: String!
}
```

```typescript
// After migration (with --alias-codebase "@src")
@GQLImport("import { UserService } from '@services/user-service'")
@GQLMethodCall({ type: "User", field: "name", call: "UserService.getName(obj.id)" })
export const getUserName = () => null;
```

**Relative Import Preservation:**
```graphql
# Before migration
# @import(import { UserHelper } from '../utils/user-helper')
type User {
  # @methodCall(UserHelper.formatName(obj.name))
  displayName: String!
}
```

```typescript
// After migration
@GQLImport("import { UserHelper } from '../utils/user-helper'")
@GQLMethodCall({ type: "User", field: "displayName", call: "UserHelper.formatName(obj.name)" })
export const getUserDisplayName = () => null;
```

The migration tool:
- **Preserves existing alias imports** from comment directives
- **Maintains relative import paths** when no alias is configured
- **Respects alias configuration** provided via `--alias-codebase` option
- **Handles mixed import styles** within the same codebase

### Function Maps Integration

Function Maps provide comprehensive documentation and runtime references for all your decorator-based resolvers, making it easy to explore and understand your GraphQL schema structure.

#### Exploring Decorator-Based Methods

```typescript
// Generated function map includes decorator-based methods
import { UserFunctionMap, UserMethods } from './output/function-maps';

// See all methods for User type (including decorator-based ones)
console.log('User methods:', UserMethods);
// Output: {
//   posts: 'getUserPosts(obj.id)',           // from @GQLMethodCall
//   profile: 'getUserProfile(obj.id)',       // from @GQLMethodCall
//   avatar: 'await getAvatarUrl(obj.id)'     // inherited from interface
// }

// Get detailed information about each method
console.log('User function map:', UserFunctionMap);
// Shows type, source (decorator/directive/inherited), call information
```

#### Function Map Structure with Decorators

```typescript
// Generated function map for User type
export const UserFunctionMap = {
  posts: {
    type: 'method',
    source: 'decorator',        // Indicates this came from @GQLMethodCall
    call: 'getUserPosts(obj.id)'
  },
  profile: {
    type: 'method',
    source: 'decorator',        // From @GQLMethodCall decorator
    call: 'getUserProfile(obj.id)'
  },
  metadata: {
    type: 'field',
    source: 'decorator',        // From @GQLField decorator
    fieldType: 'UserMetadata'
  },
  avatar: {
    type: 'method',
    source: 'inherited',        // Inherited from interface
    call: 'await getAvatarUrl(obj.id)'
  }
};
```

#### Using Function Maps for Development

```typescript
// Development tooling with decorator integration
import { UserFunctionMap, PostFunctionMap } from './output/function-maps';

// Find all decorator-based methods
function getDecoratorMethods(functionMap: any) {
  return Object.entries(functionMap)
    .filter(([_, info]: [string, any]) => info.source === 'decorator')
    .map(([name, info]: [string, any]) => ({
      name,
      call: info.call,
      type: info.type
    }));
}

const userDecoratorMethods = getDecoratorMethods(UserFunctionMap);
console.log('User decorator methods:', userDecoratorMethods);

// Generate documentation for decorator-based resolvers
function generateDecoratorDocs(typeName: string, functionMap: any) {
  const decoratorMethods = getDecoratorMethods(functionMap);

  return {
    typeName,
    decoratorMethodCount: decoratorMethods.length,
    methods: decoratorMethods,
    hasDecorators: decoratorMethods.length > 0
  };
}
```

#### Integration Benefits

- **Complete Overview**: Function maps show both decorator and directive-based methods
- **Source Tracking**: Clear indication of which methods come from decorators vs directives
- **Development Tools**: Foundation for building tools that work with decorator-based resolvers
- **Runtime Introspection**: Programmatically explore your decorator-based schema structure
- **Testing Support**: Generate tests based on decorator-defined methods

## Best Practices

### 1. Organize Decorator Files

```
src/
├── resolvers/
│   ├── user-resolvers.ts      # User-related resolvers
│   ├── post-resolvers.ts      # Post-related resolvers
│   └── auth-resolvers.ts      # Authentication resolvers
├── services/
│   ├── user-service.ts        # Business logic
│   └── post-service.ts
└── types/
    ├── context.ts             # Context definitions
    └── inputs.ts              # Input types
```

### 2. Keep Call Expressions Simple

```typescript
// Good: Simple, clear call
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)" })

// Avoid: Complex logic in call expression
@GQLMethodCall({ 
  type: "Query", 
  field: "user", 
  call: "args.id ? getUser(args.id) : getDefaultUser()" 
})
```

### 3. Use Meaningful Function Names

```typescript
// Good: Descriptive names
@GQLMethodCall({ type: "User", field: "posts", call: "getUserPosts(obj.id)" })
export const getUserPosts = (userId: string) => { /* ... */ };

// Avoid: Generic names
@GQLMethodCall({ type: "User", field: "posts", call: "getData(obj.id)" })
export const getData = (id: string) => { /* ... */ };
```

### 4. Group Related Decorators

```typescript
// Group imports and method calls together
@GQLImport("import { UserService } from '../services/user-service'")
@GQLImport("import { PostService } from '../services/post-service'")
@GQLMethodCall({ type: "User", field: "posts", call: "PostService.getByUserId(obj.id)" })
export const getUserPosts = () => null;
```

## Troubleshooting

### Common Issues

**1. Decorators not detected**
- Ensure `--codebase-dir` is provided (automatically enables decorators)
- Check `--codebase-dir` points to correct directory
- Verify file patterns include your TypeScript files

**2. Import paths incorrect**
- Check relative paths between generated files and your code
- Use `@GQLImport` for complex import scenarios
- Verify codebase directory structure

**3. Type errors in generated code**
- Ensure decorated functions have correct signatures
- Check TypeScript configuration
- Verify context types are properly defined

**4. Conflicts with comment-based directives**
- Set `overrideComments: true` if you want decorators to take precedence
- Check for syntax errors in decorator definitions
- Verify schema and field names match exactly

**5. Schema Type Enums not generated**
- Ensure you're using the latest version of gql-generator
- Check that `SchemaTypes.ts` file is being generated in output directory
- Verify schema files are being processed correctly

**6. Function Maps missing decorator methods**
- Ensure decorators are properly detected (see issue #1)
- Check that `function-maps/` directory is being generated
- Verify decorator syntax matches expected format

**7. Multi-schema issues with schema-id**
- Ensure `--schema-id` flag is used consistently across generate/watch commands
- Check that schema identifiers are valid TypeScript identifiers
- Verify each schema-output pair has a unique schema-id

**8. Generated files not reflecting decorator changes**
- Use `--force` flag to regenerate all files
- Check that watch mode is monitoring your TypeScript files
- Verify codebase-dir includes the files with decorators

**9. Import aliases not working**
- Ensure `--alias-codebase` flag is provided with valid format
- Check that files are within the specified codebase directory
- Verify alias format follows validation rules (starts with @, no path traversal)
- Update `tsconfig.json` with corresponding path mappings

**10. TypeScript errors with alias imports**
- Add path mappings to `tsconfig.json` for IDE support
- Ensure alias doesn't conflict with existing npm package names
- Check that alias paths match your actual project structure
- Verify the alias is being applied consistently across all generated files

### Debug Mode

Use debug mode for troubleshooting:

```bash
# Basic debug mode
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --enable-decorators \
  --debug

# Debug mode with schema identifiers
gql-generator generate \
  --schema ./schema/public --output ./output/public --schema-id public \
  --schema ./schema/admin --output ./output/admin --schema-id admin \
  --codebase-dir ./src \
  --enable-decorators \
  --debug

# Debug mode with import aliases
gql-generator generate \
  --schema ./schema \
  --output ./output \
  --codebase-dir ./src \
  --alias-codebase "@src" \
  --debug
```

This provides detailed information about:
- Decorator scanning process
- Found decorators and their configurations
- Import path calculations and alias resolution
- Generated code structure
- Schema Type Enums generation
- Function Maps creation
- Multi-schema processing with schema identifiers
- Import alias configuration and usage

---

For more information, see the main [README.md](./README.md) or visit the [project repository](https://github.com/your-org/gql-generator).
