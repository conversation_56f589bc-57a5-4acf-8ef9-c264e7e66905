// Test resolvers to validate the fixes

// @GQLMethodCall decorator for User.imageURL field
export const getUserImageURL = (obj: any) => {
  return `https://example.com/images/${obj.id}.jpg`;
};

// @GQLMethodCall decorator for User.name field  
export const getUserName = (obj: any) => {
  return `User ${obj.id}`;
};

// @GQLMethodCall decorator for Query.user field
export const getUser = (args: any) => {
  return {
    id: args.id,
    name: `User ${args.id}`,
    imageURL: `https://example.com/images/${args.id}.jpg`,
    ssoClient: 'GOOGLE'
  };
};

// @GQLMethodCall decorator for Query.users field
export const getUsers = () => {
  return [
    { id: '1', name: 'User 1', imageURL: 'https://example.com/images/1.jpg', ssoClient: 'GOOGLE' },
    { id: '2', name: 'User 2', imageURL: 'https://example.com/images/2.jpg', ssoClient: 'FACEBOOK' }
  ];
};
