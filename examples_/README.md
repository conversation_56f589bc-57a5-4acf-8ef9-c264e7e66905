# Migration Test Setup

This directory contains a **minimalist** test setup for validating the migration functionality from comment-based directives to TypeScript decorators. The setup includes multi-schema support with essential files only.

## Directory Structure

```
examples/
├── schema/                          # GraphQL schema files with comment-based directives
│   ├── public/                      # Public schema (external API)
│   │   ├── schema.gql              # Main schema with PublicContext
│   │   ├── root_query.gql          # Root query with methodCall directives
│   │   ├── root_mutation.gql       # Root mutation with methodCall directives
│   │   ├── types/
│   │   │   ├── user.gql            # User type with field and methodCall directives
│   │   │   └── interface.gql       # Interface definitions
│   │   ├── admin/
│   │   │   └── admin.gql           # Admin type with directives (nested folder testing)
│   │   └── queries/
│   │       └── search.gql          # Search type with directives (nested folder testing)
│   └── private/                     # Private schema (internal API)
│       ├── schema.gql              # Main schema with PrivateContext
│       ├── root_query.gql          # Root query with methodCall directives
│       ├── root_mutation.gql       # Root mutation with methodCall directives
│       ├── types/
│       │   ├── user.gql            # User type with methodCall directives
│       │   └── interface.gql       # Interface definitions
│       ├── admin/
│       │   └── system.gql          # System type with directives (nested folder testing)
│       └── queries/
│           └── analytics.gql       # Analytics type with directives (nested folder testing)
└── user_codebase/                  # TypeScript codebase (migration target)
    └── services/                   # Standalone functions and services
        ├── user-functions.ts       # Standalone functions for user operations
        ├── admin-functions.ts      # Standalone functions for admin operations
        ├── search-functions.ts     # Standalone functions for search operations
        ├── system-functions.ts     # Standalone functions for system operations
        ├── analytics-functions.ts  # Standalone functions for analytics operations
        └── user-service.ts         # Existing service with decorators (for mixed testing)
```

## Directive Coverage

### Comment-Based Directives in GraphQL Files

The test setup includes **26 directive instances** across all GraphQL files (minimalist but with nested folder testing):

#### Import Directives (`# @import`)
- Standalone function imports from various modules
- Proper import path aliasing (@services/*-functions)

#### Method Call Directives (`# @methodCall`)
- Query resolvers: `getUser(args.id)`
- Mutation resolvers: `createUser(args.input)`, `createUser(args.userInfo)`
- Field resolvers: `getUserName(obj.id)`, `getAdminName(obj.id)`, `getSearchTitle(obj.id)`, `getPageViews(obj.id)`
- No-args functions: `getUserCount()`

#### Field Directives (`# @field`)
- Type extensions: `workflow: WorkflowStatus`, `links: [String]`, `role: AdminRole`, `score: Float`, `status: SystemStatus`, `data: AnalyticsData`

### TypeScript Standalone Functions

All methodCall directives have corresponding standalone functions:

- **user-functions.ts**: getUser, createUser, getUserName
- **admin-functions.ts**: getAdminName
- **search-functions.ts**: getSearchTitle
- **system-functions.ts**: getUserCount
- **analytics-functions.ts**: getPageViews

## Multi-Schema Testing

### Schema Isolation
- **Public Schema**: Uses `PublicContext`, primarily references `PublicService`
- **Private Schema**: Uses `PrivateContext`, references `AdminService` and `UserService`
- **Shared Services**: `AdminService` is used by both schemas for testing shared functionality

### Migration Scenarios
1. **Single Schema Migration**: Test migration of public or private schema independently
2. **Multi-Schema Migration**: Test simultaneous migration of both schemas
3. **Schema Identifier Support**: Verify schema-specific decorator generation

## Usage Examples

### Basic Migration Testing
```bash
# Test public schema migration
gql-generator migrate --schema examples/schema/public --codebase examples/user_codebase --schema-id public

# Test private schema migration  
gql-generator migrate --schema examples/schema/private --codebase examples/user_codebase --schema-id private
```

### Multi-Schema Migration Testing
```bash
# Test both schemas simultaneously
gql-generator migrate --schema examples/schema/public --codebase examples/user_codebase --schema-id public
gql-generator migrate --schema examples/schema/private --codebase examples/user_codebase --schema-id private
```

### Expected Migration Results

After migration, TypeScript files should contain decorators like:

```typescript
// examples/user_codebase/services/user-functions.ts
@GQLMethodCall({ type: "Query", field: "user", call: "getUser(args.id)", schema: "public" })
export function getUser(id: string) {
  // Function definition for migration testing
  return { id, name: "Test User", email: "<EMAIL>" };
}
```

## Testing Checklist

- [ ] All 26 directive instances are properly migrated
- [ ] No duplicate decorators when running migration multiple times
- [ ] Schema identifiers are correctly applied
- [ ] Import statements are preserved and converted
- [ ] Multi-schema scenarios work without conflicts
- [ ] Standalone functions are properly decorated

## Notes

- **Minimalist approach**: Only essential files and directives for testing
- All function implementations are placeholder implementations for testing purposes
- The setup covers all major directive types with minimal complexity
- Uses standalone functions instead of service classes for cleaner migration testing
- Multi-schema support with public/private schemas for comprehensive testing
