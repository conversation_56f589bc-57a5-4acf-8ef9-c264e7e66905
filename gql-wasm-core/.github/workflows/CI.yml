name: CI
env:
  DEBUG: napi:*
  APP_NAME: gql-wasm-core
  MACOSX_DEPLOYMENT_TARGET: '10.13'
permissions:
  contents: write
  id-token: write
'on':
  push:
    branches:
      - main
    tags-ignore:
      - '**'
    paths-ignore:
      - '**/*.md'
      - LICENSE
      - '**/*.gitignore'
      - .editorconfig
      - docs/**
  pull_request: null
jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        settings:
          - host: macos-latest
            target: x86_64-apple-darwin
            build: yarn build --target x86_64-apple-darwin
          - host: windows-latest
            build: yarn build --target x86_64-pc-windows-msvc
            target: x86_64-pc-windows-msvc
          - host: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            docker: ghcr.io/napi-rs/napi-rs/nodejs-rust:lts-debian
            build: yarn build --target x86_64-unknown-linux-gnu
          - host: macos-latest
            target: aarch64-apple-darwin
            build: yarn build --target aarch64-apple-darwin
    name: stable - ${{ matrix.settings.target }} - node@20
    runs-on: ${{ matrix.settings.host }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        if: ${{ !matrix.settings.docker }}
        with:
          node-version: 20
          cache: yarn
      - name: Install
        uses: dtolnay/rust-toolchain@stable
        if: ${{ !matrix.settings.docker }}
        with:
          toolchain: stable
          targets: ${{ matrix.settings.target }}
      - name: Cache cargo
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            .cargo-cache
            target/
          key: ${{ matrix.settings.target }}-cargo-${{ matrix.settings.host }}
      - uses: goto-bus-stop/setup-zig@v2
        if: ${{ matrix.settings.target == 'armv7-unknown-linux-gnueabihf' || matrix.settings.target == 'armv7-unknown-linux-musleabihf' }}
        with:
          version: 0.13.0
      - name: Setup toolchain
        run: ${{ matrix.settings.setup }}
        if: ${{ matrix.settings.setup }}
        shell: bash
      - name: Setup node x86
        if: matrix.settings.target == 'i686-pc-windows-msvc'
        run: yarn config set supportedArchitectures.cpu "ia32"
        shell: bash
      - name: Install dependencies
        run: yarn install
      - name: Setup node x86
        uses: actions/setup-node@v4
        if: matrix.settings.target == 'i686-pc-windows-msvc'
        with:
          node-version: 20
          cache: yarn
          architecture: x86
      - name: Build in docker
        uses: addnab/docker-run-action@v3
        if: ${{ matrix.settings.docker }}
        with:
          image: ${{ matrix.settings.docker }}
          options: '--user 0:0 -v ${{ github.workspace }}/.cargo-cache/git/db:/usr/local/cargo/git/db -v ${{ github.workspace }}/.cargo/registry/cache:/usr/local/cargo/registry/cache -v ${{ github.workspace }}/.cargo/registry/index:/usr/local/cargo/registry/index -v ${{ github.workspace }}:/build -w /build'
          run: ${{ matrix.settings.build }}
      - name: Build
        run: ${{ matrix.settings.build }}
        if: ${{ !matrix.settings.docker }}
        shell: bash
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: bindings-${{ matrix.settings.target }}
          path: ${{ env.APP_NAME }}.*.node
          if-no-files-found: error
  test-macOS-windows-binding:
    name: Test bindings on ${{ matrix.settings.target }} - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        settings:
          - host: macos-latest
            target: x86_64-apple-darwin
          - host: windows-latest
            target: x86_64-pc-windows-msvc
        node:
          - '18'
          - '20'
    runs-on: ${{ matrix.settings.host }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: yarn
          architecture: x64
      - name: Install dependencies
        run: yarn install
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-${{ matrix.settings.target }}
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Test bindings
        run: yarn test
  test-linux-x64-gnu-binding:
    name: Test bindings on Linux-x64-gnu - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'
          - '20'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: yarn
      - name: Install dependencies
        run: yarn install
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-x86_64-unknown-linux-gnu
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Test bindings
        run: docker run --rm -v $(pwd):/build -w /build node:${{ matrix.node }}-slim yarn test
  publish:
    name: Publish
    runs-on: ubuntu-latest
    needs:
      - test-macOS-windows-binding
      - test-linux-x64-gnu-binding
    steps:
      - uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: yarn
      - name: Install dependencies
        run: yarn install
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      - name: Move artifacts
        run: yarn artifacts
      - name: List packages
        run: ls -R ./npm
        shell: bash
      - name: Publish
        run: |
          npm config set provenance true
          if git log -1 --pretty=%B | grep "^[0-9]\+\.[0-9]\+\.[0-9]\+$";
          then
            echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
            npm publish --access public
          elif git log -1 --pretty=%B | grep "^[0-9]\+\.[0-9]\+\.[0-9]\+";
          then
            echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
            npm publish --tag next --access public
          else
            echo "Not a release, skipping publish"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
