[package]
edition = "2021"
name = "gql-wasm-core"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Default enable napi4 feature, see https://nodejs.org/api/n-api.html#node-api-version-matrix
napi = { version = "2.12.2", default-features = false, features = ["napi4"] }
napi-derive = "2.12.2"
sha2 = "0.10"

[build-dependencies]
napi-build = "2.0.1"

[profile.release]
lto = true
strip = "symbols"
