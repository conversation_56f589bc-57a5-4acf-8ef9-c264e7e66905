{"name": "gql-wasm-core", "version": "0.0.0", "main": "index.js", "types": "index.d.ts", "napi": {"name": "gql-wasm-core", "triples": {"additional": ["aarch64-apple-darwin", "x86_64-apple-darwin", "x86_64-unknown-linux-gnu", "aarch64-unknown-linux-gnu", "x86_64-pc-windows-msvc", "aarch64-pc-windows-msvc"]}}, "license": "MIT", "devDependencies": {"@napi-rs/cli": "^2.18.4", "ava": "^6.0.1"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "build:all": "npm run build:macos && npm run build:linux && npm run build:windows", "build:macos": "napi build --platform --release --target aarch64-apple-darwin --target x86_64-apple-darwin", "build:linux": "napi build --platform --release --target x86_64-unknown-linux-gnu --target aarch64-unknown-linux-gnu", "build:windows": "napi build --platform --release --target x86_64-pc-windows-msvc", "prepublishOnly": "napi prepublish -t npm", "test": "ava", "universal": "napi universal", "version": "napi version"}}