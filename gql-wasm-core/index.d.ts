/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

/**
 * Phase 3: WASM Module for Performance-Critical Operations
 * High-performance implementations of schema parsing, template compilation,
 * and dependency graph calculations
 */
export declare function sum(a: number, b: number): number
/** Fast file hash calculation using SHA-256 */
export declare function calculateFileHash(content: string): string
/** High-performance schema parsing and validation */
export interface SchemaParseResult {
  types: Array<string>
  directives: Array<string>
  fields: Array<string>
  lineCount: number
}
export declare function parseSchemaFast(schemaContent: string): SchemaParseResult
/** Fast template compilation with caching */
export declare function compileTemplateFast(templateContent: string, templateName: string): string
/** High-performance dependency graph calculations */
export interface DependencyGraphResult {
  sortedOrder: Array<string>
  hasCycles: boolean
  totalNodes: number
  totalEdges: number
  maxDepth: number
}
export declare function calculateDependencyGraph(nodes: Array<string>, edges: Array<string>): DependencyGraphResult
