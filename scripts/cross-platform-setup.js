#!/usr/bin/env node

/**
 * Cross-platform setup script for gql-generator
 * This script ensures proper installation on both Windows and Unix-like systems (macOS, Linux)
 * Steps performed:
 * 1. Clean any previous builds
 * 2. Install dependencies
 * 3. Build the project
 * 4. Set proper permissions for executable files
 * 5. Install the package globally
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { platform } = require('os');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

// Check if we're on Windows
const isWindows = platform() === 'win32';
console.log(`${colors.bright}${colors.cyan}Running setup on ${isWindows ? 'Windows' : 'Unix-like (macOS/Linux)'} platform${colors.reset}\n`);

// Log with step numbers
let stepCount = 0;
function logStep(message) {
  stepCount++;
  console.log(`${colors.bright}${colors.green}Step ${stepCount}:${colors.reset} ${message}`);
}

// Log errors
function logError(message) {
  console.error(`${colors.bright}${colors.red}Error:${colors.reset} ${message}`);
}

// Main setup function
async function setup() {
  try {
    // Determine the project root directory
    const projectRoot = path.resolve(__dirname, '..');
    process.chdir(projectRoot);
    
    // Step 1: Clean previous builds
    logStep('Cleaning previous builds');
    try {
      const distDir = path.join(projectRoot, 'dist');
      if (fs.existsSync(distDir)) {
        console.log('Removing existing dist directory');
        fs.rmSync(distDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.log(`${colors.yellow}Warning: Could not clean dist directory: ${error.message}${colors.reset}`);
      // Continue despite error
    }
    
    // Step 2: Install dependencies
    logStep('Installing dependencies');
    try {
      console.log('Running npm install...');
      execSync('npm install', { stdio: 'inherit' });
    } catch (error) {
      logError(`Failed to install dependencies: ${error.message}`);
      throw error;
    }
    
    // Step 3: Build the project
    logStep('Building the project');
    try {
      console.log('Running npm run build...');
      execSync('npm run build', { stdio: 'inherit' });
    } catch (error) {
      logError(`Failed to build the project: ${error.message}`);
      throw error;
    }
    
    // Step 4: Set up executable permissions (handled by the postbuild script)
    logStep('Verifying executable permissions');
    console.log('The postbuild script should have set proper permissions');
    
    // Additional verification for executable files
    const executableFiles = [
      path.join(projectRoot, 'dist/src/index.js'),
      path.join(projectRoot, 'src/cli.js')
    ];
    
    for (const file of executableFiles) {
      if (fs.existsSync(file)) {
        console.log(`Verified existence of: ${path.basename(file)}`);
        
        // On Unix systems, double-check permissions
        if (!isWindows) {
          try {
            // Ensure script has executable permissions
            fs.chmodSync(file, 0o755);
            console.log(`Ensured executable permissions for: ${path.basename(file)}`);
          } catch (error) {
            console.log(`${colors.yellow}Warning: Could not set permissions for ${path.basename(file)}: ${error.message}${colors.reset}`);
            // Continue despite error
          }
        }
      } else {
        console.log(`${colors.yellow}Warning: Expected file not found: ${file}${colors.reset}`);
      }
    }
    
    // Step 5: Install the package globally
    logStep('Installing package globally');
    try {
      console.log('Running npm install -g ...');
      execSync('npm install -g .', { stdio: 'inherit' });
    } catch (error) {
      // On some systems, global install may require elevated permissions
      if (!isWindows) {
        logError(`Failed to install globally. Try running with sudo: sudo npm install -g .`);
      } else {
        logError(`Failed to install globally. Try running as administrator.`);
      }
      throw error;
    }
    
    // Success message
    console.log(`\n${colors.bright}${colors.green}Installation complete!${colors.reset}`);
    console.log(`\nYou can now use the following commands:`);
    console.log(`- ${colors.cyan}gql-generator${colors.reset}: Main command`);
    console.log(`- ${colors.cyan}gqgen${colors.reset}: Shorthand alias\n`);
    
    // Verify installation
    try {
      console.log('Verifying installation...');
      const output = execSync('gql-generator --version', { encoding: 'utf8' });
      console.log(`Installed version: ${output.trim()}`);
    } catch (error) {
      console.log(`${colors.yellow}Warning: Could not verify installation. You may need to restart your terminal.${colors.reset}`);
    }
    
  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the setup
setup(); 