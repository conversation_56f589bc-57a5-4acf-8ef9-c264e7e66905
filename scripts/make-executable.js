#!/usr/bin/env node

/**
 * Cross-platform script to make CLI scripts executable
 * This is a replacement for the Unix-specific "chmod +x" command
 * On Windows, this script ensures proper line endings and shebang
 * On Unix-based systems (Linux, macOS), it makes the files executable
 */

const fs = require('fs');
const path = require('path');
const { platform } = require('os');

// List of files to make executable
const executableFiles = [
  path.resolve(__dirname, '../dist/src/index.js'),
  path.resolve(__dirname, '../src/cli.js')
];

// Check if we're on Windows
const isWindows = platform() === 'win32';
console.log(`Running on ${isWindows ? 'Windows' : 'Unix-like'} platform`);

// Process each file
let hasErrors = false;

for (const targetFile of executableFiles) {
  console.log(`\nProcessing: ${targetFile}`);
  
  // Check if file exists
  if (!fs.existsSync(targetFile)) {
    console.warn(`⚠️ Target file does not exist: ${targetFile}`);
    // Don't fail the build for this, just warn
    continue;
  }

  try {
    // Ensure the shebang line is present and correct
    let content = fs.readFileSync(targetFile, 'utf8');
    let fileModified = false;
    
    // Normalize line endings to LF (Unix style) for scripts
    if (content.includes('\r\n')) {
      content = content.replace(/\r\n/g, '\n');
      fileModified = true;
      console.log('- Converted CRLF to LF line endings');
    }
    
    // Check if the file already has a shebang line
    if (!content.startsWith('#!/usr/bin/env node')) {
      // Prepend the shebang line if it's missing
      content = '#!/usr/bin/env node\n' + content;
      fileModified = true;
      console.log('- Added shebang line to script');
    }
    
    // Write changes if needed
    if (fileModified) {
      fs.writeFileSync(targetFile, content);
    }
    
    // Only change permissions on Unix-like systems
    if (!isWindows) {
      try {
        // Get current file mode
        const stats = fs.statSync(targetFile);
        const currentMode = stats.mode;
        
        // Add executable permissions (equivalent to chmod +x)
        // This adds executable permission for user, group, and others
        const executableMode = currentMode | 0o111;
        
        // Set the new mode
        fs.chmodSync(targetFile, executableMode);
        console.log('- Set executable permissions');
      } catch (error) {
        console.error(`⚠️ Error setting executable permissions: ${error.message}`);
        // Don't fail on permission errors
      }
    } else {
      console.log('- Windows platform: skipping permission changes');
    }
    
    console.log(`✅ Successfully processed: ${path.basename(targetFile)}`);
  } catch (error) {
    console.error(`❌ Error processing ${path.basename(targetFile)}: ${error.message}`);
    hasErrors = true;
  }
}

// Ensure the bin scripts in package.json work properly
try {
  console.log('\nEnsuring CLI binaries are executable...');
  
  // Get the package.json path
  const packageJsonPath = path.resolve(__dirname, '../package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    // Read the package.json file
    const packageJson = require(packageJsonPath);
    
    // Check if bin field exists
    if (packageJson.bin) {
      // Get all bin scripts
      const binScripts = Object.values(packageJson.bin);
      
      // Process each bin script
      for (const binScript of binScripts) {
        // Resolve full path
        const scriptPath = path.resolve(__dirname, '..', binScript);
        
        if (fs.existsSync(scriptPath)) {
          console.log(`Processing bin script: ${binScript}`);
          
          // Ensure the file is readable
          try {
            fs.accessSync(scriptPath, fs.constants.R_OK);
            
            // Only set executable on Unix systems
            if (!isWindows) {
              try {
                // Set executable permissions
                const stats = fs.statSync(scriptPath);
                const currentMode = stats.mode;
                const executableMode = currentMode | 0o111;
                fs.chmodSync(scriptPath, executableMode);
                console.log(`- Set executable permissions for ${binScript}`);
              } catch (error) {
                console.warn(`⚠️ Could not set permissions for ${binScript}: ${error.message}`);
              }
            }
            
            // Ensure proper shebang line
            let content = fs.readFileSync(scriptPath, 'utf8');
            
            if (!content.startsWith('#!/usr/bin/env node')) {
              content = '#!/usr/bin/env node\n' + content;
              fs.writeFileSync(scriptPath, content);
              console.log(`- Added shebang line to ${binScript}`);
            }
            
            // Fix line endings
            if (content.includes('\r\n')) {
              content = content.replace(/\r\n/g, '\n');
              fs.writeFileSync(scriptPath, content);
              console.log(`- Fixed line endings in ${binScript}`);
            }
            
            console.log(`✅ Successfully processed bin script: ${binScript}`);
          } catch (error) {
            console.warn(`⚠️ Issue with bin script ${binScript}: ${error.message}`);
          }
        } else {
          console.warn(`⚠️ Bin script does not exist: ${scriptPath}`);
        }
      }
    }
  }
} catch (error) {
  console.error(`⚠️ Error processing bin scripts: ${error.message}`);
}

console.log('\nScript execution completed.');

// Exit with appropriate code
process.exit(hasErrors ? 1 : 0); 