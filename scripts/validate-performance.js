#!/usr/bin/env node

/**
 * Performance validation script for comment directive optimizations
 * 
 * This script validates that the performance optimizations are working correctly
 * and provides comprehensive performance metrics.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 GQL-Generator Comment Directive Performance Validation\n');

// Test configurations
const testConfigs = [
  { name: 'Small', size: 'small', expectedSpeedup: 5 },
  { name: 'Medium', size: 'medium', expectedSpeedup: 10 },
  { name: 'Large', size: 'large', expectedSpeedup: 15 }
];

const results = [];

async function runValidation() {
  console.log('📊 Running performance validation tests...\n');

  for (const config of testConfigs) {
    console.log(`🔍 Testing ${config.name} codebase (${config.size})...`);
    
    try {
      // Run quick benchmark
      const output = execSync(
        `gql-generator quick-benchmark --test-size ${config.size}`,
        { encoding: 'utf8', timeout: 120000 }
      );
      
      // Parse speedup from output
      const speedupMatch = output.match(/Performance improvement: ([\d.]+)x faster/);
      const speedup = speedupMatch ? parseFloat(speedupMatch[1]) : 0;
      
      const result = {
        name: config.name,
        size: config.size,
        speedup,
        expectedSpeedup: config.expectedSpeedup,
        passed: speedup >= config.expectedSpeedup * 0.8, // Allow 20% variance
        output: output.trim()
      };
      
      results.push(result);
      
      if (result.passed) {
        console.log(`   ✅ PASSED: ${speedup}x speedup (expected: ${config.expectedSpeedup}x)`);
      } else {
        console.log(`   ❌ FAILED: ${speedup}x speedup (expected: ${config.expectedSpeedup}x)`);
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      results.push({
        name: config.name,
        size: config.size,
        speedup: 0,
        expectedSpeedup: config.expectedSpeedup,
        passed: false,
        error: error.message
      });
    }
    
    console.log('');
  }
}

function generateReport() {
  console.log('📄 Performance Validation Report\n');
  console.log('=' .repeat(60));
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const overallPassed = passedTests === totalTests;
  
  console.log(`Overall Result: ${overallPassed ? '✅ PASSED' : '❌ FAILED'} (${passedTests}/${totalTests} tests passed)\n`);
  
  // Detailed results
  results.forEach(result => {
    console.log(`${result.name} Codebase (${result.size}):`);
    console.log(`  Status: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`  Speedup: ${result.speedup}x (expected: ${result.expectedSpeedup}x)`);
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    }
    console.log('');
  });
  
  // Performance summary
  const validResults = results.filter(r => r.speedup > 0);
  if (validResults.length > 0) {
    const avgSpeedup = validResults.reduce((sum, r) => sum + r.speedup, 0) / validResults.length;
    const maxSpeedup = Math.max(...validResults.map(r => r.speedup));
    const minSpeedup = Math.min(...validResults.map(r => r.speedup));
    
    console.log('Performance Summary:');
    console.log(`  Average Speedup: ${avgSpeedup.toFixed(2)}x`);
    console.log(`  Maximum Speedup: ${maxSpeedup.toFixed(2)}x`);
    console.log(`  Minimum Speedup: ${minSpeedup.toFixed(2)}x`);
    console.log('');
  }
  
  // Recommendations
  console.log('Recommendations:');
  if (overallPassed) {
    console.log('  ✅ Performance optimizations are working correctly');
    console.log('  ✅ Comment directive parsing is significantly faster');
    console.log('  ✅ Ready for production use with large schemas');
  } else {
    console.log('  ⚠️  Some performance tests failed');
    console.log('  ⚠️  Review optimization configuration');
    console.log('  ⚠️  Consider system-specific tuning');
  }
  
  console.log('\n' + '=' .repeat(60));
  
  return overallPassed;
}

function validateEnvironment() {
  console.log('🔧 Validating environment...\n');
  
  try {
    // Check if gql-generator is installed
    execSync('gql-generator --version', { encoding: 'utf8' });
    console.log('✅ gql-generator is installed and accessible');
    
    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`✅ Node.js version: ${nodeVersion}`);
    
    // Check available memory
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();
    console.log(`✅ Memory: ${Math.round(freeMemory / 1024 / 1024)}MB free of ${Math.round(totalMemory / 1024 / 1024)}MB total`);
    
    console.log('');
    return true;
    
  } catch (error) {
    console.log(`❌ Environment validation failed: ${error.message}`);
    return false;
  }
}

function saveReport() {
  const reportData = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    results,
    summary: {
      totalTests: results.length,
      passedTests: results.filter(r => r.passed).length,
      averageSpeedup: results.filter(r => r.speedup > 0).reduce((sum, r) => sum + r.speedup, 0) / results.filter(r => r.speedup > 0).length || 0
    }
  };
  
  const reportPath = path.join(process.cwd(), 'performance-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  console.log(`📄 Detailed report saved to: ${reportPath}`);
}

// Main execution
async function main() {
  try {
    // Validate environment
    if (!validateEnvironment()) {
      process.exit(1);
    }
    
    // Run validation tests
    await runValidation();
    
    // Generate and display report
    const passed = generateReport();
    
    // Save detailed report
    saveReport();
    
    // Exit with appropriate code
    process.exit(passed ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Handle CLI arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node validate-performance.js [options]

Options:
  --help, -h     Show this help message
  
This script validates the performance optimizations for comment directive parsing
by running benchmarks and comparing results against expected performance targets.

Expected performance improvements:
  - Small codebases: 5x faster
  - Medium codebases: 10x faster  
  - Large codebases: 15x faster

The script will:
1. Validate the environment setup
2. Run performance benchmarks for different codebase sizes
3. Compare results against expected performance targets
4. Generate a comprehensive performance report
5. Save detailed results to performance-validation-report.json

Exit codes:
  0 - All performance tests passed
  1 - One or more performance tests failed or error occurred
`);
  process.exit(0);
}

// Run the validation
main();
