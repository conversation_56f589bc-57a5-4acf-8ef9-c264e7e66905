import type { CodegenConfig } from '@graphql-codegen/cli';

/**
 * Note: Scalar types are automatically detected from schema files.
 * Known scalars (built-in + common) keep proper TypeScript types, unknown scalars use 'unknown'.
 *
 * This configuration is used as a fallback when the gql-generator
 * CLI is not available.
 */

const config: CodegenConfig = {
  overwrite: true,
  schema: './schema/**/*.gql',
  generates: {
    'src/generated/graphql.ts': {
      plugins: ['typescript', 'typescript-resolvers'],
      config: {
        useIndexSignature: true,
        contextType: '@gql-generator/context#Context',
        mapperTypeSuffix: 'Model',
        mappers: {
          // Add your mappers here
        },
        enumsAsTypes: false,
        // Fix the naming convention to properly handle all type names
        namingConvention: {
          typeNames: './src/utils/custom-naming-convention',
          enumValues: 'keep', // Preserve enum values as specified in schema
          transformUnderscore: false
        },
        // Scalars are automatically handled by gql-generator
        scalars: {
          // Known scalars keep proper types, unknown scalars map to 'unknown'
        },
      },
    },
    './src/generated/schema.gql': {
      plugins: ['schema-ast'],
    },
    // Generate resolver stubs
    './src/resolvers/': {
      preset: 'near-operation-file',
      presetConfig: {
        baseTypesPath: '../generated/graphql.ts',
        extension: '.ts',
        folder: '../resolvers',
      },
      plugins: ['typescript-resolvers'],
      config: {
        useIndexSignature: true,
        contextType: '@gql-generator/context#Context',
        resolverTypeWrapperSignature: 'Promise<T> | T',
        skipTypename: true,
        maybeValue: 'T | null | undefined',
        avoidOptionals: false,
        onlyOperationTypes: false,
        customResolverFn:
          '(obj, args, context, info) => { throw new Error("Resolver not implemented"); }',
        defaultMapper: 'any',
        noSchemaStitching: true,
        // Fix the naming convention to properly handle all type names
        namingConvention: {
          typeNames: './src/utils/custom-naming-convention',
          enumValues: 'keep', // Preserve enum values as specified in schema
          transformUnderscore: false
        },
        // Scalars are automatically handled by gql-generator
        scalars: {
          // Known scalars keep proper types, unknown scalars map to 'unknown'
        },
      },
      schema: './schema/**/*.gql',
      hooks: {
        afterOneFileWrite: ['prettier --write'],
      },
    },
    // Generate mutator stubs
    './src/mutators/': {
      preset: 'near-operation-file',
      presetConfig: {
        baseTypesPath: '../generated/graphql.ts',
        extension: '.ts',
        folder: '../mutators',
      },
      plugins: ['typescript-resolvers'],
      config: {
        useIndexSignature: true,
        contextType: '@gql-generator/context#Context',
        resolverTypeWrapperSignature: 'Promise<T> | T',
        skipTypename: true,
        maybeValue: 'T | null | undefined',
        avoidOptionals: false,
        onlyOperationTypes: true,
        customResolverFn:
          '(obj, args, context, info) => { throw new Error("Mutator not implemented"); }',
        defaultMapper: 'any',
        noSchemaStitching: true,
        // Fix the naming convention to properly handle all type names
        namingConvention: {
          typeNames: './src/utils/custom-naming-convention',
          enumValues: 'keep', // Preserve enum values as specified in schema
          transformUnderscore: false
        },
        // Scalars are automatically handled by gql-generator
        scalars: {
          // Known scalars keep proper types, unknown scalars map to 'unknown'
        },
      },
      schema: './schema/**/*.gql',
      hooks: {
        afterOneFileWrite: ['prettier --write'],
      },
    },
  },
  hooks: {
    afterAllFileWrite: ['prettier --write'],
  },
};

export default config;
