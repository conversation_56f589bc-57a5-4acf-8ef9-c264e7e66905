# GraphQL Directive to TypeScript Decorator Migration Guide

This guide explains how to migrate from comment-based GraphQL directives to TypeScript decorators using the migration tool.

## Table of Contents

- [Installation](#installation)
- [Overview](#overview)
- [Comment-Based Directives](#comment-based-directives)
- [TypeScript Decorators](#typescript-decorators)
- [Alias Import Handling](#alias-import-handling)
- [Migration Process](#migration-process)
- [Command Reference](#command-reference)
- [Examples](#examples)
- [Troubleshooting](#troubleshooting)

## Installation

Before using the migration tool, you need to install the package. You have several options:

### Global Installation (Recommended)
```bash
# Install globally for system-wide access
npm install -g .

# Now you can use the command anywhere
gql-generator migrate --schema ./schema --codebase ./src
```

### Alternative Command Options

Once installed, you can use any of these commands:

```bash
# Full command name
gql-generator migrate --schema ./schema --codebase ./src

# Short alias
gqgen migrate --schema ./schema --codebase ./src

# NPX (no installation required)
npx gql-generator migrate --schema ./schema --codebase ./src

# Direct execution (for development)
./src/cli.js migrate --schema ./schema --codebase ./src
```

**Recommended:** Use `gql-generator` for consistency with the package name.

## Overview

The migration tool transforms comment-based directives in GraphQL schema files into TypeScript decorators on the corresponding methods. This provides better type safety, IDE support, and maintainability.

### Before Migration
```graphql
# @import(import { formatDate } from '@utils/date-utils')
# @import(import { getUserName } from '@utils/user-utils')
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  name: String!
  # @methodCall(formatDate(obj.createdAt))
  createdAt: String!
}

type Query {
  # @methodCall(findUserById(args.id))
  user(id: ID!): User
}
```

### After Migration
```typescript
// utils/user-utils.ts
@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)", schema: "public" })
export function getUserName(id: string): string {
  return `User ${id}`;
}

@GQLMethodCall({ type: "Query", field: "user", call: "findUserById(args.id)", schema: "public" })
export function findUserById(id: string) {
  return { id, name: getUserName(id), createdAt: new Date().toISOString() };
}

// utils/date-utils.ts
@GQLMethodCall({ type: "User", field: "createdAt", call: "formatDate(obj.createdAt)", schema: "public" })
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
```

```graphql
type User {
  id: ID!
  name: String!
  createdAt: String!
}

type Query {
  user(id: ID!): User
}
```

## Comment-Based Directives

### @import Directive

Specifies TypeScript imports needed for method calls:

```graphql
# @import(import { formatDate } from '@utils/date-utils')
# @import(import { getUserName, findUserById } from '@utils/user-utils')
# @import(import { UserMetadata } from '@types/user-metadata')
```

**Rules:**
- Must be valid TypeScript import syntax
- Supports both named and default imports
- Can use alias paths (e.g., `@services`, `@utils`)
- Required for all classes/functions used in @methodCall

### @methodCall Directive

Defines method calls for GraphQL field resolution:

```graphql
type User {
  # @methodCall(getUserName(obj.id))
  name: String!
  # @methodCall(getUserEmail(obj.id))
  email: String!
  # @methodCall(formatDate(obj.createdAt))
  createdAt: String!
}

type Query {
  # @methodCall(findUserById(args.id))
  user(id: ID!): User
  # @methodCall(searchUsers(args.query, args.limit))
  searchUsers(query: String!, limit: Int): [User!]!
}
```

**Supported Patterns:**
- Standalone functions: `formatDate(obj.createdAt)`
- Utility functions: `getUserName(obj.id)`
- Class static methods: `UserService.getName(obj.id)`
- Instance methods: `userService.getName(obj.id)`
- Complex expressions: `findUserByEmail(args.email.toLowerCase())`

### @field Directive

Adds custom fields with type information:

```graphql
# @import(import { UserMetadata } from '@types/user-metadata')
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  name: String!
  # @field(metadata: UserMetadata)
}
```

## TypeScript Decorators

After migration, functions are decorated with `@GQLMethodCall`:

```typescript
@GQLMethodCall({
  type: "User",           // GraphQL type name
  field: "name",          // GraphQL field name
  call: "getUserName(obj.id)",  // Original function call
  schema: "public"        // Schema identifier
})
export function getUserName(id: string): string {
  return `User ${id}`;
}
```

### Decorator Properties

- **type**: GraphQL type containing the field
- **field**: GraphQL field name
- **call**: Original method call expression
- **schema**: Schema identifier for multi-schema support

## Alias Import Handling

### Configuration

Configure aliases using the `--alias-codebase` flag:

```bash
./src/cli.js migrate --alias-codebase "@app"
```

### Alias Resolution

The tool resolves aliases to actual file paths:

```
@services/user-service → ./codebase/services/user-service.ts
@utils/date-utils      → ./codebase/utils/date-utils.ts
@types/user-metadata   → ./codebase/types/user-metadata.ts
```

### Supported Alias Patterns

#### Service Classes
```graphql
# @import(import { UserService } from '@services/user-service')
# @methodCall(UserService.findById(args.id))
```

#### Utility Functions
```graphql
# @import(import { formatDate, formatTime } from '@utils/date-utils')
# @methodCall(formatDate(obj.createdAt))
```

#### Type Definitions
```graphql
# @import(import { UserMetadata } from '@types/user-metadata')
# @field(metadata: UserMetadata)
```

#### Nested Paths
```graphql
# @import(import { executeQuery } from '@services/database/connection')
# @methodCall(executeQuery(args.sql))
```

### Multiple Aliases

For complex projects, you can use multiple aliases:

```bash
# Configure multiple aliases
--alias-codebase "@app"
# This maps all @xxx paths to ./codebase/xxx

# Or use specific aliases (if supported)
--alias-services "@services" --alias-utils "@utils"
```

## Migration Process

### 1. Pre-Migration Setup

Ensure your project structure is ready:

```
project/
├── schema/              # GraphQL schema files
│   ├── user.gql
│   └── post.gql
├── codebase/           # TypeScript source files
│   ├── services/
│   ├── utils/
│   └── types/
└── output/             # Generated files (optional)
```

### 2. Run Migration

```bash
gql-generator migrate \
  --schema ./schema \
  --codebase ./codebase \
  --schema-id public \
  --alias-codebase "@app" \
  --verbose
```

### 3. Verify Results

Check that:
- ✅ All directives were migrated
- ✅ Decorators were added to correct methods
- ✅ GraphQL files are clean (no directives)
- ✅ Backup files were created

### 4. Test Your Code

Run your tests to ensure functionality is preserved:

```bash
npm test
# or
yarn test
```

## Command Reference

### Basic Migration
```bash
gql-generator migrate --schema <schema-path> --codebase <codebase-path>
```

### Full Options
```bash
gql-generator migrate \
  --schema <schema-path> \
  --codebase <codebase-path> \
  --schema-id <schema-identifier> \
  --alias-codebase <alias-prefix> \
  --output <output-path> \
  --verbose \
  --dry-run
```

### Option Details

| Option | Description | Example |
|--------|-------------|---------|
| `--schema` | Path to GraphQL schema files | `./schema` |
| `--codebase` | Path to TypeScript source files | `./src` |
| `--schema-id` | Schema identifier for multi-schema | `public` |
| `--alias-codebase` | Alias prefix for imports | `@app` |
| `--output` | Output directory for generated files | `./generated` |
| `--verbose` | Enable detailed logging | - |
| `--dry-run` | Preview changes without modifying files | - |

## Examples

### Basic Example

**Schema (user.gql):**
```graphql
# @import(import { getUserName, findUserById } from '@utils/user-utils')
# @import(import { formatDate } from '@utils/date-utils')
type User {
  id: ID!
  # @methodCall(getUserName(obj.id))
  name: String!
  # @methodCall(formatDate(obj.createdAt))
  createdAt: String!
}

type Query {
  # @methodCall(findUserById(args.id))
  user(id: ID!): User
}
```

**Command:**
```bash
gql-generator migrate --schema ./schema --codebase ./src --alias-codebase "@app"
```

**Result:**
```typescript
// src/utils/user-utils.ts
@GQLMethodCall({ type: "User", field: "name", call: "getUserName(obj.id)", schema: "default" })
export function getUserName(id: string): string {
  return `User ${id}`;
}

@GQLMethodCall({ type: "Query", field: "user", call: "findUserById(args.id)", schema: "default" })
export function findUserById(id: string) {
  return { id, name: getUserName(id), createdAt: new Date().toISOString() };
}

// src/utils/date-utils.ts
@GQLMethodCall({ type: "User", field: "createdAt", call: "formatDate(obj.createdAt)", schema: "default" })
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}
```

### Multi-Schema Example

**Single Command (Recommended):**
```bash
gql-generator migrate \
  --schema ./schema/public --schema-id public \
  --schema ./schema/admin --schema-id admin \
  --codebase ./src --alias-codebase "@app"
```

**Separate Commands (Alternative):**
```bash
gql-generator migrate --schema ./schema/public --codebase ./src --schema-id public --alias-codebase "@app"
gql-generator migrate --schema ./schema/admin --codebase ./src --schema-id admin --alias-codebase "@app"
```

**Result:**
```typescript
// src/utils/user-utils.ts
@GQLMethodCall({ type: "User", field: "name", call: "getPublicUserName(obj.id)", schema: "public" })
export function getPublicUserName(id: string): string {
  return `User ${id}`;
}

@GQLMethodCall({ type: "AdminUser", field: "name", call: "getFullUserName(obj.id)", schema: "admin" })
export function getFullUserName(id: string): string {
  return `Admin User ${id} (Full Name)`;
}
```

## Troubleshooting

### Common Issues

#### 1. Function Not Found
```
❌ No matching function found for: getUserName(obj.id)
```

**Solutions:**
- Verify the function exists in the TypeScript file
- Check the function name matches exactly
- Ensure the file is in the correct location
- Verify alias configuration
- Check if function is properly exported

#### 2. Alias Resolution Failed
```
❌ Could not resolve alias: @utils/user-utils
```

**Solutions:**
- Check `--alias-codebase` configuration
- Verify file exists at resolved path
- Use `--verbose` to see resolution details

#### 3. Import Syntax Error
```
❌ Invalid import syntax in @import directive
```

**Solutions:**
- Ensure valid TypeScript import syntax
- Check for missing quotes or brackets
- Verify import path is correct

#### 4. Duplicate Decorators
```
⚠️ Skipping duplicate decorator for methodName
```

**Solutions:**
- This is usually safe - indicates method already has decorator
- Use `--dry-run` to preview changes
- Check if migration was run multiple times

### Debug Mode

Use verbose mode for detailed information:

```bash
./src/cli.js migrate --schema ./schema --codebase ./src --verbose
```

This shows:
- File discovery process
- Directive parsing results
- Method resolution details
- Decorator injection process
- Error details and warnings

### Backup and Recovery

The tool automatically creates backups:

```
.migration-backup-2025-01-24T10-30-45-123Z/
├── schema/
│   └── user.gql.backup
└── codebase/
    └── services/
        └── user-service.ts.backup
```

To restore from backup:
```bash
# Restore specific file
cp .migration-backup-*/codebase/services/user-service.ts.backup ./src/services/user-service.ts

# Restore all files
cp -r .migration-backup-*/codebase/* ./src/
cp -r .migration-backup-*/schema/* ./schema/
```

### Performance Tips

For large codebases:
- Use specific schema paths instead of entire directories
- Consider migrating schemas one at a time
- Use `--dry-run` first to validate
- Monitor memory usage with many files

### Getting Help

If you encounter issues:
1. Check this guide for common solutions
2. Use `--verbose` mode for detailed logs
3. Verify your setup with the examples in `./examples/`
4. Check backup files for recovery options
