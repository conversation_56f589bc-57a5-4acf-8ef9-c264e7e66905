# Dependency directories
node_modules/
jspm_packages/

# Compiled output
dist/
build/
out/
*.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Generated files
src/generated/
schema/
output/

**/generated-types/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# IDE / Editor directories
.idea/
.vscode/
.cursor/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Testing
coverage/
.nyc_output/
test-results/
tests/snapshots/
test/
test-project/

# Other
.tmp/
temp/

# Test directories
tests/test-project/
test-results/

# Cache and generated directories
.eslintcache
/graphql/
/gen/
/dist/

test-output/
test-schemas/
test-schemas-enterprise/
test-schemas-subset/

# Rust/WASM build artifacts
gql-wasm-core/target/
gql-wasm-core/Cargo.lock
# gql-wasm-core/*.node  # Commented out - we want to commit pre-built binaries
gql-wasm-core/npm/

# Performance and test files
performance-validation-report.json
phase1-test-results.json
test-phase*.js
test-phase*/

# Temporary files and scripts
temp-*.js
scalar-mappings.js

# User codebase examples (if not needed in repo)
user_codebase/

# Additional test and example directories
examples/
test/
test-phase2/

# Documentation and summary files (if auto-generated)
*_SUMMARY.md
*_IMPLEMENTATION_SUMMARY.md
PERFORMANCE_*.md
PHASE*_*.md

# Package manager files (uncomment if you don't want to commit lock files)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Runtime and compiled files
*.tsbuildinfo
*.d.ts.map
*.js.map

# Backup and temporary files
*.bak
*.tmp
*.temp
*~

# Local configuration files
.env.*
!.env.example
config/local.json
config/development.json
config/production.json